// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { logger as Importedlogger } from "../utils/logger";
import { Merchant, PaymentMethodType } from '../types';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}
import paymentAnalyticsService, {
    AnalyticsPeriod,
    AnalyticsFilter
} from "../services/analytics/payment-(analytics).service";
import { logger as Importedlogger } from "../utils/logger";
import { Merchant, PaymentMethodType } from '../types';


/**
 * Analytics controller
 */
const analyticsController = {
    /**
     * Get payment analytics
     */
    getPaymentAnalytics: async (req: Request, res: Response)  =>  {
        try {
            // Parse filter parameters
            const filter: AnalyticsFilter = {};

            if (req.query.merchantId) {
                (filter).merchantId = req.query.merchantId as string;
            }

            if (req.query.startDate) {
                (filter).startDate = new Date(req.query.startDate as string);
            }

            if (req.query.endDate) {
                (filter).endDate = new Date(req.query.endDate as string);
            }

            if (req.query.period) {
                (filter).period = req.query.period as AnalyticsPeriod;
            } else {
                (filter).period = (AnalyticsPeriod).MONTH; // Default to month
            }

            const analytics = await (paymentAnalyticsService).getPaymentAnalytics(filter);
            return res.status(200).json(analytics);
        } catch (error) {
            logger.error("Error getting payment analytics:", error);
            return res.status(500).json({ error: "Failed to get payment analytics" });
        }
    },

    /**
     * Get payment method analytics
     */
    getPaymentMethodAnalytics: async (req: Request, res: Response)  =>  {
        try {
            const { paymentMethodType } = req.params;

            if (!paymentMethodType) {
                return res.status(400).json({ error: "Payment method type is required" });
            }

            // Parse filter parameters
            const filter: AnalyticsFilter = {};

            if (req.query.merchantId) {
                (filter).merchantId = req.query.merchantId as string;
            }

            if (req.query.startDate) {
                (filter).startDate = new Date(req.query.startDate as string);
            }

            if (req.query.endDate) {
                (filter).endDate = new Date(req.query.endDate as string);
            }

            if (req.query.period) {
                (filter).period = req.query.period as AnalyticsPeriod;
            } else {
                (filter).period = (AnalyticsPeriod).MONTH; // Default to month
            }

            const analytics = await (paymentAnalyticsService).getPaymentMethodAnalytics(
                paymentMethodType as PaymentMethodType,
                filter
            );

            return res.status(200).json(analytics);
        } catch (error) {
            logger.error("Error getting payment method analytics:", error);
            return res.status(500).json({ error: "Failed to get payment method analytics" });
        }
    },

    /**
     * Get merchant analytics
     */
    getMerchantAnalytics: async (req: Request, res: Response)  =>  {
        try {
            const { merchantId } = req.params;

            if (!merchantId) {
                return res.status(400).json({ error: "Merchant ID is required" });
            }

            // Parse filter parameters
            const filter: AnalyticsFilter = {
                merchantId
            };

            if (req.query.startDate) {
                (filter).startDate = new Date(req.query.startDate as string);
            }

            if (req.query.endDate) {
                (filter).endDate = new Date(req.query.endDate as string);
            }

            if (req.query.period) {
                (filter).period = req.query.period as AnalyticsPeriod;
            } else {
                (filter).period = (AnalyticsPeriod).MONTH; // Default to month
            }

            const analytics = await (paymentAnalyticsService).getMerchantAnalytics(merchantId, filter);
            return res.status(200).json(analytics);
        } catch (error) {
            logger.error("Error getting merchant analytics:", error);
            return res.status(500).json({ error: "Failed to get merchant analytics" });
        }
    },

    /**
     * Get dashboard analytics
     */
    getDashboardAnalytics: async (req: Request, res: Response)  =>  {
        try {
            // Get user role and ID from request
            const { role, id } = req.user as { role: string; id: string };

            // Parse filter parameters
            const filter: AnalyticsFilter = {};

            if (req.query.startDate) {
                (filter).startDate = new Date(req.query.startDate as string);
            }

            if (req.query.endDate) {
                (filter).endDate = new Date(req.query.endDate as string);
            }

            if (req.query.period) {
                (filter).period = req.query.period as AnalyticsPeriod;
            } else {
                (filter).period = (AnalyticsPeriod).MONTH; // Default to month
            }

            // Get analytics based on user role
            let analytics;
            if (role === "admin") {
                // Admin sees all analytics
                analytics = await (paymentAnalyticsService).getPaymentAnalytics(filter);
            } else if (role === "merchant") {
                // Merchant sees only their analytics
                analytics = await (paymentAnalyticsService).getMerchantAnalytics(id, filter);
            } else {
                return res.status(403).json({ error: "Unauthorized" });
            }

            return res.status(200).json(analytics);
        } catch (error) {
            logger.error("Error getting dashboard analytics:", error);
            return res.status(500).json({ error: "Failed to get dashboard analytics" });
        }
    }
};

export default analyticsController;
