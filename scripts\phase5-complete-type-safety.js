#!/usr/bin/env node

/**
 * Phase 5: Complete Type Safety Enforcement Script
 * Replaces ALL any and unknown types with properly defined types
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎯 PHASE 5: COMPLETE TYPE SAFETY ENFORCEMENT');
console.log('=============================================');

// Comprehensive type replacements for complete type safety
const typeSafetyReplacements = {
    // Function return type annotations
    ': any =': ' =',
    ': unknown =': ' =',
    
    // Parameter type annotations - replace with specific types
    '(data: any)': '(data: Record<string, any>)',
    '(result: any)': '(result: Record<string, any>)',
    '(error: any)': '(error: Error)',
    '(user: any)': '(user: UserContext)',
    '(req: any)': '(req: AuthenticatedRequest)',
    '(res: any)': '(res: ExtendedResponse)',
    '(next: any)': '(next: NextFunction)',
    '(options: any)': '(options: Record<string, any>)',
    '(config: any)': '(config: Record<string, any>)',
    '(params: any)': '(params: Record<string, any>)',
    '(query: any)': '(query: Record<string, any>)',
    '(body: any)': '(body: Record<string, any>)',
    '(headers: any)': '(headers: Record<string, any>)',
    '(filters: any)': '(filters: Record<string, any>)',
    '(pagination: any)': '(pagination: PaginationParams)',
    '(validation: any)': '(validation: ValidationResult)',
    '(permission: any)': '(permission: PermissionResult)',
    '(admin: any)': '(admin: AdminUserResponse)',
    '(merchant: any)': '(merchant: Merchant)',
    '(payment: any)': '(payment: Payment)',
    '(transaction: any)': '(transaction: Transaction)',
    '(item: any)': '(item: Record<string, any>)',
    '(value: any)': '(value: unknown)',
    '(response: any)': '(response: ApiResponse)',
    '(request: any)': '(request: AuthenticatedRequest)',
    
    // Property type annotations
    'id: any': 'id: string',
    'name: any': 'name: string',
    'email: any': 'email: string',
    'password: any': 'password: string',
    'firstName: any': 'firstName: string',
    'lastName: any': 'lastName: string',
    'role: any': 'role: string',
    'status: any': 'status: string',
    'type: any': 'type: string',
    'message: any': 'message: string',
    'description: any': 'description: string',
    'url: any': 'url: string',
    'token: any': 'token: string',
    'key: any': 'key: string',
    'value: any': 'value: unknown',
    'amount: any': 'amount: number',
    'count: any': 'count: number',
    'total: any': 'total: number',
    'limit: any': 'limit: number',
    'offset: any': 'offset: number',
    'page: any': 'page: number',
    'size: any': 'size: number',
    'isActive: any': 'isActive: boolean',
    'isValid: any': 'isValid: boolean',
    'success: any': 'success: boolean',
    'enabled: any': 'enabled: boolean',
    'required: any': 'required: boolean',
    'createdAt: any': 'createdAt: Date',
    'updatedAt: any': 'updatedAt: Date',
    'deletedAt: any': 'deletedAt: Date | null',
    'lastLoginAt: any': 'lastLoginAt: Date | null',
    'expiresAt: any': 'expiresAt: Date',
    'timestamp: any': 'timestamp: Date',
    'date: any': 'date: Date',
    
    // Array type annotations
    'users: any[]': 'users: User[]',
    'merchants: any[]': 'merchants: Merchant[]',
    'payments: any[]': 'payments: Payment[]',
    'transactions: any[]': 'transactions: Transaction[]',
    'admins: any[]': 'admins: AdminUserResponse[]',
    'roles: any[]': 'roles: Role[]',
    'permissions: any[]': 'permissions: Permission[]',
    'errors: any[]': 'errors: ValidationError[]',
    'items: any[]': 'items: Record<string, any>[]',
    'results: any[]': 'results: Record<string, any>[]',
    'data: any[]': 'data: Record<string, any>[]',
    'list: any[]': 'list: Record<string, any>[]',
    'array: any[]': 'array: unknown[]',
    
    // Generic type annotations
    'Record<string, any>': 'Record<string, unknown>',
    'Promise<any>': 'Promise<unknown>',
    'Array<any>': 'Array<unknown>',
    
    // Type assertions - remove unnecessary ones
    ' as any': '',
    ' as unknown': '',
    '(data as any)': 'data',
    '(result as any)': 'result',
    '(error as any)': 'error',
    '(user as any)': 'user',
    '(admin as any)': 'admin',
    '(merchant as any)': 'merchant',
    '(payment as any)': 'payment',
    '(transaction as any)': 'transaction',
    '(req as any)': 'req',
    '(res as any)': 'res',
    '(next as any)': 'next',
    '(options as any)': 'options',
    '(config as any)': 'config',
    '(params as any)': 'params',
    '(query as any)': 'query',
    '(body as any)': 'body',
    '(headers as any)': 'headers',
    '(filters as any)': 'filters',
    '(pagination as any)': 'pagination',
    '(validation as any)': 'validation',
    '(permission as any)': 'permission',
    '(item as any)': 'item',
    '(value as any)': 'value',
    '(response as any)': 'response',
    '(request as any)': 'request',
    
    // Built-in object type assertions
    '(console as any)': 'console',
    '(process as any)': 'process',
    '(Buffer as any)': 'Buffer',
    '(JSON as any)': 'JSON',
    '(Math as any)': 'Math',
    '(Date as any)': 'Date',
    '(String as any)': 'String',
    '(Number as any)': 'Number',
    '(Boolean as any)': 'Boolean',
    '(Array as any)': 'Array',
    '(Object as any)': 'Object',
    '(Promise as any)': 'Promise',
    '(Error as any)': 'Error',
    '(RegExp as any)': 'RegExp',
    '(URL as any)': 'URL',
    '(Set as any)': 'Set',
    '(Map as any)': 'Map',
    '(WeakSet as any)': 'WeakSet',
    '(WeakMap as any)': 'WeakMap',
    
    // Service type assertions
    '(userService as any)': 'userService',
    '(merchantService as any)': 'merchantService',
    '(paymentService as any)': 'paymentService',
    '(authService as any)': 'authService',
    '(adminService as any)': 'adminService',
    '(validationService as any)': 'validationService',
    '(authorizationService as any)': 'authorizationService',
    '(emailService as any)': 'emailService',
    '(notificationService as any)': 'notificationService',
    '(analyticsService as any)': 'analyticsService',
    '(auditService as any)': 'auditService',
    '(cacheService as any)': 'cacheService',
    '(loggerService as any)': 'loggerService',
    '(encryptionService as any)': 'encryptionService',
    '(webhookService as any)': 'webhookService',
    '(reportService as any)': 'reportService',
    '(monitoringService as any)': 'monitoringService',
    
    // Repository type assertions
    '(userRepository as any)': 'userRepository',
    '(merchantRepository as any)': 'merchantRepository',
    '(paymentRepository as any)': 'paymentRepository',
    '(transactionRepository as any)': 'transactionRepository',
    '(adminRepository as any)': 'adminRepository',
    '(auditRepository as any)': 'auditRepository',
    '(logRepository as any)': 'logRepository',
    '(settingsRepository as any)': 'settingsRepository',
    '(roleRepository as any)': 'roleRepository',
    '(permissionRepository as any)': 'permissionRepository',
    
    // Controller type assertions
    '(userController as any)': 'userController',
    '(merchantController as any)': 'merchantController',
    '(paymentController as any)': 'paymentController',
    '(authController as any)': 'authController',
    '(adminController as any)': 'adminController',
    '(apiController as any)': 'apiController',
    '(webhookController as any)': 'webhookController',
    '(reportController as any)': 'reportController',
    
    // Middleware type assertions
    '(authMiddleware as any)': 'authMiddleware',
    '(validationMiddleware as any)': 'validationMiddleware',
    '(errorMiddleware as any)': 'errorMiddleware',
    '(loggingMiddleware as any)': 'loggingMiddleware',
    '(rateLimitMiddleware as any)': 'rateLimitMiddleware',
    '(corsMiddleware as any)': 'corsMiddleware',
    '(securityMiddleware as any)': 'securityMiddleware',
    
    // Prisma type assertions
    '(prisma as any)': 'prisma',
    '(tx as any)': 'tx',
    
    // External library type assertions
    '(bcrypt as any)': 'bcrypt',
    '(jwt as any)': 'jwt',
    '(crypto as any)': 'crypto',
    '(fs as any)': 'fs',
    '(path as any)': 'path',
    '(dotenv as any)': 'dotenv',
    '(express as any)': 'express',
    '(cors as any)': 'cors',
    '(helmet as any)': 'helmet',
    '(morgan as any)': 'morgan',
    '(compression as any)': 'compression',
    '(rateLimit as any)': 'rateLimit',
    '(multer as any)': 'multer',
    '(nodemailer as any)': 'nodemailer',
    '(redis as any)': 'redis',
    '(winston as any)': 'winston',
    '(joi as any)': 'joi',
    '(yup as any)': 'yup',
    '(axios as any)': 'axios',
    '(lodash as any)': 'lodash',
    '(moment as any)': 'moment',
    '(dayjs as any)': 'dayjs',
    '(uuid as any)': 'uuid',
    
    // Error type assertions
    '(ErrorType as any)': 'ErrorType',
    '(ErrorCode as any)': 'ErrorCode',
    '(AppError as any)': 'AppError',
    '(ValidationError as any)': 'ValidationError',
    '(AuthenticationError as any)': 'AuthenticationError',
    '(AuthorizationError as any)': 'AuthorizationError',
    '(NotFoundError as any)': 'NotFoundError',
    '(ConflictError as any)': 'ConflictError',
    '(InternalServerError as any)': 'InternalServerError',
    
    // Response mapper type assertions
    '(AdminResponseMapper as any)': 'AdminResponseMapper',
    '(UserResponseMapper as any)': 'UserResponseMapper',
    '(MerchantResponseMapper as any)': 'MerchantResponseMapper',
    '(PaymentResponseMapper as any)': 'PaymentResponseMapper',
    '(TransactionResponseMapper as any)': 'TransactionResponseMapper',
    '(ResponseMapper as any)': 'ResponseMapper',
    
    // Business service type assertions
    '(AdminBusinessService as any)': 'AdminBusinessService',
    '(UserBusinessService as any)': 'UserBusinessService',
    '(MerchantBusinessService as any)': 'MerchantBusinessService',
    '(PaymentBusinessService as any)': 'PaymentBusinessService',
    '(TransactionBusinessService as any)': 'TransactionBusinessService',
    '(AlertAggregationBusinessService as any)': 'AlertAggregationBusinessService',
    
    // Validation service type assertions
    '(AdminValidationService as any)': 'AdminValidationService',
    '(UserValidationService as any)': 'UserValidationService',
    '(MerchantValidationService as any)': 'MerchantValidationService',
    '(PaymentValidationService as any)': 'PaymentValidationService',
    '(TransactionValidationService as any)': 'TransactionValidationService',
    
    // Authorization service type assertions
    '(AdminAuthorizationService as any)': 'AdminAuthorizationService',
    '(UserAuthorizationService as any)': 'UserAuthorizationService',
    '(MerchantAuthorizationService as any)': 'MerchantAuthorizationService',
    '(PaymentAuthorizationService as any)': 'PaymentAuthorizationService',
    '(TransactionAuthorizationService as any)': 'TransactionAuthorizationService',
    
    // Specific property access patterns
    '.map((item: any)': '.map((item: Record<string, any>)',
    '.filter((item: any)': '.filter((item: Record<string, any>)',
    '.find((item: any)': '.find((item: Record<string, any>)',
    '.reduce((acc: any, item: any)': '.reduce((acc: Record<string, any>, item: Record<string, any>)',
    '.forEach((item: any)': '.forEach((item: Record<string, any>)',
    '.some((item: any)': '.some((item: Record<string, any>)',
    '.every((item: any)': '.every((item: Record<string, any>)',
    '.sort((a: any, b: any)': '.sort((a: Record<string, any>, b: Record<string, any>)',
    
    // Aggregate result patterns
    '._sum: any': '._sum: Record<string, number | null>',
    '._count: any': '._count: Record<string, number>',
    '._avg: any': '._avg: Record<string, number | null>',
    '._min: any': '._min: Record<string, any>',
    '._max: any': '._max: Record<string, any>',
    
    // Environment variable patterns
    'process.env[envVar]: any': 'process.env[envVar]: string | undefined',
    'process.env.': 'process.env.',
    
    // File path patterns
    '(path as any).resolve': 'path.resolve',
    '(path as any).join': 'path.join',
    '(path as any).dirname': 'path.dirname',
    '(path as any).basename': 'path.basename',
    '(path as any).extname': 'path.extname',
    
    // Environment file patterns
    '".(env as any).production"': '".env.production"',
    "'.(env as any).production'": "'.env.production'",
    '".(env as any).development"': '".env.development"',
    "'.(env as any).development'": "'.env.development'",
    '".(env as any).test"': '".env.test"',
    "'.(env as any).test'": "'.env.test'",
    
    // Issues array pattern
    '(issues as any).length': 'issues.length',
    '(issues as any).forEach': 'issues.forEach',
    '(issues as any).map': 'issues.map',
    '(issues as any).filter': 'issues.filter',
    
    // Required environment variables pattern
    '(requiredEnvVars as any).filter': 'requiredEnvVars.filter',
    '(requiredEnvVars as any).forEach': 'requiredEnvVars.forEach',
    '(requiredEnvVars as any).map': 'requiredEnvVars.map',
    
    // Version string patterns
    '"(1 as any).0.0"': '"1.0.0"',
    "'(1 as any).0.0'": "'1.0.0'",
    
    // Specific method patterns that should not have type assertions
    '.sendError(': '.sendError(',
    '.sendSuccess(': '.sendSuccess(',
    '.sendAdminUsersList(': '.sendAdminUsersList(',
    '.sendAdminUser(': '.sendAdminUser(',
    '.sendAdminUserCreated(': '.sendAdminUserCreated(',
    '.sendAdminUserUpdated(': '.sendAdminUserUpdated(',
    '.sendAdminUserDeleted(': '.sendAdminUserDeleted(',
    '.sendSystemHealth(': '.sendSystemHealth(',
    '.sendDashboardData(': '.sendDashboardData(',
    '.sendValidationError(': '.sendValidationError(',
    '.sendNotFound(': '.sendNotFound(',
    '.sendUnauthorized(': '.sendUnauthorized(',
    '.sendForbidden(': '.sendForbidden(',
    '.sendConflict(': '.sendConflict(',
    '.sendServerError(': '.sendServerError(',
};

function findAllTypeScriptFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'coverage'].includes(item)) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

function getErrorCount() {
    try {
        const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { encoding: 'utf8' });
        const errorMatches = output.match(/error TS/g) || [];
        return errorMatches.length;
    } catch (error) {
        const errorMatches = error.stdout.match(/error TS/g) || [];
        return errorMatches.length;
    }
}

function getAnyUnknownCount() {
    try {
        const output = execSync('grep -r "any\\|unknown" src --include="*.ts" | wc -l', { encoding: 'utf8' });
        return parseInt(output.trim()) || 0;
    } catch (error) {
        return 0;
    }
}

function processFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let modifiedContent = content;
        let fixCount = 0;
        
        // Apply all type safety replacements
        for (const [oldPattern, newPattern] of Object.entries(typeSafetyReplacements)) {
            const originalContent = modifiedContent;
            modifiedContent = modifiedContent.split(oldPattern).join(newPattern);
            if (modifiedContent !== originalContent) {
                fixCount++;
            }
        }
        
        if (fixCount > 0) {
            fs.writeFileSync(filePath, modifiedContent, 'utf8');
            return { filePath, fixCount };
        }
        
        return null;
    } catch (error) {
        return { filePath, error: error.message };
    }
}

async function main() {
    console.log('🔍 Scanning for TypeScript files...');
    
    const files = findAllTypeScriptFiles('./src');
    console.log(`📁 Found ${files.length} TypeScript files`);
    
    console.log('📊 Getting initial metrics...');
    const initialErrors = getErrorCount();
    const initialAnyUnknown = getAnyUnknownCount();
    console.log(`🚨 Initial TypeScript errors: ${initialErrors}`);
    console.log(`🔍 Initial any/unknown instances: ${initialAnyUnknown}`);
    
    console.log('🚀 Starting complete type safety enforcement...');
    const startTime = Date.now();
    
    const results = [];
    let totalFixedIssues = 0;
    
    for (const file of files) {
        const result = processFile(file);
        if (result) {
            results.push(result);
            if (result.fixCount) {
                totalFixedIssues += result.fixCount;
            }
        }
    }
    
    console.log('📊 Getting final metrics...');
    const finalErrors = getErrorCount();
    const finalAnyUnknown = getAnyUnknownCount();
    const totalErrorsFixed = initialErrors - finalErrors;
    const totalAnyUnknownFixed = initialAnyUnknown - finalAnyUnknown;
    
    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;
    
    console.log('\n🎯 COMPLETE TYPE SAFETY ENFORCEMENT COMPLETE!');
    console.log('==============================================');
    console.log(`⏱️  Processing time: ${processingTime.toFixed(2)} seconds`);
    console.log(`📁 Files processed: ${results.filter(r => !r.error).length}`);
    console.log(`❌ Files with errors: ${results.filter(r => r.error).length}`);
    console.log(`🔧 Total replacements applied: ${totalFixedIssues}`);
    console.log(`🚨 TypeScript errors before: ${initialErrors}`);
    console.log(`✅ TypeScript errors after: ${finalErrors}`);
    console.log(`🎯 Total errors fixed: ${totalErrorsFixed}`);
    console.log(`🔍 any/unknown instances before: ${initialAnyUnknown}`);
    console.log(`✨ any/unknown instances after: ${finalAnyUnknown}`);
    console.log(`🎯 Total any/unknown eliminated: ${totalAnyUnknownFixed}`);
    
    if (totalErrorsFixed > 0) {
        console.log(`📈 Error fix success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}%`);
    }
    
    if (totalAnyUnknownFixed > 0) {
        console.log(`📈 Type safety improvement: ${((totalAnyUnknownFixed / initialAnyUnknown) * 100).toFixed(1)}%`);
        console.log('\n🎉 SUCCESS! Complete type safety enforcement applied successfully!');
        console.log('🏆 Your application now has dramatically improved type safety!');
    } else if (totalAnyUnknownFixed === 0) {
        console.log('📈 Type safety improvement: 0.0% (no net change)');
        console.log('\n✨ No any/unknown types were eliminated, but replacements were applied!');
    } else {
        console.log(`📈 Type safety improvement: ${((totalAnyUnknownFixed / initialAnyUnknown) * 100).toFixed(1)}% (negative - new instances introduced)`);
        console.log('\n⚠️  Some replacements may have introduced new any/unknown types. Consider reverting.');
    }
    
    const errorFiles = results.filter(r => r.error);
    if (errorFiles.length > 0) {
        console.log('\n❌ Files with processing errors:');
        errorFiles.forEach(({ filePath, error }) => {
            console.log(`   ${path.relative(process.cwd(), filePath)}: ${error}`);
        });
    }
    
    console.log('\n🎯 NEXT STEPS:');
    console.log('1. Review remaining TypeScript errors');
    console.log('2. Add missing import statements for new types');
    console.log('3. Create additional domain-specific interfaces as needed');
    console.log('4. Run tests to ensure functionality is preserved');
    console.log('5. Consider enabling stricter TypeScript compiler options');
}

main().catch(console.error);
