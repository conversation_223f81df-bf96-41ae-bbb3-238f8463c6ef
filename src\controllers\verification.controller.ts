// jscpd:ignore-file

/**
 * Verification Controller
 *
 * This controller handles payment verification requests.
 */

import { Request, Response, NextFunction } from 'express';
import { validationResult } from "express-validator";
import verificationService from "../services/verification.service";
import blockchainVerificationService from "../services/blockchain-verification.service";
import { UnifiedVerificationService, PaymentVerificationStatus } from "../services/verification/unified-verification.service";
import { logger } from "../utils/logger";
import { VerificationErrorType } from "../utils/verification-error-handler";
import { validationResult } from "express-validator";
import { UnifiedVerificationService, PaymentVerificationStatus } from "../services/verification/unified-verification.service";
import { logger } from "../utils/logger";
import { VerificationErrorType } from "../utils/verification-error-handler";

class VerificationController {
    /**
   * Unified verification service
   */
    private unifiedVerificationService: UnifiedVerificationService;

    /**
   * Constructor
   */
    constructor() {
        this.unifiedVerificationService = new UnifiedVerificationService();
    }
    /**
   * Verify a payment using the appropriate verification method
   */
    async verifyPayment(req: Request, res: Response): Promise<Response> {
    // Check for validation errors
        const errors: unknown =validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ success: false, errors: errors.array() });
        }

        try {
            const {
                merchantId,
                amount,
                currency,
                paymentMethodId,
                verificationData
            } = req.body;

            // Check for blockchain verification
            if (verificationData.verificationMethod === "blockchain") {
                const { network, senderAddress, destinationAddress, txHash } = verificationData;

                // Validate required fields
                if (!network || !senderAddress) {
                    return res.status(400).json({
                        success: false,
                        message: "Missing required blockchain verification parameters"
                    });
                }

                // Perform blockchain verification
                const result: unknown = await blockchainVerificationService.verifyTransaction({
                    network,
                    senderAddress,
                    destinationAddress: destinationAddress ?? "",
                    amount,
                    currency,
                    txHash
                });

                return res.json({
                    verified: result.success,
                    status: result.success ? "success" : "pending",
                    txHash: result.txHash,
                    confirmations: result.confirmations,
                    message: (result as Error).message
                });
            }

            // Check for Binance C2C verification
            if (verificationData.verificationMethod === "binance_c2c") {
                const { note } = verificationData;

                // Validate required fields
                if (!note) {
                    return res.status(400).json({
                        success: false,
                        message: "Missing required Binance C2C verification parameters"
                    });
                }

                // Verify the note format (typically a UUID or specific format)
                const isValidNote: unknown =/^[a-zA-Z0-9-_]{6,36}$/.test(note);
                if (!isValidNote) {
                    return res.status(400).json({
                        success: false,
                        message: "Invalid note format"
                    });
                }

                // For now, pass to the standard verification service
                // In a real implementation, you would call the Binance API
                const verificationResult: unknown = await verificationService.verifyPayment({
                    merchantId,
                    amount,
                    currency,
                    paymentMethodId,
                    verificationData
                });

                return res.json({
                    verified: verificationResult.success,
                    status: verificationResult.success ? "success" : "pending",
                    message: (verificationResult as Error).message,
                    details: verificationResult.details
                });
            }

            // Check for Binance TRC20 Direct verification
            if (verificationData.verificationMethod === "binance_trc20") {
                const { txId } = verificationData;

                // Validate required fields
                if (!txId) {
                    return res.status(400).json({
                        success: false,
                        message: "Missing required Binance TRC20 verification parameters"
                    });
                }

                // Get the payment method to access the API credentials
                const paymentMethod: unknown = await verificationService.getPaymentMethod(paymentMethodId);

                if (!paymentMethod) {
                    return res.status(400).json({
                        success: false,
                        message: "Payment method not found"
                    });
                }

                // Verify that this is a Binance TRC20 payment method
                if (paymentMethod.type !== "binance_trc20") {
                    return res.status(400).json({
                        success: false,
                        message: "Invalid payment method type for Binance TRC20 verification"
                    });
                }

                // Verify the transaction using the Binance API
                const verificationResult: unknown = await verificationService.verifyBinanceTrc20Payment({
                    merchantId,
                    amount,
                    currency,
                    paymentMethodId,
                    txId,
                    paymentMethod
                });

                return res.json({
                    verified: verificationResult.success,
                    status: verificationResult.success ? "success" : "pending",
                    message: (verificationResult as Error).message,
                    details: verificationResult.details,
                    transactionId: verificationResult.transactionId
                });
            }

            // For other verification methods, use the standard verification service
            const verificationResult: unknown = await verificationService.verifyPayment({
                merchantId,
                amount,
                currency,
                paymentMethodId,
                verificationData
            });

            return res.json({
                verified: verificationResult.success,
                status: verificationResult.success ? "success" : "failed",
                message: (verificationResult as Error).message,
                details: verificationResult.details
            });
        } catch (error) {
            console.error("Verification error:", error);
            return res.status(500).json({
                success: false,
                message: "Internal server error during verification"
            });
        }
    }

    /**
   * Process webhooks from payment providers
   */
    async processWebhook(req: Request, res: Response): Promise<Response> {
        try {
            const { provider, payload, signature } = req.body;

            // Validate signature if provided
            if (signature) {
                const isValid: unknown = await this.verifyWebhookSignature(provider, payload, signature);
                if (!isValid) {
                    return res.status(401).json({
                        success: false,
                        message: "Invalid webhook signature"
                    });
                }
            }

            // Process based on provider
            switch (provider) {
            case "binance_c2c":
                return this.processBinanceC2CWebhook(req, res);

            case "blockchain":
                return this.processBlockchainWebhook(req, res);

            default:
                // Process generic webhook
                const result: unknown = await verificationService.processWebhook(payload);
                return res.json({
                    success: result.success,
                    message: (result as Error).message
                });
            }
        } catch (error) {
            console.error("Webhook processing error:", error);
            return res.status(500).json({
                success: false,
                message: "Internal server error processing webhook"
            });
        }
    }

    /**
   * Process Binance C2C webhooks
   */
    private async processBinanceC2CWebhook(req: Request, res: Response): Promise<Response> {
        try {
            const { payload } = req.body;

            // Validate required fields
            if (!payload.transactionId || !payload.merchantId || !payload.status) {
                return res.status(400).json({
                    success: false,
                    message: "Missing required Binance C2C webhook fields"
                });
            }

            // Process the webhook
            const result: unknown = await verificationService.processBinanceC2CWebhook(payload);

            return res.json({
                success: result.success,
                message: (result as Error).message
            });
        } catch (error) {
            console.error("Binance C2C webhook processing error:", error);
            return res.status(500).json({
                success: false,
                message: "Internal server error processing Binance C2C webhook"
            });
        }
    }

    /**
   * Process blockchain webhooks
   */
    private async processBlockchainWebhook(req: Request, res: Response): Promise<Response> {
        try {
            const { payload } = req.body;

            // Validate required fields
            if (!payload.transactionId || !payload.merchantId || !payload.blockchain?.network || !payload.blockchain?.txHash) {
                return res.status(400).json({
                    success: false,
                    message: "Missing required blockchain webhook fields"
                });
            }

            // Process the webhook
            const result: unknown = await blockchainVerificationService.processBlockchainWebhook(payload);

            return res.json({
                success: result.success,
                message: (result as Error).message,
                confirmations: result.confirmations
            });
        } catch (error) {
            console.error("Blockchain webhook processing error:", error);
            return res.status(500).json({
                success: false,
                message: "Internal server error processing blockchain webhook"
            });
        }
    }

    /**
   * Verify webhook signature
   */
    private async verifyWebhookSignature(
        provider: string,
        payload,
        signature: string
    ): Promise<boolean> {
        try {
            // Delegate to the appropriate service based on provider
            switch (provider) {
            case "binance_c2c":
                // In a real implementation, you would verify using Binance's signature mechanism
                return true;

            case "blockchain":
                // For blockchain webhooks, verify based on the network
                if (payload.blockchain?.network) {
                    return blockchainVerificationService.verifyWebhookSignature(
                        payload.blockchain.network,
                        payload,
                        signature
                    );
                }
                return false;

            default:
                // Generic signature verification
                return verificationService.verifyWebhookSignature(payload, signature);
            }
        } catch (error) {
            console.error("Signature verification error:", error);
            return false;
        }
    }

    /**
   * Verify a payment by ID using the unified verification service
   * @param req Request
   * @param res Response
   */
    async verifyPaymentById(req: Request, res: Response): Promise<Response> {
        try {
            const { paymentId } = req.params;

            // Validate payment ID
            if (!paymentId) {
                return res.status(400).json({
                    success: false,
                    message: "Payment ID is required"
                });
            }

            // Log verification request
            logger.info("Payment verification by ID requested", { paymentId });

            // Verify payment using the unified verification service
            const result: unknown = await this.unifiedVerificationService.verifyPayment(paymentId);

            // Return response based on verification status
            switch (result.status) {
            case PaymentVerificationStatus.VERIFIED:
                return res.json({
                    success: true,
                    status: "verified",
                    paymentId,
                    transactionId: result.transactionId,
                    paymentMethod: result.paymentMethod,
                    verifiedAt: result.verifiedAt
                });

            case PaymentVerificationStatus.PENDING:
                return res.json({
                    success: true,
                    status: "pending",
                    paymentId,
                    transactionId: result.transactionId,
                    paymentMethod: result.paymentMethod,
                    message: "Payment verification is in progress"
                });

            case PaymentVerificationStatus.EXPIRED:
                return res.json({
                    success: false,
                    status: "expired",
                    paymentId,
                    message: "Payment has expired"
                });

            case PaymentVerificationStatus.FAILED:
                return res.status(400).json({
                    success: false,
                    status: "failed",
                    paymentId,
                    message: result.errorMessage || "Payment verification failed",
                    errorCode: result.errorCode || VerificationErrorType.UNKNOWN_ERROR,
                    details: result.rawData
                });

            default:
                return res.status(500).json({
                    success: false,
                    status: "unknown",
                    paymentId,
                    message: "Unknown verification status"
                });
            }
        } catch (error) {
            // Log error
            logger.error("Error in payment verification by ID controller", {
                error: (error as Error).message || error,
                path: req.path
            });

            // Return error response
            return res.status(500).json({
                success: false,
                message: (error as Error).message || "An error occurred during payment verification"
            });
        }
    }
}

export default new VerificationController();
