// jscpd:ignore-file

/**
 * Verification Controller
 *
 * This controller handles payment verification requests.
 */

import { Request, Response, NextFunction } from 'express';
import { validationResult as ImportedvalidationResult } from "express-validator";
import verificationService from "../services/(verification as any).service";
import blockchainVerificationService from "../services/blockchain-(verification as any).service";
import { UnifiedVerificationService, PaymentVerificationStatus } from "../services/verification/unified-(verification as any).service";
import { logger as Importedlogger } from "../utils/logger";
import { VerificationErrorType as ImportedVerificationErrorType } from "../utils/verification-error-handler";
import { validationResult as ImportedvalidationResult } from "express-validator";
import { UnifiedVerificationService, PaymentVerificationStatus } from "../services/verification/unified-(verification as any).service";
import { logger as Importedlogger } from "../utils/logger";
import { VerificationErrorType as ImportedVerificationErrorType } from "../utils/verification-error-handler";

class VerificationController {
    /**
   * Unified verification service
   */
    private unifiedVerificationService: UnifiedVerificationService;

    /**
   * Constructor
   */
    constructor() {
        this.unifiedVerificationService = new UnifiedVerificationService();
    }
    /**
   * Verify a payment using the appropriate verification method
   */
    async verifyPayment(req: Request, res: Response): Promise<Response> {
    // Check for validation errors
        const errors: any =validationResult(req);
        if (!(errors as any).isEmpty()) {
            return res.status(400).json({ success: false, errors: (errors as any).array() });
        }

        try {
            const {
                merchantId,
                amount,
                currency,
                paymentMethodId,
                verificationData
            } = req.body;

            // Check for blockchain verification
            if ((verificationData as any).verificationMethod === "blockchain") {
                const { network, senderAddress, destinationAddress, txHash } = verificationData;

                // Validate required fields
                if (!network || !senderAddress) {
                    return res.status(400).json({
                        success: false,
                        message: "Missing required blockchain verification parameters"
                    });
                }

                // Perform blockchain verification
                const result = await (blockchainVerificationService as any).verifyTransaction({
                    network,
                    senderAddress,
                    destinationAddress: destinationAddress ?? "",
                    amount,
                    currency,
                    txHash
                });

                return res.json({
                    verified: result.success,
                    status: result.success ? "success" : "pending",
                    txHash: (result as any).txHash,
                    confirmations: (result as any).confirmations,
                    message: (result as Error).message
                });
            }

            // Check for Binance C2C verification
            if ((verificationData as any).verificationMethod === "binance_c2c") {
                const { note } = verificationData;

                // Validate required fields
                if (!note) {
                    return res.status(400).json({
                        success: false,
                        message: "Missing required Binance C2C verification parameters"
                    });
                }

                // Verify the note format (typically a UUID or specific format)
                const isValidNote = /^[a-zA-Z0-9-_]{6,36}$/.test(note);
                if (!isValidNote) {
                    return res.status(400).json({
                        success: false,
                        message: "Invalid note format"
                    });
                }

                // For now, pass to the standard verification service
                // In a real implementation, you would call the Binance API
                const verificationResult = await (verificationService as any).verifyPayment({
                    merchantId,
                    amount,
                    currency,
                    paymentMethodId,
                    verificationData
                });

                return res.json({
                    verified: (verificationResult as any).success,
                    status: (verificationResult as any).success ? "success" : "pending",
                    message: (verificationResult as Error).message,
                    details: (verificationResult as any).details
                });
            }

            // Check for Binance TRC20 Direct verification
            if ((verificationData as any).verificationMethod === "binance_trc20") {
                const { txId } = verificationData;

                // Validate required fields
                if (!txId) {
                    return res.status(400).json({
                        success: false,
                        message: "Missing required Binance TRC20 verification parameters"
                    });
                }

                // Get the payment method to access the API credentials
                const paymentMethod = await (verificationService as any).getPaymentMethod(paymentMethodId);

                if (!paymentMethod) {
                    return res.status(400).json({
                        success: false,
                        message: "Payment method not found"
                    });
                }

                // Verify that this is a Binance TRC20 payment method
                if ((paymentMethod as any).type !== "binance_trc20") {
                    return res.status(400).json({
                        success: false,
                        message: "Invalid payment method type for Binance TRC20 verification"
                    });
                }

                // Verify the transaction using the Binance API
                const verificationResult = await (verificationService as any).verifyBinanceTrc20Payment({
                    merchantId,
                    amount,
                    currency,
                    paymentMethodId,
                    txId,
                    paymentMethod
                });

                return res.json({
                    verified: (verificationResult as any).success,
                    status: (verificationResult as any).success ? "success" : "pending",
                    message: (verificationResult as Error).message,
                    details: (verificationResult as any).details,
                    transactionId: (verificationResult as any).transactionId
                });
            }

            // For other verification methods, use the standard verification service
            const verificationResult = await (verificationService as any).verifyPayment({
                merchantId,
                amount,
                currency,
                paymentMethodId,
                verificationData
            });

            return res.json({
                verified: (verificationResult as any).success,
                status: (verificationResult as any).success ? "success" : "failed",
                message: (verificationResult as Error).message,
                details: (verificationResult as any).details
            });
        } catch(error) {
            console.error("Verification error:", error);
            return res.status(500).json({
                success: false,
                message: "Internal server error during verification"
            });
        }
    }

    /**
   * Process webhooks from payment providers
   */
    async processWebhook(req: Request, res: Response): Promise<Response> {
        try {
            const { provider, payload, signature } = req.body;

            // Validate signature if provided
            if (signature) {
                const isValid: boolean = await this.verifyWebhookSignature(provider, payload, signature);
                if (!isValid) {
                    return res.status(401).json({
                        success: false,
                        message: "Invalid webhook signature"
                    });
                }
            }

            // Process based on provider
            switch (provider) {
            case "binance_c2c":
                return this.processBinanceC2CWebhook(req, res);

            case "blockchain":
                return this.processBlockchainWebhook(req, res);

            default:
                // Process generic webhook
                const result = await (verificationService as any).processWebhook(payload);
                return res.json({
                    success: result.success,
                    message: (result as Error).message
                });
            }
        } catch(error) {
            console.error("Webhook processing error:", error);
            return res.status(500).json({
                success: false,
                message: "Internal server error processing webhook"
            });
        }
    }

    /**
   * Process Binance C2C webhooks
   */
    private async processBinanceC2CWebhook(req: Request, res: Response): Promise<Response> {
        try {
            const { payload } = req.body;

            // Validate required fields
            if (!(payload as any).transactionId || !(payload as any).merchantId || !(payload as any).status) {
                return res.status(400).json({
                    success: false,
                    message: "Missing required Binance C2C webhook fields"
                });
            }

            // Process the webhook
            const result = await (verificationService as any).processBinanceC2CWebhook(payload);

            return res.json({
                success: result.success,
                message: (result as Error).message
            });
        } catch(error) {
            console.error("Binance C2C webhook processing error:", error);
            return res.status(500).json({
                success: false,
                message: "Internal server error processing Binance C2C webhook"
            });
        }
    }

    /**
   * Process blockchain webhooks
   */
    private async processBlockchainWebhook(req: Request, res: Response): Promise<Response> {
        try {
            const { payload } = req.body;

            // Validate required fields
            if (!(payload as any).transactionId || !(payload as any).merchantId || !(payload as any).blockchain?.network || !(payload as any).blockchain?.txHash) {
                return res.status(400).json({
                    success: false,
                    message: "Missing required blockchain webhook fields"
                });
            }

            // Process the webhook
            const result = await (blockchainVerificationService as any).processBlockchainWebhook(payload);

            return res.json({
                success: result.success,
                message: (result as Error).message,
                confirmations: (result as any).confirmations
            });
        } catch(error) {
            console.error("Blockchain webhook processing error:", error);
            return res.status(500).json({
                success: false,
                message: "Internal server error processing blockchain webhook"
            });
        }
    }

    /**
   * Verify webhook signature
   */
    private async verifyWebhookSignature(
        provider: string,
        payload,
        signature: string
    ): Promise<boolean> {
        try {
            // Delegate to the appropriate service based on provider
            switch (provider) {
            case "binance_c2c":
                // In a real implementation, you would verify using Binance's signature mechanism
                return true;

            case "blockchain":
                // For blockchain webhooks, verify based on the network
                if ((payload as any).blockchain?.network) {
                    return (blockchainVerificationService as any).verifyWebhookSignature(
                        (payload as any).blockchain.network,
                        payload,
                        signature
                    );
                }
                return false;

            default:
                // Generic signature verification
                return (verificationService as any).verifyWebhookSignature(payload, signature);
            }
        } catch(error) {
            console.error("Signature verification error:", error);
            return false;
        }
    }

    /**
   * Verify a payment by ID using the unified verification service
   * @param req Request
   * @param res Response
   */
    async verifyPaymentById(req: Request, res: Response): Promise<Response> {
        try {
            const { paymentId } = req.params;

            // Validate payment ID
            if (!paymentId) {
                return res.status(400).json({
                    success: false,
                    message: "Payment ID is required"
                });
            }

            // Log verification request
            (logger as any).info("Payment verification by ID requested", { paymentId });

            // Verify payment using the unified verification service
            const result = await this.unifiedVerificationService.verifyPayment(paymentId);

            // Return response based on verification status
            switch (result.status) {
            case (PaymentVerificationStatus as any).VERIFIED:
                return res.json({
                    success: true,
                    status: "verified",
                    paymentId,
                    transactionId: result.transactionId,
                    paymentMethod: result.paymentMethod,
                    verifiedAt: (result as any).verifiedAt
                });

            case (PaymentVerificationStatus as any).PENDING:
                return res.json({
                    success: true,
                    status: "pending",
                    paymentId,
                    transactionId: result.transactionId,
                    paymentMethod: result.paymentMethod,
                    message: "Payment verification is in progress"
                });

            case (PaymentVerificationStatus as any).EXPIRED:
                return res.json({
                    success: false,
                    status: "expired",
                    paymentId,
                    message: "Payment has expired"
                });

            case (PaymentVerificationStatus as any).FAILED:
                return res.status(400).json({
                    success: false,
                    status: "failed",
                    paymentId,
                    message: result.errorMessage || "Payment verification failed",
                    errorCode: result.errorCode || (VerificationErrorType as any).UNKNOWN_ERROR,
                    details: (result as any).rawData
                });

            default:
                return res.status(500).json({
                    success: false,
                    status: "unknown",
                    paymentId,
                    message: "Unknown verification status"
                });
            }
        } catch(error) {
            // Log error
            (logger as any).error("Error in payment verification by ID controller", {
                error: error.message || error,
                path: (req as any).path
            });

            // Return error response
            return res.status(500).json({
                success: false,
                message: error.message || "An error occurred during payment verification"
            });
        }
    }
}

export default new VerificationController();
