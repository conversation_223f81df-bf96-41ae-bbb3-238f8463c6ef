// jscpd:ignore-file

import { Router as ImportedRouter } from "express";
import { body, param } from "express-validator";
import { authenticate as Importedauthenticate } from "../middlewares/(auth as any).middleware";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import userController from "../controllers/(user as any).controller";
import { body, param } from "express-validator";
import { authenticate as Importedauthenticate } from "../middlewares/(auth as any).middleware";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";

const router: any =Router();

// Get current user details
(router as any).get(
    "/me",
    authenticate,
    (userController as any).getCurrentUser
);

// Get user profile
(router as any).get(
    "/:userId/profile",
    authenticate,
    validate([
        param("userId").notEmpty()
    ]),
    (userController as any).getUserProfile
);

// Update user profile
(router as any).put(
    "/:userId/profile",
    authenticate,
    validate([
        param("userId").notEmpty()
    ]),
    (userController as any).updateUserProfile
);

// Get user preferences
(router as any).get(
    "/:userId/preferences",
    authenticate,
    validate([
        param("userId").notEmpty()
    ]),
    (userController as any).getUserPreferences
);

// Update user preferences
(router as any).put(
    "/:userId/preferences",
    authenticate,
    validate([
        param("userId").notEmpty()
    ]),
    (userController as any).updateUserPreferences
);

// Update user settings
(router as any).put(
    "/:userId/settings",
    authenticate,
    validate([
        param("userId").notEmpty()
    ]),
    (userController as any).updateUserSettings
);

// Update notification preferences
(router as any).put(
    "/:userId/notifications/preferences",
    authenticate,
    validate([
        param("userId").notEmpty(),
        body("email").optional().isBoolean(),
        body("sms").optional().isBoolean(),
        body("push").optional().isBoolean()
    ]),
    (userController as any).updateNotificationPreferences
);

// Change password
(router as any).put(
    "/:userId/password",
    authenticate,
    validate([
        param("userId").notEmpty(),
        body("currentPassword").notEmpty().isString(),
        body("newPassword").notEmpty().isString().isLength({ min: 6 }),
        body("confirmPassword").notEmpty().isString().isLength({ min: 6 })
    ]),
    (userController as any).changePassword
);

// Request verification
(router as any).post(
    "/:userId/verification/request",
    authenticate,
    validate([
        param("userId").notEmpty(),
        body("type").isIn(["email", "phone"])
    ]),
    (userController as any).requestVerification
);

// Verify account
(router as any).post(
    "/:userId/verification/verify",
    authenticate,
    validate([
        param("userId").notEmpty(),
        body("verificationCode").notEmpty().isString(),
        body("type").isIn(["email", "phone"])
    ]),
    (userController as any).verifyAccount
);

export default router;
