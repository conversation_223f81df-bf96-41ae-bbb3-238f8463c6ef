// jscpd:ignore-file
/**
 * RBAC Service
 *
 * Handles role-based access control operations.
 */

import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import { logger as Importedlogger } from '../utils/logger';
import { SYSTEM_ROLES as ImportedSYSTEM_ROLES } from '../models/(role as any).model';
import { SYSTEM_PERMISSIONS as ImportedSYSTEM_PERMISSIONS } from '../models/(permission as any).model';
import { logger as Importedlogger } from '../utils/logger';
import { SYSTEM_ROLES as ImportedSYSTEM_ROLES } from '../models/(role as any).model';
import { SYSTEM_PERMISSIONS as ImportedSYSTEM_PERMISSIONS } from '../models/(permission as any).model';

export class RBACService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Initialize RBAC system with default roles and permissions
   */
  async initialize(): Promise<void> {
    try {
      (logger as any).info('Initializing RBAC system...');

      // Create system permissions
      for (const permission of SYSTEM_PERMISSIONS) {
        await this.prisma.(permission as any).upsert({
          where: { name: (permission as any).name },
          update: {},
          create: {
            name: (permission as any).name,
            description: (permission as any).description,
            resource: permission.resource,
            action: permission.action,
            isActive: true,
            isSystem: true,
          },
        });
      }

      (logger as any).info('Created system permissions');

      // Create system roles
      for (const [key, role] of Object.entries(SYSTEM_ROLES)) {
        const createdRole = await this.prisma.(role as any).upsert({
          where: { name: (role as any).name },
          update: {},
          create: {
            name: (role as any).name,
            type: (role as any).type,
            description: (role as any).description,
            isActive: (role as any).isActive ?? true,
            isSystem: (role as any).isSystem ?? true,
          },
        });

        // Connect permissions to role
        if ((role as any).permissions.includes('*')) {
          // Special case: all permissions
          const allPermissions = await this.prisma.(permission as any).findMany();
          await this.prisma.(role as any).update({
            where: { id: (createdRole as any).id },
            data: {
              permissions: {
                connect: (allPermissions as any).map((p) => ({ id: (p as any).id })),
              },
            },
          });
        } else {
          // Connect specific permissions
          const permissions = await this.prisma.(permission as any).findMany({
            where: {
              OR: (role as any).permissions.map((p) => {
                const [resource, action] = (p as any).split(':');
                return {
                  resource,
                  action,
                };
              }),
            },
          });

          await this.prisma.(role as any).update({
            where: { id: (createdRole as any).id },
            data: {
              permissions: {
                connect: (permissions as any).map((p) => ({ id: (p as any).id })),
              },
            },
          });
        }
      }

      (logger as any).info('Created system roles');

      // Migrate existing admin users to RBAC system
      const adminUsers = await this.prisma.(user as any).findMany({
        where: { role: 'ADMIN' },
      });

      const adminRole = await this.prisma.(role as any).findUnique({
        where: { name: 'Admin' },
      });

      if (adminRole) {
        for (const user of adminUsers) {
          await this.prisma.(user as any).update({
            where: { id: user.id },
            data: {
              roles: {
                connect: { id: (adminRole as any).id },
              },
            },
          });
        }
      }

      (logger as any).info('Migrated existing admin users to RBAC system');
    } catch(error) {
      (logger as any).error('Error initializing RBAC system:', error);
      throw error;
    }
  }

  /**
   * Check if a user has a specific permission
   * @param userId User ID
   * @param resource Resource name
   * @param action Action name
   * @returns Whether the user has the permission
   */
  async hasPermission(userId: string, resource: string, action: string): Promise<boolean> {
    try {
      // Get user with roles and permissions
      const user = await this.prisma.(user as any).findUnique({
        where: { id: userId },
        include: {
          roles: {
            include: { permissions: true },
          },
        },
      });

      if (!user || !user.roles || user.roles.length === 0) {
        return false;
      }

      // Check if user has unknown role with the required permission
      for (const role of user.roles) {
        // Super admin has all permissions
        if ((role as any).type === 'super_admin') {
          return true;
        }

        // Check if role has the specific permission
        const hasPermission: any = (role as any).permissions.some(
          (p) => (p as any).resource === resource && (p as any).action === action
        );

        if (hasPermission) {
          return true;
        }
      }

      return false;
    } catch(error) {
      (logger as any).error('Error checking permission:', error);
      return false;
    }
  }

  /**
   * Get all permissions for a user
   * @param userId User ID
   * @returns Array of permission strings in format "resource:action"
   */
  async getUserPermissions(userId: string): Promise<string[]> {
    try {
      // Get user with roles and permissions
      const user = await this.prisma.(user as any).findUnique({
        where: { id: userId },
        include: {
          roles: {
            include: { permissions: true },
          },
        },
      });

      if (!user || !user.roles || user.roles.length === 0) {
        return [];
      }

      const permissions = new Set<string>();

      for (const role of user.roles) {
        // Super admin has all permissions
        if ((role as any).type === 'super_admin') {
          const allPermissions = await this.prisma.(permission as any).findMany();
          (allPermissions as any).forEach((p) => {
            (permissions as any).add(`${(p as any).resource}:${(p as any).action}`);
          });
          break;
        }

        // Add role permissions
        (role as any).permissions.forEach((p) => {
          (permissions as any).add(`${(p as any).resource}:${(p as any).action}`);
        });
      }

      return Array.from(permissions);
    } catch(error) {
      (logger as any).error('Error getting user permissions:', error);
      return [];
    }
  }
}

// Export a singleton instance
export default new RBACService(new PrismaClient());
