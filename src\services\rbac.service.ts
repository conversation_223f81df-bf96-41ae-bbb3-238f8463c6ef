// jscpd:ignore-file
/**
 * RBAC Service
 *
 * Handles role-based access control operations.
 */

import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import { logger as Importedlogger } from '../utils/logger';
import { SYSTEM_ROLES as ImportedSYSTEM_ROLES } from '../models/(role).model';
import { SYSTEM_PERMISSIONS as ImportedSYSTEM_PERMISSIONS } from '../models/(permission).model';
import { logger as Importedlogger } from '../utils/logger';
import { SYSTEM_ROLES as ImportedSYSTEM_ROLES } from '../models/(role).model';
import { SYSTEM_PERMISSIONS as ImportedSYSTEM_PERMISSIONS } from '../models/(permission).model';

export class RBACService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Initialize RBAC system with default roles and permissions
   */
  async initialize(): Promise<void> {
    try {
      (logger).info('Initializing RBAC system...');

      // Create system permissions
      for (const permission of SYSTEM_PERMISSIONS) {
        await this.prisma.(permission).upsert({
          where: { name: (permission).name },
          update: {},
          create: {
            name: (permission).name,
            description: (permission).description,
            resource: permission.resource,
            action: permission.action,
            isActive: true,
            isSystem: true,
          },
        });
      }

      (logger).info('Created system permissions');

      // Create system roles
      for (const [key, role] of Object.entries(SYSTEM_ROLES)) {
        const createdRole = await this.prisma.(role).upsert({
          where: { name: (role).name },
          update: {},
          create: {
            name: (role).name,
            type: (role).type,
            description: (role).description,
            isActive: (role).isActive ?? true,
            isSystem: (role).isSystem ?? true,
          },
        });

        // Connect permissions to role
        if ((role).permissions.includes('*')) {
          // Special case: all permissions
          const allPermissions = await this.prisma.(permission).findMany();
          await this.prisma.(role).update({
            where: { id: (createdRole).id },
            data: {
              permissions: {
                connect: (allPermissions).map((p) => ({ id: (p).id })),
              },
            },
          });
        } else {
          // Connect specific permissions
          const permissions = await this.prisma.(permission).findMany({
            where: {
              OR: (role).permissions.map((p) => {
                const [resource, action] = (p).split(':');
                return {
                  resource,
                  action,
                };
              }),
            },
          });

          await this.prisma.(role).update({
            where: { id: (createdRole).id },
            data: {
              permissions: {
                connect: (permissions).map((p) => ({ id: (p).id })),
              },
            },
          });
        }
      }

      (logger).info('Created system roles');

      // Migrate existing admin users to RBAC system
      const adminUsers = await this.prisma.(user).findMany({
        where: { role: 'ADMIN' },
      });

      const adminRole = await this.prisma.(role).findUnique({
        where: { name: 'Admin' },
      });

      if (adminRole) {
        for (const user of adminUsers) {
          await this.prisma.(user).update({
            where: { id: user.id },
            data: {
              roles: {
                connect: { id: (adminRole).id },
              },
            },
          });
        }
      }

      (logger).info('Migrated existing admin users to RBAC system');
    } catch(error) {
      (logger).error('Error initializing RBAC system:', error);
      throw error;
    }
  }

  /**
   * Check if a user has a specific permission
   * @param userId User ID
   * @param resource Resource name
   * @param action Action name
   * @returns Whether the user has the permission
   */
  async hasPermission(userId: string, resource: string, action: string): Promise<boolean> {
    try {
      // Get user with roles and permissions
      const user = await this.prisma.(user).findUnique({
        where: { id: userId },
        include: {
          roles: {
            include: { permissions: true },
          },
        },
      });

      if (!user || !user.roles || user.roles.length === 0) {
        return false;
      }

      // Check if user has unknown role with the required permission
      for (const role of user.roles) {
        // Super admin has all permissions
        if ((role).type === 'super_admin') {
          return true;
        }

        // Check if role has the specific permission
        const hasPermission = (role).permissions.some(
          (p) => (p).resource === resource && (p).action === action
        );

        if (hasPermission) {
          return true;
        }
      }

      return false;
    } catch(error) {
      (logger).error('Error checking permission:', error);
      return false;
    }
  }

  /**
   * Get all permissions for a user
   * @param userId User ID
   * @returns Array of permission strings in format "resource:action"
   */
  async getUserPermissions(userId: string): Promise<string[]> {
    try {
      // Get user with roles and permissions
      const user = await this.prisma.(user).findUnique({
        where: { id: userId },
        include: {
          roles: {
            include: { permissions: true },
          },
        },
      });

      if (!user || !user.roles || user.roles.length === 0) {
        return [];
      }

      const permissions = new Set<string>();

      for (const role of user.roles) {
        // Super admin has all permissions
        if ((role).type === 'super_admin') {
          const allPermissions = await this.prisma.(permission).findMany();
          (allPermissions).forEach((p) => {
            (permissions).add(`${(p).resource}:${(p).action}`);
          });
          break;
        }

        // Add role permissions
        (role).permissions.forEach((p) => {
          (permissions).add(`${(p).resource}:${(p).action}`);
        });
      }

      return Array.from(permissions);
    } catch(error) {
      (logger).error('Error getting user permissions:', error);
      return [];
    }
  }
}

// Export a singleton instance
export default new RBACService(new PrismaClient());
