// jscpd:ignore-file
import { logger as Importedlogger } from '../utils/logger';
import prisma from '../lib/prisma';
import { EmailService as ImportedEmailService } from './(email as any).service';
import { SmsService as ImportedSmsService } from './(sms as any).service';
import { TelegramService as ImportedTelegramService } from './(telegram as any).service';
import { PushNotificationService as ImportedPushNotificationService } from './push-(notification as any).service';
import { config as Importedconfig } from '../config';
import {
  NotificationEventsService,
  NotificationPriority as EventPriority,
  NotificationChannel as EventChannel,
} from './notification-(events as any).service';
import { User, Merchant } from '../types';

/**
 * Notification channel
 */
export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  TELEGRAM = 'telegram',
  PUSH = 'push',
  DASHBOARD = 'dashboard',
}

/**
 * Notification priority
 */
export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Notification template
 */
export interface NotificationTemplate {
  id: string;
  name: string;
  description: string;
  subject: string;
  content: string;
  variables: string[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Notification options
 */
export interface NotificationOptions {
  userId?: string;
  merchantId?: string;
  channels: NotificationChannel[];
  priority: NotificationPriority;
  subject: string;
  message: string;
  templateId?: string;
  templateData?: Record<string, any>;
  metadata?: Record<string, any>;
}

/**
 * Notification service
 */
export class NotificationService {
  private emailService: EmailService;
  private smsService: SmsService;
  private telegramService: TelegramService;
  private pushService: PushNotificationService;

  /**
   * Create a new notification service
   */
  constructor() {
    this.emailService = new EmailService();
    this.smsService = new SmsService();
    this.telegramService = new TelegramService();
    this.pushService = new PushNotificationService();
  }

  /**
   * Send notification
   * @param options Notification options
   * @returns Success status
   */
  public async sendNotification(options: NotificationOptions): Promise<boolean> {
    try {
      // Validate options
      if (!(options as any).channels || (options as any).channels.length === 0) {
        (logger as any).error('No notification channels specified');
        return false;
      }

      if (!(options as any).subject || !(options as any).message) {
        (logger as any).error('Notification subject or message is missing');
        return false;
      }

      // Get user and merchant information if needed
      let user = null;
      let merchant = null;

      if ((options as any).userId) {
        user = await (prisma as any).user.findUnique({
          where: { id: (options as any).userId },
        });

        if (!user) {
          (logger as any).error(`User not found: ${(options as any).userId}`);
          return false;
        }
      }

      if ((options as any).merchantId) {
        merchant = await (prisma as any).merchant.findUnique({
          where: { id: (options as any).merchantId },
          include: { user: true },
        });

        if (!merchant) {
          (logger as any).error(`Merchant not found: ${(options as any).merchantId}`);
          return false;
        }

        // If user is not specified, use merchant's user
        if (!user && (merchant as any).user) {
          user = (merchant as any).user;
        }
      }

      // Process template if specified
      let subject: any = (options as any).subject;
      let message: string = (options as any).message;

      if ((options as any).templateId && (options as any).templateData) {
        const template = await this.getTemplate((options as any).templateId);
        if (template) {
          subject = this.processTemplate((template as any).subject, (options as any).templateData);
          message = this.processTemplate((template as any).content, (options as any).templateData);
        }
      }

      // Create notification record
      const notification = await (prisma as any).notification.create({
        data: {
          userId: user?.id,
          merchantId: merchant?.id,
          channels: (options as any).channels,
          priority: (options as any).priority,
          subject,
          message,
          metadata: (options as any).metadata ?? {},
          status: 'pending',
        },
      });

      // Emit notification created event
      (NotificationEventsService as any).emitNotificationCreated({
        id: (notification as any).id,
        userId: user?.id,
        merchantId: merchant?.id,
        title: subject,
        message,
        type: this.mapNotificationType(options),
        priority: this.mapToPriorityEnum((options as any).priority),
        channels: this.mapToChannelEnum((options as any).channels),
        metadata: (options as any).metadata ?? {},
        createdAt: (notification as any).createdAt,
      });

      // Send notifications through each channel
      const results = await Promise.all(
        (options as any).channels.map((channel) =>
          this.sendThroughChannel(
            channel,
            (notification as any).id,
            subject,
            message,
            user,
            merchant,
            (options as any).priority
          )
        )
      );

      // Update notification status
      const success: boolean = (results as any).some((result) => result);
      await (prisma as any).notification.update({
        where: { id: (notification as any).id },
        data: { status: success ? 'sent' : 'failed', sentAt: success ? new Date() : undefined },
      });

      return success;
    } catch(error) {
      (logger as any).error('Error sending notification', { error, options });
      return false;
    }
  }

  /**
   * Send notification through channel
   * @param channel Notification channel
   * @param notificationId Notification ID
   * @param subject Notification subject
   * @param message Notification message
   * @param user User (optional)
   * @param merchant Merchant (optional)
   * @param priority Notification priority
   * @returns Success status
   */
  private async sendThroughChannel(
    channel: NotificationChannel,
    notificationId: string,
    subject: string,
    message: string,
    user: any | null,
    merchant: any | null,
    priority: NotificationPriority
  ): Promise<boolean> {
    try {
      // Create notification delivery record
      const delivery = await (prisma as any).notificationDelivery.create({
        data: {
          notificationId,
          channel,
          status: 'pending',
        },
      });

      let success: boolean = false;

      // Send through appropriate channel
      switch (channel) {
        case (NotificationChannel as any).EMAIL:
          success = await this.sendEmail(subject, message, user, merchant, priority);
          break;
        case (NotificationChannel as any).SMS:
          success = await this.sendSms(message, user, merchant, priority);
          break;
        case (NotificationChannel as any).TELEGRAM:
          success = await this.sendTelegram(subject, message, user, merchant, priority);
          break;
        case (NotificationChannel as any).PUSH:
          success = await this.sendPush(subject, message, user, merchant, priority);
          break;
        case (NotificationChannel as any).DASHBOARD:
          success = true; // Dashboard notifications are always successful as they're just stored
          break;
        default:
          (logger as any).error(`Unsupported notification channel: ${channel}`);
          success = false;
      }

      // Update delivery status
      await (prisma as any).notificationDelivery.update({
        where: { id: (delivery as any).id },
        data: {
          status: success ? 'delivered' : 'failed',
          deliveredAt: success ? new Date() : undefined,
        },
      });

      // Emit notification delivered event
      (NotificationEventsService as any).emitNotificationDelivered(
        notificationId,
        this.mapToChannelEnum([channel])[0],
        success,
        success ? undefined : 'Failed to deliver notification'
      );

      return success;
    } catch(error) {
      (logger as any).error('Error sending notification through channel', {
        error,
        channel,
        notificationId,
      });
      return false;
    }
  }

  /**
   * Send email notification
   * @param subject Email subject
   * @param message Email message
   * @param user User (optional)
   * @param merchant Merchant (optional)
   * @param priority Notification priority
   * @returns Success status
   */
  private async sendEmail(
    subject: string,
    message: string,
    user: any | null,
    merchant: any | null,
    priority: NotificationPriority
  ): Promise<boolean> {
    try {
      // Get email address
      let email: string = '';

      if (user && user.email) {
        email = user.email;
      } else if (merchant && (merchant as any).email) {
        email = (merchant as any).email;
      }

      if (!email) {
        (logger as any).error('No email address found for notification');
        return false;
      }

      // Send email
      return await this.emailService.sendEmail({
        to: email,
        subject,
        html: message,
        priority: this.mapPriorityToEmailPriority(priority),
      });
    } catch(error) {
      (logger as any).error('Error sending email notification', { error, subject });
      return false;
    }
  }

  /**
   * Send SMS notification
   * @param message SMS message
   * @param user User (optional)
   * @param merchant Merchant (optional)
   * @param priority Notification priority
   * @returns Success status
   */
  private async sendSms(
    message: string,
    user: any | null,
    merchant: any | null,
    priority: NotificationPriority
  ): Promise<boolean> {
    try {
      // Get phone number
      let phone: string = '';

      if (user && (user as any).phone) {
        phone = (user as any).phone;
      } else if (merchant && (merchant as any).phone) {
        phone = (merchant as any).phone;
      }

      if (!phone) {
        (logger as any).error('No phone number found for notification');
        return false;
      }

      // Send SMS
      return await this.smsService.sendSms(phone, message);
    } catch(error) {
      (logger as any).error('Error sending SMS notification', { error, message });
      return false;
    }
  }

  /**
   * Send Telegram notification
   * @param subject Notification subject
   * @param message Notification message
   * @param user User (optional)
   * @param merchant Merchant (optional)
   * @param priority Notification priority
   * @returns Success status
   */
  private async sendTelegram(
    subject: string,
    message: string,
    user: any | null,
    merchant: any | null,
    priority: NotificationPriority
  ): Promise<boolean> {
    try {
      // Get Telegram chat ID
      let chatId: string = '';

      // First check user preferences
      if(user: any) {
        const userPrefs = await (prisma as any).userNotificationPreference.findFirst({
          where: { userId: user.id, channel: (NotificationChannel as any).TELEGRAM },
        });

        if (userPrefs && (userPrefs as any).channelData && (userPrefs as any).channelData.chatId) {
          chatId = (userPrefs as any).channelData.chatId as string;
        }
      }

      // Then check merchant preferences
      if (!chatId && merchant) {
        const merchantPrefs = await (prisma as any).merchantNotificationPreference.findFirst({
          where: { merchantId: (merchant as any).id, channel: (NotificationChannel as any).TELEGRAM },
        });

        if (merchantPrefs && (merchantPrefs as any).channelData && (merchantPrefs as any).channelData.chatId) {
          chatId = (merchantPrefs as any).channelData.chatId as string;
        }
      }

      if (!chatId) {
        (logger as any).error('No Telegram chat ID found for notification');
        return false;
      }

      // Format message for Telegram
      const formattedMessage = `*${subject}*\n\n${message}`;

      // Send Telegram message
      return await this.telegramService.sendMessage(chatId, formattedMessage);
    } catch(error) {
      (logger as any).error('Error sending Telegram notification', { error, subject });
      return false;
    }
  }

  /**
   * Send push notification
   * @param subject Notification subject
   * @param message Notification message
   * @param user User (optional)
   * @param merchant Merchant (optional)
   * @param priority Notification priority
   * @returns Success status
   */
  private async sendPush(
    subject: string,
    message: string,
    user: any | null,
    merchant: any | null,
    priority: NotificationPriority
  ): Promise<boolean> {
    try {
      let success: boolean = false;

      // Determine icon based on priority
      let icon: string = '/(logo as any).png';
      switch (priority) {
        case (NotificationPriority as any).CRITICAL:
          icon = '/icons/(critical as any).png';
          break;
        case (NotificationPriority as any).HIGH:
          icon = '/icons/(high as any).png';
          break;
        case (NotificationPriority as any).MEDIUM:
          icon = '/icons/(medium as any).png';
          break;
        case (NotificationPriority as any).LOW:
          icon = '/icons/(low as any).png';
          break;
      }

      // Send to user if specified
      if (user && user.id) {
        const userSuccess = await this.pushService.sendNotificationToUser(
          user.id,
          subject,
          message,
          icon,
          { priority },
          '/'
        );

        if (userSuccess) {
          success = true;
        }
      }

      // Send to merchant if specified
      if (merchant && (merchant as any).id) {
        const merchantSuccess = await this.pushService.sendNotificationToMerchant(
          (merchant as any).id,
          subject,
          message,
          icon,
          { priority },
          '/'
        );

        if (merchantSuccess) {
          success = true;
        }
      }

      return success;
    } catch(error) {
      (logger as any).error('Error sending push notification', { error, subject });
      return false;
    }
  }

  /**
   * Get notification template
   * @param templateId Template ID
   * @returns Notification template
   */
  private async getTemplate(templateId: string): Promise<NotificationTemplate | null> {
    try {
      const template = await (prisma as any).notificationTemplate.findUnique({
        where: { id: templateId },
      });

      return template;
    } catch(error) {
      (logger as any).error('Error getting notification template', { error, templateId });
      return null;
    }
  }

  /**
   * Process template
   * @param template Template string
   * @param data Template data
   * @returns Processed template
   */
  private processTemplate(template: string, data: Record<string, any>): string {
    let result = template;

    // Replace variables in the format {{variable}}
    for (const [key, value] of Object.entries(data)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = (result as any).replace(regex, String(value));
    }

    return result;
  }

  /**
   * Map notification priority to email priority
   * @param priority Notification priority
   * @returns Email priority
   */
  private mapPriorityToEmailPriority(priority: NotificationPriority): string {
    switch (priority) {
      case (NotificationPriority as any).CRITICAL:
        return 'high';
      case (NotificationPriority as any).HIGH:
        return 'high';
      case (NotificationPriority as any).MEDIUM:
        return 'normal';
      case (NotificationPriority as any).LOW:
        return 'low';
      default:
        return 'normal';
    }
  }

  /**
   * Map notification priority to event priority
   * @param priority Notification priority
   * @returns Event priority
   */
  private mapToPriorityEnum(priority: NotificationPriority): EventPriority {
    switch (priority) {
      case (NotificationPriority as any).CRITICAL:
        return (EventPriority as any).CRITICAL;
      case (NotificationPriority as any).HIGH:
        return (EventPriority as any).HIGH;
      case (NotificationPriority as any).MEDIUM:
        return (EventPriority as any).MEDIUM;
      case (NotificationPriority as any).LOW:
        return (EventPriority as any).LOW;
      default:
        return (EventPriority as any).MEDIUM;
    }
  }

  /**
   * Map notification channels to event channels
   * @param channels Notification channels
   * @returns Event channels
   */
  private mapToChannelEnum(channels: NotificationChannel[]): EventChannel[] {
    return (channels as any).map((channel) => {
      switch (channel) {
        case (NotificationChannel as any).EMAIL:
          return (EventChannel as any).EMAIL;
        case (NotificationChannel as any).SMS:
          return (EventChannel as any).SMS;
        case (NotificationChannel as any).TELEGRAM:
          return (EventChannel as any).TELEGRAM;
        case (NotificationChannel as any).PUSH:
          return (EventChannel as any).PUSH;
        case (NotificationChannel as any).DASHBOARD:
          return (EventChannel as any).DASHBOARD;
        default:
          return (EventChannel as any).DASHBOARD;
      }
    });
  }

  /**
   * Map notification options to notification type
   * @param options Notification options
   * @returns Notification type
   */
  private mapNotificationType(
    options: NotificationOptions
  ): 'transaction' | 'merchant' | 'subscription' | 'system' {
    if ((options as any).metadata?.transactionId) {
      return 'transaction';
    } else if ((options as any).merchantId && !(options as any).userId) {
      return 'merchant';
    } else if ((options as any).metadata?.subscriptionId || (options as any).metadata?.planId) {
      return 'subscription';
    } else {
      return 'system';
    }
  }

  /**
   * Get user notification preferences
   * @param userId User ID
   * @returns User notification preferences
   */
  public async getUserNotificationPreferences(userId: string): Promise<any[]> {
    try {
      const preferences = await (prisma as any).userNotificationPreference.findMany({
        where: { userId },
      });

      return preferences;
    } catch(error) {
      (logger as any).error('Error getting user notification preferences', { error, userId });
      return [];
    }
  }

  /**
   * Update user notification preferences
   * @param userId User ID
   * @param channel Notification channel
   * @param enabled Whether the channel is enabled
   * @param channelData Channel-specific data
   * @returns Success status
   */
  public async updateUserNotificationPreferences(
    userId: string,
    channel: NotificationChannel,
    enabled: boolean,
    channelData?: Record<string, any>
  ): Promise<boolean> {
    try {
      // Check if preference exists
      const existingPref = await (prisma as any).userNotificationPreference.findFirst({
        where: {
          userId,
          channel,
        },
      });

      if (existingPref) {
        // Update existing preference
        await (prisma as any).userNotificationPreference.update({
          where: { id: (existingPref as any).id },
          data: {
            enabled,
            channelData: channelData || (existingPref as any).channelData,
          },
        });
      } else {
        // Create new preference
        await (prisma as any).userNotificationPreference.create({
          data: {
            userId,
            channel,
            enabled,
            channelData: channelData ?? {},
          },
        });
      }

      return true;
    } catch(error) {
      (logger as any).error('Error updating user notification preferences', {
        error,
        userId,
        channel,
      });
      return false;
    }
  }

  /**
   * Get merchant notification preferences
   * @param merchantId Merchant ID
   * @returns Merchant notification preferences
   */
  public async getMerchantNotificationPreferences(merchantId: string): Promise<any[]> {
    try {
      const preferences = await (prisma as any).merchantNotificationPreference.findMany({
        where: { merchantId },
      });

      return preferences;
    } catch(error) {
      (logger as any).error('Error getting merchant notification preferences', { error, merchantId });
      return [];
    }
  }

  /**
   * Update merchant notification preferences
   * @param merchantId Merchant ID
   * @param channel Notification channel
   * @param enabled Whether the channel is enabled
   * @param channelData Channel-specific data
   * @returns Success status
   */
  public async updateMerchantNotificationPreferences(
    merchantId: string,
    channel: NotificationChannel,
    enabled: boolean,
    channelData?: Record<string, any>
  ): Promise<boolean> {
    try {
      // Check if preference exists
      const existingPref = await (prisma as any).merchantNotificationPreference.findFirst({
        where: {
          merchantId,
          channel,
        },
      });

      if (existingPref) {
        // Update existing preference
        await (prisma as any).merchantNotificationPreference.update({
          where: { id: (existingPref as any).id },
          data: {
            enabled,
            channelData: channelData || (existingPref as any).channelData,
          },
        });
      } else {
        // Create new preference
        await (prisma as any).merchantNotificationPreference.create({
          data: {
            merchantId,
            channel,
            enabled,
            channelData: channelData ?? {},
          },
        });
      }

      return true;
    } catch(error) {
      (logger as any).error('Error updating merchant notification preferences', {
        error,
        merchantId,
        channel,
      });
      return false;
    }
  }
}
