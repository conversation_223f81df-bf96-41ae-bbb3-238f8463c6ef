// jscpd:ignore-file
/**
 * Audit Service
 *
 * Handles audit logging for admin actions.
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../lib/logger';
import { logger } from '../lib/logger';

/**
 * Audit log entry data
 */
export interface AuditLogData {
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  ipAddress?: string;
  userAgent?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  statusCode?: number;
  errorMessage?: string;
}

export class AuditService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Log an admin action
   */
  async logAction(data: AuditLogData): Promise<void> {
    try {
      await this.prisma.enhancedAuditLog.create({
        data: {
          userId: data.id,
          action: data.action,
          resource: data.resource,
          resourceId: data.resourceId,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          oldValues: data.oldValues as any,
          newValues: data.newValues as any,
          statusCode: data.statusCode,
          errorMessage: data.errorMessage,
        },
      });

      logger.info(`Audit log created for ${data.action} on ${data.resource}`, {
        userId: data.id,
        resource: data.resource,
        resourceId: data.resourceId,
      });
    } catch (error) {
      logger.error('Error creating audit log:', error);
    }
  }

  /**
   * Get audit logs with filtering
   */
  async getAuditLogs(filters: {
    userId?: string;
    action?: string;
    resource?: string;
    resourceId?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  }): Promise<unknown[]> {
    try {
      const {
        userId,
        action,
        resource,
        resourceId,
        startDate,
        endDate,
        limit = 50,
        offset = 0,
      } = filters;

      const where = {};

      if (userId) {
        where.userId = userId;
      }

      if (action) {
        where.action = action;
      }

      if (resource) {
        where.resource = resource;
      }

      if (resourceId) {
        where.resourceId = resourceId;
      }

      if (startDate || endDate) {
        where.timestamp = {};

        if (startDate) {
          where.timestamp.gte = startDate;
        }

        if (endDate) {
          where.timestamp.lte = endDate;
        }
      }

      const auditLogs = await this.prisma.enhancedAuditLog.findMany({
        where,
        orderBy: { timestamp: 'desc' },
        skip: offset,
        take: limit,
        include: {
          user: {
            select: { id: true, email: true },
          },
        },
      });

      return auditLogs;
    } catch (error) {
      logger.error('Error getting audit logs:', error);
      return [];
    }
  }

  /**
   * Get audit log by ID
   */
  async getAuditLogById(id: string): Promise<any | null> {
    try {
      const auditLog = await this.prisma.enhancedAuditLog.findUnique({
        where: { id },
        include: {
          user: {
            select: { id: true, email: true },
          },
        },
      });

      return auditLog;
    } catch (error) {
      logger.error('Error getting audit log by ID:', error);
      return null;
    }
  }
}
