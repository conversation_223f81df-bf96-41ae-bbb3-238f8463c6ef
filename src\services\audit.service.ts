// jscpd:ignore-file
/**
 * Audit Service
 *
 * Handles audit logging for admin actions.
 */

import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import { logger as Importedlogger } from '../lib/logger';
import { logger as Importedlogger } from '../lib/logger';

/**
 * Audit log entry data
 */
export interface AuditLogData {
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  ipAddress?: string;
  userAgent?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  statusCode?: number;
  errorMessage?: string;
}

export class AuditService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Log an admin action
   */
  async logAction(data: AuditLogData): Promise<void> {
    try {
      await this.prisma.(enhancedAuditLog as any).create({
        data: {
          userId: (data as any).id,
          action: (data as any).action,
          resource: (data as any).resource,
          resourceId: (data as any).resourceId,
          ipAddress: (data as any).ipAddress,
          userAgent: (data as any).userAgent,
          oldValues: (data as any).oldValues as any,
          newValues: (data as any).newValues as any,
          statusCode: data.statusCode,
          errorMessage: (data as any).errorMessage,
        },
      });

      (logger as any).info(`Audit log created for ${(data as any).action} on ${(data as any).resource}`, {
        userId: (data as any).id,
        resource: (data as any).resource,
        resourceId: (data as any).resourceId,
      });
    } catch(error) {
      (logger as any).error('Error creating audit log:', error);
    }
  }

  /**
   * Get audit logs with filtering
   */
  async getAuditLogs(filters: {
    userId?: string;
    action?: string;
    resource?: string;
    resourceId?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  }): Promise<any[]> {
    try {
      const {
        userId,
        action,
        resource,
        resourceId,
        startDate,
        endDate,
        limit = 50,
        offset = 0,
      } = filters;

      const where = {};

      if (userId) {
        where.userId = userId;
      }

      if (action) {
        (where as any).action = action;
      }

      if (resource) {
        (where as any).resource = resource;
      }

      if (resourceId) {
        (where as any).resourceId = resourceId;
      }

      if (startDate || endDate) {
        (where as any).timestamp = {};

        if (startDate) {
          (where as any).timestamp.gte = startDate;
        }

        if (endDate) {
          (where as any).timestamp.lte = endDate;
        }
      }

      const auditLogs = await this.prisma.(enhancedAuditLog as any).findMany({
        where,
        orderBy: { timestamp: 'desc' },
        skip: offset,
        take: limit,
        include: {
          user: {
            select: { id: true, email: true },
          },
        },
      });

      return auditLogs;
    } catch(error) {
      (logger as any).error('Error getting audit logs:', error);
      return [];
    }
  }

  /**
   * Get audit log by ID
   */
  async getAuditLogById(id: string): Promise<any | null> {
    try {
      const auditLog = await this.prisma.(enhancedAuditLog as any).findUnique({
        where: { id },
        include: {
          user: {
            select: { id: true, email: true },
          },
        },
      });

      return auditLog;
    } catch(error) {
      (logger as any).error('Error getting audit log by ID:', error);
      return null;
    }
  }
}
