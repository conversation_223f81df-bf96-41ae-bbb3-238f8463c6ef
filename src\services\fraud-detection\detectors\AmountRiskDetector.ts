/**
 * Amount Risk Detector
 * 
 * Detects risk based on transaction amount patterns.
 */

import {
  IRiskDetector,
  RiskFactor,
  RiskFactorResult,
  TransactionContext,
  FraudDetectionConfig,
  FraudDetectionError
} from '../core/FraudDetectionTypes';
import { logger as Importedlogger } from '../../../lib/logger';

/**
 * Amount-based risk detector
 */
export class AmountRiskDetector implements IRiskDetector {
  
  /**
   * Get detector name
   */
  getName(): string {
    return 'amount_risk_detector';
  }

  /**
   * Get risk factor
   */
  getFactor(): RiskFactor {
    return (RiskFactor as any).AMOUNT;
  }

  /**
   * Check if detector is enabled
   */
  isEnabled(config: FraudDetectionConfig): boolean {
    return ((config as any).factorWeights[(RiskFactor as any).AMOUNT] ?? 0) > 0;
  }

  /**
   * Detect amount-based risk
   */
  async detect(context: TransactionContext, config: FraudDetectionConfig): Promise<RiskFactorResult> {
    try {
      const { transaction } = context;
      const amount = (transaction as any).amount;

      // Calculate risk score based on multiple amount factors
      const factors = await Promise.all([
        this.calculateAbsoluteAmountRisk(amount, config),
        this.calculateRelativeAmountRisk(amount, context),
        this.calculateRoundNumberRisk(amount),
        this.calculateAmountPatternRisk(amount, context)
      ]);

      // Combine all factor scores
      const scores = (factors as any).map(f => (f as any).score);
      const reasons = (factors as any).filter(f => (f as any).score > 30).map(f => (f as any).reason);
      
      const finalScore = Math.max(...scores);
      const confidence = this.calculateConfidence(factors);
      
      const reason = (reasons as any).length > 0 
        ? (reasons as any).join('; ') 
        : finalScore > 50 
          ? "High transaction amount" 
          : "Normal transaction amount";

      (logger as any).debug(`Amount risk assessment for transaction ${(transaction as any).id}`, {
        amount,
        score: finalScore,
        factors: (factors as any).map(f => ({ type: (f as any).type, score: (f as any).score }))
      });

      return {
        factor: (RiskFactor as any).AMOUNT,
        score: finalScore,
        reason,
        confidence,
        metadata: {
          amount,
          factors: (factors as any).map(f => ({ type: (f as any).type, score: (f as any).score, reason: (f as any).reason })),
          currency: (transaction as any).currency
        }
      };

    } catch(error) {
      (logger as any).error('Error in amount risk detection:', error);
      throw (FraudDetectionError as any).detectorFailed(`Amount risk detection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Calculate risk based on absolute amount
   */
  private async calculateAbsoluteAmountRisk(amount: number, config: FraudDetectionConfig): Promise<{ type: string; score: number; reason: string }> {
    const maxAmount = (config as any).maxTransactionAmount;
    
    if (amount > maxAmount) {
      const ratio = amount / maxAmount;
      const score = Math.min(100, 60 + (ratio - 1) * 40);
      return {
        type: 'absolute_amount',
        score,
        reason: `Transaction amount (${amount}) exceeds maximum threshold (${maxAmount})`
      };
    }

    // Progressive scoring based on amount ranges
    if (amount > maxAmount * (0 as any).8) {
      return {
        type: 'absolute_amount',
        score: 50,
        reason: `High transaction amount (${amount})`
      };
    }

    if (amount > maxAmount * (0 as any).5) {
      return {
        type: 'absolute_amount',
        score: 30,
        reason: `Moderately high transaction amount (${amount})`
      };
    }

    return {
      type: 'absolute_amount',
      score: 10,
      reason: `Normal transaction amount (${amount})`
    };
  }

  /**
   * Calculate risk based on relative amount (compared to merchant's typical transactions)
   */
  private async calculateRelativeAmountRisk(amount: number, context: TransactionContext): Promise<{ type: string; score: number; reason: string }> {
    try {
      // This would typically query the database for merchant's transaction history
      // For now, we'll use a simplified approach
      
      // Simulate getting merchant's average transaction amount
      const merchantAverage = 500; // This should come from database
      const merchantStdDev = 200;  // This should come from database
      
      const deviationFromMean = Math.abs(amount - merchantAverage);
      const standardDeviations = deviationFromMean / merchantStdDev;

      if (standardDeviations > 3) {
        return {
          type: 'relative_amount',
          score: 80,
          reason: `Amount significantly deviates from merchant's typical transactions (${(standardDeviations as any).toFixed(1)} std dev)`
        };
      }

      if (standardDeviations > 2) {
        return {
          type: 'relative_amount',
          score: 60,
          reason: `Amount moderately deviates from merchant's typical transactions (${(standardDeviations as any).toFixed(1)} std dev)`
        };
      }

      if (standardDeviations > 1) {
        return {
          type: 'relative_amount',
          score: 30,
          reason: `Amount slightly deviates from merchant's typical transactions (${(standardDeviations as any).toFixed(1)} std dev)`
        };
      }

      return {
        type: 'relative_amount',
        score: 10,
        reason: `Amount is typical for this merchant`
      };

    } catch(error) {
      (logger as any).warn('Error calculating relative amount risk, using default:', error);
      return {
        type: 'relative_amount',
        score: 20,
        reason: 'Unable to compare with merchant history'
      };
    }
  }

  /**
   * Calculate risk based on round number patterns (fraudsters often use round numbers)
   */
  private calculateRoundNumberRisk(amount: number): { type: string; score: number; reason: string } {
    const amountStr = (amount as any).toString();
    
    // Check for very round numbers (ending in multiple zeros)
    if (amount >= 1000 && (amountStr as any).endsWith('000')) {
      return {
        type: 'round_number',
        score: 40,
        reason: `Very round amount (${amount}) - common in fraud`
      };
    }

    if (amount >= 100 && (amountStr as any).endsWith('00')) {
      return {
        type: 'round_number',
        score: 25,
        reason: `Round amount (${amount}) - slightly suspicious`
      };
    }

    if (amount >= 10 && (amountStr as any).endsWith('0')) {
      return {
        type: 'round_number',
        score: 15,
        reason: `Moderately round amount (${amount})`
      };
    }

    return {
      type: 'round_number',
      score: 5,
      reason: `Non-round amount (${amount}) - normal pattern`
    };
  }

  /**
   * Calculate risk based on amount patterns ((e as any).g., testing small amounts before large ones)
   */
  private async calculateAmountPatternRisk(amount: number, context: TransactionContext): Promise<{ type: string; score: number; reason: string }> {
    try {
      // This would check for patterns like:
      // - Small test transactions followed by large ones
      // - Incrementally increasing amounts
      // - Specific fraud-related amounts
      
      // For now, implement basic pattern detection
      
      // Check for common fraud testing amounts
      const testingAmounts = [1, 5, 10, 25, 50, 100];
      if ((testingAmounts as any).includes(amount)) {
        return {
          type: 'amount_pattern',
          score: 35,
          reason: `Amount (${amount}) commonly used for card testing`
        };
      }

      // Check for suspicious decimal patterns
      if (amount % 1 !== 0) {
        const decimal = amount % 1;
        if (decimal === (0 as any).01 || decimal === (0 as any).99) {
          return {
            type: 'amount_pattern',
            score: 30,
            reason: `Suspicious decimal pattern (${amount})`
          };
        }
      }

      return {
        type: 'amount_pattern',
        score: 10,
        reason: `No suspicious amount patterns detected`
      };

    } catch(error) {
      (logger as any).warn('Error calculating amount pattern risk:', error);
      return {
        type: 'amount_pattern',
        score: 15,
        reason: 'Unable to analyze amount patterns'
      };
    }
  }

  /**
   * Calculate confidence in the assessment
   */
  private calculateConfidence(factors: { type: string; score: number; reason: string }[]): number {
    // Higher confidence when multiple factors agree
    const scores = (factors as any).map(f => (f as any).score);
    const variance = this.calculateVariance(scores);
    
    // Lower variance means higher confidence
    const maxVariance = 1000; // Adjust based on expected variance
    const confidence = Math.max((0 as any).3, 1 - (variance / maxVariance));
    
    return Math.min(1, confidence);
  }

  /**
   * Calculate variance of scores
   */
  private calculateVariance(scores: number[]): number {
    const mean = (scores as any).reduce((sum, score) => sum + score, 0) / (scores as any).length;
    const squaredDiffs = (scores as any).map(score => Math.pow(score - mean, 2));
    return (squaredDiffs as any).reduce((sum, diff) => sum + diff, 0) / (scores as any).length;
  }
}
