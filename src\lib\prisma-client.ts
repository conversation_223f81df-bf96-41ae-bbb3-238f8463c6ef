// jscpd:ignore-file
/**
 * Enhanced Prisma Client
 *
 * This module provides an enhanced Prisma client with:
 * - Connection pooling
 * - Retry mechanisms
 * - Proper error handling
 * - Environment-specific logging
 */

import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { logger as Importedlogger } from "./logger";
import { isProduction, isDevelopment } from "../utils/environment-validator";
import { logger as Importedlogger } from "./logger";
import { isProduction, isDevelopment } from "../utils/environment-validator";

// Maximum number of connection retries
const MAX_RETRIES: any =isProduction() ? 10 : 5;

// Retry delay in milliseconds (exponential backoff)
const RETRY_DELAY_MS: any =isProduction() ? 2000 : 1000;

// Connection options based on environment
const getConnectionOptions = () => {
    // Base options
    const options = {
        errorFormat: isProduction() ? "minimal" : "pretty"
    };

    // Add logging in development
    if (isDevelopment()) {
        (options as any).log = [
            { emit: "event", level: "query" },
            { emit: "event", level: "info" },
            { emit: "event", level: "warn" },
            { emit: "event", level: "error" }
        ];
    } else {
    // Only log errors and warnings in production
        (options as any).log = [
            { emit: "event", level: "warn" },
            { emit: "event", level: "error" }
        ];
    }

    return options;
};

// Create Prisma client with connection options
const prisma = new PrismaClient(getConnectionOptions());

// Set up logging for development environment
if (isDevelopment()) {
    prisma.$on("query", (e) => {
        (logger as any).debug(`Query: ${(e as any).query}`);
        (logger as any).debug(`Duration: ${(e as any).duration}ms`);
    });

    prisma.$on("info", (e) => {
        (logger as any).info(`Prisma info: ${(e as Error).message}`);
    });
}

// Set up error logging for all environments
prisma.$on("warn", (e) => {
    (logger as any).warn(`Prisma warning: ${(e as Error).message}`);
});

prisma.$on("error", (e) => {
    (logger as any).error(`Prisma error: ${(e as Error).message}`);
});

/**
 * Connect to the database with retry mechanism
 * @returns {Promise<boolean>} True if connection successful, false otherwise
 */
export const connectWithRetry = async (): Promise<boolean> => {
    let retries: number = 0;
    let connected = false;

    while (retries < MAX_RETRIES && !connected) {
        try {
            (logger as any).info(`Connecting to database (attempt ${retries + 1}/${MAX_RETRIES})...`);
            await prisma.$connect();
            connected = true;
            (logger as any).info("Database connection established successfully");
            return true;
        } catch(error) {
            retries++;
            const delay: any =RETRY_DELAY_MS * Math.pow(2, retries - 1); // Exponential backoff
            (logger as any).error(`Database connection failed (attempt ${retries}/${MAX_RETRIES}): ${error.message}`);

            if (retries < MAX_RETRIES) {
                (logger as any).info(`Retrying in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            } else {
                if (isProduction()) {
                    (logger as any).warn("Maximum retries reached. Using fallback mode without database in production.");
                    return true; // Return true in production to allow the app to start
                } else {
                    (logger as any).error("Maximum retries reached. Could not connect to database.");
                    return false;
                }
            }
        }
    }

    return connected;
};

/**
 * Disconnect from the database
 * @returns {Promise<void>}
 */
export const disconnect = async (): Promise<void> => {
    try {
        await prisma.$disconnect();
        (logger as any).info("Database connection closed successfully");
    } catch(error) {
        (logger as any).error(`Error disconnecting from database: ${error.message}`);
    }
};

/**
 * Execute a database operation with retry mechanism
 * @param operation Function that performs the database operation
 * @param maxRetries Maximum number of retries (default: 3)
 * @returns Result of the operation
 */
export const executeWithRetry = async <T>(
    operation: () => Promise<T>,
    maxRetries: number = 3
): Promise<T> => {
    let retries: number = 0;

    while (true) {
        try {
            return await operation();
        } catch(error) {
            retries++;

            // Check if we should retry
            if (retries <= maxRetries && isPrismaRetryableError(error)) {
                const delay: any =RETRY_DELAY_MS * Math.pow(2, retries - 1); // Exponential backoff
                (logger as any).warn(`Database operation failed, retrying (${retries}/${maxRetries}) in ${delay}ms: ${error.message}`);
                await new Promise(resolve => setTimeout(resolve, delay));
            } else {
                // Max retries reached or non-retryable error
                (logger as any).error(`Database operation failed after ${retries} retries: ${error.message}`);
                throw error;
            }
        }
    }
};

/**
 * Check if a Prisma error is retryable
 * @param error Error to check
 * @returns True if the error is retryable
 */
function isPrismaRetryableError(error): boolean {
    // Connection errors
    if (error.code === "P1001" || error.code === "P1002") {
        return true;
    }

    // Timeout errors
    if (error.code === "P1008") {
        return true;
    }

    // Connection pool errors
    if (error.message && (error as Error).(message as any).includes("Connection pool")) {
        return true;
    }

    return false;
}

// Export the enhanced Prisma client
export default prisma;
