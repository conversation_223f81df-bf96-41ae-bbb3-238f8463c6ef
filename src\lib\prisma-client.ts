// jscpd:ignore-file
/**
 * Enhanced Prisma Client
 *
 * This module provides an enhanced Prisma client with:
 * - Connection pooling
 * - Retry mechanisms
 * - Proper error handling
 * - Environment-specific logging
 */

import { PrismaClient } from "@prisma/client";
import { logger } from "./logger";
import { isProduction, isDevelopment } from "../utils/environment-validator";
import { logger } from "./logger";
import { isProduction, isDevelopment } from "../utils/environment-validator";

// Maximum number of connection retries
const MAX_RETRIES: unknown =isProduction() ? 10 : 5;

// Retry delay in milliseconds (exponential backoff)
const RETRY_DELAY_MS: unknown =isProduction() ? 2000 : 1000;

// Connection options based on environment
const getConnectionOptions = () => {
    // Base options
    const options = {
        errorFormat: isProduction() ? "minimal" : "pretty"
    };

    // Add logging in development
    if (isDevelopment()) {
        options.log = [
            { emit: "event", level: "query" },
            { emit: "event", level: "info" },
            { emit: "event", level: "warn" },
            { emit: "event", level: "error" }
        ];
    } else {
    // Only log errors and warnings in production
        options.log = [
            { emit: "event", level: "warn" },
            { emit: "event", level: "error" }
        ];
    }

    return options;
};

// Create Prisma client with connection options
const prisma = new PrismaClient(getConnectionOptions());

// Set up logging for development environment
if (isDevelopment()) {
    prisma.$on("query", (e) => {
        logger.debug(`Query: ${e.query}`);
        logger.debug(`Duration: ${e.duration}ms`);
    });

    prisma.$on("info", (e) => {
        logger.info(`Prisma info: ${(e as Error).message}`);
    });
}

// Set up error logging for all environments
prisma.$on("warn", (e) => {
    logger.warn(`Prisma warning: ${(e as Error).message}`);
});

prisma.$on("error", (e) => {
    logger.error(`Prisma error: ${(e as Error).message}`);
});

/**
 * Connect to the database with retry mechanism
 * @returns {Promise<boolean>} True if connection successful, false otherwise
 */
export const connectWithRetry = async (): Promise<boolean> => {
    let retries: number = 0;
    let connected = false;

    while (retries < MAX_RETRIES && !connected) {
        try {
            logger.info(`Connecting to database (attempt ${retries + 1}/${MAX_RETRIES})...`);
            await prisma.$connect();
            connected = true;
            logger.info("Database connection established successfully");
            return true;
        } catch (error) {
            retries++;
            const delay: unknown =RETRY_DELAY_MS * Math.pow(2, retries - 1); // Exponential backoff
            logger.error(`Database connection failed (attempt ${retries}/${MAX_RETRIES}): ${(error as Error).message}`);

            if (retries < MAX_RETRIES) {
                logger.info(`Retrying in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            } else {
                if (isProduction()) {
                    logger.warn("Maximum retries reached. Using fallback mode without database in production.");
                    return true; // Return true in production to allow the app to start
                } else {
                    logger.error("Maximum retries reached. Could not connect to database.");
                    return false;
                }
            }
        }
    }

    return connected;
};

/**
 * Disconnect from the database
 * @returns {Promise<void>}
 */
export const disconnect = async (): Promise<void> => {
    try {
        await prisma.$disconnect();
        logger.info("Database connection closed successfully");
    } catch (error) {
        logger.error(`Error disconnecting from database: ${(error as Error).message}`);
    }
};

/**
 * Execute a database operation with retry mechanism
 * @param operation Function that performs the database operation
 * @param maxRetries Maximum number of retries (default: 3)
 * @returns Result of the operation
 */
export const executeWithRetry = async <T>(
    operation: () => Promise<T>,
    maxRetries: number = 3
): Promise<T> => {
    let retries: number = 0;

    while (true) {
        try {
            return await operation();
        } catch (error) {
            retries++;

            // Check if we should retry
            if (retries <= maxRetries && isPrismaRetryableError(error)) {
                const delay: unknown =RETRY_DELAY_MS * Math.pow(2, retries - 1); // Exponential backoff
                logger.warn(`Database operation failed, retrying (${retries}/${maxRetries}) in ${delay}ms: ${(error as Error).message}`);
                await new Promise(resolve => setTimeout(resolve, delay));
            } else {
                // Max retries reached or non-retryable error
                logger.error(`Database operation failed after ${retries} retries: ${(error as Error).message}`);
                throw error;
            }
        }
    }
};

/**
 * Check if a Prisma error is retryable
 * @param error Error to check
 * @returns True if the error is retryable
 */
function isPrismaRetryableError(error: Error): boolean {
    // Connection errors
    if (error.code === "P1001" || error.code === "P1002") {
        return true;
    }

    // Timeout errors
    if (error.code === "P1008") {
        return true;
    }

    // Connection pool errors
    if ((error as Error).message && (error as Error).message.includes("Connection pool")) {
        return true;
    }

    return false;
}

// Export the enhanced Prisma client
export default prisma;
