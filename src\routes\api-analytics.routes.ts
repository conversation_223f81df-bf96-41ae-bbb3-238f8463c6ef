// jscpd:ignore-file
import { param } from "express-validator";
import { ApiAnalyticsController } from "../controllers/refactored/api-analytics.controller";
import routeProvider from "../core/RouteProvider";
import { ApiAnalyticsController } from "../controllers/refactored/api-analytics.controller";

// Create API analytics controller
const apiAnalyticsController: unknown = new ApiAnalyticsController();

// Create a route builder for API analytics routes
const routeBuilder: unknown =routeProvider.createRouteBuilder(
  "apiAnalytics",
  "/api/analytics/api",
  "API analytics routes"
)
.tags("analytics", "api");

// Add routes
routeBuilder
  // Get API analytics
  .get(
    "/",
    apiAnalyticsController.getAnalytics,
    ["ADMIN"]
  )
  
  // Get API analytics summary
  .get(
    "/summary",
    apiAnalyticsController.getAnalyticsSummary,
    ["ADMIN"]
  )
  
  // Get API analytics by version
  .get(
    "/version/:version",
    apiAnalyticsController.getAnalyticsByVersion,
    ["ADMIN"],
    [param("version").notEmpty()]
  )
  
  // Get API analytics summary by version
  .get(
    "/version/:version/summary",
    apiAnalyticsController.getAnalyticsSummaryByVersion,
    ["ADMIN"],
    [param("version").notEmpty()]
  );

// Build the router
const router: unknown =routeBuilder.build();

export default router;
