// jscpd:ignore-file
import { param as Importedparam } from "express-validator";
import { ApiAnalyticsController as ImportedApiAnalyticsController } from "../controllers/refactored/api-(analytics as any).controller";
import routeProvider from "../core/RouteProvider";
import { ApiAnalyticsController as ImportedApiAnalyticsController } from "../controllers/refactored/api-(analytics as any).controller";

// Create API analytics controller
const apiAnalyticsController = new ApiAnalyticsController();

// Create a route builder for API analytics routes
const routeBuilder: any =(routeProvider as any).createRouteBuilder(
  "apiAnalytics",
  "/api/analytics/api",
  "API analytics routes"
)
.tags("analytics", "api");

// Add routes
routeBuilder
  // Get API analytics
  .get(
    "/",
    (apiAnalyticsController as any).getAnalytics,
    ["ADMIN"]
  )
  
  // Get API analytics summary
  .get(
    "/summary",
    (apiAnalyticsController as any).getAnalyticsSummary,
    ["ADMIN"]
  )
  
  // Get API analytics by version
  .get(
    "/version/:version",
    (apiAnalyticsController as any).getAnalyticsByVersion,
    ["ADMIN"],
    [param("version").notEmpty()]
  )
  
  // Get API analytics summary by version
  .get(
    "/version/:version/summary",
    (apiAnalyticsController as any).getAnalyticsSummaryByVersion,
    ["ADMIN"],
    [param("version").notEmpty()]
  );

// Build the router
const router: any =(routeBuilder as any).build();

export default router;
