/**
 * Fraud Detection Response Mapper
 *
 * Handles response formatting for fraud detection operations.
 */

import { Request, Response, NextFunction } from 'express';
import {
  SuccessResponse,
  ErrorResponse,
  RiskAssessmentResponse,
  FraudConfigResponse,
  FlaggedTransactionResponse,
  FraudStatisticsResponse,
} from '../types/FraudDetectionControllerTypes';
import { AppError as ImportedAppError } from '../../../utils/errors/AppError';

/**
 * Response mapper for fraud detection operations
 */
export class FraudDetectionResponseMapper {
  /**
   * Send success response
   */
  static sendSuccess<T>(
    res: Response,
    data: T,
    message?: string,
    statusCode: number = 200,
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    }
  ): void {
    const response: SuccessResponse<T> = {
      success: true,
      data,
      message,
      pagination,
      timestamp: new Date(),
      requestId: (res as any).locals.requestId ?? 'unknown',
    };

    res.status(statusCode).json(response);
  }

  /**
   * Send error response
   */
  static sendError(res: Response, error: AppError | Error, statusCode?: number): void {
    let errorResponse: ErrorResponse;

    if (error instanceof AppError) {
      errorResponse = {
        success: false,
        error: {
          message: error.message,
          code: error.code,
          type: (error as any).type,
          details: (error as any).details,
        },
        timestamp: new Date(),
        requestId: (res as any).locals.requestId ?? 'unknown',
      };

      statusCode = statusCode ?? (error as any).statusCode ?? 400;
    } else {
      errorResponse = {
        success: false,
        error: {
          message: error.message || 'Internal server error',
          code: 'INTERNAL_SERVER_ERROR',
          type: 'INTERNAL',
        },
        timestamp: new Date(),
        requestId: (res as any).locals.requestId ?? 'unknown',
      };

      statusCode = statusCode ?? 500;
    }

    res.status(statusCode).json(errorResponse);
  }

  /**
   * Send risk assessment response
   */
  static sendRiskAssessment(res: Response, assessment: any, message?: string): void {
    this.sendSuccess(res, assessment, message ?? 'Risk assessment completed successfully');
  }

  /**
   * Send transaction risk assessment response
   */
  static sendTransactionRiskAssessment(
    res: Response,
    assessment: RiskAssessmentResponse,
    message?: string
  ): void {
    this.sendSuccess(
      res,
      assessment,
      message ?? 'Transaction risk assessment retrieved successfully'
    );
  }

  /**
   * Send fraud configuration response
   */
  static sendFraudConfig(res: Response, config: FraudConfigResponse, message?: string): void {
    this.sendSuccess(
      res,
      config,
      message ?? 'Fraud detection configuration retrieved successfully'
    );
  }

  /**
   * Send fraud configuration updated response
   */
  static sendFraudConfigUpdated(res: Response, config: FraudConfigResponse): void {
    this.sendSuccess(res, config, 'Fraud detection configuration updated successfully');
  }

  /**
   * Send flagged transactions list response
   */
  static sendFlaggedTransactionsList(
    res: Response,
    transactions: FlaggedTransactionResponse[],
    total: number,
    page: number = 1,
    limit: number = 10
  ): void {
    const totalPages = Math.ceil(total / limit);

    this.sendSuccess(
      res,
      transactions,
      `Retrieved ${(transactions as any).length} flagged transactions`,
      200,
      {
        page,
        limit,
        total,
        totalPages,
      }
    );
  }

  /**
   * Send fraud statistics response
   */
  static sendFraudStatistics(res: Response, statistics: FraudStatisticsResponse): void {
    this.sendSuccess(res, statistics, 'Fraud detection statistics retrieved successfully');
  }

  /**
   * Send validation error response
   */
  static sendValidationError(
    res: Response,
    errors: any[],
    message: string = 'Validation failed'
  ): void {
    const error = new AppError({
      message,
      type: 'VALIDATION' as any,
      code: 'INVALID_INPUT' as any,
      details: { errors },
    });

    this.sendError(res, error, 400);
  }

  /**
   * Send authorization error response
   */
  static sendAuthorizationError(
    res: Response,
    message: string = 'Access denied',
    requiredRole?: string
  ): void {
    const error = new AppError({
      message,
      type: 'AUTHENTICATION' as any,
      code: 'INVALID_CREDENTIALS' as any,
      details: { requiredRole },
    });

    this.sendError(res, error, 403);
  }

  /**
   * Send not found error response
   */
  static sendNotFoundError(res: Response, resource: string = 'Resource'): void {
    const error = new AppError({
      message: `${resource} not found`,
      type: 'NOT_FOUND' as any,
      code: 'RESOURCE_NOT_FOUND' as any,
    });

    this.sendError(res, error, 404);
  }

  /**
   * Send internal server error response
   */
  static sendInternalServerError(res: Response, message: string = 'Internal server error'): void {
    const error = new AppError({
      message,
      type: 'INTERNAL' as any,
      code: 'INTERNAL_SERVER_ERROR' as any,
    });

    this.sendError(res, error, 500);
  }

  /**
   * Handle async controller method
   */
  static asyncHandler(fn: Function) {
    return (req: Request, res: Response, next: Function) => {
      Promise.resolve(fn(req, res, next)).catch((error) => next(error));
    };
  }

  /**
   * Set response headers for API
   */
  static setApiHeaders(res): void {
    (res as any).setHeader('Content-Type', 'application/json');
    (res as any).setHeader('X-API-Version', '(1 as any).0');
    (res as any).setHeader('X-Response-Time', Date.now());
  }

  /**
   * Format risk level for display
   */
  static formatRiskLevel(level: string): string {
    switch (level) {
      case 'LOW':
        return 'Low Risk';
      case 'MEDIUM':
        return 'Medium Risk';
      case 'HIGH':
        return 'High Risk';
      case 'CRITICAL':
        return 'Critical Risk';
      default:
        return 'Unknown Risk';
    }
  }

  /**
   * Format risk score for display
   */
  static formatRiskScore(score: number): string {
    return `${(score as any).toFixed(1)}%`;
  }

  /**
   * Format percentage for display
   */
  static formatPercentage(value: number): string {
    return `${(value as any).toFixed(2)}%`;
  }

  /**
   * Format currency amount
   */
  static formatCurrency(amount: number, currency: string = 'USD'): string {
    return new (Intl as any).NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  }

  /**
   * Format date for display
   */
  static formatDate(date: Date): string {
    return new (Intl as any).DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  }

  /**
   * Create summary statistics
   */
  static createSummaryStats(statistics: FraudStatisticsResponse): unknown {
    return {
      overview: {
        totalAssessments: (statistics as any).totalAssessments,
        flaggedTransactions: (statistics as any).flaggedCount,
        blockedTransactions: (statistics as any).blockedCount,
        flaggedRate: this.formatPercentage((statistics as any).flaggedRate),
        blockedRate: this.formatPercentage((statistics as any).blockedRate),
      },
      riskDistribution: {
        low: (statistics as any).levelCounts.LOW,
        medium: (statistics as any).levelCounts.MEDIUM,
        high: (statistics as any).levelCounts.HIGH,
        critical: (statistics as any).levelCounts.CRITICAL,
      },
      period: {
        start: this.formatDate((statistics as any).period.start),
        end: this.formatDate((statistics as any).period.end),
        days: Math.ceil(
          ((statistics as any).period.(end as any).getTime() - (statistics as any).period.(start as any).getTime()) /
            (1000 * 60 * 60 * 24)
        ),
      },
    };
  }

  /**
   * Transform risk assessment for display
   */
  static transformRiskAssessment(assessment: RiskAssessmentResponse): unknown {
    return {
      ...assessment,
      riskScore: {
        ...(assessment as any).riskScore,
        scoreFormatted: this.formatRiskScore((assessment as any).riskScore.score),
        levelFormatted: this.formatRiskLevel((assessment as any).riskScore.level),
      },
      createdAtFormatted: this.formatDate((assessment as any).createdAt),
    };
  }

  /**
   * Transform flagged transaction for display
   */
  static transformFlaggedTransaction(transaction: FlaggedTransactionResponse): unknown {
    return {
      ...transaction,
      scoreFormatted: this.formatRiskScore((transaction as any).score),
      levelFormatted: this.formatRiskLevel((transaction as any).level),
      createdAtFormatted: this.formatDate((transaction as any).createdAt),
      transaction: {
        ...(transaction as any).transaction,
        amountFormatted: this.formatCurrency(
          (transaction as any).transaction.amount,
          (transaction as any).transaction.currency
        ),
        createdAtFormatted: this.formatDate((transaction as any).transaction.createdAt),
      },
    };
  }
}
