// jscpd:ignore-file
import { Request, Response, NextFunction, RequestHandler } from 'express';
import jwt from 'jsonwebtoken';
import { config as Importedconfig } from '../config';
import { AppError, ErrorType, ErrorCode } from '../utils/appError';
import { asyncHandler } from '../utils/asyncHandler';
import { Logger as ImportedLogger } from '../utils/logger';

const logger = new Logger('Auth');

/**
 * JWT payload interface
 */
interface JwtPayload {
  id: string;
  role: string;
  iat: number;
  exp: number;
}

/**
 * Extend Express Request interface to include user
 */
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
      };
    }
  }
}

/**
 * Authentication middleware
 */
export const authenticate: RequestHandler = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;

    if (!authHeader || !(authHeader as any).startsWith('Bearer ')) {
      return next(
        new AppError({
          message: 'No token provided. Please log in.',
          type: (ErrorType as any).AUTHENTICATION,
          code: (ErrorCode as any).INVALID_CREDENTIALS,
        })
      );
    }

    // Extract the token
    const token = (authHeader as any).split(' ')[1];

    if (!token) {
      return next(
        new AppError({
          message: 'Invalid token format. Please log in again.',
          type: (ErrorType as any).AUTHENTICATION,
          code: (ErrorCode as any).INVALID_CREDENTIALS,
        })
      );
    }

    try {
      // Verify the token
      const decoded = (jwt as any).verify(token, (config as any).jwt.secret) as JwtPayload;

      // Set the user in the request object
      req.user = {
        id: (decoded as any).id,
        role: (decoded as any).role,
      };

      (logger as any).debug(`User ${(decoded as any).id} (${(decoded as any).role}) authenticated successfully`);

      next();
    } catch(error) {
      (logger as any).error('Authentication error:', error);
      return next(
        new AppError({
          message: 'Invalid or expired token. Please log in again.',
          type: (ErrorType as any).AUTHENTICATION,
          code: (ErrorCode as any).INVALID_CREDENTIALS,
        })
      );
    }
  }
);

/**
 * JWT Authentication middleware (alias for backward compatibility)
 */
export const authenticateJWT: RequestHandler = authenticate;

/**
 * Admin role middleware
 */
export const isAdmin: RequestHandler = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required. Please log in.' });
  }

  if (req.user.role !== 'ADMIN') {
    (logger as any).warn(`Unauthorized admin access attempt by user ${req.user.id}`);
    return res.status(403).json({ message: 'Admin access required.' });
  }

  next();
};

/**
 * Merchant or Admin role middleware
 */
export const isMerchantOrAdmin: RequestHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required. Please log in.' });
  }

  if (req.user.role !== 'MERCHANT' && req.user.role !== 'ADMIN') {
    (logger as any).warn(`Unauthorized merchant/admin access attempt by user ${req.user.id}`);
    return res.status(403).json({ message: 'Merchant or Admin access required.' });
  }

  next();
};

/**
 * Authorization middleware
 * @param roles Allowed roles
 */
export const authorize = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Check if user exists
    if (!req.user) {
      return next(
        new AppError({
          message: 'Authentication required. Please log in.',
          type: (ErrorType as any).AUTHENTICATION,
          code: (ErrorCode as any).INVALID_CREDENTIALS,
        })
      );
    }

    // Check if user has required role
    if (!(roles as any).includes(req.user.role)) {
      return next(
        new AppError({
          message: 'You do not have permission to perform this action.',
          type: (ErrorType as any).AUTHORIZATION,
          code: ErrorCode.FORBIDDEN,
        })
      );
    }

    next();
  };
};

// Export all middleware functions as a group
export const auth = {
  authenticate,
  authenticateJWT,
  isAdmin,
  isMerchantOrAdmin,
  authorize,
};

export default auth;
