// jscpd:ignore-file
import { Request, Response, NextFunction, RequestHandler } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '../config';
import { AppError, ErrorType, ErrorCode } from '../utils/appError';
import { asyncHand<PERSON> } from '../utils/asyncHandler';
import { Logger } from '../utils/logger';

const logger = new Logger('Auth');

/**
 * JWT payload interface
 */
interface JwtPayload {
  id: string;
  role: string;
  iat: number;
  exp: number;
}

/**
 * Extend Express Request interface to include user
 */
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
      };
    }
  }
}

/**
 * Authentication middleware
 */
export const authenticate: RequestHandler = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(
        new AppError({
          message: 'No token provided. Please log in.',
          type: ErrorType.AUTHENTICATION,
          code: ErrorCode.INVALID_CREDENTIALS,
        })
      );
    }

    // Extract the token
    const token = authHeader.split(' ')[1];

    if (!token) {
      return next(
        new AppError({
          message: 'Invalid token format. Please log in again.',
          type: ErrorType.AUTHENTICATION,
          code: ErrorCode.INVALID_CREDENTIALS,
        })
      );
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload;

      // Set the user in the request object
      req.user = {
        id: decoded.id,
        role: decoded.role,
      };

      logger.debug(`User ${decoded.id} (${decoded.role}) authenticated successfully`);

      next();
    } catch (error) {
      logger.error('Authentication error:', error);
      return next(
        new AppError({
          message: 'Invalid or expired token. Please log in again.',
          type: ErrorType.AUTHENTICATION,
          code: ErrorCode.INVALID_CREDENTIALS,
        })
      );
    }
  }
);

/**
 * JWT Authentication middleware (alias for backward compatibility)
 */
export const authenticateJWT: RequestHandler = authenticate;

/**
 * Admin role middleware
 */
export const isAdmin: RequestHandler = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required. Please log in.' });
  }

  if (req.user.role !== 'ADMIN') {
    logger.warn(`Unauthorized admin access attempt by user ${req.user.id}`);
    return res.status(403).json({ message: 'Admin access required.' });
  }

  next();
};

/**
 * Merchant or Admin role middleware
 */
export const isMerchantOrAdmin: RequestHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required. Please log in.' });
  }

  if (req.user.role !== 'MERCHANT' && req.user.role !== 'ADMIN') {
    logger.warn(`Unauthorized merchant/admin access attempt by user ${req.user.id}`);
    return res.status(403).json({ message: 'Merchant or Admin access required.' });
  }

  next();
};

/**
 * Authorization middleware
 * @param roles Allowed roles
 */
export const authorize = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Check if user exists
    if (!req.user) {
      return next(
        new AppError({
          message: 'Authentication required. Please log in.',
          type: ErrorType.AUTHENTICATION,
          code: ErrorCode.INVALID_CREDENTIALS,
        })
      );
    }

    // Check if user has required role
    if (!roles.includes(req.user.role)) {
      return next(
        new AppError({
          message: 'You do not have permission to perform this action.',
          type: ErrorType.AUTHORIZATION,
          code: ErrorCode.FORBIDDEN,
        })
      );
    }

    next();
  };
};

// Export all middleware functions as a group
export const auth = {
  authenticate,
  authenticateJWT,
  isAdmin,
  isMerchantOrAdmin,
  authorize,
};

export default auth;
