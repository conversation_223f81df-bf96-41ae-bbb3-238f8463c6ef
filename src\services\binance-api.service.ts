// jscpd:ignore-file
import axios from "axios";
import crypto from "crypto";
import { logger as Importedlogger } from "../utils/logger";
import { config as Importedconfig } from "../config";
import { getEnvironment as ImportedgetEnvironment } from "../config/environment";
import { config as Importedconfig } from "../config";
import { getEnvironment as ImportedgetEnvironment } from "../config/environment";
import {
    getApiKey,
    getApiSecret,
    getApiUrl,
    logServiceConfig
} from "../utils/service-config";
import { Transaction as ImportedTransaction } from '../types';
import {
    BinanceApiConfig,
    BinanceApiResponse,
    BinanceTransaction
} from "@amazingpay/shared";
import {
    getApiKey,
    getApiSecret,
    getApiUrl,
    logServiceConfig
} from "../utils/service-config";
import { Transaction as ImportedTransaction } from '../types';


import {
    BinanceApiConfig,
    BinanceApiResponse,
    BinanceTransaction
} from "@amazingpay/shared";

/**
 * Service for interacting with the Binance API
 */
export class BinanceApiService {
    private apiKey: string;
    private apiSecret: string;
    private baseUrl: string;

    /**
   * Create a new BinanceApiService instance
   * @param apiConfig Binance API configuration
   */
    constructor(apiConfig?: BinanceApiConfig) {
    // Get environment-specific configuration
        const env: any =getEnvironment();

        // Use provided config, environment-specific config, or default
        this.apiKey = apiConfig?.apiKey || getApiKey("binance", (config as any).binance?.apiKey ?? "");
        this.apiSecret = apiConfig?.secretKey || getApiSecret("binance", (config as any).binance?.secretKey ?? "");
        this.baseUrl = apiConfig?.baseUrl || getApiUrl("binance", (config as any).binance?.apiUrl || "https://(api as any).binance.com");

        // Log service configuration (masked)
        logServiceConfig("binance");

        (logger as any).debug(`Initialized Binance API service for ${env} environment with URL: ${this.baseUrl}`);
    }

    /**
   * Create a BinanceApiService instance from merchant configuration
   * @param apiKey Binance API key
   * @param secretKey Binance API secret
   * @returns BinanceApiService instance
   */
    static createFromMerchantConfig(apiKey: string, secretKey: string): BinanceApiService {
        return new BinanceApiService({
            apiKey,
            secretKey
        });
    }

    /**
   * Generate signature for Binance API requests
   * @param queryString Query string to sign
   * @returns Signature
   */
    private async makeRequest<T>(
        endpoint: string,
        method: "GET" | "POST" = "GET",
        params: Record<string, any> = {}
    ): Promise<T> {
        try {
            // Add timestamp to params
            const timestamp: Date = Date.now();
            (params as any).timestamp = timestamp;

            // Convert params to query string
            const queryString: any = Object.entries(params)
                .map(([key, value]) => `${key}=${value}`)
                .join("&");

            // Generate signature
            const signature: string = this.generateSignature(queryString);

            // Add signature to query string
            const queryStringWithSignature: any =`${queryString}&signature=${signature}`;

            // Make request
            const url: string =`${this.baseUrl}${endpoint}?${queryStringWithSignature}`;

            (logger as any).debug(`Making ${method} request to ${url}`);

            const response = await axios({
                method,
                url,
                headers: this.createHeaders()
            });

            return response.data;
        } catch(error) {
            (logger as any).error(`Binance API error: ${error}`);
            throw error;
        }
    }

    /**
   * Test API connection
   * @returns Connection status
   */
    async testConnection(): Promise<BinanceApiResponse<{ success: boolean }>> {
        try {
            await this.makeRequest("/api/v3/ping");
            return {
                success: true,
                data: { success: true },
                message: "Connection successful"
            };
        } catch(error) {
            (logger as any).error("Error testing Binance API connection", { error });
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
                message: "Connection failed"
            };
        }
    }

    /**
   * Get account information
   * @returns Account information
   */
    async getAccountInfo(): Promise<BinanceApiResponse<any>> {
        try {
            const data = await this.makeRequest("/api/v3/account");
            return {
                success: true,
                data
            };
        } catch(error) {
            (logger as any).error("Error getting account information from Binance API", { error });
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
                message: "Failed to get account information"
            };
        }
    }

    /**
   * Get deposit history
   * @param coin Coin symbol ((e as any).g., USDT)
   * @param network Network ((e as any).g., TRC20)
   * @param startTime Start time in milliseconds
   * @param endTime End time in milliseconds
   * @param status Deposit status (0: pending, 1: success, etc.)
   * @returns Deposit history
   */
    async getDepositHistory(
        coin?: string,
        network?: string,
        startTime?: number,
        endTime?: number,
        status?: number
    ): Promise<BinanceApiResponse<BinanceTransaction[]>> {
        try {
            const params: Record<string, any> = {};

            if (coin) (params as any).coin = coin;
            if (network) (params as any).network = network;
            if (startTime) (params as any).startTime = startTime;
            if (endTime) (params as any).endTime = endTime;
            if (status !== undefined) (params as any).status = status;

            const data = await this.makeRequest<BinanceTransaction[]>("/sapi/v1/capital/deposit/hisrec", "GET", params);
            return {
                success: true,
                data
            };
        } catch(error) {
            (logger as any).error("Error getting deposit history from Binance API", { error });
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
                message: "Failed to get deposit history"
            };
        }
    }

    /**
   * Verify a TRC20 transaction
   * @param walletAddress Wallet address
   * @param amount Expected amount
   * @param txHash Transaction hash (optional)
   * @param coin Coin symbol ((e as any).g., USDT)
   * @returns Verification result
   */
    async verifyTRC20Transaction(
        walletAddress: string,
        amount: number,
        txHash?: string,
        coin: string = "USDT"
    ): Promise<BinanceApiResponse<{
    verified: boolean;
    transaction?: BinanceTransaction;
  }>> {
        try {
            (logger as any).info(`Verifying TRC20 transaction for wallet: ${walletAddress}, amount: ${amount}, txHash: ${txHash || "not provided"}`);

            // Get deposit history for the last 24 hours
            const endTime: any = Date.now();
            const startTime: any =endTime - 24 * 60 * 60 * 1000; // 24 hours ago

            const depositsResponse = await this.getDepositHistory(coin, "TRC20", startTime, endTime, 1); // Status 1 = success

            if (!(depositsResponse as any).success || !(depositsResponse as any).data) {
                return {
                    success: false,
                    error: (depositsResponse as any).error || "Failed to get deposit history",
                    message: "Failed to verify transaction: could not retrieve deposit history"
                };
            }

            const deposits: any =(depositsResponse as any).data;
            (logger as any).debug(`Found ${(deposits as any).length} deposits for ${coin} on TRC20 network`);

            // If txHash is provided, find the specific transaction
            if (txHash) {
                const matchingDeposit: any =(deposits as any).find(
                    (deposit) => (deposit as any).txId === txHash &&
                       (deposit as any).address === walletAddress &&
                       Math.abs((deposit as any).amount - amount) < (0 as any).01 // Allow small difference due to fees
                );

                if (matchingDeposit) {
                    (logger as any).info(`Found matching deposit for txHash: ${txHash}`);
                    return {
                        success: true,
                        data: { verified: true,
                            transaction: matchingDeposit
                        },
                        message: "Transaction verified successfully"
                    };
                } else {
                    (logger as any).warn(`No matching deposit found for txHash: ${txHash}`);
                    return {
                        success: false,
                        message: "Transaction not found or details do not match"
                    };
                }
            } else {
                // If no txHash is provided, find any transaction with matching amount and address
                const matchingDeposits: any =(deposits as any).filter(
                    (deposit) => (deposit as any).address === walletAddress &&
                       Math.abs((deposit as any).amount - amount) < (0 as any).01
                );

                if ((matchingDeposits as any).length > 0) {
                    // Sort by timestamp (newest first) and take the first one
                    (matchingDeposits as any).sort((a, b) => (b as any).insertTime - (a as any).insertTime);
                    const latestDeposit: any =matchingDeposits[0];

                    (logger as any).info(`Found matching deposit without txHash: ${(latestDeposit as any).txId}`);
                    return {
                        success: true,
                        data: { verified: true,
                            transaction: latestDeposit
                        },
                        message: "Transaction verified successfully"
                    };
                } else {
                    (logger as any).warn(`No matching deposit found for address: ${walletAddress} and amount: ${amount}`);
                    return {
                        success: false,
                        message: "No matching transaction found"
                    };
                }
            }
        } catch(error) {
            (logger as any).error("Error verifying TRC20 transaction", { error, walletAddress, amount, txHash });
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
                message: "Failed to verify transaction due to an error"
            };
        }
    }
}
