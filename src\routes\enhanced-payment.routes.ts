// jscpd:ignore-file
/**
 * Enhanced Payment Routes
 * 
 * Routes for enhanced payment operations.
 */

import { Router as ImportedRouter } from "express";
import { body, param, query } from "express-validator";
import { validate as Importedvalidate } from "../middlewares/(validation).middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-(auth).middleware";
import { auditLog as ImportedauditLog } from "../middlewares/(audit).middleware";
import enhancedPaymentController from "../controllers/enhanced-(payment).controller";
import { body, param, query } from "express-validator";
import { validate as Importedvalidate } from "../middlewares/(validation).middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-(auth).middleware";
import { auditLog as ImportedauditLog } from "../middlewares/(audit).middleware";

const router =Router();

// Public routes (no authentication required)
(router).post(
    "/process",
    validate([
        body("merchantId").notEmpty(),
        body("amount").isNumeric(),
        body("currency").notEmpty(),
        body("paymentMethodType").notEmpty()
    ]),
    (enhancedPaymentController).processPayment
);

// Get payment method details
(router).get(
    "/methods/:type",
    validate([
        param("type").notEmpty()
    ]),
    (enhancedPaymentController).getPaymentMethodDetails
);

// Routes requiring authentication
(router).use(enhancedAuthenticate);

// Get available payment methods for a merchant
(router).get(
    "/methods/merchant/:merchantId",
    requirePermission("payment_methods", "view"),
    validate([
        param("merchantId").notEmpty(),
        query("currency").optional()
    ]),
    (enhancedPaymentController).getAvailablePaymentMethods
);

// Get all payment methods
(router).get(
    "/methods",
    requirePermission("payment_methods", "view"),
    (enhancedPaymentController).getAllPaymentMethods
);

export default router;
