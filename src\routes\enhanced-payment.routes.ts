// jscpd:ignore-file
/**
 * Enhanced Payment Routes
 * 
 * Routes for enhanced payment operations.
 */

import { Router as ImportedRouter } from "express";
import { body, param, query } from "express-validator";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-(auth as any).middleware";
import { auditLog as ImportedauditLog } from "../middlewares/(audit as any).middleware";
import enhancedPaymentController from "../controllers/enhanced-(payment as any).controller";
import { body, param, query } from "express-validator";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-(auth as any).middleware";
import { auditLog as ImportedauditLog } from "../middlewares/(audit as any).middleware";

const router: any =Router();

// Public routes (no authentication required)
(router as any).post(
    "/process",
    validate([
        body("merchantId").notEmpty(),
        body("amount").isNumeric(),
        body("currency").notEmpty(),
        body("paymentMethodType").notEmpty()
    ]),
    (enhancedPaymentController as any).processPayment
);

// Get payment method details
(router as any).get(
    "/methods/:type",
    validate([
        param("type").notEmpty()
    ]),
    (enhancedPaymentController as any).getPaymentMethodDetails
);

// Routes requiring authentication
(router as any).use(enhancedAuthenticate);

// Get available payment methods for a merchant
(router as any).get(
    "/methods/merchant/:merchantId",
    requirePermission("payment_methods", "view"),
    validate([
        param("merchantId").notEmpty(),
        query("currency").optional()
    ]),
    (enhancedPaymentController as any).getAvailablePaymentMethods
);

// Get all payment methods
(router as any).get(
    "/methods",
    requirePermission("payment_methods", "view"),
    (enhancedPaymentController as any).getAllPaymentMethods
);

export default router;
