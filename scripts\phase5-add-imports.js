#!/usr/bin/env node

/**
 * Phase 5: Add Missing Import Statements Script
 * Adds necessary import statements for the new types we're using
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎯 PHASE 5: ADDING MISSING IMPORT STATEMENTS');
console.log('============================================');

// Import statements to add based on usage patterns
const importMappings = {
    // Common types
    'UserContext': "import { UserContext } from '../types/common';",
    'RequestContext': "import { RequestContext } from '../types/common';",
    'PaginationParams': "import { PaginationParams } from '../types/common';",
    'ValidationError': "import { ValidationError } from '../types/common';",
    'ApiResponse': "import { ApiResponse } from '../types/common';",
    'ServiceResult': "import { ServiceResult } from '../types/common';",
    'ValidationResult': "import { ValidationResult } from '../types/services';",
    'PermissionResult': "import { PermissionResult } from '../types/services';",
    
    // Express types
    'AuthenticatedRequest': "import { AuthenticatedRequest } from '../types/express';",
    'ExtendedResponse': "import { ExtendedResponse } from '../types/express';",
    'NextFunction': "import { NextFunction } from 'express';",
    
    // User types
    'User': "import { User } from '../types/user';",
    'CreateUserData': "import { CreateUserData } from '../types/user';",
    'UpdateUserData': "import { UpdateUserData } from '../types/user';",
    'UserFilters': "import { UserFilters } from '../types/user';",
    
    // Merchant types
    'Merchant': "import { Merchant } from '../types/merchant';",
    'CreateMerchantData': "import { CreateMerchantData } from '../types/merchant';",
    'UpdateMerchantData': "import { UpdateMerchantData } from '../types/merchant';",
    'MerchantFilters': "import { MerchantFilters } from '../types/merchant';",
    
    // Payment types
    'Payment': "import { Payment } from '../types/payment';",
    'CreatePaymentRequest': "import { CreatePaymentRequest } from '../types/payment';",
    'UpdatePaymentRequest': "import { UpdatePaymentRequest } from '../types/payment';",
    'PaymentFilters': "import { PaymentFilters } from '../types/payment';",
    'RefundRequest': "import { RefundRequest } from '../types/payment';",
    'RefundResponse': "import { RefundResponse } from '../types/payment';",
    
    // Transaction types
    'Transaction': "import { Transaction } from '../types/payment';",
    'CreateTransactionData': "import { CreateTransactionData } from '../types/payment';",
    'UpdateTransactionData': "import { UpdateTransactionData } from '../types/payment';",
    'TransactionFilters': "import { TransactionFilters } from '../types/payment';",
    
    // Admin types
    'AdminUserResponse': "import { AdminUserResponse } from '../types/admin';",
    'CreateAdminUserRequest': "import { CreateAdminUserRequest } from '../types/admin';",
    'UpdateAdminUserRequest': "import { UpdateAdminUserRequest } from '../types/admin';",
    'AdminUserFilters': "import { AdminUserFilters } from '../types/admin';",
    'DashboardDataResponse': "import { DashboardDataResponse } from '../types/admin';",
    'SystemHealthStatus': "import { SystemHealthStatus } from '../types/admin';",
    'Role': "import { Role } from '../types/admin';",
    'Permission': "import { Permission } from '../types/admin';",
    
    // Auth types
    'LoginCredentials': "import { LoginCredentials } from '../types/auth';",
    'AuthTokens': "import { AuthTokens } from '../types/auth';",
    'JwtPayload': "import { JwtPayload } from '../types/auth';",
    'PasswordResetRequest': "import { PasswordResetRequest } from '../types/auth';",
    'ChangePassword': "import { ChangePassword } from '../types/auth';",
    
    // Prisma types
    'PrismaClientType': "import { PrismaClientType } from '../types/prisma';",
    'PrismaTransaction': "import { PrismaTransaction } from '../types/prisma';",
    'UserWhereInput': "import { UserWhereInput } from '../types/prisma';",
    'MerchantWhereInput': "import { MerchantWhereInput } from '../types/prisma';",
    'PaymentWhereInput': "import { PaymentWhereInput } from '../types/prisma';",
    'TransactionWhereInput': "import { TransactionWhereInput } from '../types/prisma';",
    'AdminWhereInput': "import { AdminWhereInput } from '../types/prisma';",
};

function findAllTypeScriptFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'coverage'].includes(item)) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

function getErrorCount() {
    try {
        const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { encoding: 'utf8' });
        const errorMatches = output.match(/error TS/g) || [];
        return errorMatches.length;
    } catch (error) {
        const errorMatches = error.stdout.match(/error TS/g) || [];
        return errorMatches.length;
    }
}

function getRelativeImportPath(fromFile, toFile) {
    const relativePath = path.relative(path.dirname(fromFile), toFile);
    return relativePath.startsWith('.') ? relativePath : './' + relativePath;
}

function processFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let modifiedContent = content;
        let addedImports = [];
        
        // Check which types are used in this file
        const usedTypes = [];
        for (const [typeName, importStatement] of Object.entries(importMappings)) {
            if (content.includes(typeName) && !content.includes(`import`) || !content.includes(typeName)) {
                // Check if the type is actually used (not just in comments)
                const typeRegex = new RegExp(`\\b${typeName}\\b`, 'g');
                const matches = content.match(typeRegex);
                if (matches && matches.length > 0) {
                    // Check if it's not already imported
                    if (!content.includes(`import`) || !content.includes(typeName)) {
                        usedTypes.push({ typeName, importStatement });
                    }
                }
            }
        }
        
        if (usedTypes.length > 0) {
            // Find the position to insert imports (after existing imports or at the top)
            const lines = content.split('\n');
            let insertPosition = 0;
            let hasImports = false;
            
            // Find the last import statement
            for (let i = 0; i < lines.length; i++) {
                if (lines[i].trim().startsWith('import ')) {
                    hasImports = true;
                    insertPosition = i + 1;
                } else if (hasImports && !lines[i].trim().startsWith('import ') && lines[i].trim() !== '') {
                    break;
                }
            }
            
            // If no imports found, insert after any initial comments
            if (!hasImports) {
                for (let i = 0; i < lines.length; i++) {
                    if (!lines[i].trim().startsWith('//') && !lines[i].trim().startsWith('/*') && lines[i].trim() !== '') {
                        insertPosition = i;
                        break;
                    }
                }
            }
            
            // Adjust import paths based on file location
            const adjustedImports = usedTypes.map(({ typeName, importStatement }) => {
                // Simple path adjustment - this is a basic implementation
                // In a real scenario, you'd want more sophisticated path resolution
                let adjustedImport = importStatement;
                
                // Count directory depth to adjust relative paths
                const depth = filePath.split(path.sep).length - 2; // -2 for src and filename
                if (depth > 1) {
                    adjustedImport = importStatement.replace('../types/', '../'.repeat(depth - 1) + 'types/');
                }
                
                return adjustedImport;
            });
            
            // Insert the imports
            lines.splice(insertPosition, 0, ...adjustedImports, '');
            modifiedContent = lines.join('\n');
            addedImports = adjustedImports;
        }
        
        if (addedImports.length > 0) {
            fs.writeFileSync(filePath, modifiedContent, 'utf8');
            return { filePath, addedImports };
        }
        
        return null;
    } catch (error) {
        return { filePath, error: error.message };
    }
}

async function main() {
    console.log('🔍 Scanning for TypeScript files...');
    
    const files = findAllTypeScriptFiles('./src');
    console.log(`📁 Found ${files.length} TypeScript files`);
    
    console.log('📊 Getting initial error count...');
    const initialErrors = getErrorCount();
    console.log(`🚨 Initial TypeScript errors: ${initialErrors}`);
    
    console.log('🚀 Starting import statement addition...');
    const startTime = Date.now();
    
    const results = [];
    let totalImportsAdded = 0;
    
    for (const file of files) {
        const result = processFile(file);
        if (result) {
            results.push(result);
            if (result.addedImports) {
                totalImportsAdded += result.addedImports.length;
            }
        }
    }
    
    console.log('📊 Getting final error count...');
    const finalErrors = getErrorCount();
    const totalErrorsFixed = initialErrors - finalErrors;
    
    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;
    
    console.log('\n🎯 IMPORT STATEMENT ADDITION COMPLETE!');
    console.log('======================================');
    console.log(`⏱️  Processing time: ${processingTime.toFixed(2)} seconds`);
    console.log(`📁 Files processed: ${results.filter(r => !r.error).length}`);
    console.log(`❌ Files with errors: ${results.filter(r => r.error).length}`);
    console.log(`📦 Total imports added: ${totalImportsAdded}`);
    console.log(`🚨 TypeScript errors before: ${initialErrors}`);
    console.log(`✅ TypeScript errors after: ${finalErrors}`);
    console.log(`🎯 Total errors fixed: ${totalErrorsFixed}`);
    
    if (totalErrorsFixed > 0) {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}%`);
        console.log('\n🎉 SUCCESS! Import statements added successfully!');
        console.log('🏆 Your application now has better type imports!');
    } else if (totalErrorsFixed === 0) {
        console.log('📈 Success rate: 0.0% (no net change)');
        console.log('\n✨ No errors were fixed, but imports were added!');
    } else {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}% (negative - new errors introduced)`);
        console.log('\n⚠️  Some imports may have introduced new errors. Consider reviewing.');
    }
    
    const errorFiles = results.filter(r => r.error);
    if (errorFiles.length > 0) {
        console.log('\n❌ Files with processing errors:');
        errorFiles.forEach(({ filePath, error }) => {
            console.log(`   ${path.relative(process.cwd(), filePath)}: ${error}`);
        });
    }
    
    const successFiles = results.filter(r => r.addedImports && r.addedImports.length > 0);
    if (successFiles.length > 0) {
        console.log(`\n✅ Successfully added imports to ${successFiles.length} files`);
        console.log('Sample files with added imports:');
        successFiles.slice(0, 5).forEach(({ filePath, addedImports }) => {
            console.log(`   ${path.relative(process.cwd(), filePath)}: ${addedImports.length} imports`);
        });
    }
}

main().catch(console.error);
