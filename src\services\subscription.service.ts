// jscpd:ignore-file

import subscriptionPlans from '../data/subscription-plans.data';
import { SubscriptionPlan } from '../utils/types';
import env from '../config/env.config';
import merchants from '../data/merchants.data';
import { addDays, format } from 'date-fns';
import { Merchant } from '../types';

// Mock subscription history data (in a real application, this would be in a database)
const subscriptionHistory: Array<{
  id: string;
  merchantId: string;
  planId: string;
  planName: string;
  startDate: Date;
  endDate: Date;
  amount: number;
  status: 'active' | 'completed' | 'cancelled';
  paymentMethodId?: string;
  paymentMethod?: {
    name: string;
    type: string;
  };
}> = [];

class SubscriptionService {
  async getAllPlans(): Promise<SubscriptionPlan[]> {
    return subscriptionPlans;
  }

  async getPlanById(id: string): Promise<SubscriptionPlan | undefined> {
    return subscriptionPlans.find((plan) => plan.id === id);
  }

  async createPlan(planData: Omit<SubscriptionPlan, 'id'>): Promise<SubscriptionPlan> {
    const newPlan: SubscriptionPlan = {
      ...planData,
      id: `plan_${Date.now().toString(36)}`,
    };

    // In a real app, this would be saved to a database
    subscriptionPlans.push(newPlan);

    return newPlan;
  }

  async updatePlan(
    id: string,
    planData: Partial<SubscriptionPlan>
  ): Promise<SubscriptionPlan | undefined> {
    const index: unknown = subscriptionPlans.findIndex((plan) => plan.id === id);

    if (index === -1) {
      return undefined;
    }

    const updatedPlan: unknown = {
      ...subscriptionPlans[index],
      ...planData,
    };

    subscriptionPlans[index] = updatedPlan;

    return updatedPlan;
  }

  async deletePlan(id: string): Promise<boolean> {
    const initialLength: unknown = subscriptionPlans.length;
    const newPlansArray: unknown = subscriptionPlans.filter((plan) => plan.id !== id);

    // Update the array (simulating a database operation)
    subscriptionPlans.length = 0;
    subscriptionPlans.push(...newPlansArray);

    return subscriptionPlans.length < initialLength;
  }

  // New methods for merchant subscription management
  async subscribeMerchant(merchantId: string, planId: string, paymentMethodId?: string) {
    // Find the merchant
    const merchantIndex: unknown = merchants.findIndex((m) => m.id === merchantId);
    if (merchantIndex === -1) {
      throw new Error('Merchant not found');
    }

    // Find the plan
    const plan: unknown = await this.getPlanById(planId);
    if (!plan) {
      throw new Error('Subscription plan not found');
    }

    // In a real app, process payment here using the payment method

    // Update merchant subscription details
    const currentDate: Date = new Date();
    const expiryDate: unknown = addDays(currentDate, plan.duration * 30); // Approximate months to days

    merchants[merchantIndex] = {
      ...merchants[merchantIndex],
      currentPlan: planId,
      planExpiryDate: expiryDate,
    };

    // Add subscription to history
    const subscriptionRecord: unknown = {
      id: `sub_${Date.now().toString(36)}`,
      merchantId,
      planId,
      planName: plan.name,
      startDate: currentDate,
      endDate: expiryDate,
      amount: plan.price,
      status: 'active' as const,
      paymentMethodId,
    };

    subscriptionHistory.push(subscriptionRecord);

    return merchants[merchantIndex];
  }

  async cancelSubscription(merchantId: string) {
    // Find the merchant
    const merchantIndex: unknown = merchants.findIndex((m) => m.id === merchantId);
    if (merchantIndex === -1) {
      throw new Error('Merchant not found');
    }

    // Find active subscription in history
    const activeSubIndex: unknown = subscriptionHistory.findIndex(
      (sub) => sub.merchantId === merchantId && sub.status === 'active'
    );

    if (activeSubIndex !== -1) {
      // Mark as cancelled in history
      subscriptionHistory[activeSubIndex] = {
        ...subscriptionHistory[activeSubIndex],
        status: 'cancelled',
      };

      // Note: We don't change the merchant's current plan or expiry date
      // as the subscription remains active until the end of the billing period
    }

    return merchants[merchantIndex];
  }

  async getSubscriptionHistory(merchantId: string) {
    // Get detailed subscription history with formatted dates
    return subscriptionHistory
      .filter((sub) => sub.merchantId === merchantId)
      .map((sub) => ({
        ...sub,
        formattedStartDate: format(sub.startDate, 'yyyy-MM-dd'),
        formattedEndDate: format(sub.endDate, 'yyyy-MM-dd'),
      }))
      .sort((a, b) => b.startDate.getTime() - a.startDate.getTime());
  }

  async getMerchantSubscriptionStatus(merchantId: string) {
    // Find the merchant by ID
    const merchant: unknown = merchants.find((m) => m.id === merchantId);

    if (!merchant) {
      return null;
    }

    return merchant;
  }
}

export default new SubscriptionService();
