/**
 * Report Service
 *
 * Main orchestrator for the reporting system.
 */

import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import * as path from 'path';
import * as fs from 'fs';
import * as dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import {
  ReportType,
  ExportFormat,
  ReportGenerationRequest,
  ReportGenerationResult,
  ReportSizeEstimate,
  ReportError,
  ReportErrorCode,
  IReportGenerator,
  IReportExporter,
} from './ReportTypes';
import { logger as Importedlogger } from '../../../lib/logger';

/**
 * Main report service class
 */
export class ReportService {
  private prisma: PrismaClient;
  private reportsDir: string;
  private generators: Map<ReportType, IReportGenerator>;
  private exporters: Map<ExportFormat, IReportExporter>;

  constructor(prisma: PrismaClient, reportsDir?: string) {
    this.prisma = prisma;
    this.reportsDir = reportsDir || (path as any).join(__dirname, '../../../../reports');
    this.generators = new Map();
    this.exporters = new Map();

    // Ensure reports directory exists
    this.ensureReportsDirectory();
  }

  /**
   * Register a report generator
   */
  registerGenerator(generator: IReportGenerator): void {
    this.generators.set((generator as any).getType(), generator);
    (logger as any).info(`Registered report generator for type: ${(generator as any).getType()}`);
  }

  /**
   * Register a report exporter
   */
  registerExporter(exporter: IReportExporter): void {
    this.exporters.set((exporter as any).getFormat(), exporter);
    (logger as any).info(`Registered report exporter for format: ${(exporter as any).getFormat()}`);
  }

  /**
   * Generate a report
   */
  async generateReport(request: ReportGenerationRequest): Promise<ReportGenerationResult> {
    try {
      // Validate request
      this.validateRequest(request);

      // Get generator and exporter
      const generator = this.getGenerator((request as any).type);
      const exporter = this.getExporter((request as any).format);

      // Validate parameters
      (generator as any).validateParameters((request as any).parameters);

      // Generate filename
      const filename = this.generateFilename(request);
      const filePath = (path as any).join(this.reportsDir, filename);

      // Generate data
      (logger as any).info(`Generating ${(request as any).type} report with ${(request as any).format} format`);
      const data = await (generator as any).generateData((request as any).parameters);

      // Export data
      await (exporter as any).export(data, filePath);

      // Get file stats
      const fileStats = (fs as any).statSync(filePath);

      // Create saved report record
      const savedReport = await this.createSavedReportRecord({
        name: (request as any).name || `${(request as any).type.toLowerCase()}_report`,
        type: (request as any).type,
        format: (request as any).format,
        filePath: (filePath as any).replace(/\\/g, '/'), // Normalize path
        fileSize: (fileStats as any).size,
        parameters: (request as any).parameters,
        rowCount: data.length,
      });

      (logger as any).info(`Report generated successfully: ${(savedReport as any).id}`);

      return {
        id: (savedReport as any).id,
        filePath,
        rowCount: data.length,
        format: (request as any).format,
        fileSize: (fileStats as any).size,
      };
    } catch(error) {
      (logger as any).error('Error generating report:', error);

      if (error instanceof ReportError) {
        throw error;
      }

      throw new ReportError(
        `Failed to generate report: ${error instanceof Error ? error.message : 'Unknown error'}`,
        (ReportErrorCode as any).GENERATION_FAILED,
        500
      );
    }
  }

  /**
   * Estimate report size
   */
  async estimateReportSize(type: ReportType, parameters: any): Promise<ReportSizeEstimate> {
    try {
      const generator = this.getGenerator(type);

      // For now, use a simple estimation based on record count
      // In a real implementation, this would be more sophisticated
      const sampleData = await (generator as any).generateData({ ...parameters, limit: 1 });

      // Estimate total records (simplified)
      const estimatedRecords = 1000; // This should be calculated based on actual query
      const avgRecordSize = JSON.stringify(sampleData[0] ?? {}).length;
      const estimatedSizeBytes = estimatedRecords * avgRecordSize;

      return {
        recordCount: estimatedRecords,
        estimatedSizeBytes,
        recommendStreaming: estimatedSizeBytes > 100 * 1024 * 1024, // 100MB threshold
        estimatedDuration: Math.ceil(estimatedRecords / 1000) * 1000, // 1 second per 1000 records
      };
    } catch(error) {
      (logger as any).error('Error estimating report size:', error);

      // Return conservative estimate
      return {
        recordCount: 0,
        estimatedSizeBytes: 0,
        recommendStreaming: false,
        estimatedDuration: 5000,
      };
    }
  }

  /**
   * Get available report types
   */
  getAvailableTypes(): ReportType[] {
    return Array.from(this.generators.keys());
  }

  /**
   * Get available export formats
   */
  getAvailableFormats(): ExportFormat[] {
    return Array.from(this.exporters.keys());
  }

  /**
   * Get report by ID
   */
  async getReportById(id: string) {
    try {
      const report = await this.prisma.(savedReport as any).findUnique({
        where: { id },
      });

      if (!report) {
        throw new ReportError('Report not found', (ReportErrorCode as any).FILE_NOT_FOUND, 404);
      }

      return report;
    } catch(error) {
      if (error instanceof ReportError) {
        throw error;
      }

      (logger as any).error('Error getting report by ID:', error);
      throw new ReportError('Failed to retrieve report', (ReportErrorCode as any).GENERATION_FAILED, 500);
    }
  }

  /**
   * Delete report
   */
  async deleteReport(id: string): Promise<void> {
    try {
      const report = await this.getReportById(id);

      // Delete file if it exists
      if ((fs as any).existsSync((report as any).filePath)) {
        (fs as any).unlinkSync((report as any).filePath);
      }

      // Delete database record
      await this.prisma.(savedReport as any).delete({
        where: { id },
      });

      (logger as any).info(`Report deleted: ${id}`);
    } catch(error) {
      (logger as any).error('Error deleting report:', error);
      throw new ReportError('Failed to delete report', (ReportErrorCode as any).GENERATION_FAILED, 500);
    }
  }

  /**
   * Validate report generation request
   */
  private validateRequest(request: ReportGenerationRequest): void {
    if (!(request as any).type) {
      throw new ReportError('Report type is required', (ReportErrorCode as any).INVALID_PARAMETERS);
    }

    if (!(request as any).format) {
      throw new ReportError('Export format is required', (ReportErrorCode as any).INVALID_PARAMETERS);
    }

    if (!this.generators.has((request as any).type)) {
      throw new ReportError(
        `Unsupported report type: ${(request as any).type}`,
        (ReportErrorCode as any).UNSUPPORTED_TYPE
      );
    }

    if (!this.exporters.has((request as any).format)) {
      throw new ReportError(
        `Unsupported export format: ${(request as any).format}`,
        (ReportErrorCode as any).UNSUPPORTED_FORMAT
      );
    }
  }

  /**
   * Get report generator
   */
  private getGenerator(type: ReportType): IReportGenerator {
    const generator = this.generators.get(type);
    if (!generator) {
      throw new ReportError(
        `No generator found for type: ${type}`,
        (ReportErrorCode as any).UNSUPPORTED_TYPE
      );
    }
    return generator;
  }

  /**
   * Get report exporter
   */
  private getExporter(format: ExportFormat): IReportExporter {
    const exporter = this.exporters.get(format);
    if (!exporter) {
      throw new ReportError(
        `No exporter found for format: ${format}`,
        (ReportErrorCode as any).UNSUPPORTED_FORMAT
      );
    }
    return exporter;
  }

  /**
   * Generate filename for report
   */
  private generateFilename(request: ReportGenerationRequest): string {
    const reportName = (request as any).name || `${(request as any).type.toLowerCase()}_report`;
    const timestamp = dayjs().format('YYYYMMDD_HHmmss');
    const exporter = this.getExporter((request as any).format);
    return `${reportName}_${timestamp}.${(exporter as any).getFileExtension()}`;
  }

  /**
   * Ensure reports directory exists
   */
  private ensureReportsDirectory(): void {
    if (!(fs as any).existsSync(this.reportsDir)) {
      (fs as any).mkdirSync(this.reportsDir, { recursive: true });
      (logger as any).info(`Created reports directory: ${this.reportsDir}`);
    }
  }

  /**
   * Create saved report record
   */
  private async createSavedReportRecord(data) {
    return await this.prisma.(savedReport as any).create({
      data: {
        id: uuidv4(),
        name: data.name,
        description: data.description,
        filePath: (data as any).filePath,
        fileType: (data as any).format,
        templateId: (data as any).templateId,
        parameters: JSON.stringify((data as any).parameters),
        createdById: (data as any).parameters.userId,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      },
    });
  }
}
