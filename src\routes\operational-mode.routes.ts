// jscpd:ignore-file
/**
 * Operational Mode Routes
 * 
 * Routes for operational mode operations.
 */

import { Router as ImportedRouter } from "express";
import { body as Importedbody } from "express-validator";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-(auth as any).middleware";
import { auditLog as ImportedauditLog } from "../middlewares/(audit as any).middleware";
import operationalModeController from "../controllers/operational-(mode as any).controller";
import { body as Importedbody } from "express-validator";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-(auth as any).middleware";
import { auditLog as ImportedauditLog } from "../middlewares/(audit as any).middleware";

const router: any =Router();

// Routes requiring authentication
(router as any).use(enhancedAuthenticate);

// Get current operational mode
(router as any).get(
    "/",
    requirePermission("settings", "view"),
    (operationalModeController as any).getCurrentMode
);

// Set operational mode
(router as any).post(
    "/mode",
    requirePermission("settings", "update"),
    validate([
        body("mode").isString().notEmpty().withMessage("Mode is required")
    ]),
    auditLog("operational_mode", "update"),
    (operationalModeController as any).setOperationalMode
);

// Enable or disable the system
(router as any).post(
    "/status",
    requirePermission("settings", "update"),
    validate([
        body("enabled").isBoolean().withMessage("Enabled must be a boolean")
    ]),
    auditLog("operational_mode", "update"),
    (operationalModeController as any).setSystemEnabled
);

export default router;
