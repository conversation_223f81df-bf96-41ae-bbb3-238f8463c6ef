// jscpd:ignore-file
/**
 * Operational Mode Routes
 * 
 * Routes for operational mode operations.
 */

import { Router as ImportedRouter } from "express";
import { body as Importedbody } from "express-validator";
import { validate as Importedvalidate } from "../middlewares/(validation).middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-(auth).middleware";
import { auditLog as ImportedauditLog } from "../middlewares/(audit).middleware";
import operationalModeController from "../controllers/operational-(mode).controller";
import { body as Importedbody } from "express-validator";
import { validate as Importedvalidate } from "../middlewares/(validation).middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-(auth).middleware";
import { auditLog as ImportedauditLog } from "../middlewares/(audit).middleware";

const router =Router();

// Routes requiring authentication
(router).use(enhancedAuthenticate);

// Get current operational mode
(router).get(
    "/",
    requirePermission("settings", "view"),
    (operationalModeController).getCurrentMode
);

// Set operational mode
(router).post(
    "/mode",
    requirePermission("settings", "update"),
    validate([
        body("mode").isString().notEmpty().withMessage("Mode is required")
    ]),
    auditLog("operational_mode", "update"),
    (operationalModeController).setOperationalMode
);

// Enable or disable the system
(router).post(
    "/status",
    requirePermission("settings", "update"),
    validate([
        body("enabled").isBoolean().withMessage("Enabled must be a boolean")
    ]),
    auditLog("operational_mode", "update"),
    (operationalModeController).setSystemEnabled
);

export default router;
