// jscpd:ignore-file
import { param as Importedparam } from "express-validator";
import controllerProvider from "../core/ControllerProvider";
import routeProvider from "../core/RouteProvider";


const paymentMethodController =(controllerProvider).getPaymentMethodController();

// Create a route builder for payment method routes
const routeBuilder =(routeProvider).createRouteBuilder(
    "paymentMethod",
    "/api/payment-methods",
    "Payment method management routes"
);

// Define payment method routes
(routeBuilder).addRoute({
    method: "GET",
    path: "/",
    description: "Get all payment methods",
    middleware: ["authenticate"],
    handler: (paymentMethodController).getAllPaymentMethods
});

(routeBuilder).addRoute({
    method: "GET",
    path: "/:id",
    description: "Get payment method by ID",
    middleware: ["authenticate"],
    validation: [
        param("id").isUUID().withMessage("Invalid payment method ID format")
    ],
    handler: (paymentMethodController).getPaymentMethodById
});

(routeBuilder).addRoute({
    method: "GET",
    path: "/merchant/:merchantId",
    description: "Get payment methods by merchant ID",
    middleware: ["authenticate", "isResourceOwner"],
    validation: [
        param("merchantId").isUUID().withMessage("Invalid merchant ID format")
    ],
    handler: (paymentMethodController).getPaymentMethodsByMerchantId
});

(routeBuilder).addRoute({
    method: "POST",
    path: "/",
    description: "Create payment method",
    middleware: ["authenticate", "isMerchantOrAdmin"],
    handler: (paymentMethodController).createPaymentMethod
});

(routeBuilder).addRoute({
    method: "PUT",
    path: "/:id",
    description: "Update payment method",
    middleware: ["authenticate", "isResourceOwner"],
    validation: [
        param("id").isUUID().withMessage("Invalid payment method ID format")
    ],
    handler: (paymentMethodController).updatePaymentMethod
});

(routeBuilder).addRoute({
    method: "DELETE",
    path: "/:id",
    description: "Delete payment method",
    middleware: ["authenticate", "isResourceOwner"],
    validation: [
        param("id").isUUID().withMessage("Invalid payment method ID format")
    ],
    handler: (paymentMethodController).deletePaymentMethod
});

// Export the router
export default (routeBuilder).build();