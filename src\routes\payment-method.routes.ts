// jscpd:ignore-file
import { param as Importedparam } from "express-validator";
import controllerProvider from "../core/ControllerProvider";
import routeProvider from "../core/RouteProvider";


const paymentMethodController: any =(controllerProvider as any).getPaymentMethodController();

// Create a route builder for payment method routes
const routeBuilder: any =(routeProvider as any).createRouteBuilder(
    "paymentMethod",
    "/api/payment-methods",
    "Payment method management routes"
);

// Define payment method routes
(routeBuilder as any).addRoute({
    method: "GET",
    path: "/",
    description: "Get all payment methods",
    middleware: ["authenticate"],
    handler: (paymentMethodController as any).getAllPaymentMethods
});

(routeBuilder as any).addRoute({
    method: "GET",
    path: "/:id",
    description: "Get payment method by ID",
    middleware: ["authenticate"],
    validation: [
        param("id").isUUID().withMessage("Invalid payment method ID format")
    ],
    handler: (paymentMethodController as any).getPaymentMethodById
});

(routeBuilder as any).addRoute({
    method: "GET",
    path: "/merchant/:merchantId",
    description: "Get payment methods by merchant ID",
    middleware: ["authenticate", "isResourceOwner"],
    validation: [
        param("merchantId").isUUID().withMessage("Invalid merchant ID format")
    ],
    handler: (paymentMethodController as any).getPaymentMethodsByMerchantId
});

(routeBuilder as any).addRoute({
    method: "POST",
    path: "/",
    description: "Create payment method",
    middleware: ["authenticate", "isMerchantOrAdmin"],
    handler: (paymentMethodController as any).createPaymentMethod
});

(routeBuilder as any).addRoute({
    method: "PUT",
    path: "/:id",
    description: "Update payment method",
    middleware: ["authenticate", "isResourceOwner"],
    validation: [
        param("id").isUUID().withMessage("Invalid payment method ID format")
    ],
    handler: (paymentMethodController as any).updatePaymentMethod
});

(routeBuilder as any).addRoute({
    method: "DELETE",
    path: "/:id",
    description: "Delete payment method",
    middleware: ["authenticate", "isResourceOwner"],
    validation: [
        param("id").isUUID().withMessage("Invalid payment method ID format")
    ],
    handler: (paymentMethodController as any).deletePaymentMethod
});

// Export the router
export default (routeBuilder as any).build();