// jscpd:ignore-file
import { Router } from "express";
import { body } from "express-validator";
import blockchainVerificationController from "../controllers/blockchain-verification.controller";
import { validate } from "../middlewares/validation.middleware";
import { authenticate } from "../middlewares/auth.middleware";
import { PrismaClient } from "@prisma/client";
import { body } from "express-validator";
import { validate } from "../middlewares/validation.middleware";
import { authenticate } from "../middlewares/auth.middleware";
import { PrismaClient } from "@prisma/client";

const router: unknown =Router();
const prisma = new PrismaClient();

/**
 * @route POST /api/verification/blockchain
 * @desc Verify a blockchain transaction
 * @access Public
 */
router.post(
    "/blockchain",
    validate([
        body("network").notEmpty().withMessage("Network is required"),
        body("txHash").notEmpty().withMessage("Transaction hash is required"),
        body("amount").isNumeric().withMessage("Amount must be a number")
    ]),
    blockchainVerificationController.verifyBlockchainTransaction
);

/**
 * @route POST /api/verification/binance/trc20
 * @desc Verify a Binance TRC20 transaction
 * @access Public
 */
router.post(
    "/binance/trc20",
    validate([
        body("txHash").notEmpty().withMessage("Transaction hash is required"),
        body("amount").isNumeric().withMessage("Amount must be a number"),
        body("currency").notEmpty().withMessage("Currency is required")
    ]),
    blockchainVerificationController.verifyBinanceTransaction
);

/**
 * @route GET /api/verification/payment/:id/status
 * @desc Get verification status for a payment
 * @access Private
 */
router.get(
    "/payment/:id/status",
    authenticate,
    async (req, res) => {
        try {
            const { id } = req.params;

            // Find the transaction
            const transaction = await prisma.transaction.findFirst({
                where: { paymentId: id
                },
                include: { verificationRecords: {
                        orderBy: { timestamp: "desc"
                        },
                        take: 1
                    }
                }
            });

            if (!transaction) {
                return res.status(404).json({
                    success: false,
                    message: "Transaction not found"
                });
            }

            // Map status
            let status;
            switch (transaction.status) {
            case "COMPLETED":
                status = "VERIFIED";
                break;
            case "FAILED":
                status = "FAILED";
                break;
            case "EXPIRED":
                status = "EXPIRED";
                break;
            default:
                status = "PENDING";
            }

            return res.status(200).json({
                success: transaction.status === "COMPLETED",
                status,
                paymentId: id,
                merchantId: transaction.merchantId,
                verificationMethod: transaction.verificationMethod,
                verifiedAt: transaction.completedAt?.toISOString(),
                message: transaction.verificationRecords[0]?.data?.message,
                transactionDetails: transaction.verificationData
            });
        } catch (error) {
            console.error("Error getting verification status:", error);
            return res.status(500).json({
                success: false,
                message: "Internal server error",
                error: error instanceof Error ? (error as Error).message : "Unknown error"
            });
        }
    }
);

export default router;