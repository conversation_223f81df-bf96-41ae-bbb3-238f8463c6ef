// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from "./base.controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/errors/AppError';
import { AlertAnalyticsService } from '../services/alert-analytics.service';
import { BaseController } from "./base.controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/errors/AppError';
import { AlertAnalyticsService } from '../services/alert-analytics.service';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * AlertAnalyticsController
 */
export class AlertAnalyticsController extends BaseController {
  constructor() {
    super();
  }

  /**
   * Get alert count by status
   */
  getAlertCountByStatus = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Check authorization
        const { userRole, userId, merchantId } = this.checkAuthorization(req);

        // Parse date range
        const { startDate, endDate } = this.parseDateRange(
            req.query.startDate as string,
            req.query.endDate as string
        );

        // Determine target merchant ID
        const targetMerchantId: unknown = this.determineTargetMerchantId(
            userRole,
            merchantId,
            req.query.merchantId as string
        );

        // Create analytics service
        const analyticsService: unknown = new AlertAnalyticsService();

        // Get alert count by status
        const data: unknown = await analyticsService.getAlertCountByStatus(
            startDate,
            endDate,
            targetMerchantId
        );

        // Return data
        return res.status(200).json({
            success: true,
            data
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get alert count by status",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get alert count by severity
   */
  getAlertCountBySeverity = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Check authorization
        const { userRole, userId, merchantId } = this.checkAuthorization(req);

        // Parse date range
        const { startDate, endDate } = this.parseDateRange(
            req.query.startDate as string,
            req.query.endDate as string
        );

        // Determine target merchant ID
        const targetMerchantId: unknown = this.determineTargetMerchantId(
            userRole,
            merchantId,
            req.query.merchantId as string
        );

        // Create analytics service
        const analyticsService: unknown = new AlertAnalyticsService();

        // Get alert count by severity
        const data: unknown = await analyticsService.getAlertCountBySeverity(
            startDate,
            endDate,
            targetMerchantId
        );

        // Return data
        return res.status(200).json({
            success: true,
            data
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get alert count by severity",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get alert count by type
   */
  getAlertCountByType = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Check authorization
        const { userRole, userId, merchantId } = this.checkAuthorization(req);

        // Parse date range
        const { startDate, endDate } = this.parseDateRange(
            req.query.startDate as string,
            req.query.endDate as string
        );

        // Determine target merchant ID
        const targetMerchantId: unknown = this.determineTargetMerchantId(
            userRole,
            merchantId,
            req.query.merchantId as string
        );

        // Create analytics service
        const analyticsService: unknown = new AlertAnalyticsService();

        // Get alert count by type
        const data: unknown = await analyticsService.getAlertCountByType(
            startDate,
            endDate,
            targetMerchantId
        );

        // Return data
        return res.status(200).json({
            success: true,
            data
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get alert count by type",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get alert count by day
   */
  getAlertCountByDay = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Check authorization
        const { userRole, userId, merchantId } = this.checkAuthorization(req);

        // Parse date range
        const { startDate, endDate } = this.parseDateRange(
            req.query.startDate as string,
            req.query.endDate as string
        );

        // Determine target merchant ID
        const targetMerchantId: unknown = this.determineTargetMerchantId(
            userRole,
            merchantId,
            req.query.merchantId as string
        );

        // Create analytics service
        const analyticsService: unknown = new AlertAnalyticsService();

        // Get alert count by day
        const data: unknown = await analyticsService.getAlertCountByDay(
            startDate,
            endDate,
            targetMerchantId
        );

        // Return data
        return res.status(200).json({
            success: true,
            data
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get alert count by day",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get alert count by hour
   */
  getAlertCountByHour = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Check authorization
        const { userRole, userId, merchantId } = this.checkAuthorization(req);

        // Get query parameters
        const dateStr: unknown = req.query.date as string;

        // Validate date parameter
        if (!dateStr) {
            throw new AppError({
            message: "Date is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
        }

        // Parse date
        const date: Date = new Date(dateStr);

        // Validate date
        if (isNaN(date.getTime())) {
            throw new AppError({
            message: "Invalid date format",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        // Determine target merchant ID
        const targetMerchantId: unknown = this.determineTargetMerchantId(
            userRole,
            merchantId,
            req.query.merchantId as string
        );

        // Create analytics service
        const analyticsService: unknown = new AlertAnalyticsService();

        // Get alert count by hour
        const data: unknown = await analyticsService.getAlertCountByHour(
            date,
            targetMerchantId
        );

        // Return data
        return res.status(200).json({
            success: true,
            data
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get alert count by hour",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get top merchants by alert count
   */
  getTopMerchantsByAlertCount = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role and ID
        const userRole: unknown = req.user?.role;

        // Only admins can access this endpoint
        this.checkAdminRole(userRole);

        // Parse date range
        const { startDate, endDate } = this.parseDateRange(
            req.query.startDate as string,
            req.query.endDate as string
        );

        // Get limit parameter
        const limitStr: unknown = req.query.limit as string;
        const limit: unknown =limitStr ? parseInt(limitStr) : 10;

        // Validate limit
        if (isNaN(limit) || limit <= 0) {
            throw new AppError({
            message: "Invalid limit",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        // Create analytics service
        const analyticsService: unknown = new AlertAnalyticsService();

        // Get top merchants by alert count
        const data: unknown = await analyticsService.getTopMerchantsByAlertCount(
            startDate,
            endDate,
            limit
        );

        // Return data
        return res.status(200).json({
            success: true,
            data
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get top merchants by alert count",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get alert resolution time statistics
   */
  getAlertResolutionTimeStats = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Check authorization
        const { userRole, userId, merchantId } = this.checkAuthorization(req);

        // Parse date range
        const { startDate, endDate } = this.parseDateRange(
            req.query.startDate as string,
            req.query.endDate as string
        );

        // Determine target merchant ID
        const targetMerchantId: unknown = this.determineTargetMerchantId(
            userRole,
            merchantId,
            req.query.merchantId as string
        );

        // Create analytics service
        const analyticsService: unknown = new AlertAnalyticsService();

        // Get alert resolution time statistics
        const data: unknown = await analyticsService.getAlertResolutionTimeStats(
            startDate,
            endDate,
            targetMerchantId
        );

        // Return data
        return res.status(200).json({
            success: true,
            data
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get alert resolution time statistics",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get alert trends
   */
  getAlertTrends = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Check authorization
        const { userRole, userId, merchantId } = this.checkAuthorization(req);

        // Get query parameters
        const daysStr: unknown = req.query.days as string;

        // Parse days
        const days: unknown =daysStr ? parseInt(daysStr) : 30;

        // Validate days
        if (isNaN(days) || days <= 0) {
            throw new AppError({
            message: "Invalid days parameter",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        // Determine target merchant ID
        const targetMerchantId: unknown = this.determineTargetMerchantId(
            userRole,
            merchantId,
            req.query.merchantId as string
        );

        // Create analytics service
        const analyticsService: unknown = new AlertAnalyticsService();

        // Get alert trends
        const data: unknown = await analyticsService.getAlertTrends(
            days,
            targetMerchantId
        );

        // Return data
        return res.status(200).json({
            success: true,
            data
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get alert trends",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Helper method to check admin role
   */
  private checkAdminRole(userRole: string | undefined): void {
    if (!userRole || userRole !== "ADMIN") {
        throw new AppError({
            message: "Unauthorized. Admin role required.",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
    }
  }

  /**
   * Helper method to parse date range
   */
  private parseDateRange(startDateStr: string, endDateStr: string): { startDate: Date, endDate: Date } {
    if (!startDateStr || !endDateStr) {
        throw new AppError({
            message: "Start date and end date are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    const startDate: Date = new Date(startDateStr);
    const endDate: Date = new Date(endDateStr);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        throw new AppError({
            message: "Invalid date format",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }

    return { startDate, endDate };
  }

  /**
   * Helper method to determine target merchant ID
   */
  private determineTargetMerchantId(userRole: string, merchantId: string | undefined, requestedMerchantId: string): string | undefined {
    // Admin can request data for any merchant
    if (userRole === "ADMIN" && requestedMerchantId) {
        return requestedMerchantId;
    }

    // Non-admin users can only access their own merchant data
    if (userRole !== "ADMIN" && (!merchantId || (requestedMerchantId && requestedMerchantId !== merchantId))) {
        throw new AppError({
            message: "Unauthorized. You can only access your own merchant data.",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
    }

    return merchantId;
  }

  /**
   * Helper method to check authorization
   */
  private checkAuthorization(req: Request): { userRole: string, userId: string, merchantId: string | undefined } {
    const userRole: unknown = req.user?.role;
    const userId: unknown = req.user?.id;
    const merchantId: unknown = req.user?.merchantId;

    if (!userRole || !userId) {
        throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
    }

    return { userRole, userId, merchantId };
  }
}

export default new AlertAnalyticsController();
