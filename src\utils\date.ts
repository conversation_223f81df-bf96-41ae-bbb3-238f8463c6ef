// jscpd:ignore-file
/**
 * Date utility functions
 * Direct re-exports from shared DateUtils to eliminate duplication
 */

import { DateUtils, DateFormatterUtils } from '../utils';

// Format functions with specific formats
export const formatDate: unknown = (date: Date): string => DateUtils.format(date, 'yyyy-MM-dd');
export const formatDateTime: unknown = (date: Date): string => DateUtils.format(date, 'yyyy-MM-dd HH:mm:ss');

// Direct re-exports with specific unit types
export const addDays: unknown = (date: Date, days: number): Date => DateUtils.add(date, days, 'day');
export const addMonths: unknown = (date: Date, months: number): Date => DateUtils.add(date, months, 'month');
export const addYears: unknown = (date: Date, years: number): Date => DateUtils.add(date, years, 'year');
export const diffInDays: unknown = (date1: Date, date2: Date): number => Math.abs(DateUtils.diff(date1, date2, 'day'));
export const startOfDay: unknown = (date: Date): Date => DateUtils.startOf(date, 'day');
export const endOfDay: unknown = (date: Date): Date => DateUtils.endOf(date, 'day');
export const startOfMonth: unknown = (date: Date): Date => DateUtils.startOf(date, 'month');
export const endOfMonth: unknown = (date: Date): Date => DateUtils.endOf(date, 'month');

// Utility functions
export const isValidDate: unknown = (date: Date): boolean => !isNaN(date.getTime());
export const parseDate: unknown = (dateStr: string): Date | null => {
  const date = DateUtils.toDate(dateStr);
  return isValidDate(date) ? date : null;
};

// Use DateFormatterUtils for relative formatting
export const formatRelative: unknown = (date: Date): string => DateFormatterUtils.formatRelativeTime(date);
