// Admin-related types and interfaces
import { AdminUserResponse } from '../types/admin';
import { CreateAdminUserRequest } from '../types/admin';
import { UpdateAdminUserRequest } from '../types/admin';
import { AdminUserFilters } from '../types/admin';
import { DashboardDataResponse } from '../types/admin';
import { SystemHealthStatus } from '../types/admin';

export interface Admin {
  id: string;
  userId: string;
  department: string;
  permissions: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AdminUser {
  id: string;
  name: string;
  email: string;
  status: AdminUserStatus;
  role: AdminRole;
  permissions: string[];
  department?: string;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export type AdminUserStatus = 'active' | 'inactive' | 'suspended' | 'pending';

export interface AdminRole {
  id: string;
  name: string;
  description?: string;
  permissions: AdminPermission[];
  isSystem: boolean;
}

export interface AdminPermission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
}

export interface CreateAdminUserRequest {
  name: string;
  email: string;
  password: string;
  roleId?: string;
  department?: string;
  permissions?: string[];
}

export interface UpdateAdminUserRequest {
  name?: string;
  email?: string;
  roleId?: string;
  isActive?: boolean;
  department?: string;
  permissions?: string[];
}

export interface AdminUserResponse {
  id: string;
  name: string;
  email: string;
  status: AdminUserStatus;
  role?: AdminRole;
  department?: string;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  user: {
    id: string;
    email: string;
    isActive: boolean;
    roles: AdminRole[];
  };
}

export interface AdminUserFilters {
  status?: AdminUserStatus;
  roleId?: string;
  department?: string;
  search?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface CreateRoleRequest {
  name: string;
  description?: string;
  permissions: string[];
}

export interface UpdateRoleRequest {
  name?: string;
  description?: string;
  permissions?: string[];
}

export interface RoleResponse {
  id: string;
  name: string;
  description?: string;
  permissions: AdminPermission[];
  userCount: number;
  isSystem: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface RoleFilters {
  search?: string;
  isSystem?: boolean;
}

export interface CreatePermissionRequest {
  name: string;
  resource: string;
  action: string;
  description?: string;
}

export interface UpdatePermissionRequest {
  name?: string;
  resource?: string;
  action?: string;
  description?: string;
}

export interface PermissionResponse {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
  roleCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface PermissionFilters {
  resource?: string;
  action?: string;
  search?: string;
}

export interface DashboardDataResponse {
  merchantCount: number;
  transactionCount: number;
  activePaymentMethodsCount: number;
  totalRevenue: number;
  recentTransactions: any[];
  recentMerchants: any[];
}

export interface DashboardStatistics {
  totalUsers: number;
  activeUsers: number;
  totalMerchants: number;
  activeMerchants: number;
  totalTransactions: number;
  successfulTransactions: number;
  totalRevenue: number;
  monthlyRevenue: number;
  recentActivity: AdminActivity[];
}

export interface AdminActivity {
  id: string;
  adminId: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: Record<string, unknown>;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
}

export interface SystemHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  services: {
    database: ServiceStatus;
    cache: ServiceStatus;
    external: ServiceStatus;
    queue?: ServiceStatus;
  };
  metrics: {
    uptime: number;
    memoryUsage: number;
    cpuUsage: number;
    diskUsage?: number;
  };
}

export type ServiceStatus = 'connected' | 'disconnected' | 'error';

export interface AdminAuditLog {
  id: string;
  adminId: string;
  action: AdminAction;
  resource: string;
  resourceId?: string;
  oldValues?: Record<string, unknown>;
  newValues?: Record<string, unknown>;
  ipAddress: string;
  userAgent: string;
  success: boolean;
  errorMessage?: string;
  createdAt: Date;
}

export type AdminAction = 
  | 'CREATE'
  | 'READ'
  | 'UPDATE'
  | 'DELETE'
  | 'LOGIN'
  | 'LOGOUT'
  | 'EXPORT'
  | 'IMPORT'
  | 'APPROVE'
  | 'REJECT'
  | 'SUSPEND'
  | 'ACTIVATE';

export interface AdminSettings {
  id: string;
  key: string;
  value: unknown;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description?: string;
  isSystem: boolean;
  updatedBy: string;
  updatedAt: Date;
}

export interface UpdateAdminSettingsRequest {
  settings: Array<{
    key: string;
    value: unknown;
  }>;
}

export interface AdminNotification {
  id: string;
  adminId: string;
  type: AdminNotificationType;
  title: string;
  message: string;
  data?: Record<string, unknown>;
  isRead: boolean;
  createdAt: Date;
  readAt?: Date;
}

export type AdminNotificationType = 
  | 'SYSTEM'
  | 'SECURITY'
  | 'USER'
  | 'PAYMENT'
  | 'MERCHANT'
  | 'REPORT';

export interface AdminReport {
  id: string;
  name: string;
  type: AdminReportType;
  parameters: Record<string, unknown>;
  status: ReportStatus;
  fileUrl?: string;
  generatedBy: string;
  generatedAt?: Date;
  createdAt: Date;
}

export type AdminReportType = 
  | 'USER_ACTIVITY'
  | 'PAYMENT_SUMMARY'
  | 'MERCHANT_PERFORMANCE'
  | 'SYSTEM_HEALTH'
  | 'AUDIT_LOG'
  | 'FINANCIAL_SUMMARY';

export type ReportStatus = 
  | 'PENDING'
  | 'GENERATING'
  | 'COMPLETED'
  | 'FAILED';

export interface GenerateReportRequest {
  type: AdminReportType;
  parameters: Record<string, unknown>;
  format?: 'PDF' | 'CSV' | 'XLSX';
}

export interface AdminSession {
  id: string;
  adminId: string;
  token: string;
  expiresAt: Date;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
  lastActivityAt: Date;
  createdAt: Date;
}

export interface AdminLoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface AdminLoginResponse {
  admin: AdminUserResponse;
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  };
  session: {
    id: string;
    expiresAt: Date;
  };
}

export interface AdminAuthContext {
  admin: AdminUserResponse;
  permissions: string[];
  session: AdminSession;
}

export interface AdminSecurityConfig {
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
    maxAge: number; // days
  };
  sessionConfig: {
    timeout: number; // minutes
    maxConcurrentSessions: number;
    requireTwoFactor: boolean;
  };
  ipRestrictions: {
    enabled: boolean;
    allowedIps: string[];
    blockedIps: string[];
  };
}

export interface AdminTwoFactorAuth {
  id: string;
  adminId: string;
  method: '2FA_APP' | 'SMS' | 'EMAIL';
  secret: string;
  isEnabled: boolean;
  backupCodes: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface AdminBackup {
  id: string;
  type: 'FULL' | 'INCREMENTAL' | 'DIFFERENTIAL';
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED';
  fileSize?: number;
  filePath?: string;
  createdBy: string;
  createdAt: Date;
  completedAt?: Date;
}

export interface AdminMaintenanceMode {
  enabled: boolean;
  message?: string;
  allowedIps: string[];
  enabledBy: string;
  enabledAt?: Date;
}
