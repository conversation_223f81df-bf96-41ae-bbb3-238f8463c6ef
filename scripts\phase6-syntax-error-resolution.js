#!/usr/bin/env node

/**
 * Phase 6: Syntax Error Resolution Script
 * Targets specific TypeScript syntax errors (TS1005, TS1003, TS1128, etc.)
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎯 PHASE 6: SYNTAX ERROR RESOLUTION');
console.log('===================================');

// Syntax error resolution patterns targeting specific TS error codes
const syntaxErrorPatterns = {
    // TS1005: ',' expected or ':' expected - Fix malformed object literals and function parameters
    
    // Fix malformed object property definitions
    'anomalyThreshold: (2).5': 'anomalyThreshold: 2.5',
    'minConfidence: (0).7': 'minConfidence: 0.7',
    'minConfidence: (0).8': 'minConfidence: 0.8',
    'minConfidence: (0).9': 'minConfidence: 0.9',
    'threshold: (1).5': 'threshold: 1.5',
    'threshold: (2).0': 'threshold: 2.0',
    'threshold: (2).5': 'threshold: 2.5',
    'threshold: (3).0': 'threshold: 3.0',
    'multiplier: (1).5': 'multiplier: 1.5',
    'multiplier: (2).0': 'multiplier: 2.0',
    'factor: (0).5': 'factor: 0.5',
    'factor: (1).0': 'factor: 1.0',
    'factor: (1).5': 'factor: 1.5',
    'rate: (0).1': 'rate: 0.1',
    'rate: (0).2': 'rate: 0.2',
    'rate: (0).5': 'rate: 0.5',
    'percentage: (0).1': 'percentage: 0.1',
    'percentage: (0).5': 'percentage: 0.5',
    'percentage: (1).0': 'percentage: 1.0',
    'score: (0).5': 'score: 0.5',
    'score: (1).0': 'score: 1.0',
    'weight: (0).5': 'weight: 0.5',
    'weight: (1).0': 'weight: 1.0',
    'limit: (1).0': 'limit: 1.0',
    'limit: (2).0': 'limit: 2.0',
    'limit: (5).0': 'limit: 5.0',
    'limit: (10).0': 'limit: 10.0',
    
    // Fix malformed array access and method calls
    'select: { id: true': 'select: { id: true }',
    'select: { name: true': 'select: { name: true }',
    'select: { email: true': 'select: { email: true }',
    'select: { status: true': 'select: { status: true }',
    'select: { createdAt: true': 'select: { createdAt: true }',
    'select: { updatedAt: true': 'select: { updatedAt: true }',
    
    // Fix malformed where clauses
    'where: { gte: parsedStartDate,': 'where: { gte: parsedStartDate,',
    'lte: parsedEndDate': 'lte: parsedEndDate',
    'where: { merchantId,': 'where: { merchantId,',
    'createdAt: { gte: parsedStartDate,': 'createdAt: { gte: parsedStartDate,',
    
    // Fix malformed function calls and method chaining
    '}).then(transactions => transactions.map(tx => tx.id))': '}).then(transactions => transactions.map(tx => tx.id))',
    
    // TS1003: Identifier expected - Fix malformed identifiers and property access
    
    // Fix remaining malformed Prisma calls that weren't caught before
    'await this.prisma.(': 'await this.prisma.',
    'this.prisma.(': 'this.prisma.',
    
    // Fix malformed property access in complex expressions
    '((existingRule).conditions as any)?.type': '(existingRule.conditions as any)?.type',
    '((existingRule).conditions as any)?.severity': '(existingRule.conditions as any)?.severity',
    '((existingRule).conditions as any)?.timeWindow': '(existingRule.conditions as any)?.timeWindow',
    '((existingRule).conditions as any)?.threshold': '(existingRule.conditions as any)?.threshold',
    '((existingRule).conditions as any)?.groupBy': '(existingRule.conditions as any)?.groupBy',
    
    '((existingRule).actions as any)': '(existingRule.actions as any)',
    '((existingRule).settings as any)': '(existingRule.settings as any)',
    '((existingRule).metadata as any)': '(existingRule.metadata as any)',
    
    // Fix malformed destructuring assignments
    'const { gte: parsedStartDate,': 'const { gte: parsedStartDate,',
    'lte: parsedEndDate }': 'lte: parsedEndDate }',
    
    // TS1128: Declaration or statement expected - Fix malformed declarations
    
    // Fix malformed import statements
    'import {': 'import {',
    'import type {': 'import type {',
    'import * as': 'import * as',
    'import {,': 'import {',
    'import { ,': 'import {',
    
    // Fix malformed export statements
    'export {': 'export {',
    'export type {': 'export type {',
    'export * from': 'export * from',
    'export default': 'export default',
    'export const': 'export const',
    'export interface': 'export interface',
    'export class': 'export class',
    'export function': 'export function',
    
    // Fix malformed class declarations
    'class {': 'class ClassName {',
    'interface {': 'interface InterfaceName {',
    'type {': 'type TypeName = {',
    'enum {': 'enum EnumName {',
    
    // Fix malformed function declarations
    'function (': 'function functionName(',
    'async function (': 'async function functionName(',
    'const function': 'const functionName = function',
    'const async': 'const functionName = async',
    
    // TS1434: Unexpected keyword or identifier - Fix unexpected keywords
    
    // Fix malformed async/await patterns
    'async await': 'await',
    'await async': 'await',
    'return await async': 'return await',
    'const await': 'const result = await',
    'let await': 'let result = await',
    'var await': 'var result = await',
    
    // Fix malformed try/catch patterns
    'try {': 'try {',
    'catch(error) {': 'catch (error) {',
    'catch (error) {': 'catch (error) {',
    'finally {': 'finally {',
    
    // Fix malformed if/else patterns
    'if (': 'if (',
    'else if (': 'else if (',
    'else {': 'else {',
    
    // Fix malformed for/while patterns
    'for (': 'for (',
    'while (': 'while (',
    'do {': 'do {',
    
    // Fix malformed switch patterns
    'switch (': 'switch (',
    'case ': 'case ',
    'default:': 'default:',
    'break;': 'break;',
    
    // TS1109: Expression expected - Fix missing expressions
    
    // Fix malformed conditional expressions
    '? :': '? undefined :',
    '? : undefined': '? undefined : undefined',
    '? null :': '? null :',
    '? : null': '? undefined : null',
    
    // Fix malformed array expressions
    '[,': '[',
    '[, ]': '[]',
    '[,,]': '[]',
    
    // Fix malformed object expressions
    '{,': '{',
    '{, }': '{}',
    '{,,}': '{}',
    
    // Fix malformed function call expressions
    '(,': '(',
    '(, )': '()',
    '(,,)': '()',
    
    // Additional common syntax fixes
    
    // Fix malformed template literals
    '`${': '`${',
    '}`': '}`',
    
    // Fix malformed regular expressions
    '/^': '/^',
    '$/': '$/',
    '/g': '/g',
    '/i': '/i',
    '/m': '/m',
    
    // Fix malformed JSON operations
    'JSON.parse(': 'JSON.parse(',
    'JSON.stringify(': 'JSON.stringify(',
    
    // Fix malformed console operations
    'console.log(': 'console.log(',
    'console.error(': 'console.error(',
    'console.warn(': 'console.warn(',
    'console.info(': 'console.info(',
    'console.debug(': 'console.debug(',
    
    // Fix malformed Math operations
    'Math.floor(': 'Math.floor(',
    'Math.ceil(': 'Math.ceil(',
    'Math.round(': 'Math.round(',
    'Math.max(': 'Math.max(',
    'Math.min(': 'Math.min(',
    'Math.abs(': 'Math.abs(',
    'Math.random()': 'Math.random()',
    
    // Fix malformed Date operations
    'new Date(': 'new Date(',
    'Date.now()': 'Date.now()',
    
    // Fix malformed Promise operations
    'new Promise(': 'new Promise(',
    'Promise.resolve(': 'Promise.resolve(',
    'Promise.reject(': 'Promise.reject(',
    'Promise.all(': 'Promise.all(',
    'Promise.race(': 'Promise.race(',
    
    // Fix malformed Error operations
    'new Error(': 'new Error(',
    'throw new': 'throw new',
    'throw error': 'throw error',
    
    // Fix malformed type assertions
    ' as ': ' as ',
    ' as any': ' as any',
    ' as string': ' as string',
    ' as number': ' as number',
    ' as boolean': ' as boolean',
    ' as object': ' as object',
    ' as unknown': ' as unknown',
    
    // Fix malformed type guards
    'typeof ': 'typeof ',
    'instanceof ': 'instanceof ',
    
    // Fix malformed null checks
    ' == null': ' == null',
    ' === null': ' === null',
    ' != null': ' != null',
    ' !== null': ' !== null',
    ' == undefined': ' == undefined',
    ' === undefined': ' === undefined',
    ' != undefined': ' != undefined',
    ' !== undefined': ' !== undefined',
    
    // Fix malformed boolean operations
    ' && ': ' && ',
    ' || ': ' || ',
    ' ! ': ' !',
    ' !! ': ' !!',
    
    // Fix malformed comparison operations
    ' === ': ' === ',
    ' !== ': ' !== ',
    ' == ': ' == ',
    ' != ': ' != ',
    ' < ': ' < ',
    ' > ': ' > ',
    ' <= ': ' <= ',
    ' >= ': ' >= ',
    
    // Fix malformed arithmetic operations
    ' + ': ' + ',
    ' - ': ' - ',
    ' * ': ' * ',
    ' / ': ' / ',
    ' % ': ' % ',
    ' ** ': ' ** ',
    
    // Fix malformed assignment operations
    ' = ': ' = ',
    ' += ': ' += ',
    ' -= ': ' -= ',
    ' *= ': ' *= ',
    ' /= ': ' /= ',
    ' %= ': ' %= ',
    
    // Fix malformed increment/decrement operations
    '++': '++',
    '--': '--',
    
    // Fix malformed ternary operations
    ' ? ': ' ? ',
    ' : ': ' : ',
    
    // Fix malformed spread operations
    '...': '...',
    
    // Fix malformed destructuring operations
    '{ ': '{ ',
    ' }': ' }',
    '[ ': '[ ',
    ' ]': ' ]',
    
    // Fix malformed arrow functions
    ' => ': ' => ',
    '=>': ' => ',
    
    // Fix malformed function expressions
    'function(': 'function (',
    'function (': 'function (',
    
    // Fix malformed method definitions
    'get ': 'get ',
    'set ': 'set ',
    'static ': 'static ',
    'async ': 'async ',
    'private ': 'private ',
    'protected ': 'protected ',
    'public ': 'public ',
    'readonly ': 'readonly ',
    
    // Fix malformed decorators
    '@': '@',
    
    // Fix malformed generics
    '<': '<',
    '>': '>',
    
    // Fix malformed union types
    ' | ': ' | ',
    
    // Fix malformed intersection types
    ' & ': ' & ',
    
    // Fix malformed optional chaining
    '?.': '?.',
    
    // Fix malformed nullish coalescing
    ' ?? ': ' ?? ',
};

function findAllTypeScriptFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'coverage'].includes(item)) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

function getErrorCount() {
    try {
        const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { encoding: 'utf8' });
        const errorMatches = output.match(/error TS/g) || [];
        return errorMatches.length;
    } catch (error) {
        const errorMatches = error.stdout.match(/error TS/g) || [];
        return errorMatches.length;
    }
}

function getSpecificErrorCount(errorCode) {
    try {
        const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { encoding: 'utf8' });
        const errorMatches = output.match(new RegExp(`error ${errorCode}`, 'g')) || [];
        return errorMatches.length;
    } catch (error) {
        const errorMatches = error.stdout.match(new RegExp(`error ${errorCode}`, 'g')) || [];
        return errorMatches.length;
    }
}

function processFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let modifiedContent = content;
        let fixCount = 0;
        
        // Apply all syntax error patterns
        for (const [oldPattern, newPattern] of Object.entries(syntaxErrorPatterns)) {
            const originalContent = modifiedContent;
            modifiedContent = modifiedContent.split(oldPattern).join(newPattern);
            if (modifiedContent !== originalContent) {
                fixCount++;
            }
        }
        
        if (fixCount > 0) {
            fs.writeFileSync(filePath, modifiedContent, 'utf8');
            return { filePath, fixCount };
        }
        
        return null;
    } catch (error) {
        return { filePath, error: error.message };
    }
}

async function main() {
    console.log('🔍 Scanning for TypeScript files...');
    
    const files = findAllTypeScriptFiles('./src');
    console.log(`📁 Found ${files.length} TypeScript files`);
    
    console.log('📊 Getting initial error counts...');
    const initialErrors = getErrorCount();
    const initialTS1005 = getSpecificErrorCount('TS1005');
    const initialTS1003 = getSpecificErrorCount('TS1003');
    const initialTS1128 = getSpecificErrorCount('TS1128');
    const initialTS1434 = getSpecificErrorCount('TS1434');
    const initialTS1109 = getSpecificErrorCount('TS1109');
    
    console.log(`🚨 Initial TypeScript errors: ${initialErrors}`);
    console.log(`   TS1005 (comma/colon expected): ${initialTS1005}`);
    console.log(`   TS1003 (identifier expected): ${initialTS1003}`);
    console.log(`   TS1128 (declaration expected): ${initialTS1128}`);
    console.log(`   TS1434 (unexpected keyword): ${initialTS1434}`);
    console.log(`   TS1109 (expression expected): ${initialTS1109}`);
    
    console.log('🚀 Starting syntax error resolution...');
    const startTime = Date.now();
    
    const results = [];
    let totalFixedIssues = 0;
    
    for (const file of files) {
        const result = processFile(file);
        if (result) {
            results.push(result);
            if (result.fixCount) {
                totalFixedIssues += result.fixCount;
            }
        }
    }
    
    console.log('📊 Getting final error counts...');
    const finalErrors = getErrorCount();
    const finalTS1005 = getSpecificErrorCount('TS1005');
    const finalTS1003 = getSpecificErrorCount('TS1003');
    const finalTS1128 = getSpecificErrorCount('TS1128');
    const finalTS1434 = getSpecificErrorCount('TS1434');
    const finalTS1109 = getSpecificErrorCount('TS1109');
    
    const totalErrorsFixed = initialErrors - finalErrors;
    const ts1005Fixed = initialTS1005 - finalTS1005;
    const ts1003Fixed = initialTS1003 - finalTS1003;
    const ts1128Fixed = initialTS1128 - finalTS1128;
    const ts1434Fixed = initialTS1434 - finalTS1434;
    const ts1109Fixed = initialTS1109 - finalTS1109;
    
    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;
    
    console.log('\n🎯 SYNTAX ERROR RESOLUTION COMPLETE!');
    console.log('====================================');
    console.log(`⏱️  Processing time: ${processingTime.toFixed(2)} seconds`);
    console.log(`📁 Files processed: ${results.filter(r => !r.error).length}`);
    console.log(`❌ Files with errors: ${results.filter(r => r.error).length}`);
    console.log(`🔧 Total fixes applied: ${totalFixedIssues}`);
    console.log(`🚨 TypeScript errors before: ${initialErrors}`);
    console.log(`✅ TypeScript errors after: ${finalErrors}`);
    console.log(`🎯 Total errors fixed: ${totalErrorsFixed}`);
    console.log('\n📊 Specific Error Type Fixes:');
    console.log(`   TS1005 fixed: ${ts1005Fixed} (${initialTS1005} → ${finalTS1005})`);
    console.log(`   TS1003 fixed: ${ts1003Fixed} (${initialTS1003} → ${finalTS1003})`);
    console.log(`   TS1128 fixed: ${ts1128Fixed} (${initialTS1128} → ${finalTS1128})`);
    console.log(`   TS1434 fixed: ${ts1434Fixed} (${initialTS1434} → ${finalTS1434})`);
    console.log(`   TS1109 fixed: ${ts1109Fixed} (${initialTS1109} → ${finalTS1109})`);
    
    if (totalErrorsFixed > 0) {
        console.log(`📈 Overall success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}%`);
        console.log('\n🎉 SUCCESS! Syntax error resolution completed successfully!');
        console.log('🏆 Your application now has significantly fewer syntax errors!');
    } else if (totalErrorsFixed === 0) {
        console.log('📈 Success rate: 0.0% (no net change)');
        console.log('\n✨ No errors were fixed, but syntax patterns were applied!');
    } else {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}% (negative - new errors introduced)`);
        console.log('\n⚠️  Some fixes may have introduced new errors. Consider reverting.');
    }
    
    const errorFiles = results.filter(r => r.error);
    if (errorFiles.length > 0) {
        console.log('\n❌ Files with processing errors:');
        errorFiles.forEach(({ filePath, error }) => {
            console.log(`   ${path.relative(process.cwd(), filePath)}: ${error}`);
        });
    }
    
    const successFiles = results.filter(r => r.fixCount && r.fixCount > 0);
    if (successFiles.length > 0) {
        console.log(`\n✅ Successfully applied syntax fixes to ${successFiles.length} files`);
        console.log('Top files with most syntax fixes:');
        successFiles
            .sort((a, b) => b.fixCount - a.fixCount)
            .slice(0, 10)
            .forEach(({ filePath, fixCount }) => {
                console.log(`   ${path.relative(process.cwd(), filePath)}: ${fixCount} fixes`);
            });
    }
    
    console.log('\n🎯 NEXT STEPS:');
    console.log('1. Review remaining syntax errors manually');
    console.log('2. Focus on files with the highest error counts');
    console.log('3. Create specific fixes for complex syntax patterns');
    console.log('4. Consider using AST-based transformations for complex cases');
    console.log('5. Run tests to ensure functionality is preserved');
}

main().catch(console.error);
