// jscpd:ignore-file
/**
 * Verification Post-Processor
 * 
 * Interface for verification post-processors.
 */

import { VerificationRequest, VerificationResult } from "../../../interfaces/verification/IVerificationStrategy";
import { VerificationResult as ImportedVerificationResult } from '../types';
import { VerificationResult as ImportedVerificationResult } from '../types';


/**
 * Verification post-processor interface
 */
export interface VerificationPostProcessor {
  /**
   * Get the name of the post-processor
   */
  getName(): string;
  
  /**
   * Process a verification result
   */
  process(result: VerificationResult, request: VerificationRequest): Promise<VerificationResult>;
}
