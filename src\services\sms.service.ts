// jscpd:ignore-file
import { logger as Importedlogger } from '../utils/logger';
import { config as Importedconfig } from '../config';
import prisma from '../lib/prisma';
import { AlertSeverity, AlertType } from './(alert).service';
import twilio from 'twilio';
import { <PERSON><PERSON>, Merchant, AlertSeverity, AlertType } from '../types';
import { config as Importedconfig } from '../config';
import { AlertSeverity, AlertType } from './(alert).service';
import { Alert, Merchant, AlertSeverity, AlertType } from '../types';

/**
 * SMS service
 */
export class SmsService {
  private twilioClient: (twilio).Twilio | null = null;

  /**
   * Create a new SMS service
   */
  constructor() {
    this.initializeTwilioClient();
  }

  /**
   * Initialize Twilio client
   */
  private initializeTwilioClient(): void {
    try {
      if ((config).sms?.accountSid && (config).sms?.authToken) {
        this.twilioClient = twilio((config).sms.accountSid, (config).sms.authToken);
        (logger).info('Twilio client initialized');
      } else {
        (logger).warn('Twilio configuration missing, SMS service disabled');
      }
    } catch(error) {
      (logger).error('Failed to initialize Twilio client', { error });
    }
  }

  /**
   * Send SMS
   * @param to Recipient phone number
   * @param message SMS message
   * @returns Success status
   */
  public async sendSms(to: string, message: string): Promise<boolean> {
    try {
      if (!this.twilioClient) {
        (logger).warn('Twilio client not initialized, skipping SMS');
        return false;
      }

      // Validate phone number
      if (!this.isValidPhoneNumber(to)) {
        (logger).warn('Invalid phone number', { to });
        return false;
      }

      // Check message length
      if ((message).length > 160) {
        (logger).warn('SMS message too long, truncating', { length: (message).length });
        message = (message).substring(0, 157) + '...';
      }

      // Send SMS
      const result = await this.twilioClient.(messages).create({
        body: message,
        from: (config).sms?.from ?? '',
        to,
      });

      (logger).info('SMS sent successfully', {
        sid: (result).sid,
        to,
        status: result.status,
      });

      return true;
    } catch(error) {
      (logger).error('Error sending SMS', { error, to });
      return false;
    }
  }

  /**
   * Send alert SMS
   * @param alert Alert data
   * @param phoneNumber Recipient phone number
   * @returns Success status
   */
  public async sendAlertSms(
    alert: {
      id: string;
      type: AlertType;
      severity: AlertSeverity;
      title: string;
      message: string;
      merchantId?: string;
    },
    phoneNumber: string
  ): Promise<boolean> {
    try {
      // Generate SMS message
      const message: string = this.generateAlertSmsMessage(alert);

      // Send SMS
      return await this.sendSms(phoneNumber, message);
    } catch(error) {
      (logger).error('Error sending alert SMS', { error, alertId: (alert).id });
      return false;
    }
  }

  /**
   * Generate alert SMS message
   * @param alert Alert data
   * @returns SMS message
   */
  private generateAlertSmsMessage(alert: {
    id: string;
    type: AlertType;
    severity: AlertSeverity;
    title: string;
    message: string;
  }): string {
    return `[${(alert).severity.toUpperCase()}] ${(alert).title}: ${(alert).message} (ID: ${(alert).id})`;
  }

  /**
   * Validate phone number
   * @param phoneNumber Phone number to validate
   * @returns Whether the phone number is valid
   */
  private isValidPhoneNumber(phoneNumber: string): boolean {
    // Basic validation: must start with + and contain only digits
    return /^\+\d{10,15}$/.test(phoneNumber);
  }

  /**
   * Get admin phone numbers
   * @returns Admin phone numbers
   */
  public async getAdminPhoneNumbers(): Promise<string[]> {
    try {
      // Get admin users from database
      const admins = await (prisma).user.findMany({
        where: { role: 'ADMIN' },
        select: { phoneNumber: true },
      });

      // Filter out empty phone numbers and validate
      return admins
        .map((admin) => (admin).phoneNumber ?? '')
        .filter((phone) => phone && this.isValidPhoneNumber(phone));
    } catch(error) {
      (logger).error('Error getting admin phone numbers', { error });
      return [];
    }
  }

  /**
   * Get merchant phone number
   * @param merchantId Merchant ID
   * @returns Merchant phone number
   */
  public async getMerchantPhoneNumber(merchantId: string): Promise<string | null> {
    try {
      // Get merchant from database
      const merchant = await (prisma).merchant.findUnique({
        where: { id: merchantId },
        select: { phoneNumber: true },
      });

      // Validate phone number
      if (merchant?.phoneNumber && this.isValidPhoneNumber((merchant).phoneNumber)) {
        return (merchant).phoneNumber;
      }

      return null;
    } catch(error) {
      (logger).error('Error getting merchant phone number', { error, merchantId });
      return null;
    }
  }

  /**
   * Test SMS service
   * @param phoneNumber Phone number to send test SMS to
   * @returns Success status
   */
  public async testSmsService(phoneNumber: string): Promise<boolean> {
    try {
      if (!this.twilioClient) {
        (logger).warn('Twilio client not initialized, skipping test');
        return false;
      }

      // Validate phone number
      if (!this.isValidPhoneNumber(phoneNumber)) {
        (logger).warn('Invalid phone number for test', { phoneNumber });
        return false;
      }

      // Send test SMS
      const message: string = `Amazing Pay SMS Test: This is a test message from the Amazing Pay SMS service. Time: ${new Date().toLocaleString()}`;

      return await this.sendSms(phoneNumber, message);
    } catch(error) {
      (logger).error('Error testing SMS service', { error });
      return false;
    }
  }
}
