// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { PaymentPageService } from "../services/payment-page.service";
import { AppError, asyncHandler } from '../middlewares/error.middleware';
import { prisma } from "../index";
import { PaymentPageService } from "../services/payment-page.service";
import { AppError, asyncHandler } from '../middlewares/error.middleware';
import { prisma } from "../index";

// Get all payment pages
export const getAllPaymentPages: unknown =asyncHandler(async (req: Request, res: Response) => {
    // In a real implementation, this would get all payment pages with pagination
    // For now, we'll get all payment pages for all merchants (admin only)
    const paymentPages: unknown =await prisma.paymentPage.findMany({
        orderBy: { createdAt: "desc" },
        include: { merchant: {
                select: { id: true,
                    name: true,
                    email: true,
                    businessName: true
                }
            }
        }
    });

    res.status(200).json(paymentPages);
});

// Get payment page by ID
export const getPaymentPageById: unknown =asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    const paymentPage: unknown =await PaymentPageService.getPaymentPageById(id);

    res.status(200).json(paymentPage);
});

// Get payment page by slug
export const getPaymentPageBySlug: unknown =asyncHandler(async (req: Request, res: Response) => {
    const { slug, merchantId } = req.params;

    const paymentPage: unknown =await PaymentPageService.getPaymentPageBySlug(slug, merchantId);

    res.status(200).json(paymentPage);
});

// Create a new payment page
export const createPaymentPage: unknown =asyncHandler(async (req: Request, res: Response) => {
    const {
        title,
        description,
        slug,
        amount,
        currency,
        merchantId,
        isActive,
        isTemplate,
        logoUrl,
        successUrl,
        cancelUrl,
        allowCustomAmount,
        collectCustomerInfo,
        redirectAutomatically,
        paymentMethodIds,
        expiryMinutes,
        metadata
    } = req.body;

    // Validate required fields
    if (!title || !slug || !amount || !currency || !merchantId || !paymentMethodIds || !Array.isArray(paymentMethodIds)) {
        throw new AppError({
            message: "Missing required fields",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Create payment page
    const paymentPage: unknown =await PaymentPageService.createPaymentPage({
        title,
        description,
        slug,
        amount,
        currency,
        merchantId,
        isActive,
        isTemplate,
        logoUrl,
        successUrl,
        cancelUrl,
        allowCustomAmount,
        collectCustomerInfo,
        redirectAutomatically,
        paymentMethodIds,
        expiryMinutes,
        metadata
    });

    res.status(201).json(paymentPage);
});

// Update payment page
export const updatePaymentPage: unknown =asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const {
        title,
        description,
        slug,
        amount,
        currency,
        isActive,
        isTemplate,
        logoUrl,
        successUrl,
        cancelUrl,
        allowCustomAmount,
        collectCustomerInfo,
        redirectAutomatically,
        paymentMethodIds,
        expiryMinutes,
        metadata
    } = req.body;

    // Update payment page
    const paymentPage: unknown =await PaymentPageService.updatePaymentPage(id, {
        title,
        description,
        slug,
        amount,
        currency,
        isActive,
        isTemplate,
        logoUrl,
        successUrl,
        cancelUrl,
        allowCustomAmount,
        collectCustomerInfo,
        redirectAutomatically,
        paymentMethodIds,
        expiryMinutes,
        metadata
    });

    res.status(200).json(paymentPage);
});

// Delete payment page
export const deletePaymentPage: unknown =asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    const result: unknown =await PaymentPageService.deletePaymentPage(id);

    res.status(200).json(result);
});

// Get merchant payment pages
export const getMerchantPaymentPages: unknown =asyncHandler(async (req: Request, res: Response) => {
    const { merchantId } = req.params;

    const paymentPages: unknown =await PaymentPageService.getMerchantPaymentPages(merchantId);

    res.status(200).json(paymentPages);
});

// Create transaction from payment page
export const createTransactionFromPaymentPage: unknown =asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const {
        amount,
        paymentMethodId,
        customerEmail,
        customerName,
        customerPhone,
        metadata
    } = req.body;

    // Validate required fields
    if (!paymentMethodId) {
        throw new AppError({
            message: "Payment method is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Create transaction
    const transaction: unknown =await PaymentPageService.createTransactionFromPaymentPage(id, {
        amount,
        paymentMethodId,
        customerEmail,
        customerName,
        customerPhone,
        metadata
    });

    res.status(201).json(transaction);
});
