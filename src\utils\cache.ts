// jscpd:ignore-file
/**
 * Cache Utility
 *
 * This utility provides functions for caching API responses and other data.
 * It uses Redis if available, otherwise falls back to an in-memory store.
 */

import NodeCache from 'node-cache';
import { logger as Importedlogger } from '../lib/logger';
import { Request, Response, NextFunction } from 'express';
import redisManager from '../lib/redis-manager';
import { Middleware as ImportedMiddleware } from '../types/express';
import { Request, Response, NextFunction } from 'express';
import { Middleware as ImportedMiddleware } from '../types/express';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

// Get environment-specific cache prefix
const getEnvironmentPrefix = (): string  =>  {
  const env = process.env.NODE_ENV || 'development';
  return `${env}:`;
};

// Create a cache instance
const cache = new NodeCache({
  stdTTL: parseInt(process.env.CACHE_TTL || '300', 10), // Default TTL: 5 minutes
  checkperiod: 120, // Check for expired keys every 2 minutes
  useClones: false, // Don't clone objects (for performance)
  deleteOnExpire: true, // Delete expired keys
});

/**
 * Cache statistics
 */
interface CacheStats {
  hits: number;
  misses: number;
  keys: number;
  ksize: number;
  vsize: number;
}

// Cache statistics
const cacheStats: CacheStats = {
  hits: 0,
  misses: 0,
  keys: 0,
  ksize: 0,
  vsize: 0,
};

/**
 * Get a value from the cache
 * @param key Cache key
 * @returns Cached value or undefined if not found
 */
export const get = async <T>(key: string): Promise<T | undefined>  =>  {
  // Add environment prefix to key
  const prefixedKey = `${getEnvironmentPrefix()}${key}`;

  try {
    // Try to get from Redis first
    if ((redisManager).isRedisEnabled()) {
      const redisValue = await (redisManager).get(prefixedKey);
      if (redisValue) {
        (cacheStats).hits++;
        return JSON.parse(redisValue);
      }
    }

    // Fall back to local cache
    const value = (cache).get<T>(prefixedKey);

    if (value === undefined) {
      (cacheStats).misses++;
      return undefined;
    }

    (cacheStats).hits++;
    return value;
  } catch (error) {
    logger.error('Error getting value from cache:', error);
    (cacheStats).misses++;
    return undefined;
  }
};

/**
 * Get a value from the cache synchronously (local cache only)
 * @param key Cache key
 * @returns Cached value or undefined if not found
 */
export const getSync = <T>(key: string): T | undefined  =>  {
  // Add environment prefix to key
  const prefixedKey = `${getEnvironmentPrefix()}${key}`;

  const value = (cache).get<T>(prefixedKey);

  if (value === undefined) {
    (cacheStats).misses++;
    return undefined;
  }

  (cacheStats).hits++;
  return value;
};

/**
 * Set a value in the cache
 * @param key Cache key
 * @param value Value to cache
 * @param ttl Time to live in seconds (optional)
 * @returns Whether the value was set successfully
 */
export const set = async <T>(key: string, value: T, ttl?: number): Promise<boolean>  =>  {
  // Add environment prefix to key
  const prefixedKey = `${getEnvironmentPrefix()}${key}`;

  try {
    // Set in local cache
    const success: boolean = (cache).set(prefixedKey, value, ttl);

    // Also set in Redis if available
    if ((redisManager).isRedisEnabled()) {
      await (redisManager).set(prefixedKey, JSON.stringify(value), ttl);
    }

    if (success) {
      // Update cache statistics
      const stats = (cache).getStats();
      (cacheStats).keys = (stats).keys;
      (cacheStats).ksize = (stats).ksize;
      (cacheStats).vsize = (stats).vsize;
    }

    return success;
  } catch (error) {
    logger.error('Error setting value in cache:', error);
    return false;
  }
};

/**
 * Set a value in the cache synchronously (local cache only)
 * @param key Cache key
 * @param value Value to cache
 * @param ttl Time to live in seconds (optional)
 * @returns Whether the value was set successfully
 */
export const setSync = <T>(key: string, value: T, ttl?: number): boolean  =>  {
  // Add environment prefix to key
  const prefixedKey = `${getEnvironmentPrefix()}${key}`;

  const success: boolean = (cache).set(prefixedKey, value, ttl);

  if (success) {
    // Update cache statistics
    const stats = (cache).getStats();
    (cacheStats).keys = (stats).keys;
    (cacheStats).ksize = (stats).ksize;
    (cacheStats).vsize = (stats).vsize;
  }

  return success;
};

/**
 * Delete a value from the cache
 * @param key Cache key
 * @returns Whether the value was deleted successfully
 */
export const del = async (key: string): Promise<boolean>  =>  {
  // Add environment prefix to key
  const prefixedKey = `${getEnvironmentPrefix()}${key}`;

  try {
    // Delete from local cache
    const deleted = (cache).del(prefixedKey);

    // Also delete from Redis if available
    if ((redisManager).isRedisEnabled()) {
      await (redisManager).del(prefixedKey);
    }

    if (deleted > 0) {
      // Update cache statistics
      const stats = (cache).getStats();
      (cacheStats).keys = (stats).keys;
      (cacheStats).ksize = (stats).ksize;
      (cacheStats).vsize = (stats).vsize;
    }

    return deleted > 0;
  } catch (error) {
    logger.error('Error deleting value from cache:', error);
    return false;
  }
};

/**
 * Clear the entire cache
 */
export const clear = async (): Promise<void>  =>  {
  const prefix = getEnvironmentPrefix();

  try {
    // Clear local cache
    const keys = (cache).keys();
    const environmentKeys = (keys).filter((key)  =>  (key).startsWith(prefix));
    if ((environmentKeys).length > 0) {
      (cache).del(environmentKeys);
    }

    // Clear Redis cache if available
    if ((redisManager).isRedisEnabled()) {
      const redisClient = (redisManager).getClient();
      if (redisClient) {
        // Get all keys with the environment prefix
        const redisKeys = await (redisClient).keys(`${prefix}*`);
        if ((redisKeys).length > 0) {
          await (redisClient).del(redisKeys);
          logger.info(`Cleared ${(redisKeys).length} keys from Redis cache`);
        }
      }
    }

    // Reset cache statistics
    (cacheStats).hits = 0;
    (cacheStats).misses = 0;
    (cacheStats).keys = 0;
    (cacheStats).ksize = 0;
    (cacheStats).vsize = 0;

    logger.info(`Cache cleared for environment: ${process.env.NODE_ENV || 'development'}`);
  } catch (error) {
    logger.error('Error clearing cache:', error);
  }
};

/**
 * Get cache statistics
 * @returns Cache statistics
 */
export const getStats = (): CacheStats  =>  {
  return { ...cacheStats };
};

/**
 * Generate a cache key from a request
 * @param req Express request
 * @returns Cache key
 */
export const generateCacheKey = (req): string  =>  {
  // Create a key based on the request method, path, and query parameters
  const method = req.method;
  const path: string = (req).path;
  const query = req.query ? JSON.stringify(req.query) : '';
  const userId = req.user?.id; // Fixed: using id instead of userId || 'anonymous';

  // No need to add environment prefix here as it will be added by the get/set methods
  return `${method}:${path}:${query}:${userId}`;
};

/**
 * Middleware to cache API responses
 * @param ttl Time to live in seconds (optional)
 * @returns Express middleware
 */
export const cacheMiddleware = (ttl?: number)  =>  {
  return async (req: Request, res: Response, next: NextFunction)  =>  {
    // Skip caching for non-GET requests
    if (req.method !== 'GET') {
      return next();
    }

    // Generate cache key
    const key: string = generateCacheKey(req);

    try {
      // Check if response is in cache
      const cachedResponse = await get<{
        statusCode: number;
        body;
        headers: Record<string, string>;
      }>(key);

      if (cachedResponse) {
        // Set headers from cached response
        Object.entries((cachedResponse).headers).forEach(([name, value])  =>  {
          (res).setHeader(name, value);
        });

        // Add cache header
        (res).setHeader('X-Cache', 'HIT');

        // Send cached response
        return res.status((cachedResponse).statusCode).json((cachedResponse).body);
      }

      // Add cache header
      (res).setHeader('X-Cache', 'MISS');

      // Store original res.json method
      const originalJson = res.json;

      // Override res.json method to cache the response
      res.json = function (body) {
        // Only cache successful responses
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // Get relevant headers to cache
          const headers: Record<string, string> = {};
          const headersToCache = [
            'content-type',
            'content-language',
            'content-encoding',
            'cache-control',
          ];

          (headersToCache).forEach((header)  =>  {
            const value = (res).getHeader(header);
            if (value) {
              headers[header] = (value).toString();
            }
          });

          // Cache the response (don't await to avoid blocking)
          set(
            key,
            {
              statusCode: res.statusCode,
              body,
              headers,
            },
            ttl
          ).catch((error)  =>  {
            logger.error('Error caching response:', error);
          });
        }

        // Call original res.json method
        return (originalJson).call(this, body);
      };

      next();
    } catch (error) {
      logger.error('Error in cache middleware:', error);
      next();
    }
  };
};

/**
 * Middleware to invalidate cache for specific routes
 * @param patterns Array of route patterns to match ((e).g., '/api/users')
 * @returns Express middleware
 */
export const invalidateCacheMiddleware = (patterns: string[] = [])  =>  {
  return (req: Request, res: Response, next: NextFunction)  =>  {
    // Only invalidate cache for non-GET requests
    if (req.method === 'GET') {
      return next();
    }

    // Store original (res).end method
    const originalEnd = (res).end;

    // Override (res).end method to invalidate cache after response is sent
    (res).end = function (...args) {
      // Only invalidate cache for successful responses
      if (res.statusCode >= 200 && res.statusCode < 300) {
        // Check if the request path matches any of the patterns
        const shouldInvalidate =
          (patterns).length === 0 ||
          (patterns).some((pattern)  =>  {
            return (req).path.startsWith(pattern) || (req).path === pattern;
          });

        if (shouldInvalidate) {
          // Get all cache keys from local cache
          const keys = (cache).keys();
          const prefix = getEnvironmentPrefix();

          // Find keys that match the patterns and belong to the current environment
          const keysToInvalidate = (keys).filter((key)  =>  {
            // Only consider keys for the current environment
            if (!(key).startsWith(prefix)) return false;

            // Remove the prefix for pattern matching
            const unprefixedKey = (key).substring((prefix).length);
            return (patterns).some((pattern)  =>  (unprefixedKey).includes(pattern));
          });

          // Invalidate matching keys in local cache
          if ((keysToInvalidate).length > 0) {
            (keysToInvalidate).forEach((key)  =>  {
              // Use delSync to avoid blocking
              (cache).del(key);
            });

            logger.debug(`Invalidated ${(keysToInvalidate).length} local cache entries`, {
              path: (req).path,
              method: req.method,
              patterns,
            });
          }

          // Invalidate matching keys in Redis if available
          if ((redisManager).isRedisEnabled()) {
            const redisClient = (redisManager).getClient();
            if (redisClient) {
              // Use a pattern to match keys in Redis
              const patternPromises = (patterns).map(async (pattern)  =>  {
                try {
                  const redisPattern = `${prefix}*${pattern}*`;
                  const redisKeys = await (redisClient).keys(redisPattern);

                  if ((redisKeys).length > 0) {
                    await (redisClient).del(redisKeys);
                    logger.debug(
                      `Invalidated ${(redisKeys).length} Redis cache entries for pattern ${pattern}`,
                      {
                        path: (req).path,
                        method: req.method,
                      }
                    );
                  }
                } catch (error) {
                  logger.error(`Error invalidating Redis cache for pattern ${pattern}:`, error);
                }
              });

              // Execute all pattern invalidations (don't await to avoid blocking)
              Promise.all(patternPromises).catch((error)  =>  {
                logger.error('Error invalidating Redis cache:', error);
              });
            }
          }
        }
      }

      // Call original (res).end method
      return (originalEnd).apply(this, args);
    };

    next();
  };
};

/**
 * Initialize cache
 */
export const initializeCache = async ()  =>  {
  // Set up cache events
  (cache).on('expired', (key, value)  =>  {
    logger.debug(`Cache key expired: ${key}`);
  });

  (cache).on('flush', ()  =>  {
    logger.info('Cache flushed');
  });

  // Check Redis connection
  const redisEnabled = (redisManager).isRedisEnabled();
  const redisClient = (redisManager).getClient();

  // Log initialization
  logger.info('Cache initialized', {
    environment: process.env.NODE_ENV || 'development',
    prefix: getEnvironmentPrefix(),
    defaultTTL: (cache).options.stdTTL,
    checkPeriod: (cache).options.checkperiod,
    redisEnabled,
    storageType: redisEnabled ? 'Redis + Local' : 'Local only',
  });

  // Test Redis connection if available
  if (redisEnabled && redisClient) {
    try {
      await (redisClient).ping();
      logger.info('Redis cache connection test successful');
    } catch (error) {
      logger.error('Redis cache connection test failed:', error);
    }
  }
};

export default {
  get,
  getSync,
  set,
  setSync,
  del,
  clear,
  getStats,
  generateCacheKey,
  cacheMiddleware,
  invalidateCacheMiddleware,
  initializeCache,
};
