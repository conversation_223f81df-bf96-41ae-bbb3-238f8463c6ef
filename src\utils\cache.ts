// jscpd:ignore-file
/**
 * Cache Utility
 *
 * This utility provides functions for caching API responses and other data.
 * It uses Redis if available, otherwise falls back to an in-memory store.
 */

import NodeCache from 'node-cache';
import { logger as Importedlogger } from '../lib/logger';
import { Request, Response, NextFunction } from 'express';
import redisManager from '../lib/redis-manager';
import { Middleware as ImportedMiddleware } from '../types/express';
import { Request, Response, NextFunction } from 'express';
import { Middleware as ImportedMiddleware } from '../types/express';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

// Get environment-specific cache prefix
const getEnvironmentPrefix = (): string => {
  const env = process.env.NODE_ENV || 'development';
  return `${env}:`;
};

// Create a cache instance
const cache = new NodeCache({
  stdTTL: parseInt(process.env.CACHE_TTL || '300', 10), // Default TTL: 5 minutes
  checkperiod: 120, // Check for expired keys every 2 minutes
  useClones: false, // Don't clone objects (for performance)
  deleteOnExpire: true, // Delete expired keys
});

/**
 * Cache statistics
 */
interface CacheStats {
  hits: number;
  misses: number;
  keys: number;
  ksize: number;
  vsize: number;
}

// Cache statistics
const cacheStats: CacheStats = {
  hits: 0,
  misses: 0,
  keys: 0,
  ksize: 0,
  vsize: 0,
};

/**
 * Get a value from the cache
 * @param key Cache key
 * @returns Cached value or undefined if not found
 */
export const get = async <T>(key: string): Promise<T | undefined> => {
  // Add environment prefix to key
  const prefixedKey = `${getEnvironmentPrefix()}${key}`;

  try {
    // Try to get from Redis first
    if ((redisManager as any).isRedisEnabled()) {
      const redisValue = await (redisManager as any).get(prefixedKey);
      if (redisValue) {
        (cacheStats as any).hits++;
        return JSON.parse(redisValue);
      }
    }

    // Fall back to local cache
    const value = (cache as any).get<T>(prefixedKey);

    if (value === undefined) {
      (cacheStats as any).misses++;
      return undefined;
    }

    (cacheStats as any).hits++;
    return value;
  } catch(error) {
    (logger as any).error('Error getting value from cache:', error);
    (cacheStats as any).misses++;
    return undefined;
  }
};

/**
 * Get a value from the cache synchronously (local cache only)
 * @param key Cache key
 * @returns Cached value or undefined if not found
 */
export const getSync: any = <T>(key: string): T | undefined => {
  // Add environment prefix to key
  const prefixedKey = `${getEnvironmentPrefix()}${key}`;

  const value = (cache as any).get<T>(prefixedKey);

  if (value === undefined) {
    (cacheStats as any).misses++;
    return undefined;
  }

  (cacheStats as any).hits++;
  return value;
};

/**
 * Set a value in the cache
 * @param key Cache key
 * @param value Value to cache
 * @param ttl Time to live in seconds (optional)
 * @returns Whether the value was set successfully
 */
export const set = async <T>(key: string, value: T, ttl?: number): Promise<boolean> => {
  // Add environment prefix to key
  const prefixedKey = `${getEnvironmentPrefix()}${key}`;

  try {
    // Set in local cache
    const success: boolean = (cache as any).set(prefixedKey, value, ttl);

    // Also set in Redis if available
    if ((redisManager as any).isRedisEnabled()) {
      await (redisManager as any).set(prefixedKey, JSON.stringify(value), ttl);
    }

    if (success) {
      // Update cache statistics
      const stats: any = (cache as any).getStats();
      (cacheStats as any).keys = (stats as any).keys;
      (cacheStats as any).ksize = (stats as any).ksize;
      (cacheStats as any).vsize = (stats as any).vsize;
    }

    return success;
  } catch(error) {
    (logger as any).error('Error setting value in cache:', error);
    return false;
  }
};

/**
 * Set a value in the cache synchronously (local cache only)
 * @param key Cache key
 * @param value Value to cache
 * @param ttl Time to live in seconds (optional)
 * @returns Whether the value was set successfully
 */
export const setSync: any = <T>(key: string, value: T, ttl?: number): boolean => {
  // Add environment prefix to key
  const prefixedKey = `${getEnvironmentPrefix()}${key}`;

  const success: boolean = (cache as any).set(prefixedKey, value, ttl);

  if (success) {
    // Update cache statistics
    const stats: any = (cache as any).getStats();
    (cacheStats as any).keys = (stats as any).keys;
    (cacheStats as any).ksize = (stats as any).ksize;
    (cacheStats as any).vsize = (stats as any).vsize;
  }

  return success;
};

/**
 * Delete a value from the cache
 * @param key Cache key
 * @returns Whether the value was deleted successfully
 */
export const del = async (key: string): Promise<boolean> => {
  // Add environment prefix to key
  const prefixedKey = `${getEnvironmentPrefix()}${key}`;

  try {
    // Delete from local cache
    const deleted: any = (cache as any).del(prefixedKey);

    // Also delete from Redis if available
    if ((redisManager as any).isRedisEnabled()) {
      await (redisManager as any).del(prefixedKey);
    }

    if (deleted > 0) {
      // Update cache statistics
      const stats: any = (cache as any).getStats();
      (cacheStats as any).keys = (stats as any).keys;
      (cacheStats as any).ksize = (stats as any).ksize;
      (cacheStats as any).vsize = (stats as any).vsize;
    }

    return deleted > 0;
  } catch(error) {
    (logger as any).error('Error deleting value from cache:', error);
    return false;
  }
};

/**
 * Clear the entire cache
 */
export const clear = async (): Promise<void> => {
  const prefix: any = getEnvironmentPrefix();

  try {
    // Clear local cache
    const keys: any = (cache as any).keys();
    const environmentKeys: any = (keys as any).filter((key) => (key as any).startsWith(prefix));
    if ((environmentKeys as any).length > 0) {
      (cache as any).del(environmentKeys);
    }

    // Clear Redis cache if available
    if ((redisManager as any).isRedisEnabled()) {
      const redisClient: any = (redisManager as any).getClient();
      if (redisClient) {
        // Get all keys with the environment prefix
        const redisKeys = await (redisClient as any).keys(`${prefix}*`);
        if ((redisKeys as any).length > 0) {
          await (redisClient as any).del(redisKeys);
          (logger as any).info(`Cleared ${(redisKeys as any).length} keys from Redis cache`);
        }
      }
    }

    // Reset cache statistics
    (cacheStats as any).hits = 0;
    (cacheStats as any).misses = 0;
    (cacheStats as any).keys = 0;
    (cacheStats as any).ksize = 0;
    (cacheStats as any).vsize = 0;

    (logger as any).info(`Cache cleared for environment: ${process.env.NODE_ENV || 'development'}`);
  } catch(error) {
    (logger as any).error('Error clearing cache:', error);
  }
};

/**
 * Get cache statistics
 * @returns Cache statistics
 */
export const getStats = (): CacheStats => {
  return { ...cacheStats };
};

/**
 * Generate a cache key from a request
 * @param req Express request
 * @returns Cache key
 */
export const generateCacheKey = (req): string => {
  // Create a key based on the request method, path, and query parameters
  const method: any = req.method;
  const path: string = (req as any).path;
  const query = req.query ? JSON.stringify(req.query) : '';
  const userId = req.user?.id; // Fixed: using id instead of userId || 'anonymous';

  // No need to add environment prefix here as it will be added by the get/set methods
  return `${method}:${path}:${query}:${userId}`;
};

/**
 * Middleware to cache API responses
 * @param ttl Time to live in seconds (optional)
 * @returns Express middleware
 */
export const cacheMiddleware = (ttl?: number) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip caching for non-GET requests
    if (req.method !== 'GET') {
      return next();
    }

    // Generate cache key
    const key: string = generateCacheKey(req);

    try {
      // Check if response is in cache
      const cachedResponse = await get<{
        statusCode: number;
        body;
        headers: Record<string, string>;
      }>(key);

      if (cachedResponse) {
        // Set headers from cached response
        Object.entries((cachedResponse as any).headers).forEach(([name, value]) => {
          (res as any).setHeader(name, value);
        });

        // Add cache header
        (res as any).setHeader('X-Cache', 'HIT');

        // Send cached response
        return res.status((cachedResponse as any).statusCode).json((cachedResponse as any).body);
      }

      // Add cache header
      (res as any).setHeader('X-Cache', 'MISS');

      // Store original res.json method
      const originalJson: any = res.json;

      // Override res.json method to cache the response
      res.json = function (body) {
        // Only cache successful responses
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // Get relevant headers to cache
          const headers: Record<string, string> = {};
          const headersToCache = [
            'content-type',
            'content-language',
            'content-encoding',
            'cache-control',
          ];

          (headersToCache as any).forEach((header) => {
            const value = (res as any).getHeader(header);
            if (value) {
              headers[header] = (value as any).toString();
            }
          });

          // Cache the response (don't await to avoid blocking)
          set(
            key,
            {
              statusCode: res.statusCode,
              body,
              headers,
            },
            ttl
          ).catch((error) => {
            (logger as any).error('Error caching response:', error);
          });
        }

        // Call original res.json method
        return (originalJson as any).call(this, body);
      };

      next();
    } catch(error) {
      (logger as any).error('Error in cache middleware:', error);
      next();
    }
  };
};

/**
 * Middleware to invalidate cache for specific routes
 * @param patterns Array of route patterns to match ((e as any).g., '/api/users')
 * @returns Express middleware
 */
export const invalidateCacheMiddleware = (patterns: string[] = []) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Only invalidate cache for non-GET requests
    if (req.method === 'GET') {
      return next();
    }

    // Store original (res as any).end method
    const originalEnd: any = (res as any).end;

    // Override (res as any).end method to invalidate cache after response is sent
    (res as any).end = function (...args) {
      // Only invalidate cache for successful responses
      if (res.statusCode >= 200 && res.statusCode < 300) {
        // Check if the request path matches any of the patterns
        const shouldInvalidate: any =
          (patterns as any).length === 0 ||
          (patterns as any).some((pattern) => {
            return (req as any).path.startsWith(pattern) || (req as any).path === pattern;
          });

        if (shouldInvalidate) {
          // Get all cache keys from local cache
          const keys: any = (cache as any).keys();
          const prefix: any = getEnvironmentPrefix();

          // Find keys that match the patterns and belong to the current environment
          const keysToInvalidate: any = (keys as any).filter((key) => {
            // Only consider keys for the current environment
            if (!(key as any).startsWith(prefix)) return false;

            // Remove the prefix for pattern matching
            const unprefixedKey: any = (key as any).substring((prefix as any).length);
            return (patterns as any).some((pattern) => (unprefixedKey as any).includes(pattern));
          });

          // Invalidate matching keys in local cache
          if ((keysToInvalidate as any).length > 0) {
            (keysToInvalidate as any).forEach((key) => {
              // Use delSync to avoid blocking
              (cache as any).del(key);
            });

            (logger as any).debug(`Invalidated ${(keysToInvalidate as any).length} local cache entries`, {
              path: (req as any).path,
              method: req.method,
              patterns,
            });
          }

          // Invalidate matching keys in Redis if available
          if ((redisManager as any).isRedisEnabled()) {
            const redisClient: any = (redisManager as any).getClient();
            if (redisClient) {
              // Use a pattern to match keys in Redis
              const patternPromises: any = (patterns as any).map(async (pattern) => {
                try {
                  const redisPattern = `${prefix}*${pattern}*`;
                  const redisKeys = await (redisClient as any).keys(redisPattern);

                  if ((redisKeys as any).length > 0) {
                    await (redisClient as any).del(redisKeys);
                    (logger as any).debug(
                      `Invalidated ${(redisKeys as any).length} Redis cache entries for pattern ${pattern}`,
                      {
                        path: (req as any).path,
                        method: req.method,
                      }
                    );
                  }
                } catch(error) {
                  (logger as any).error(`Error invalidating Redis cache for pattern ${pattern}:`, error);
                }
              });

              // Execute all pattern invalidations (don't await to avoid blocking)
              Promise.all(patternPromises).catch((error) => {
                (logger as any).error('Error invalidating Redis cache:', error);
              });
            }
          }
        }
      }

      // Call original (res as any).end method
      return (originalEnd as any).apply(this, args);
    };

    next();
  };
};

/**
 * Initialize cache
 */
export const initializeCache = async () => {
  // Set up cache events
  (cache as any).on('expired', (key, value) => {
    (logger as any).debug(`Cache key expired: ${key}`);
  });

  (cache as any).on('flush', () => {
    (logger as any).info('Cache flushed');
  });

  // Check Redis connection
  const redisEnabled: any = (redisManager as any).isRedisEnabled();
  const redisClient: any = (redisManager as any).getClient();

  // Log initialization
  (logger as any).info('Cache initialized', {
    environment: process.env.NODE_ENV || 'development',
    prefix: getEnvironmentPrefix(),
    defaultTTL: (cache as any).options.stdTTL,
    checkPeriod: (cache as any).options.checkperiod,
    redisEnabled,
    storageType: redisEnabled ? 'Redis + Local' : 'Local only',
  });

  // Test Redis connection if available
  if (redisEnabled && redisClient) {
    try {
      await (redisClient as any).ping();
      (logger as any).info('Redis cache connection test successful');
    } catch(error) {
      (logger as any).error('Redis cache connection test failed:', error);
    }
  }
};

export default {
  get,
  getSync,
  set,
  setSync,
  del,
  clear,
  getStats,
  generateCacheKey,
  cacheMiddleware,
  invalidateCacheMiddleware,
  initializeCache,
};
