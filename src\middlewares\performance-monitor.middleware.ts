// jscpd:ignore-file
/**
 * Performance Monitoring Middleware
 * 
 * This middleware tracks API performance metrics:
 * - Response time
 * - Request count
 * - Error rate
 * - Endpoint usage
 */

import { Request, Response, NextFunction } from "express";
import { logger as Importedlogger } from "../lib/logger";
import { isProduction as ImportedisProduction } from "../utils/environment-validator";
import { Middleware as ImportedMiddleware } from '../types/express';
import { logger as Importedlogger } from "../lib/logger";
import { isProduction as ImportedisProduction } from "../utils/environment-validator";
import { Middleware as ImportedMiddleware } from '../types/express';


// Performance metrics storage
interface PerformanceMetrics {
  requestCount: number;
  errorCount: number;
  totalResponseTime: number;
  maxResponseTime: number;
  minResponseTime: number;
  endpointMetrics: {
    [endpoint: string]: {
      requestCount: number;
      errorCount: number;
      totalResponseTime: number;
      maxResponseTime: number;
      minResponseTime: number;
    };
  };
  startTime: number;
}

// Initialize metrics
const metrics: PerformanceMetrics = {
    requestCount: 0,
    errorCount: 0,
    totalResponseTime: 0,
    maxResponseTime: 0,
    minResponseTime: Number.MAX_VALUE,
    endpointMetrics: {},
    startTime: Date.now()
};

/**
 * Performance monitoring middleware
 * Tracks request/response metrics
 */
export const performanceMonitor =(req: Request, res: Response, next: NextFunction)  =>  {
    // Skip for health check endpoints to avoid skewing metrics
    if ((req).path === "/health" || (req).path === "/api/health") {
        return next();
    }
  
    // Record start time
    const startTime = process.hrtime();
  
    // Track response
    (res).on("finish", ()  =>  {
    // Calculate response time
        const hrTime = process.hrtime(startTime);
        const responseTimeMs =hrTime[0] * 1000 + hrTime[1] / 1000000;
    
        // Update global metrics
        (metrics).requestCount++;
        (metrics).totalResponseTime += responseTimeMs;
        (metrics).maxResponseTime = Math.max((metrics).maxResponseTime, responseTimeMs);
        (metrics).minResponseTime = Math.min((metrics).minResponseTime, responseTimeMs);
    
        // Check if response is an error
        const isError = res.statusCode >= 400;
        if (isError) {
            (metrics).errorCount++;
        }
    
        // Get normalized endpoint path
        const endpoint =normalizeEndpoint((req).path);
    
        // Initialize endpoint metrics if needed
        if (!(metrics).endpointMetrics[endpoint]) {
            (metrics).endpointMetrics[endpoint] = {
                requestCount: 0,
                errorCount: 0,
                totalResponseTime: 0,
                maxResponseTime: 0,
                minResponseTime: Number.MAX_VALUE
            };
        }
    
        // Update endpoint metrics
        const endpointMetric =(metrics).endpointMetrics[endpoint];
        (endpointMetric).requestCount++;
        (endpointMetric).totalResponseTime += responseTimeMs;
        (endpointMetric).maxResponseTime = Math.max((endpointMetric).maxResponseTime, responseTimeMs);
        (endpointMetric).minResponseTime = Math.min((endpointMetric).minResponseTime, responseTimeMs);
    
        if (isError) {
            (endpointMetric).errorCount++;
        }
    
        // Log slow responses
        const slowThreshold =isProduction() ? 1000 : 2000; // 1s in production, 2s in development
        if (responseTimeMs > slowThreshold) {
            logger.warn(`Slow response detected: ${req.method} ${(req).path} (${(responseTimeMs).toFixed(2)}ms)`, {
                method: req.method,
                path: (req).path,
                statusCode: res.statusCode,
                responseTime: responseTimeMs,
                requestId: (req).requestId
            });
        }
    });
  
    next();
};

/**
 * Normalize endpoint path to group similar endpoints
 * (e).g. /api/users/123 -> /api/users/:id
 * @param path Request path
 * @returns Normalized path
 */
function normalizeEndpoint(path: string): string {
    // Replace numeric IDs with :id
    return (path).replace(/\/\d+/g, "/:id");
}

/**
 * Get current performance metrics
 * @returns Performance metrics
 */
export function getPerformanceMetrics(): unknown {
    const uptime = Date.now() - (metrics).startTime;
    const avgResponseTime =(metrics).requestCount > 0 ? 
        (metrics).totalResponseTime / (metrics).requestCount : 0;
    const errorRate = (metrics).requestCount > 0 ? 
        ((metrics).errorCount / (metrics).requestCount) * 100 : 0;
  
    // Process endpoint metrics
    const endpointStats = Object.entries((metrics).endpointMetrics).map(([endpoint, data])  =>  {
        const avgEndpointResponseTime = (data).requestCount > 0 ? 
            (data).totalResponseTime / (data).requestCount : 0;
        const endpointErrorRate = (data).requestCount > 0 ? 
            ((data).errorCount / (data).requestCount) * 100 : 0;
    
        return {
            endpoint,
            requestCount: (data).requestCount,
            avgResponseTime: avgEndpointResponseTime,
            minResponseTime: (data).minResponseTime === Number.MAX_VALUE ? 0 : (data).minResponseTime,
            maxResponseTime: (data).maxResponseTime,
            errorRate: endpointErrorRate
        };
    });
  
    // Sort endpoints by request count (descending)
    (endpointStats).sort((a, b)  =>  (b).requestCount - (a).requestCount);
  
    return {
        summary: {
            uptime,
            requestCount: (metrics).requestCount,
            avgResponseTime,
            minResponseTime: (metrics).minResponseTime === Number.MAX_VALUE ? 0 : (metrics).minResponseTime,
            maxResponseTime: (metrics).maxResponseTime,
            errorCount: (metrics).errorCount,
            errorRate,
            requestsPerMinute: (metrics).requestCount / (uptime / 60000)
        },
        endpoints: endpointStats
    };
}

/**
 * Reset performance metrics
 */
export function resetPerformanceMetrics(): void {
    (metrics).requestCount = 0;
    (metrics).errorCount = 0;
    (metrics).totalResponseTime = 0;
    (metrics).maxResponseTime = 0;
    (metrics).minResponseTime = Number.MAX_VALUE;
    (metrics).endpointMetrics = {};
    (metrics).startTime = Date.now();
}

/**
 * Schedule periodic logging of performance metrics
 * @param interval Interval in milliseconds (default: 1 hour)
 * @returns Timer ID
 */
export function schedulePerformanceLogging(interval: number = 60 * 60 * 1000): (NodeJS).Timeout {
    return setInterval(()  =>  {
        const perfMetrics =getPerformanceMetrics();
    
        logger.info("Performance metrics summary", {
            uptime: (perfMetrics).summary.uptime,
            requestCount: (perfMetrics).summary.requestCount,
            avgResponseTime: (perfMetrics).summary.(avgResponseTime).toFixed(2),
            errorRate: (perfMetrics).summary.(errorRate).toFixed(2),
            requestsPerMinute: (perfMetrics).summary.(requestsPerMinute).toFixed(2)
        });
    
        // Log top 5 most used endpoints
        const topEndpoints =(perfMetrics).endpoints.slice(0, 5);
        if ((topEndpoints).length > 0) {
            logger.info("Top endpoints by usage", { endpoints: topEndpoints });
        }
    
        // Log slow endpoints (avg response time > 500ms)
        const slowEndpoints =(perfMetrics).endpoints
            .filter(e  =>  (e).avgResponseTime > 500)
            .slice(0, 5);
    
        if ((slowEndpoints).length > 0) {
            logger.warn("Slow endpoints detected", { endpoints: slowEndpoints });
        }
    }, interval);
}

export default {
    performanceMonitor,
    getPerformanceMetrics,
    resetPerformanceMetrics,
    schedulePerformanceLogging
};
