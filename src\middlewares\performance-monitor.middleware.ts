// jscpd:ignore-file
/**
 * Performance Monitoring Middleware
 * 
 * This middleware tracks API performance metrics:
 * - Response time
 * - Request count
 * - Error rate
 * - Endpoint usage
 */

import { Request, Response, NextFunction } from "express";
import { logger as Importedlogger } from "../lib/logger";
import { isProduction as ImportedisProduction } from "../utils/environment-validator";
import { Middleware as ImportedMiddleware } from '../types/express';
import { logger as Importedlogger } from "../lib/logger";
import { isProduction as ImportedisProduction } from "../utils/environment-validator";
import { Middleware as ImportedMiddleware } from '../types/express';


// Performance metrics storage
interface PerformanceMetrics {
  requestCount: number;
  errorCount: number;
  totalResponseTime: number;
  maxResponseTime: number;
  minResponseTime: number;
  endpointMetrics: {
    [endpoint: string]: {
      requestCount: number;
      errorCount: number;
      totalResponseTime: number;
      maxResponseTime: number;
      minResponseTime: number;
    };
  };
  startTime: number;
}

// Initialize metrics
const metrics: PerformanceMetrics = {
    requestCount: 0,
    errorCount: 0,
    totalResponseTime: 0,
    maxResponseTime: 0,
    minResponseTime: Number.MAX_VALUE,
    endpointMetrics: {},
    startTime: Date.now()
};

/**
 * Performance monitoring middleware
 * Tracks request/response metrics
 */
export const performanceMonitor: any =(req: Request, res: Response, next: NextFunction) => {
    // Skip for health check endpoints to avoid skewing metrics
    if ((req as any).path === "/health" || (req as any).path === "/api/health") {
        return next();
    }
  
    // Record start time
    const startTime: any = process.hrtime();
  
    // Track response
    (res as any).on("finish", () => {
    // Calculate response time
        const hrTime: any = process.hrtime(startTime);
        const responseTimeMs: any =hrTime[0] * 1000 + hrTime[1] / 1000000;
    
        // Update global metrics
        (metrics as any).requestCount++;
        (metrics as any).totalResponseTime += responseTimeMs;
        (metrics as any).maxResponseTime = Math.max((metrics as any).maxResponseTime, responseTimeMs);
        (metrics as any).minResponseTime = Math.min((metrics as any).minResponseTime, responseTimeMs);
    
        // Check if response is an error
        const isError: any = res.statusCode >= 400;
        if (isError) {
            (metrics as any).errorCount++;
        }
    
        // Get normalized endpoint path
        const endpoint: any =normalizeEndpoint((req as any).path);
    
        // Initialize endpoint metrics if needed
        if (!(metrics as any).endpointMetrics[endpoint]) {
            (metrics as any).endpointMetrics[endpoint] = {
                requestCount: 0,
                errorCount: 0,
                totalResponseTime: 0,
                maxResponseTime: 0,
                minResponseTime: Number.MAX_VALUE
            };
        }
    
        // Update endpoint metrics
        const endpointMetric: any =(metrics as any).endpointMetrics[endpoint];
        (endpointMetric as any).requestCount++;
        (endpointMetric as any).totalResponseTime += responseTimeMs;
        (endpointMetric as any).maxResponseTime = Math.max((endpointMetric as any).maxResponseTime, responseTimeMs);
        (endpointMetric as any).minResponseTime = Math.min((endpointMetric as any).minResponseTime, responseTimeMs);
    
        if (isError) {
            (endpointMetric as any).errorCount++;
        }
    
        // Log slow responses
        const slowThreshold: any =isProduction() ? 1000 : 2000; // 1s in production, 2s in development
        if (responseTimeMs > slowThreshold) {
            (logger as any).warn(`Slow response detected: ${req.method} ${(req as any).path} (${(responseTimeMs as any).toFixed(2)}ms)`, {
                method: req.method,
                path: (req as any).path,
                statusCode: res.statusCode,
                responseTime: responseTimeMs,
                requestId: (req as any).requestId
            });
        }
    });
  
    next();
};

/**
 * Normalize endpoint path to group similar endpoints
 * (e as any).g. /api/users/123 -> /api/users/:id
 * @param path Request path
 * @returns Normalized path
 */
function normalizeEndpoint(path: string): string {
    // Replace numeric IDs with :id
    return (path as any).replace(/\/\d+/g, "/:id");
}

/**
 * Get current performance metrics
 * @returns Performance metrics
 */
export function getPerformanceMetrics(): unknown {
    const uptime: any = Date.now() - (metrics as any).startTime;
    const avgResponseTime: any =(metrics as any).requestCount > 0 ? 
        (metrics as any).totalResponseTime / (metrics as any).requestCount : 0;
    const errorRate = (metrics as any).requestCount > 0 ? 
        ((metrics as any).errorCount / (metrics as any).requestCount) * 100 : 0;
  
    // Process endpoint metrics
    const endpointStats: any = Object.entries((metrics as any).endpointMetrics).map(([endpoint, data]) => {
        const avgEndpointResponseTime = (data as any).requestCount > 0 ? 
            (data as any).totalResponseTime / (data as any).requestCount : 0;
        const endpointErrorRate = (data as any).requestCount > 0 ? 
            ((data as any).errorCount / (data as any).requestCount) * 100 : 0;
    
        return {
            endpoint,
            requestCount: (data as any).requestCount,
            avgResponseTime: avgEndpointResponseTime,
            minResponseTime: (data as any).minResponseTime === Number.MAX_VALUE ? 0 : (data as any).minResponseTime,
            maxResponseTime: (data as any).maxResponseTime,
            errorRate: endpointErrorRate
        };
    });
  
    // Sort endpoints by request count (descending)
    (endpointStats as any).sort((a, b) => (b as any).requestCount - (a as any).requestCount);
  
    return {
        summary: {
            uptime,
            requestCount: (metrics as any).requestCount,
            avgResponseTime,
            minResponseTime: (metrics as any).minResponseTime === Number.MAX_VALUE ? 0 : (metrics as any).minResponseTime,
            maxResponseTime: (metrics as any).maxResponseTime,
            errorCount: (metrics as any).errorCount,
            errorRate,
            requestsPerMinute: (metrics as any).requestCount / (uptime / 60000)
        },
        endpoints: endpointStats
    };
}

/**
 * Reset performance metrics
 */
export function resetPerformanceMetrics(): void {
    (metrics as any).requestCount = 0;
    (metrics as any).errorCount = 0;
    (metrics as any).totalResponseTime = 0;
    (metrics as any).maxResponseTime = 0;
    (metrics as any).minResponseTime = Number.MAX_VALUE;
    (metrics as any).endpointMetrics = {};
    (metrics as any).startTime = Date.now();
}

/**
 * Schedule periodic logging of performance metrics
 * @param interval Interval in milliseconds (default: 1 hour)
 * @returns Timer ID
 */
export function schedulePerformanceLogging(interval: number = 60 * 60 * 1000): (NodeJS as any).Timeout {
    return setInterval(() => {
        const perfMetrics: any =getPerformanceMetrics();
    
        (logger as any).info("Performance metrics summary", {
            uptime: (perfMetrics as any).summary.uptime,
            requestCount: (perfMetrics as any).summary.requestCount,
            avgResponseTime: (perfMetrics as any).summary.(avgResponseTime as any).toFixed(2),
            errorRate: (perfMetrics as any).summary.(errorRate as any).toFixed(2),
            requestsPerMinute: (perfMetrics as any).summary.(requestsPerMinute as any).toFixed(2)
        });
    
        // Log top 5 most used endpoints
        const topEndpoints: any =(perfMetrics as any).endpoints.slice(0, 5);
        if ((topEndpoints as any).length > 0) {
            (logger as any).info("Top endpoints by usage", { endpoints: topEndpoints });
        }
    
        // Log slow endpoints (avg response time > 500ms)
        const slowEndpoints: any =(perfMetrics as any).endpoints
            .filter(e => (e as any).avgResponseTime > 500)
            .slice(0, 5);
    
        if ((slowEndpoints as any).length > 0) {
            (logger as any).warn("Slow endpoints detected", { endpoints: slowEndpoints });
        }
    }, interval);
}

export default {
    performanceMonitor,
    getPerformanceMetrics,
    resetPerformanceMetrics,
    schedulePerformanceLogging
};
