// jscpd:ignore-file
/**
 * Merchant Segmentation Controller
 * 
 * This controller handles API requests related to merchant segmentation.
 */

import { Request, Response, NextFunction } from 'express';
import { BaseController } from "./(base).controller";
import { MerchantSegmentationService, SegmentationCriteria } from "../services/merchant-(segmentation).service";
import { logger as Importedlogger } from "../utils/logger";
import { Merchant as ImportedMerchant } from '../types';
import { BaseController } from "./(base).controller";
import { MerchantSegmentationService, SegmentationCriteria } from "../services/merchant-(segmentation).service";
import { logger as Importedlogger } from "../utils/logger";
import { Merchant as ImportedMerchant } from '../types';


/**
 * Merchant segmentation controller
 */
export class MerchantSegmentationController extends BaseController {
    private merchantSegmentationService: MerchantSegmentationService;
  
    constructor() {
        super();
        this.merchantSegmentationService = new MerchantSegmentationService();
    }
  
    /**
   * Create a new merchant category
   */
    createCategory = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { name, description } = req.body;
      
            // Validate required fields
            if (!name) {
                return (res).badRequest("Category name is required");
            }
      
            // Create category
            const category = await this.merchantSegmentationService.createCategory(name, description);
      
            return res.success("Merchant category created", category);
        } catch(error) {
            (logger).error("Error creating merchant category:", error);
            return (res).serverError("Failed to create merchant category");
        }
    });
  
    /**
   * Get all merchant categories
   */
    getAllCategories = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const categories = await this.merchantSegmentationService.getAllCategories();
      
            return res.success("Merchant categories retrieved", categories);
        } catch(error) {
            (logger).error("Error getting merchant categories:", error);
            return (res).serverError("Failed to get merchant categories");
        }
    });
  
    /**
   * Add merchant to category
   */
    addMerchantToCategory = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { merchantId, categoryId } = req.params;
      
            // Add merchant to category
            const merchant = await this.merchantSegmentationService.addMerchantToCategory(merchantId, categoryId);
      
            return res.success("Merchant added to category", merchant);
        } catch(error) {
            (logger).error("Error adding merchant to category:", error);
            return (res).serverError("Failed to add merchant to category");
        }
    });
  
    /**
   * Create a new merchant segment
   */
    createSegment = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { name, description, criteria } = req.body;
      
            // Validate required fields
            if (!name) {
                return (res).badRequest("Segment name is required");
            }
      
            if (!criteria) {
                return (res).badRequest("Segmentation criteria are required");
            }
      
            // Create segment
            const segment = await this.merchantSegmentationService.createSegment(
                name,
                description ?? "",
        criteria as SegmentationCriteria
            );
      
            return res.success("Merchant segment created", segment);
        } catch(error) {
            (logger).error("Error creating merchant segment:", error);
            return (res).serverError("Failed to create merchant segment");
        }
    });
  
    /**
   * Get all merchant segments
   */
    getAllSegments = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const segments = await this.merchantSegmentationService.getAllSegments();
      
            return res.success("Merchant segments retrieved", segments);
        } catch(error) {
            (logger).error("Error getting merchant segments:", error);
            return (res).serverError("Failed to get merchant segments");
        }
    });
  
    /**
   * Apply segment to matching merchants
   */
    applySegment = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { segmentId } = req.params;
      
            // Apply segment
            const merchantCount = await this.merchantSegmentationService.applySegmentToMerchants(segmentId);
      
            return res.success("Segment applied to merchants", { merchantCount });
        } catch(error) {
            (logger).error("Error applying segment to merchants:", error);
            return (res).serverError("Failed to apply segment to merchants");
        }
    });
  
    /**
   * Create a new merchant performance tier
   */
    createPerformanceTier = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const {
                name,
                description,
                minimumRevenue,
                minimumTransactions,
                successRateThreshold,
                benefits
            } = req.body;
      
            // Validate required fields
            if (!name) {
                return (res).badRequest("Tier name is required");
            }
      
            if (minimumRevenue === undefined || minimumRevenue === null) {
                return (res).badRequest("Minimum revenue is required");
            }
      
            if (minimumTransactions === undefined || minimumTransactions === null) {
                return (res).badRequest("Minimum transactions is required");
            }
      
            // Create tier
            const tier = await this.merchantSegmentationService.createPerformanceTier(
                name,
                description ?? "",
                parseFloat(minimumRevenue),
                parseInt(minimumTransactions),
                successRateThreshold ? parseFloat(successRateThreshold) : undefined,
                benefits
            );
      
            return res.success("Merchant performance tier created", tier);
        } catch(error) {
            (logger).error("Error creating merchant performance tier:", error);
            return (res).serverError("Failed to create merchant performance tier");
        }
    });
  
    /**
   * Apply performance tier to qualifying merchants
   */
    applyPerformanceTier = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const { tierId } = req.params;
      
            // Apply tier
            const merchantCount = await this.merchantSegmentationService.applyPerformanceTierToMerchants(tierId);
      
            return res.success("Performance tier applied to merchants", { merchantCount });
        } catch(error) {
            (logger).error("Error applying performance tier to merchants:", error);
            return (res).serverError("Failed to apply performance tier to merchants");
        }
    });
}
