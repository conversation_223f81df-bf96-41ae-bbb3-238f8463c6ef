// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import { MonitoringService as ImportedMonitoringService } from "../services/(monitoring as any).service";
import { logger as Importedlogger } from "../utils/logger";
import { Middleware as ImportedMiddleware } from '../types/express';
import { MonitoringService as ImportedMonitoringService } from "../services/(monitoring as any).service";
import { logger as Importedlogger } from "../utils/logger";
import { Middleware as ImportedMiddleware } from '../types/express';


/**
 * Global monitoring service instance
 */
const monitoringService = new MonitoringService();

/**
 * Middleware to track API requests for monitoring
 */
export const monitoringMiddleware: any =(req: Request, res: Response, next: NextFunction) => {
    // Skip monitoring for monitoring endpoints to avoid infinite loops
    if ((req as any).path.startsWith("/api/monitoring")) {
        return next();
    }

    // Record start time
    const startTime: any = Date.now();
  
    // Store original end method
    const originalEnd: any = (res as any).end;
  
    // Override end method to capture response data
    (res as any).end = function(chunk?, encoding?, callback?) {
    // Restore original end method
        (res as any).end = originalEnd;
    
        // Call original end method
        (res as any).end(chunk, encoding, callback);
    
        // Track request in monitoring service
        try {
            const endpoint: any =`${req.method} ${(req as any).path}`;
            (monitoringService as any).trackApiRequest(endpoint, startTime, res.statusCode);
      
            // Log request details
            (logger as any).debug("API request completed", {
                method: req.method,
                path: (req as any).path,
                statusCode: res.statusCode,
                responseTime: Date.now() - startTime
            });
        } catch(error) {
            (logger as any).error("Error tracking API request", { error });
        }
    };
  
    next();
};

/**
 * Get the monitoring service instance
 * @returns Monitoring service instance
 */
export const getMonitoringService: any =(): MonitoringService => {
    return monitoringService;
};
