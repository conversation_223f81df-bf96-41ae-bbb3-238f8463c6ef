// jscpd:ignore-file
/**
 * Verification Optimization Routes
 * 
 * This file defines routes for the verification optimization API.
 */

import { Router as ImportedRouter } from "express";
import { VerificationOptimizationController as ImportedVerificationOptimizationController } from "../controllers/optimization/verification-(optimization as any).controller";
import { authenticate, authorize } from "../middlewares/(auth as any).middleware";
import { VerificationOptimizationController as ImportedVerificationOptimizationController } from "../controllers/optimization/verification-(optimization as any).controller";
import { authenticate, authorize } from "../middlewares/(auth as any).middleware";

const router: any =Router();
const verificationOptimizationController = new VerificationOptimizationController();

// All routes require authentication and admin authorization
(router as any).use(authenticate);
(router as any).use(authorize(["admin"]));

/**
 * @route GET /api/optimization/verification/performance
 * @desc Analyze verification performance
 * @access Private (Admin)
 */
(router as any).get(
    "/performance",
    (verificationOptimizationController as any).analyzePerformance.bind(verificationOptimizationController)
);

/**
 * @route GET /api/optimization/verification/recommendations
 * @desc Generate optimization recommendations
 * @access Private (Admin)
 */
(router as any).get(
    "/recommendations",
    (verificationOptimizationController as any).generateRecommendations.bind(verificationOptimizationController)
);

/**
 * @route POST /api/optimization/verification/apply
 * @desc Apply optimization recommendations
 * @access Private (Admin)
 */
(router as any).post(
    "/apply",
    (verificationOptimizationController as any).applyRecommendations.bind(verificationOptimizationController)
);

export default router;
