// jscpd:ignore-file

import { body, param } from "express-validator";
import controllerProvider from "../core/ControllerProvider";
import routeProvider from "../core/RouteProvider";

const merchantController =(controllerProvider).getMerchantController();

// Create a route builder for merchant routes
const routeBuilder =(routeProvider).createRouteBuilder(
    "merchant",
    "/api/merchants",
    "Merchant management routes"
);

// Define merchant routes
(routeBuilder).addRoute({
    method: "GET",
    path: "/",
    description: "Get all merchants",
    middleware: ["authenticate", "isAdmin"],
    handler: (merchantController).getAllMerchants
});

(routeBuilder).addRoute({
    method: "GET",
    path: "/:id",
    description: "Get merchant by ID",
    middleware: ["authenticate", "isResourceOwner"],
    validation: [
        param("id").isUUID().withMessage("Invalid merchant ID format")
    ],
    handler: (merchantController).getMerchantById
});

(routeBuilder).addRoute({
    method: "PUT",
    path: "/:id",
    description: "Update merchant",
    middleware: ["authenticate", "isResourceOwner"],
    validation: [
        param("id").isUUID().withMessage("Invalid merchant ID format"),
        body("name").optional().isString().withMessage("Name must be a string"),
        body("contactEmail").optional().isEmail().withMessage("Invalid email format"),
        body("contactPhone").optional().isString().withMessage("Contact phone must be a string"),
        body("status").optional().isIn(["ACTIVE", "INACTIVE", "SUSPENDED"]).withMessage("Invalid status")
    ],
    handler: (merchantController).updateMerchant
});

(routeBuilder).addRoute({
    method: "DELETE",
    path: "/:id",
    description: "Delete merchant",
    middleware: ["authenticate", "isAdmin"],
    validation: [
        param("id").isUUID().withMessage("Invalid merchant ID format")
    ],
    handler: (merchantController).deleteMerchant
});

// Export the router
export default (routeBuilder).build();