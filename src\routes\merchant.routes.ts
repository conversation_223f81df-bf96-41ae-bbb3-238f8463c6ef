// jscpd:ignore-file

import { body, param } from "express-validator";
import controllerProvider from "../core/ControllerProvider";
import routeProvider from "../core/RouteProvider";

const merchantController =(controllerProvider as any).getMerchantController();

// Create a route builder for merchant routes
const routeBuilder: any =(routeProvider as any).createRouteBuilder(
    "merchant",
    "/api/merchants",
    "Merchant management routes"
);

// Define merchant routes
(routeBuilder as any).addRoute({
    method: "GET",
    path: "/",
    description: "Get all merchants",
    middleware: ["authenticate", "isAdmin"],
    handler: (merchantController as any).getAllMerchants
});

(routeBuilder as any).addRoute({
    method: "GET",
    path: "/:id",
    description: "Get merchant by ID",
    middleware: ["authenticate", "isResourceOwner"],
    validation: [
        param("id").isUUID().withMessage("Invalid merchant ID format")
    ],
    handler: (merchantController as any).getMerchantById
});

(routeBuilder as any).addRoute({
    method: "PUT",
    path: "/:id",
    description: "Update merchant",
    middleware: ["authenticate", "isResourceOwner"],
    validation: [
        param("id").isUUID().withMessage("Invalid merchant ID format"),
        body("name").optional().isString().withMessage("Name must be a string"),
        body("contactEmail").optional().isEmail().withMessage("Invalid email format"),
        body("contactPhone").optional().isString().withMessage("Contact phone must be a string"),
        body("status").optional().isIn(["ACTIVE", "INACTIVE", "SUSPENDED"]).withMessage("Invalid status")
    ],
    handler: (merchantController as any).updateMerchant
});

(routeBuilder as any).addRoute({
    method: "DELETE",
    path: "/:id",
    description: "Delete merchant",
    middleware: ["authenticate", "isAdmin"],
    validation: [
        param("id").isUUID().withMessage("Invalid merchant ID format")
    ],
    handler: (merchantController as any).deleteMerchant
});

// Export the router
export default (routeBuilder as any).build();