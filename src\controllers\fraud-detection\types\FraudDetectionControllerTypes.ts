/**
 * Fraud Detection Controller Types
 * 
 * Type definitions for fraud detection controller components.
 */

import { Request, Response, NextFunction } from 'express';

/**
 * Extended request interface with user information
 */
export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    role: string;
    merchantId?: string;
  };
}

/**
 * Risk assessment request
 */
export interface AssessTransactionRiskRequest {
  transactionId: string;
  ipAddress: string;
  userAgent?: string;
  deviceId?: string;
}

/**
 * Fraud configuration update request
 */
export interface UpdateFraudConfigRequest {
  flagThreshold?: number;
  blockThreshold?: number;
  autoBlock?: boolean;
  factorWeights?: Record<string, number>;
  highRiskCountries?: string[];
  highRiskIpRanges?: string[];
  maxTransactionAmount?: number;
  maxTransactionsPerHour?: number;
  maxTransactionsPerDay?: number;
}

/**
 * Risk assessment response
 */
export interface RiskAssessmentResponse {
  id: string;
  transactionId: string;
  riskScore: {
    score: number;
    level: RiskLevel;
    factors: RiskFactor[];
    timestamp: Date;
  };
  isFlagged: boolean;
  isBlocked: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

/**
 * Fraud configuration response
 */
export interface FraudConfigResponse {
  merchantId: number;
  flagThreshold: number;
  blockThreshold: number;
  autoBlock: boolean;
  factorWeights: Record<string, number>;
  highRiskCountries: string[];
  highRiskIpRanges: string[];
  maxTransactionAmount: number;
  maxTransactionsPerHour: number;
  maxTransactionsPerDay: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Flagged transaction response
 */
export interface FlaggedTransactionResponse {
  id: string;
  transactionId: string;
  score: number;
  level: RiskLevel;
  isFlagged: boolean;
  isBlocked: boolean;
  createdAt: Date;
  transaction: {
    id: string;
    amount: number;
    currency: string;
    status: string;
    customerEmail: string;
    customerName: string;
    createdAt: Date;
    merchant: {
      id: number;
      name: string;
    };
    paymentMethod: {
      id: string;
      name: string;
      code: string;
    };
  };
}

/**
 * Fraud statistics response
 */
export interface FraudStatisticsResponse {
  totalAssessments: number;
  flaggedCount: number;
  blockedCount: number;
  flaggedRate: number;
  blockedRate: number;
  levelCounts: Record<RiskLevel, number>;
  dailyStats: DailyFraudStats[];
  period: {
    start: Date;
    end: Date;
  };
}

/**
 * Daily fraud statistics
 */
export interface DailyFraudStats {
  date: string;
  totalAssessments: number;
  flaggedCount: number;
  blockedCount: number;
  levelCounts: Record<RiskLevel, number>;
}

/**
 * Risk level enum
 */
export enum RiskLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

/**
 * Risk factor interface
 */
export interface RiskFactor {
  type: string;
  value: unknown;
  weight: number;
  contribution: number;
  description: string;
}

/**
 * API response wrapper
 */
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

/**
 * Pagination parameters
 */
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Fraud detection filters
 */
export interface FraudDetectionFilters {
  merchantId?: number;
  riskLevel?: RiskLevel;
  isFlagged?: boolean;
  isBlocked?: boolean;
  startDate?: Date;
  endDate?: Date;
  minScore?: number;
  maxScore?: number;
}

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string;
  message: string;
  value?: unknown;
}

/**
 * Controller method options
 */
export interface ControllerMethodOptions {
  requireAuth?: boolean;
  requiredRole?: string;
  validateInput?: boolean;
  logRequest?: boolean;
}

/**
 * Request context interface
 */
export interface RequestContext {
  user?: {
    id: string;
    role: string;
    merchantId?: string;
  };
  requestId: string;
  timestamp: Date;
  ip: string;
  userAgent: string;
}

/**
 * Service dependencies interface
 */
export interface FraudDetectionServiceDependencies {
  fraudDetectionService: any;
  authorizationService: any;
  validationService: any;
  auditService?: unknown;
}

/**
 * Controller configuration
 */
export interface FraudDetectionControllerConfig {
  enableAuditLogging?: boolean;
  enableRateLimiting?: boolean;
  defaultPageSize?: number;
  maxPageSize?: number;
  cacheEnabled?: boolean;
  cacheTtl?: number;
  maxRiskScore?: number;
  defaultFlagThreshold?: number;
  defaultBlockThreshold?: number;
}

/**
 * Error response format
 */
export interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code: string;
    type: string;
    details?: unknown;
  };
  timestamp: Date;
  requestId: string;
}

/**
 * Success response format
 */
export interface SuccessResponse<T = unknown> {
  success: true;
  data: T;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  timestamp: Date;
  requestId: string;
}

/**
 * Controller method result
 */
export type ControllerResult<T = unknown> = Promise<SuccessResponse<T> | ErrorResponse>;

/**
 * Middleware function type
 */
export type MiddlewareFunction = (req: AuthenticatedRequest, res: Response, next: Function) => void | Promise<void>;

/**
 * Controller method type
 */
export type ControllerMethod<T = unknown> = (req: AuthenticatedRequest, res: Response) => ControllerResult<T>;

/**
 * Authorization context
 */
export interface AuthorizationContext {
  user: {
    id: string;
    role: string;
    merchantId?: string;
  };
  resource: string;
  action: string;
  resourceId?: string;
}

/**
 * Permission check result
 */
export interface PermissionResult {
  allowed: boolean;
  reason?: string;
  requiredRole?: string;
  requiredPermissions?: string[];
}

/**
 * Fraud detection action types
 */
export enum FraudDetectionAction {
  ASSESS_RISK = 'assess_risk',
  VIEW_ASSESSMENT = 'view_assessment',
  VIEW_CONFIG = 'view_config',
  UPDATE_CONFIG = 'update_config',
  VIEW_FLAGGED = 'view_flagged',
  VIEW_STATISTICS = 'view_statistics'
}

/**
 * Fraud detection resource types
 */
export enum FraudDetectionResource {
  RISK_ASSESSMENT = 'risk_assessment',
  FRAUD_CONFIG = 'fraud_config',
  FLAGGED_TRANSACTIONS = 'flagged_transactions',
  FRAUD_STATISTICS = 'fraud_statistics'
}

/**
 * Risk assessment metadata
 */
export interface RiskAssessmentMetadata {
  ipAddress: string;
  userAgent: string;
  deviceId: string;
  geolocation?: {
    country: string;
    region: string;
    city: string;
    latitude: number;
    longitude: number;
  };
  deviceFingerprint?: {
    screenResolution: string;
    timezone: string;
    language: string;
    platform: string;
  };
}

/**
 * Fraud detection rule
 */
export interface FraudDetectionRule {
  id: string;
  name: string;
  description: string;
  condition: string;
  action: 'FLAG' | 'BLOCK' | 'REVIEW';
  isActive: boolean;
  priority: number;
  createdAt: Date;
  updatedAt: Date;
}
