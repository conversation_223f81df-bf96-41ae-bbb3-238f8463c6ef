// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { asyncHandler } from "../../middleware/asyncHandler";
import { CrudController } from "../base/CrudController";
import { MerchantService } from "../../services/refactored/merchant.service";
import { Merchant, Prisma } from "@prisma/client";
import { AppError } from "../../utils/appError";
import { ServiceFactory } from "../../factories/ServiceFactory";
import { Merchant } from '../types';
import { asyncHandler } from "../../middleware/asyncHandler";
import { CrudController } from "../base/CrudController";
import { MerchantService } from "../../services/refactored/merchant.service";
import { Merchant, Prisma } from "@prisma/client";
import { AppError } from "../../utils/appError";
import { ServiceFactory } from "../../factories/ServiceFactory";
import { Merchant } from '../types';


/**
 * Merchant controller
 */
export class MerchantController extends CrudController<
  Merchant,
  Prisma.MerchantCreateInput,
  Prisma.MerchantUpdateInput
> {
  private merchantService: MerchantService;

  constructor() {
    const serviceFactory =ServiceFactory.getInstance();
    const service =serviceFactory.getMerchantService();
    super(service, "Merchant");
    this.merchantService = service;

    // Set required fields
    this.requiredCreateFields = ["name", "email"];
    this.requiredUpdateFields = [];
  }

  /**
   * Get merchant statistics
   * @route GET /api/merchants/:id/stats
   */
  getStats = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Check authentication
      const { userRole, userId, merchantId } = this.checkAuthorization(req);

      // Get merchant ID
      const id: string = req.params.id;

      // Check if user is authorized to access this merchant
      if (userRole !== "ADMIN" && merchantId !== id) {
        throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHORIZATION,
            code: ErrorCode.FORBIDDEN
        });
      }

      // Get date range
      const { startDate, endDate } = this.parseDateRange(req);

      // Get statistics
      const stats = await this.merchantService.getMerchantStats(id, {
        startDate,
        endDate
      });

      // Return response
      return this.sendSuccess(res, stats);
    } catch (error) {
      this.handleError(error, res);
    }
  });

  /**
   * Verify merchant
   * @route POST /api/merchants/:id/verify
   */
  verifyMerchant = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Check if user is admin
      this.checkAdminRole(req);

      // Get merchant ID
      const id: string = req.params.id;

      // Verify merchant
      const merchant = await this.merchantService.verifyMerchant(id);

      // Return response
      return this.sendSuccess(res, merchant);
    } catch (error) {
      this.handleError(error, res);
    }
  });

  /**
   * Suspend merchant
   * @route POST /api/merchants/:id/suspend
   */
  suspendMerchant = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Check if user is admin
      this.checkAdminRole(req);

      // Get merchant ID
      const id: string = req.params.id;

      // Validate required fields
      this.validateRequiredFields(req, ["reason"]);

      // Get reason
      const { reason } = req.body;

      // Suspend merchant
      const merchant = await this.merchantService.suspendMerchant(id, reason);

      // Return response
      return this.sendSuccess(res, merchant);
    } catch (error) {
      this.handleError(error, res);
    }
  });

  /**
   * Get all merchants
   * @param page Page number
   * @param limit Items per page
   * @param offset Offset
   * @param filters Filters
   * @returns Merchants and total count
   */
  protected async getAllEntities(
    page: number,
    limit: number,
    offset: number,
    filters: Record<string, any>
  ): Promise<{ data: Merchant[]; total: number }> {
    return this.merchantService.getMerchants({
      limit,
      offset,
      ...filters
    });
  }

  /**
   * Get merchant by ID
   * @param id Merchant ID
   * @returns Merchant
   */
  protected async getEntityById(id: string): Promise<Merchant> {
    const merchant = await this.merchantService.getMerchantById(id);

    if (!merchant) {
      throw new AppError(`Merchant with ID ${id} not found`, 404);
    }

    return merchant;
  }

  /**
   * Create merchant
   * @param data Merchant data
   * @returns Created merchant
   */
  protected async createEntity(data: Prisma.MerchantCreateInput): Promise<Merchant> {
    return this.merchantService.createMerchant(data);
  }

  /**
   * Update merchant
   * @param id Merchant ID
   * @param data Merchant data
   * @returns Updated merchant
   */
  protected async updateEntity(id: string, data: Prisma.MerchantUpdateInput): Promise<Merchant> {
    return this.merchantService.updateMerchant(id, data);
  }

  /**
   * Delete merchant
   * @param id Merchant ID
   */
  protected async deleteEntity(id: string): Promise<void> {
    await this.merchantService.deleteMerchant(id);
  }

  /**
   * Parse filters from request
   * @param req Request
   * @returns Filters
   */
  protected parseFilters(req: Request): Record<string, any> {
    const { page, limit, sortBy, sortOrder, search, status } = req.query;

    return {
      search: search as string,
      status: status as string
    };
  }

  /**
   * Validate create input
   * @param req Request
   */
  protected validateCreateInput(req: Request): void {
    const { email } = req.body;

    // Validate email format
    if (email && !this.isValidEmail(email)) {
      throw new AppError({
            message: "Invalid email format",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }
  }

  /**
   * Validate update input
   * @param req Request
   */
  protected validateUpdateInput(req: Request): void {
    const { email, status } = req.body;

    // Validate email format
    if (email && !this.isValidEmail(email)) {
      throw new AppError({
            message: "Invalid email format",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }

    // Validate status
    if (status && !["PENDING", "ACTIVE", "SUSPENDED"].includes(status)) {
      throw new AppError({
            message: "Invalid status",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }
  }

  /**
   * Validate email format
   * @param email Email
   * @returns Whether email is valid
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

export default MerchantController;
