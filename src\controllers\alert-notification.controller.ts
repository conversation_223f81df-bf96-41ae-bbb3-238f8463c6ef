// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from "./base.controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/errors/AppError';
import { BaseController } from "./base.controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/errors/AppError';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * AlertNotificationController
 * Controller for handling alert notifications
 */
export class AlertNotificationController extends BaseController {
  constructor() {
    super();
  }

  /**
   * Get notifications for a user
   */
  getNotifications = asyncHandler(async (req: Request, res: Response) => {
    try {
      const userId: unknown = req.user?.id;

      if (!userId) {
        throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
      }

      // Return empty array for now as placeholder
      return res.status(200).json({
        success: true,
        data: []
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
            message: "Failed to get notifications",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });
}

export default new AlertNotificationController();
