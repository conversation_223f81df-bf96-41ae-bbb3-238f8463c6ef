// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { ServiceError as ImportedServiceError } from '../services/(base).service';
import { PaginationInfo as ImportedPaginationInfo } from '../middlewares/apiResponseMiddleware';

/**
 * Base controller class
 */
export class BaseController {
  /**
   * Handle async route handler
   * @param fn Async route handler function
   * @returns Express route handler
   */
  protected asyncHandler(fn: (req: Request, res: Response, next: NextFunction)  =>  Promise<unknown>) {
    return (req: Request, res: Response, next: NextFunction)  =>  {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }

  /**
   * Send a success response
   * @param res Express response object
   * @param data Response data
   * @param message Success message
   * @param meta Response metadata
   */
  protected sendSuccess<T>(
    res: Response,
    data?: T,
    message?: string,
    meta?: Record<string, unknown>
  ): void {
    res.status(200).json({
      success: true,
      data,
      message,
      meta,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send a paginated response
   * @param res Express response object
   * @param data Response data
   * @param pagination Pagination information
   * @param message Success message
   * @param meta Response metadata
   */
  protected sendPaginated<T>(
    res: Response,
    data: T,
    pagination: PaginationInfo,
    message?: string,
    meta?: Record<string, unknown>
  ): void {
    res.status(200).json({
      success: true,
      data,
      pagination,
      message,
      meta,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send an error response
   * @param res Express response object
   * @param error Service error
   */
  protected sendError(res: Response, error: ServiceError): void {
    if ((error).validationErrors) {
      res.status(400).json({
        success: false,
        error: error.message || 'Validation failed',
        errorCode: (error).errorCode,
        validationErrors: (error).validationErrors,
        timestamp: new Date().toISOString(),
      });
    } else if ((error).errorCode === 'NOT_FOUND' && (error).resource) {
      const message = (error).id
        ? `${(error).resource} with ID ${(error).id} not found`
        : `${(error).resource} not found`;

      res.status(404).json({
        success: false,
        error: message,
        errorCode: (error).errorCode,
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status((error).statusCode || 500).json({
        success: false,
        error: error.message,
        errorCode: (error).errorCode,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Create pagination info from request query parameters
   * @param req Express request object
   * @param total Total number of items
   * @returns Pagination information
   */
  protected createPaginationInfo(req: Request, total: number): PaginationInfo {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const totalPages = Math.ceil(total / limit);

    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * Get pagination parameters from request query parameters
   * @param req Express request object
   * @returns Pagination parameters
   */
  protected getPaginationParams(req): {
    skip: number;
    take: number;
    page: number;
    limit: number;
  } {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    return {
      skip,
      take: limit,
      page,
      limit,
    };
  }

  /**
   * Get sort parameters from request query parameters
   * @param req Express request object
   * @param defaultField Default sort field
   * @param defaultOrder Default sort order
   * @returns Sort parameters
   */
  protected getSortParams(
    req: Request,
    defaultField: string = 'createdAt',
    defaultOrder: 'asc' | 'desc' = 'desc'
  ): { field: string; order: 'asc' | 'desc' } {
    const field = (req.query.sortBy as string) || defaultField;
    const orderParam = (req.query.sortOrder as string)?.toLowerCase();
    const order = orderParam === 'asc' ? 'asc' : 'desc';

    return {
      field,
      order,
    };
  }

  /**
   * Get filter parameters from request query parameters
   * @param req Express request object
   * @param allowedFields Allowed filter fields
   * @returns Filter parameters
   */
  protected getFilterParams(req: Request, allowedFields: string[]): Record<string, unknown> {
    const filters: Record<string, unknown> = {};

    (allowedFields).forEach((field)  =>  {
      if (req.query[field] !== undefined) {
        filters[field] = req.query[field];
      }
    });

    return filters;
  }

  /**
   * Get search parameters from request query parameters
   * @param req Express request object
   * @param searchFields Search fields
   * @returns Search parameters
   */
  protected getSearchParams(req: Request, searchFields: string[]): Record<string, unknown> | null {
    const searchTerm = req.query.search as string;

    if (!searchTerm) {
      return null;
    }

    const searchParams: Record<string, unknown> = {
      OR: (searchFields).map((field)  =>  ({
        [field]: {
          contains: searchTerm,
          mode: 'insensitive',
        },
      })),
    };

    return searchParams;
  }
}
