import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import { DashboardController as ImportedDashboardController } from '../controllers/(dashboard as any).controller';
import { DashboardWidgetController as ImportedDashboardWidgetController } from '../controllers/dashboard-(widget as any).controller';

// Mock Prisma
const mockPrisma = {
  dashboard: {
    findMany: (vi as any).fn(),
    findUnique: (vi as any).fn(),
    create: (vi as any).fn(),
    update: (vi as any).fn(),
    delete: (vi as any).fn(),
  },
  dashboardWidget: {
    findMany: (vi as any).fn(),
    findUnique: (vi as any).fn(),
    findFirst: (vi as any).fn(),
    create: (vi as any).fn(),
    update: (vi as any).fn(),
    delete: (vi as any).fn(),
    deleteMany: (vi as any).fn(),
  },
};

(vi as any).mock('@prisma/client', () => ({
  PrismaClient: (vi as any).fn(() => mockPrisma),
}));

const app = express();
(app as any).use((express as any).json());

// Mock auth middleware
const mockAuthMiddleware = (req: Request, res: Response, next: NextFunction) => {
  req.user = {
    id: 'user-1',
    role: 'MERCHANT',
    email: 'test@(example as any).com',
  };
  next();
};

// Setup routes
const dashboardController = new DashboardController();
const widgetController = new DashboardWidgetController();

(app as any).get('/api/dashboards', mockAuthMiddleware, (dashboardController as any).getDashboards);
(app as any).get('/api/dashboards/:id', mockAuthMiddleware, (dashboardController as any).getDashboardById);
(app as any).post('/api/dashboards', mockAuthMiddleware, (dashboardController as any).createDashboard);
(app as any).put('/api/dashboards/:id', mockAuthMiddleware, (dashboardController as any).updateDashboard);
(app as any).delete('/api/dashboards/:id', mockAuthMiddleware, (dashboardController as any).deleteDashboard);

(app as any).get('/api/dashboards/:dashboardId/widgets', mockAuthMiddleware, (widgetController as any).getWidgets);
(app as any).get('/api/dashboards/widgets/:id', mockAuthMiddleware, (widgetController as any).getWidgetById);
(app as any).post('/api/dashboards/:dashboardId/widgets', mockAuthMiddleware, (widgetController as any).createWidget);
(app as any).put('/api/dashboards/widgets/:id', mockAuthMiddleware, (widgetController as any).updateWidget);
(app as any).delete('/api/dashboards/widgets/:id', mockAuthMiddleware, (widgetController as any).deleteWidget);
(app as any).post('/api/dashboards/:dashboardId/widgets/reorder', mockAuthMiddleware, (widgetController as any).reorderWidgets);

describe('DashboardController', () => {
  beforeEach(() => {
    (vi as any).clearAllMocks();
  });

  describe('GET /api/dashboards', () => {
    it('should get dashboards for the current user', async () => {
      const mockDashboards = [
        {
          id: 'dashboard-1',
          name: 'My Dashboard',
          createdById: 'user-1',
          isPublic: false,
        },
        {
          id: 'dashboard-2',
          name: 'Public Dashboard',
          createdById: 'user-2',
          isPublic: true,
        },
      ];

      (mockPrisma as any).dashboard.(findMany as any).mockResolvedValue(mockDashboards);

      const response = await request(app).get('/api/dashboards');

      expect(response.status).toBe(200);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data).toEqual(mockDashboards);
      expect((mockPrisma as any).dashboard.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { createdById: 'user-1' },
            { isPublic: true },
          ],
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    });
  });

  describe('GET /api/dashboards/:id', () => {
    it('should get a dashboard by ID with widgets', async () => {
      const mockDashboard = {
        id: 'dashboard-1',
        name: 'My Dashboard',
        createdById: 'user-1',
        isPublic: false,
        widgets: [
          {
            id: 'widget-1',
            title: 'Transaction Chart',
            type: 'CHART',
            position: 0,
          },
        ],
      };

      (mockPrisma as any).dashboard.(findUnique as any).mockResolvedValue(mockDashboard);

      const response = await request(app).get('/api/dashboards/dashboard-1');

      expect(response.status).toBe(200);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data).toEqual(mockDashboard);
      expect((mockPrisma as any).dashboard.findUnique).toHaveBeenCalledWith({
        where: { id: 'dashboard-1' },
        include: {
          widgets: {
            orderBy: {
              position: 'asc',
            },
          },
        },
      });
    });

    it('should return 404 for non-existent dashboard', async () => {
      (mockPrisma as any).dashboard.(findUnique as any).mockResolvedValue(null);

      const response = await request(app).get('/api/dashboards/non-existent');

      expect(response.status).toBe(404);
      expect((response as any).body.success).toBe(false);
      expect((response as any).body.message).toBe('Dashboard not found');
    });

    it('should return 403 for private dashboard not owned by user', async () => {
      const mockDashboard = {
        id: 'dashboard-1',
        name: 'Private Dashboard',
        createdById: 'user-2',
        isPublic: false,
      };

      (mockPrisma as any).dashboard.(findUnique as any).mockResolvedValue(mockDashboard);

      const response = await request(app).get('/api/dashboards/dashboard-1');

      expect(response.status).toBe(403);
      expect((response as any).body.success).toBe(false);
      expect((response as any).body.message).toBe('Access denied');
    });
  });

  describe('POST /api/dashboards', () => {
    it('should create a new dashboard', async () => {
      const dashboardData = {
        name: 'New Dashboard',
        description: 'Test Description',
        layout: { columns: 2 },
        isPublic: false,
      };

      const mockDashboard = {
        id: 'dashboard-1',
        ...dashboardData,
        createdById: 'user-1',
      };

      (mockPrisma as any).dashboard.(create as any).mockResolvedValue(mockDashboard);

      const response = await request(app)
        .post('/api/dashboards')
        .send(dashboardData);

      expect(response.status).toBe(201);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data).toEqual(mockDashboard);
      expect((mockPrisma as any).dashboard.create).toHaveBeenCalledWith({
        data: {
          ...dashboardData,
          createdById: 'user-1',
        },
      });
    });
  });

  describe('PUT /api/dashboards/:id', () => {
    it('should update a dashboard', async () => {
      const existingDashboard = {
        id: 'dashboard-1',
        name: 'Old Name',
        createdById: 'user-1',
      };

      const updateData = {
        name: 'Updated Name',
        description: 'Updated Description',
      };

      const updatedDashboard = {
        ...existingDashboard,
        ...updateData,
      };

      (mockPrisma as any).dashboard.(findUnique as any).mockResolvedValue(existingDashboard);
      (mockPrisma as any).dashboard.(update as any).mockResolvedValue(updatedDashboard);

      const response = await request(app)
        .put('/api/dashboards/dashboard-1')
        .send(updateData);

      expect(response.status).toBe(200);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data).toEqual(updatedDashboard);
      expect((mockPrisma as any).dashboard.update).toHaveBeenCalledWith({
        where: { id: 'dashboard-1' },
        data: updateData,
      });
    });

    it('should return 403 for dashboard not owned by user', async () => {
      const existingDashboard = {
        id: 'dashboard-1',
        name: 'Dashboard',
        createdById: 'user-2',
      };

      (mockPrisma as any).dashboard.(findUnique as any).mockResolvedValue(existingDashboard);

      const response = await request(app)
        .put('/api/dashboards/dashboard-1')
        .send({ name: 'Updated Name' });

      expect(response.status).toBe(403);
      expect((response as any).body.success).toBe(false);
      expect((response as any).body.message).toBe('Access denied');
    });
  });

  describe('DELETE /api/dashboards/:id', () => {
    it('should delete a dashboard and its widgets', async () => {
      const existingDashboard = {
        id: 'dashboard-1',
        name: 'Dashboard',
        createdById: 'user-1',
      };

      (mockPrisma as any).dashboard.(findUnique as any).mockResolvedValue(existingDashboard);
      (mockPrisma as any).dashboardWidget.(deleteMany as any).mockResolvedValue({ count: 2 });
      (mockPrisma as any).dashboard.(delete as any).mockResolvedValue(existingDashboard);

      const response = await request(app).delete('/api/dashboards/dashboard-1');

      expect(response.status).toBe(200);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.message).toBe('Dashboard deleted successfully');
      expect((mockPrisma as any).dashboardWidget.deleteMany).toHaveBeenCalledWith({
        where: { dashboardId: 'dashboard-1' },
      });
      expect((mockPrisma as any).dashboard.delete).toHaveBeenCalledWith({
        where: { id: 'dashboard-1' },
      });
    });
  });
});

describe('DashboardWidgetController', () => {
  beforeEach(() => {
    (vi as any).clearAllMocks();
  });

  describe('GET /api/dashboards/:dashboardId/widgets', () => {
    it('should get widgets for a dashboard', async () => {
      const mockDashboard = {
        id: 'dashboard-1',
        createdById: 'user-1',
        isPublic: false,
      };

      const mockWidgets = [
        {
          id: 'widget-1',
          title: 'Chart Widget',
          type: 'CHART',
          position: 0,
        },
        {
          id: 'widget-2',
          title: 'Table Widget',
          type: 'TABLE',
          position: 1,
        },
      ];

      (mockPrisma as any).dashboard.(findUnique as any).mockResolvedValue(mockDashboard);
      (mockPrisma as any).dashboardWidget.(findMany as any).mockResolvedValue(mockWidgets);

      const response = await request(app).get('/api/dashboards/dashboard-1/widgets');

      expect(response.status).toBe(200);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data).toEqual(mockWidgets);
      expect((mockPrisma as any).dashboardWidget.findMany).toHaveBeenCalledWith({
        where: { dashboardId: 'dashboard-1' },
        orderBy: { position: 'asc' },
      });
    });
  });

  describe('POST /api/dashboards/:dashboardId/widgets', () => {
    it('should create a new widget', async () => {
      const mockDashboard = {
        id: 'dashboard-1',
        createdById: 'user-1',
      };

      const widgetData = {
        title: 'New Widget',
        type: 'CHART',
        config: { chartType: 'line' },
        width: 2,
        height: 1,
      };

      const mockWidget = {
        id: 'widget-1',
        dashboardId: 'dashboard-1',
        ...widgetData,
        position: 0,
      };

      (mockPrisma as any).dashboard.(findUnique as any).mockResolvedValue(mockDashboard);
      (mockPrisma as any).dashboardWidget.(findFirst as any).mockResolvedValue(null);
      (mockPrisma as any).dashboardWidget.(create as any).mockResolvedValue(mockWidget);

      const response = await request(app)
        .post('/api/dashboards/dashboard-1/widgets')
        .send(widgetData);

      expect(response.status).toBe(201);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data).toEqual(mockWidget);
      expect((mockPrisma as any).dashboardWidget.create).toHaveBeenCalledWith({
        data: {
          dashboardId: 'dashboard-1',
          ...widgetData,
          position: 0,
        },
      });
    });
  });

  describe('POST /api/dashboards/:dashboardId/widgets/reorder', () => {
    it('should reorder widgets', async () => {
      const mockDashboard = {
        id: 'dashboard-1',
        createdById: 'user-1',
      };

      const widgetsData = [
        { id: 'widget-1', position: 1 },
        { id: 'widget-2', position: 0 },
      ];

      (mockPrisma as any).dashboard.(findUnique as any).mockResolvedValue(mockDashboard);
      (mockPrisma as any).dashboardWidget.(update as any).mockResolvedValue({});

      const response = await request(app)
        .post('/api/dashboards/dashboard-1/widgets/reorder')
        .send({ widgets: widgetsData });

      expect(response.status).toBe(200);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.message).toBe('Widgets reordered successfully');
      expect((mockPrisma as any).dashboardWidget.update).toHaveBeenCalledTimes(2);
    });
  });
});
