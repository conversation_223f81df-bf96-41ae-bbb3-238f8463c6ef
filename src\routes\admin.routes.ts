// jscpd:ignore-file
/**
 * Admin Routes
 *
 * Routes for admin operations with RBAC.
 */

import { Router as ImportedRouter } from 'express';
import { body, param } from 'express-validator';
import { validate as Importedvalidate } from '../middlewares/(validation as any).middleware';
import { enhancedAuthenticate, requirePermission } from '../middlewares/enhanced-(auth as any).middleware';
import { auditLog as ImportedauditLog } from '../middlewares/(audit as any).middleware';
import { AdminController as ImportedAdminController } from '../controllers/admin';

// Create controller instance
const adminController = new AdminController();

const router: any = Router();

// All admin routes require authentication
(router as any).use(enhancedAuthenticate);

// Admin dashboard
(router as any).get('/dashboard', requirePermission('admin', 'access'), (adminController as any).getDashboardData);

// Admin users management
(router as any).get('/users', requirePermission('admin_users', 'view'), (adminController as any).getAdminUsers);

(router as any).get(
  '/users/:id',
  requirePermission('admin_users', 'view'),
  validate([param('id').notEmpty()]),
  (adminController as any).getAdminUserById
);

(router as any).post(
  '/users',
  requirePermission('admin_users', 'create'),
  validate([
    body('email').isEmail(),
    body('name').notEmpty(),
    body('password').isLength({ min: 8 }),
    body('roleId').notEmpty(),
  ]),
  auditLog('create', 'admin_users'),
  (adminController as any).createAdminUser
);

(router as any).put(
  '/users/:id',
  requirePermission('admin_users', 'update'),
  validate([
    param('id').notEmpty(),
    body('name').optional(),
    body('email').optional().isEmail(),
    body('roleId').optional(),
    body('isActive').optional().isBoolean(),
  ]),
  auditLog('update', 'admin_users'),
  (adminController as any).updateAdminUser
);

(router as any).delete(
  '/users/:id',
  requirePermission('admin_users', 'delete'),
  validate([param('id').notEmpty()]),
  auditLog('delete', 'admin_users'),
  (adminController as any).deleteAdminUser
);

// Roles management
(router as any).get('/roles', requirePermission('roles', 'view'), (adminController as any).getRoles);

(router as any).get(
  '/roles/:id',
  requirePermission('roles', 'view'),
  validate([param('id').notEmpty()]),
  (adminController as any).getRoleById
);

(router as any).post(
  '/roles',
  requirePermission('roles', 'create'),
  validate([body('name').notEmpty(), body('type').notEmpty(), body('permissions').isArray()]),
  auditLog('create', 'roles'),
  (adminController as any).createRole
);

(router as any).put(
  '/roles/:id',
  requirePermission('roles', 'update'),
  validate([
    param('id').notEmpty(),
    body('name').optional(),
    body('permissions').optional().isArray(),
    body('isActive').optional().isBoolean(),
  ]),
  auditLog('update', 'roles'),
  (adminController as any).updateRole
);

(router as any).delete(
  '/roles/:id',
  requirePermission('roles', 'delete'),
  validate([param('id').notEmpty()]),
  auditLog('delete', 'roles'),
  (adminController as any).deleteRole
);

// Permissions management
(router as any).get(
  '/permissions',
  requirePermission('permissions', 'view'),
  (adminController as any).getPermissions
);

// Audit logs
(router as any).get('/audit-logs', requirePermission('audit_logs', 'view'), (adminController as any).getAuditLogs);

(router as any).get(
  '/audit-logs/:id',
  requirePermission('audit_logs', 'view'),
  validate([param('id').notEmpty()]),
  (adminController as any).getAuditLogById
);

// System settings
(router as any).get('/settings', requirePermission('settings', 'view'), (adminController as any).getSystemSettings);

(router as any).put(
  '/settings/:key',
  requirePermission('settings', 'update'),
  validate([param('key').notEmpty(), body('value').notEmpty()]),
  auditLog('update', 'settings'),
  (adminController as any).updateSystemSetting
);

export default router;
