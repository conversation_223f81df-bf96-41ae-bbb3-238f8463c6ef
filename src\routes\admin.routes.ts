// jscpd:ignore-file
/**
 * Admin Routes
 *
 * Routes for admin operations with RBAC.
 */

import { Router as ImportedRouter } from 'express';
import { body, param } from 'express-validator';
import { validate as Importedvalidate } from '../middlewares/(validation).middleware';
import { enhancedAuthenticate, requirePermission } from '../middlewares/enhanced-(auth).middleware';
import { auditLog as ImportedauditLog } from '../middlewares/(audit).middleware';
import { AdminController as ImportedAdminController } from '../controllers/admin';

// Create controller instance
const adminController = new AdminController();

const router = Router();

// All admin routes require authentication
(router).use(enhancedAuthenticate);

// Admin dashboard
(router).get('/dashboard', requirePermission('admin', 'access'), (adminController).getDashboardData);

// Admin users management
(router).get('/users', requirePermission('admin_users', 'view'), (adminController).getAdminUsers);

(router).get(
  '/users/:id',
  requirePermission('admin_users', 'view'),
  validate([param('id').notEmpty()]),
  (adminController).getAdminUserById
);

(router).post(
  '/users',
  requirePermission('admin_users', 'create'),
  validate([
    body('email').isEmail(),
    body('name').notEmpty(),
    body('password').isLength({ min: 8 }),
    body('roleId').notEmpty(),
  ]),
  auditLog('create', 'admin_users'),
  (adminController).createAdminUser
);

(router).put(
  '/users/:id',
  requirePermission('admin_users', 'update'),
  validate([
    param('id').notEmpty(),
    body('name').optional(),
    body('email').optional().isEmail(),
    body('roleId').optional(),
    body('isActive').optional().isBoolean(),
  ]),
  auditLog('update', 'admin_users'),
  (adminController).updateAdminUser
);

(router).delete(
  '/users/:id',
  requirePermission('admin_users', 'delete'),
  validate([param('id').notEmpty()]),
  auditLog('delete', 'admin_users'),
  (adminController).deleteAdminUser
);

// Roles management
(router).get('/roles', requirePermission('roles', 'view'), (adminController).getRoles);

(router).get(
  '/roles/:id',
  requirePermission('roles', 'view'),
  validate([param('id').notEmpty()]),
  (adminController).getRoleById
);

(router).post(
  '/roles',
  requirePermission('roles', 'create'),
  validate([body('name').notEmpty(), body('type').notEmpty(), body('permissions').isArray()]),
  auditLog('create', 'roles'),
  (adminController).createRole
);

(router).put(
  '/roles/:id',
  requirePermission('roles', 'update'),
  validate([
    param('id').notEmpty(),
    body('name').optional(),
    body('permissions').optional().isArray(),
    body('isActive').optional().isBoolean(),
  ]),
  auditLog('update', 'roles'),
  (adminController).updateRole
);

(router).delete(
  '/roles/:id',
  requirePermission('roles', 'delete'),
  validate([param('id').notEmpty()]),
  auditLog('delete', 'roles'),
  (adminController).deleteRole
);

// Permissions management
(router).get(
  '/permissions',
  requirePermission('permissions', 'view'),
  (adminController).getPermissions
);

// Audit logs
(router).get('/audit-logs', requirePermission('audit_logs', 'view'), (adminController).getAuditLogs);

(router).get(
  '/audit-logs/:id',
  requirePermission('audit_logs', 'view'),
  validate([param('id').notEmpty()]),
  (adminController).getAuditLogById
);

// System settings
(router).get('/settings', requirePermission('settings', 'view'), (adminController).getSystemSettings);

(router).put(
  '/settings/:key',
  requirePermission('settings', 'update'),
  validate([param('key').notEmpty(), body('value').notEmpty()]),
  auditLog('update', 'settings'),
  (adminController).updateSystemSetting
);

export default router;
