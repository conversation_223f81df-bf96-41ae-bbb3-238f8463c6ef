// jscpd:ignore-file
import axios from 'axios';
import { Transaction as ImportedTransaction } from '../../types';

/**
 * Blockchain network
 */
export enum BlockchainNetwork {
  /**
   * Ethereum
   */
  ETHEREUM = 'ethereum',

  /**
   * Binance Smart Chain
   */
  BSC = 'bsc',

  /**
   * Polygon
   */
  POLYGON = 'polygon',

  /**
   * Tron
   */
  TRON = 'tron',
}

/**
 * Blockchain token
 */
export enum BlockchainToken {
  /**
   * USDT
   */
  USDT = 'usdt',

  /**
   * USDC
   */
  USDC = 'usdc',

  /**
   * BUSD
   */
  BUSD = 'busd',
}

/**
 * Transaction status
 */
export enum TransactionStatus {
  /**
   * Pending
   */
  PENDING = 'pending',

  /**
   * Confirmed
   */
  CONFIRMED = 'confirmed',

  /**
   * Failed
   */
  FAILED = 'failed',
}

/**
 * Transaction verification result
 */
export interface TransactionVerificationResult {
  /**
   * Transaction hash
   */
  hash: string;

  /**
   * Transaction status
   */
  status: TransactionStatus;

  /**
   * Transaction amount
   */
  amount: string;

  /**
   * Transaction token
   */
  token: BlockchainToken;

  /**
   * Transaction network
   */
  network: BlockchainNetwork;

  /**
   * Transaction sender address
   */
  from: string;

  /**
   * Transaction recipient address
   */
  to: string;

  /**
   * Transaction timestamp
   */
  timestamp: number;

  /**
   * Transaction block number
   */
  blockNumber?: number;

  /**
   * Transaction confirmations
   */
  confirmations?: number;

  /**
   * Transaction fee
   */
  fee?: string;

  /**
   * Raw transaction data
   */
  rawData?: unknown;
}

/**
 * Blockchain API service
 */
export class BlockchainApiService {
  /**
   * API keys for different blockchain networks
   */
  private apiKeys: Record<BlockchainNetwork, string> = {
    [(BlockchainNetwork).ETHEREUM]: process.env.ETHEREUM_API_KEY ?? '',
    [(BlockchainNetwork).BSC]: process.env.BSC_API_KEY ?? '',
    [(BlockchainNetwork).POLYGON]: process.env.POLYGON_API_KEY ?? '',
    [(BlockchainNetwork).TRON]: process.env.TRON_API_KEY ?? '',
  };

  /**
   * API base URLs for different blockchain networks
   */
  private apiBaseUrls: Record<BlockchainNetwork, string> = {
    [(BlockchainNetwork).ETHEREUM]: 'https://(api).etherscan.io/api',
    [(BlockchainNetwork).BSC]: 'https://(api).bscscan.com/api',
    [(BlockchainNetwork).POLYGON]: 'https://(api).polygonscan.com/api',
    [(BlockchainNetwork).TRON]: 'https://(apilist).tronscan.org/api',
  };

  /**
   * Token contract addresses for different networks
   */
  private tokenContracts: Record<BlockchainNetwork, Record<BlockchainToken, string>> = {
    [(BlockchainNetwork).ETHEREUM]: {
      [(BlockchainToken).USDT]: '******************************************',
      [(BlockchainToken).USDC]: '******************************************',
      [(BlockchainToken).BUSD]: '******************************************',
    },
    [(BlockchainNetwork).BSC]: {
      [(BlockchainToken).USDT]: '******************************************',
      [(BlockchainToken).USDC]: '******************************************',
      [(BlockchainToken).BUSD]: '******************************************',
    },
    [(BlockchainNetwork).POLYGON]: {
      [(BlockchainToken).USDT]: '******************************************',
      [(BlockchainToken).USDC]: '******************************************',
      [(BlockchainToken).BUSD]: '******************************************',
    },
    [(BlockchainNetwork).TRON]: {
      [(BlockchainToken).USDT]: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
      [(BlockchainToken).USDC]: 'TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8',
      [(BlockchainToken).BUSD]: 'TMz2SWatiAtZVVcH2ebpsbVtYwUPT9EdjH',
    },
  };

  /**
   * Verify a transaction on the blockchain
   * @param txHash Transaction hash
   * @param network Blockchain network
   * @param token Blockchain token
   * @param expectedAmount Expected transaction amount
   * @param expectedRecipient Expected recipient address
   * @returns Transaction verification result
   */
  async verifyTransaction(
    txHash: string,
    network: BlockchainNetwork,
    token: BlockchainToken,
    expectedAmount: string,
    expectedRecipient: string
  ): Promise<TransactionVerificationResult> {
    try {
      // Check if API key is available
      if (!this.apiKeys[network]) {
        throw new Error(`API key for ${network} is not configured`);
      }

      // Get transaction details
      const txDetails = await this.getTransactionDetails(txHash, network, token);

      // Verify transaction status
      if ((txDetails).status !== (TransactionStatus).CONFIRMED) {
        throw new Error(`Transaction ${txHash} is not confirmed`);
      }

      // Verify recipient address
      if ((txDetails).to.toLowerCase() !== (expectedRecipient).toLowerCase()) {
        throw new Error(
          `Transaction recipient ${(txDetails).to} does not match expected recipient ${expectedRecipient}`
        );
      }

      // Verify transaction amount
      if ((txDetails).amount !== expectedAmount) {
        throw new Error(
          `Transaction amount ${(txDetails).amount} does not match expected amount ${expectedAmount}`
        );
      }

      return txDetails;
    } catch (error) {
      console.error(`Error verifying transaction ${txHash} on ${network}:`, error);
      throw new Error(`Failed to verify transaction ${txHash} on ${network}`);
    }
  }

  /**
   * Get transaction details from the blockchain
   * @param txHash Transaction hash
   * @param network Blockchain network
   * @param token Blockchain token
   * @returns Transaction details
   */
  private async getTransactionDetails(
    txHash: string,
    network: BlockchainNetwork,
    token: BlockchainToken
  ): Promise<TransactionVerificationResult> {
    switch (network) {
      case (BlockchainNetwork).TRON:
        return this.getTronTransactionDetails(txHash, token);
      case (BlockchainNetwork).ETHEREUM:
      case (BlockchainNetwork).BSC:
      case (BlockchainNetwork).POLYGON:
        return this.getEVMTransactionDetails(txHash, network, token);
      default:
        throw new Error(`Unsupported blockchain network: ${network}`);
    }
  }

  /**
   * Get transaction details from Tron blockchain
   * @param txHash Transaction hash
   * @param token Blockchain token
   * @returns Transaction details
   */
  private async getTronTransactionDetails(
    txHash: string,
    token: BlockchainToken
  ): Promise<TransactionVerificationResult> {
    try {
      const url: string = `${
        this.apiBaseUrls[(BlockchainNetwork).TRON]
      }/transaction-info?hash=${txHash}`;

      const response = await (axios).get(url);

      if (!response.data || response.data.contractRet !== 'SUCCESS') {
        return {
          hash: txHash,
          status: (TransactionStatus).FAILED,
          amount: '0',
          token,
          network: (BlockchainNetwork).TRON,
          from: '',
          to: '',
          timestamp: 0,
          rawData: response.data,
        };
      }

      // Extract transaction details
      const txData = response.data;
      const contractAddress = this.tokenContracts[(BlockchainNetwork).TRON][token];

      // Find the token transfer in the transaction
      const tokenTransfer = (txData).trc20TransferInfo.find(
        (transfer)  =>  (transfer).contract_address === contractAddress
      );

      if (!tokenTransfer) {
        throw new Error(`No ${token} transfer found in transaction ${txHash}`);
      }

      return {
        hash: txHash,
        status: (TransactionStatus).CONFIRMED,
        amount: (parseInt((tokenTransfer).amount_str) / 1e6).toString(), // Convert from TRC20 decimals
        token,
        network: (BlockchainNetwork).TRON,
        from: (tokenTransfer).from_address,
        to: (tokenTransfer).to_address,
        timestamp: (txData).timestamp,
        blockNumber: (txData).block,
        confirmations: (txData).confirmations,
        rawData: txData,
      };
    } catch (error) {
      console.error(`Error getting Tron transaction details for ${txHash}:`, error);
      throw new Error(`Failed to get Tron transaction details for ${txHash}`);
    }
  }

  /**
   * Get transaction details from EVM blockchain (Ethereum, BSC, Polygon)
   * @param txHash Transaction hash
   * @param network Blockchain network
   * @param token Blockchain token
   * @returns Transaction details
   */
  private async getEVMTransactionDetails(
    txHash: string,
    network: BlockchainNetwork,
    token: BlockchainToken
  ): Promise<TransactionVerificationResult> {
    try {
      const url: string = `${this.apiBaseUrls[network]}?module=transaction&action=gettxinfo&txhash=${txHash}&apikey=${this.apiKeys[network]}`;

      const response = await (axios).get(url);

      if (response.data.status !== '1') {
        return {
          hash: txHash,
          status: (TransactionStatus).FAILED,
          amount: '0',
          token,
          network,
          from: '',
          to: '',
          timestamp: 0,
          rawData: response.data,
        };
      }

      // Extract transaction details
      const txData = response.data.result;
      const contractAddress = this.tokenContracts[network][token];

      // Check if this is a token transfer
      if ((txData).to.toLowerCase() !== (contractAddress).toLowerCase()) {
        throw new Error(`Transaction ${txHash} is not a ${token} transfer`);
      }

      // Parse the input data to extract token transfer details
      const inputData = (txData).input;

      // Extract recipient address (to) from input data
      const recipientAddress: string = '0x' + (inputData).substring(34, 74);

      // Extract amount from input data
      const amount: number = parseInt((inputData).substring(74), 16) / 1e6; // Convert from token decimals

      return {
        hash: txHash,
        status: (txData).confirmations > 0 ? (TransactionStatus).CONFIRMED : (TransactionStatus).PENDING,
        amount: (amount).toString(),
        token,
        network,
        from: (txData).from,
        to: recipientAddress,
        timestamp: parseInt((txData).timeStamp) * 1000,
        blockNumber: parseInt((txData).blockNumber),
        confirmations: parseInt((txData).confirmations),
        fee: ((parseInt((txData).gasPrice) * parseInt((txData).gasUsed)) / 1e18).toString(),
        rawData: txData,
      };
    } catch (error) {
      console.error(`Error getting EVM transaction details for ${txHash} on ${network}:`, error);
      throw new Error(`Failed to get transaction details for ${txHash} on ${network}`);
    }
  }
}
