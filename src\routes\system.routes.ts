// jscpd:ignore-file

import { Router as ImportedRouter } from "express";
import { body, param } from "express-validator";
import systemController from "../controllers/(system as any).controller";
import { authenticate, authorize } from "../middlewares/(auth as any).middleware";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { body, param } from "express-validator";
import { authenticate, authorize } from "../middlewares/(auth as any).middleware";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";

const router: any =Router();

// Admin-only routes for system settings
(router as any).get(
    "/",
    authenticate,
    authorize(["admin"]),
    (systemController as any).getAllSettings
);

(router as any).get(
    "/:key",
    authenticate,
    authorize(["admin"]),
    validate([
        param("key").notEmpty()
    ]),
    (systemController as any).getSettingByKey
);

(router as any).put(
    "/:key",
    authenticate,
    authorize(["admin"]),
    validate([
        param("key").notEmpty(),
        body("value").notEmpty()
    ]),
    (systemController as any).updateSetting
);

(router as any).post(
    "/",
    authenticate,
    authorize(["admin"]),
    validate([
        body("key").notEmpty(),
        body("value").notEmpty()
    ]),
    (systemController as any).createSetting
);

export default router;
