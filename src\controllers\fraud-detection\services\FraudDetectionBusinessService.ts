/**
 * Fraud Detection Business Service
 *
 * Handles business logic for fraud detection operations.
 */

import { PrismaClient } from '@prisma/client';
import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import { FraudDetectionService } from '../../../services/fraud-detection';
import {
  AssessTransactionRiskRequest,
  UpdateFraudConfigRequest,
  RiskAssessmentResponse,
  FraudConfigResponse,
  FlaggedTransactionResponse,
  FraudStatisticsResponse,
  DailyFraudStats,
  FraudDetectionFilters,
  PaginationParams,
  RiskLevel,
} from '../types/FraudDetectionControllerTypes';

/**
 * Business service for fraud detection operations
 */
export class FraudDetectionBusinessService {
  private readonly fraudDetectionService: FraudDetectionService;
  private readonly prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.fraudDetectionService = new FraudDetectionService(this.prisma);
  }

  /**
   * Assess transaction risk
   */
  async assessTransactionRisk(data: AssessTransactionRiskRequest): Promise<any> {
    try {
      // Get transaction
      const transaction = await this.prisma.transaction.findUnique({
        where: { id: data.transactionId },
      });

      if (!transaction) {
        throw new AppError({
          message: 'Transaction not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      // Get merchant
      const merchant = await this.prisma.merchant.findUnique({
        where: { id: transaction.merchantId },
      });

      if (!merchant) {
        throw new AppError({
          message: 'Merchant not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      // Assess transaction risk using the fraud detection service
      const context = {
        transaction,
        merchant,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent ?? 'Unknown',
        deviceId: data.deviceId ?? 'Unknown',
        timestamp: new Date(),
      };

      const riskAssessment = await this.fraudDetectionService.assessTransactionRisk(context);

      return riskAssessment;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to assess transaction risk',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error },
      });
    }
  }

  /**
   * Get transaction risk assessment
   */
  async getTransactionRiskAssessment(transactionId: string): Promise<RiskAssessmentResponse> {
    try {
      const riskAssessment = await this.prisma.riskAssessment.findFirst({
        where: { transactionId },
        orderBy: { createdAt: 'desc' },
      });

      if (!riskAssessment) {
        throw new AppError({
          message: 'Risk assessment not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      return this.mapRiskAssessmentToResponse(riskAssessment);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to get transaction risk assessment',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error },
      });
    }
  }

  /**
   * Get merchant fraud configuration
   */
  async getMerchantFraudConfig(merchantId: number): Promise<FraudConfigResponse> {
    try {
      const config = await this.prisma.fraudDetectionConfig.findUnique({
        where: { merchantId },
      });

      if (!config) {
        throw new AppError({
          message: 'Fraud detection configuration not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      return this.mapFraudConfigToResponse(config);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to get fraud detection configuration',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error },
      });
    }
  }

  /**
   * Update merchant fraud configuration
   */
  async updateMerchantFraudConfig(
    merchantId: number,
    data: UpdateFraudConfigRequest
  ): Promise<FraudConfigResponse> {
    try {
      // Check if merchant exists
      const merchant = await this.prisma.merchant.findUnique({
        where: { id: merchantId.toString() },
      });

      if (!merchant) {
        throw new AppError({
          message: 'Merchant not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      // Update or create fraud detection configuration
      const config = await this.prisma.fraudDetectionConfig.upsert({
        where: { merchantId },
        update: {
          flagThreshold: data.flagThreshold,
          blockThreshold: data.blockThreshold,
          autoBlock: data.autoBlock,
          factorWeights: data.factorWeights ? JSON.stringify(data.factorWeights) : undefined,
          highRiskCountries: data.highRiskCountries
            ? JSON.stringify(data.highRiskCountries)
            : undefined,
          highRiskIpRanges: data.highRiskIpRanges
            ? JSON.stringify(data.highRiskIpRanges)
            : undefined,
          maxTransactionAmount: data.maxTransactionAmount,
          maxTransactionsPerHour: data.maxTransactionsPerHour,
          maxTransactionsPerDay: data.maxTransactionsPerDay,
          updatedAt: new Date(),
        },
        create: {
          merchantId,
          flagThreshold: data.flagThreshold ?? 70,
          blockThreshold: data.blockThreshold ?? 90,
          autoBlock: data.autoBlock ?? false,
          factorWeights: JSON.stringify(data.factorWeights ?? {}),
          highRiskCountries: JSON.stringify(data.highRiskCountries ?? []),
          highRiskIpRanges: JSON.stringify(data.highRiskIpRanges ?? []),
          maxTransactionAmount: data.maxTransactionAmount ?? 10000,
          maxTransactionsPerHour: data.maxTransactionsPerHour ?? 100,
          maxTransactionsPerDay: data.maxTransactionsPerDay ?? 1000,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      return this.mapFraudConfigToResponse(config);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to update fraud detection configuration',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error },
      });
    }
  }

  /**
   * Get flagged transactions
   */
  async getFlaggedTransactions(
    filters?: FraudDetectionFilters,
    pagination?: PaginationParams
  ): Promise<{ transactions: FlaggedTransactionResponse[]; total: number }> {
    try {
      const where = {
        isFlagged: true,
      };

      // Apply filters
      if (filters?.merchantId) {
        where.transaction = {
          merchantId: filters.merchantId,
        };
      }

      if (filters?.riskLevel) {
        where.level = filters.riskLevel;
      }

      if (filters?.isBlocked !== undefined) {
        where.isBlocked = filters.isBlocked;
      }

      if (filters?.startDate || filters?.endDate) {
        where.createdAt = {};
        if (filters.startDate) where.createdAt.gte = filters.startDate;
        if (filters.endDate) where.createdAt.lte = filters.endDate;
      }

      if (filters?.minScore !== undefined || filters?.maxScore !== undefined) {
        where.score = {};
        if (filters.minScore !== undefined) where.score.gte = filters.minScore;
        if (filters.maxScore !== undefined) where.score.lte = filters.maxScore;
      }

      // Build query options
      const queryOptions = {
        where,
        include: {
          transaction: {
            include: {
              merchant: {
                select: { id: true, name: true },
              },
              paymentMethod: {
                select: { id: true, name: true, code: true },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      };

      // Apply pagination
      if (pagination) {
        const { page = 1, limit = 10, sortBy, sortOrder } = pagination;
        queryOptions.skip = (page - 1) * limit;
        queryOptions.take = limit;

        if (sortBy) {
          queryOptions.orderBy = { [sortBy]: sortOrder ?? 'desc' };
        }
      }

      // Execute queries
      const [riskAssessments, total] = await Promise.all([
        this.prisma.riskAssessment.findMany(queryOptions),
        this.prisma.riskAssessment.count({ where }),
      ]);

      return {
        transactions: riskAssessments.map(this.mapFlaggedTransactionToResponse),
        total,
      };
    } catch (error) {
      throw new AppError({
        message: 'Failed to get flagged transactions',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error },
      });
    }
  }

  /**
   * Get fraud statistics
   */
  async getFraudStatistics(
    merchantId?: number,
    startDate?: Date,
    endDate?: Date
  ): Promise<FraudStatisticsResponse> {
    try {
      const where = {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      };

      // Add merchant filter if provided
      if (merchantId) {
        where.transaction = {
          merchantId,
        };
      }

      // Get risk assessments
      const riskAssessments = await this.prisma.riskAssessment.findMany({
        where,
        select: {
          id: true,
          score: true,
          level: true,
          isFlagged: true,
          isBlocked: true,
          createdAt: true,
        },
      });

      // Calculate statistics
      const totalAssessments = riskAssessments.length;
      const flaggedCount = riskAssessments.filter((a) => a.isFlagged).length;
      const blockedCount = riskAssessments.filter((a) => a.isBlocked).length;

      const levelCounts = {
        ['LOW']: riskAssessments.filter((a) => a.level === 'LOW').length,
        ['MEDIUM']: riskAssessments.filter((a) => a.level === 'MEDIUM').length,
        ['HIGH']: riskAssessments.filter((a) => a.level === 'HIGH').length,
        ['CRITICAL']: riskAssessments.filter((a) => a.level === 'CRITICAL').length,
      };

      // Group by day
      const dailyStats = this.groupByDay(riskAssessments, startDate!, endDate!);

      return {
        totalAssessments,
        flaggedCount,
        blockedCount,
        flaggedRate: totalAssessments > 0 ? (flaggedCount / totalAssessments) * 100 : 0,
        blockedRate: totalAssessments > 0 ? (blockedCount / totalAssessments) * 100 : 0,
        levelCounts,
        dailyStats,
        period: {
          start: startDate!,
          end: endDate!,
        },
      };
    } catch (error) {
      throw new AppError({
        message: 'Failed to get fraud statistics',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error },
      });
    }
  }

  /**
   * Map risk assessment to response format
   */
  private mapRiskAssessmentToResponse(assessment: unknown): RiskAssessmentResponse {
    return {
      id: assessment.id,
      transactionId: assessment.transactionId,
      riskScore: {
        score: assessment.score,
        level: assessment.level as RiskLevel,
        factors: JSON.parse(assessment.factors ?? '[]'),
        timestamp: assessment.createdAt,
      },
      isFlagged: assessment.isFlagged,
      isBlocked: assessment.isBlocked,
      createdAt: assessment.createdAt,
      updatedAt: assessment.updatedAt,
    };
  }

  /**
   * Map fraud config to response format
   */
  private mapFraudConfigToResponse(config: Record<string, any>): FraudConfigResponse {
    return {
      merchantId: config.merchantId,
      flagThreshold: config.flagThreshold,
      blockThreshold: config.blockThreshold,
      autoBlock: config.autoBlock,
      factorWeights: JSON.parse(config.factorWeights ?? '{}'),
      highRiskCountries: JSON.parse(config.highRiskCountries ?? '[]'),
      highRiskIpRanges: JSON.parse(config.highRiskIpRanges ?? '[]'),
      maxTransactionAmount: config.maxTransactionAmount,
      maxTransactionsPerHour: config.maxTransactionsPerHour,
      maxTransactionsPerDay: config.maxTransactionsPerDay,
      createdAt: config.createdAt,
      updatedAt: config.updatedAt,
    };
  }

  /**
   * Map flagged transaction to response format
   */
  private mapFlaggedTransactionToResponse(assessment: unknown): FlaggedTransactionResponse {
    return {
      id: assessment.id,
      transactionId: assessment.transactionId,
      score: assessment.score,
      level: assessment.level,
      isFlagged: assessment.isFlagged,
      isBlocked: assessment.isBlocked,
      createdAt: assessment.createdAt,
      transaction: {
        id: assessment.transaction.id,
        amount: assessment.transaction.amount,
        currency: assessment.transaction.currency,
        status: assessment.transaction.status,
        customerEmail: assessment.transaction.customerEmail,
        customerName: assessment.transaction.customerName,
        createdAt: assessment.transaction.createdAt,
        merchant: assessment.transaction.merchant,
        paymentMethod: assessment.transaction.paymentMethod,
      },
    };
  }

  /**
   * Group risk assessments by day
   */
  private groupByDay(assessments: any[], startDate: Date, endDate: Date): DailyFraudStats[] {
    const dailyStats: DailyFraudStats[] = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const day = currentDate.toISOString().split('T')[0];
      const dayAssessments = assessments.filter((a) => {
        const assessmentDay = a.createdAt.toISOString().split('T')[0];
        return assessmentDay === day;
      });

      dailyStats.push({
        date: day,
        totalAssessments: dayAssessments.length,
        flaggedCount: dayAssessments.filter((a) => a.isFlagged).length,
        blockedCount: dayAssessments.filter((a) => a.isBlocked).length,
        levelCounts: {
          ['LOW']: dayAssessments.filter((a) => a.level === 'LOW').length,
          ['MEDIUM']: dayAssessments.filter((a) => a.level === 'MEDIUM').length,
          ['HIGH']: dayAssessments.filter((a) => a.level === 'HIGH').length,
          ['CRITICAL']: dayAssessments.filter((a) => a.level === 'CRITICAL').length,
        },
      });

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dailyStats;
  }
}
