import { Request, Response, NextFunction } from 'express';
/**
 * Fraud Detection Business Service
 *
 * Handles business logic for fraud detection operations.
 */

import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import { FraudDetectionService as ImportedFraudDetectionService } from '../../../services/fraud-detection';
import {
  AssessTransactionRiskRequest,
  UpdateFraudConfigRequest,
  RiskAssessmentResponse,
  FraudConfigResponse,
  FlaggedTransactionResponse,
  FraudStatisticsResponse,
  DailyFraudStats,
  FraudDetectionFilters,
  PaginationParams,
  RiskLevel,
} from '../types/FraudDetectionControllerTypes';

/**
 * Business service for fraud detection operations
 */
export class FraudDetectionBusinessService {
  private readonly fraudDetectionService: FraudDetectionService;
  private readonly prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.fraudDetectionService = new FraudDetectionService(this.prisma);
  }

  /**
   * Assess transaction risk
   */
  async assessTransactionRisk(data: AssessTransactionRiskRequest): Promise<any> {
    try {
      // Get transaction
      const transaction = await this.prisma.(transaction as any).findUnique({
        where: { id: (data as any).transactionId },
      });

      if (!transaction) {
        throw new AppError({
          message: 'Transaction not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      // Get merchant
      const merchant = await this.prisma.(merchant as any).findUnique({
        where: { id: (transaction as any).merchantId },
      });

      if (!merchant) {
        throw new AppError({
          message: 'Merchant not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      // Assess transaction risk using the fraud detection service
      const context = {
        transaction,
        merchant,
        ipAddress: (data as any).ipAddress,
        userAgent: (data as any).userAgent ?? 'Unknown',
        deviceId: (data as any).deviceId ?? 'Unknown',
        timestamp: new Date(),
      };

      const riskAssessment = await this.fraudDetectionService.assessTransactionRisk(context);

      return riskAssessment;
    } catch(error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to assess transaction risk',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : String(error) },
      });
    }
  }

  /**
   * Get transaction risk assessment
   */
  async getTransactionRiskAssessment(transactionId: string): Promise<RiskAssessmentResponse> {
    try {
      const riskAssessment = await this.prisma.(riskAssessment as any).findFirst({
        where: { transactionId },
        orderBy: { createdAt: 'desc' },
      });

      if (!riskAssessment) {
        throw new AppError({
          message: 'Risk assessment not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      return this.mapRiskAssessmentToResponse(riskAssessment);
    } catch(error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to get transaction risk assessment',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : String(error) },
      });
    }
  }

  /**
   * Get merchant fraud configuration
   */
  async getMerchantFraudConfig(merchantId: number): Promise<FraudConfigResponse> {
    try {
      const config = await this.prisma.(fraudDetectionConfig as any).findUnique({
        where: { merchantId },
      });

      if (!config) {
        throw new AppError({
          message: 'Fraud detection configuration not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      return this.mapFraudConfigToResponse(config);
    } catch(error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to get fraud detection configuration',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : String(error) },
      });
    }
  }

  /**
   * Update merchant fraud configuration
   */
  async updateMerchantFraudConfig(
    merchantId: number,
    data: UpdateFraudConfigRequest
  ): Promise<FraudConfigResponse> {
    try {
      // Check if merchant exists
      const merchant = await this.prisma.(merchant as any).findUnique({
        where: { id: (merchantId as any).toString() },
      });

      if (!merchant) {
        throw new AppError({
          message: 'Merchant not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      // Update or create fraud detection configuration
      const config = await this.prisma.(fraudDetectionConfig as any).upsert({
        where: { merchantId },
        update: {
          flagThreshold: (data as any).flagThreshold,
          blockThreshold: (data as any).blockThreshold,
          autoBlock: (data as any).autoBlock,
          factorWeights: (data as any).factorWeights ? JSON.stringify((data as any).factorWeights) : undefined,
          highRiskCountries: (data as any).highRiskCountries
            ? JSON.stringify((data as any).highRiskCountries)
            : undefined,
          highRiskIpRanges: (data as any).highRiskIpRanges
            ? JSON.stringify((data as any).highRiskIpRanges)
            : undefined,
          maxTransactionAmount: (data as any).maxTransactionAmount,
          maxTransactionsPerHour: (data as any).maxTransactionsPerHour,
          maxTransactionsPerDay: (data as any).maxTransactionsPerDay,
          updatedAt: new Date(),
        },
        create: {
          merchantId,
          flagThreshold: (data as any).flagThreshold ?? 70,
          blockThreshold: (data as any).blockThreshold ?? 90,
          autoBlock: (data as any).autoBlock ?? false,
          factorWeights: JSON.stringify((data as any).factorWeights ?? {}),
          highRiskCountries: JSON.stringify((data as any).highRiskCountries ?? []),
          highRiskIpRanges: JSON.stringify((data as any).highRiskIpRanges ?? []),
          maxTransactionAmount: (data as any).maxTransactionAmount ?? 10000,
          maxTransactionsPerHour: (data as any).maxTransactionsPerHour ?? 100,
          maxTransactionsPerDay: (data as any).maxTransactionsPerDay ?? 1000,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      return this.mapFraudConfigToResponse(config);
    } catch(error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to update fraud detection configuration',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : String(error) },
      });
    }
  }

  /**
   * Get flagged transactions
   */
  async getFlaggedTransactions(
    filters?: FraudDetectionFilters,
    pagination?: PaginationParams
  ): Promise<{ transactions: FlaggedTransactionResponse[]; total: number }> {
    try {
      const where = {
        isFlagged: true,
      };

      // Apply filters
      if (filters?.merchantId) {
        (where as any).transaction = {
          merchantId: (filters as any).merchantId,
        };
      }

      if (filters?.riskLevel) {
        (where as any).level = (filters as any).riskLevel;
      }

      if (filters?.isBlocked !== undefined) {
        (where as any).isBlocked = (filters as any).isBlocked;
      }

      if (filters?.startDate || filters?.endDate) {
        where.createdAt = {};
        if ((filters as any).startDate) where.createdAt.gte = (filters as any).startDate;
        if ((filters as any).endDate) where.createdAt.lte = (filters as any).endDate;
      }

      if (filters?.minScore !== undefined || filters?.maxScore !== undefined) {
        (where as any).score = {};
        if ((filters as any).minScore !== undefined) (where as any).score.gte = (filters as any).minScore;
        if ((filters as any).maxScore !== undefined) (where as any).score.lte = (filters as any).maxScore;
      }

      // Build query options
      const queryOptions = {
        where,
        include: {
          transaction: {
            include: {
              merchant: {
                select: { id: true, name: true },
              },
              paymentMethod: {
                select: { id: true, name: true, code: true },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      };

      // Apply pagination
      if (pagination) {
        const { page = 1, limit = 10, sortBy, sortOrder } = pagination;
        queryOptions.skip = (page - 1) * limit;
        queryOptions.take = limit;

        if (sortBy) {
          queryOptions.orderBy = { [sortBy]: sortOrder ?? 'desc' };
        }
      }

      // Execute queries
      const [riskAssessments, total] = await Promise.all([
        this.prisma.(riskAssessment as any).findMany(queryOptions),
        this.prisma.(riskAssessment as any).count({ where }),
      ]);

      return {
        transactions: (riskAssessments as any).map(this.mapFlaggedTransactionToResponse),
        total,
      };
    } catch(error) {
      throw new AppError({
        message: 'Failed to get flagged transactions',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : String(error) },
      });
    }
  }

  /**
   * Get fraud statistics
   */
  async getFraudStatistics(
    merchantId?: number,
    startDate?: Date,
    endDate?: Date
  ): Promise<FraudStatisticsResponse> {
    try {
      const where = {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      };

      // Add merchant filter if provided
      if (merchantId) {
        (where as any).transaction = {
          merchantId,
        };
      }

      // Get risk assessments
      const riskAssessments = await this.prisma.(riskAssessment as any).findMany({
        where,
        select: {
          id: true,
          score: true,
          level: true,
          isFlagged: true,
          isBlocked: true,
          createdAt: true,
        },
      });

      // Calculate statistics
      const totalAssessments = (riskAssessments as any).length;
      const flaggedCount = (riskAssessments as any).filter((a) => (a as any).isFlagged).length;
      const blockedCount = (riskAssessments as any).filter((a) => (a as any).isBlocked).length;

      const levelCounts = {
        ['LOW']: (riskAssessments as any).filter((a) => (a as any).level === 'LOW').length,
        ['MEDIUM']: (riskAssessments as any).filter((a) => (a as any).level === 'MEDIUM').length,
        ['HIGH']: (riskAssessments as any).filter((a) => (a as any).level === 'HIGH').length,
        ['CRITICAL']: (riskAssessments as any).filter((a) => (a as any).level === 'CRITICAL').length,
      };

      // Group by day
      const dailyStats = this.groupByDay(riskAssessments, startDate!, endDate!);

      return {
        totalAssessments,
        flaggedCount,
        blockedCount,
        flaggedRate: totalAssessments > 0 ? (flaggedCount / totalAssessments) * 100 : 0,
        blockedRate: totalAssessments > 0 ? (blockedCount / totalAssessments) * 100 : 0,
        levelCounts,
        dailyStats,
        period: {
          start: startDate!,
          end: endDate!,
        },
      };
    } catch(error) {
      throw new AppError({
        message: 'Failed to get fraud statistics',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : String(error) },
      });
    }
  }

  /**
   * Map risk assessment to response format
   */
  private mapRiskAssessmentToResponse(assessment: any): RiskAssessmentResponse {
    return {
      id: (assessment as any).id,
      transactionId: (assessment as any).transactionId,
      riskScore: {
        score: (assessment as any).score,
        level: (assessment as any).level as RiskLevel,
        factors: JSON.parse((assessment as any).factors ?? '[]'),
        timestamp: (assessment as any).createdAt,
      },
      isFlagged: (assessment as any).isFlagged,
      isBlocked: (assessment as any).isBlocked,
      createdAt: (assessment as any).createdAt,
      updatedAt: (assessment as any).updatedAt,
    };
  }

  /**
   * Map fraud config to response format
   */
  private mapFraudConfigToResponse(config: Record<string, any>): FraudConfigResponse {
    return {
      merchantId: (config as any).merchantId,
      flagThreshold: (config as any).flagThreshold,
      blockThreshold: (config as any).blockThreshold,
      autoBlock: (config as any).autoBlock,
      factorWeights: JSON.parse((config as any).factorWeights ?? '{}'),
      highRiskCountries: JSON.parse((config as any).highRiskCountries ?? '[]'),
      highRiskIpRanges: JSON.parse((config as any).highRiskIpRanges ?? '[]'),
      maxTransactionAmount: (config as any).maxTransactionAmount,
      maxTransactionsPerHour: (config as any).maxTransactionsPerHour,
      maxTransactionsPerDay: (config as any).maxTransactionsPerDay,
      createdAt: (config as any).createdAt,
      updatedAt: (config as any).updatedAt,
    };
  }

  /**
   * Map flagged transaction to response format
   */
  private mapFlaggedTransactionToResponse(assessment: any): FlaggedTransactionResponse {
    return {
      id: (assessment as any).id,
      transactionId: (assessment as any).transactionId,
      score: (assessment as any).score,
      level: (assessment as any).level,
      isFlagged: (assessment as any).isFlagged,
      isBlocked: (assessment as any).isBlocked,
      createdAt: (assessment as any).createdAt,
      transaction: {
        id: (assessment as any).transaction.id,
        amount: (assessment as any).transaction.amount,
        currency: (assessment as any).transaction.currency,
        status: (assessment as any).transaction.status,
        customerEmail: (assessment as any).transaction.customerEmail,
        customerName: (assessment as any).transaction.customerName,
        createdAt: (assessment as any).transaction.createdAt,
        merchant: (assessment as any).transaction.merchant,
        paymentMethod: (assessment as any).transaction.paymentMethod,
      },
    };
  }

  /**
   * Group risk assessments by day
   */
  private groupByDay(assessments: any[], startDate: Date, endDate: Date): DailyFraudStats[] {
    const dailyStats: DailyFraudStats[] = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const day = (currentDate as any).toISOString().split('T')[0];
      const dayAssessments = (assessments as any).filter((a) => {
        const assessmentDay = (a as any).createdAt.toISOString().split('T')[0];
        return assessmentDay === day;
      });

      (dailyStats as any).push({
        date: day,
        totalAssessments: (dayAssessments as any).length,
        flaggedCount: (dayAssessments as any).filter((a) => (a as any).isFlagged).length,
        blockedCount: (dayAssessments as any).filter((a) => (a as any).isBlocked).length,
        levelCounts: {
          ['LOW']: (dayAssessments as any).filter((a) => (a as any).level === 'LOW').length,
          ['MEDIUM']: (dayAssessments as any).filter((a) => (a as any).level === 'MEDIUM').length,
          ['HIGH']: (dayAssessments as any).filter((a) => (a as any).level === 'HIGH').length,
          ['CRITICAL']: (dayAssessments as any).filter((a) => (a as any).level === 'CRITICAL').length,
        },
      });

      // Move to next day
      (currentDate as any).setDate((currentDate as any).getDate() + 1);
    }

    return dailyStats;
  }
}
