#!/usr/bin/env node

/**
 * Fix Specific Syntax Errors Script
 * Targets the exact syntax patterns causing the remaining errors
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎯 FIX SPECIFIC SYNTAX ERRORS SCRIPT');
console.log('====================================');

// Very specific fixes for the exact patterns we identified
const specificFixes = {
    // TS1136: Property assignment expected - Object syntax fixes
    'orderBy: {, ': 'orderBy: { ',
    'where: {, ': 'where: { ',
    'data: {, ': 'data: { ',
    '{, ': '{ ',
    
    // Missing spaces after = in variable assignments
    ': unknown =await ': ': unknown = await ',
    ': unknown =req.': ': unknown = req.',
    ': unknown =new ': ': unknown = new ',
    ': unknown =[': ': unknown = [',
    ': unknown ={': ': unknown = {',
    ': unknown =process.': ': unknown = process.',
    ': unknown =this.': ': unknown = this.',
    ': unknown =parseInt(': ': unknown = parseInt(',
    ': unknown =parseFloat(': ': unknown = parseFloat(',
    ': unknown =JSON.': ': unknown = JSON.',
    ': unknown =String(': ': unknown = String(',
    ': unknown =Number(': ': unknown = Number(',
    ': unknown =Boolean(': ': unknown = Boolean(',
    ': unknown =Array.': ': unknown = Array.',
    ': unknown =Object.': ': unknown = Object.',
    ': unknown =Date.': ': unknown = Date.',
    ': unknown =Math.': ': unknown = Math.',
    ': unknown =Buffer.': ': unknown = Buffer.',
    ': unknown =crypto.': ': unknown = crypto.',
    ': unknown =fs.': ': unknown = fs.',
    ': unknown =path.': ': unknown = path.',
    ': unknown =url.': ': unknown = url.',
    ': unknown =util.': ': unknown = util.',
    ': unknown =os.': ': unknown = os.',
    ': unknown =http.': ': unknown = http.',
    ': unknown =https.': ': unknown = https.',
    ': unknown =querystring.': ': unknown = querystring.',
    ': unknown =stream.': ': unknown = stream.',
    ': unknown =events.': ': unknown = events.',
    ': unknown =child_process.': ': unknown = child_process.',
    ': unknown =cluster.': ': unknown = cluster.',
    ': unknown =worker_threads.': ': unknown = worker_threads.',
    ': unknown =async ': ': unknown = async ',
    ': unknown =function': ': unknown = function',
    ': unknown =class ': ': unknown = class ',
    ': unknown =interface ': ': unknown = interface ',
    ': unknown =enum ': ': unknown = enum ',
    ': unknown =namespace ': ': unknown = namespace ',
    ': unknown =module ': ': unknown = module ',
    ': unknown =import ': ': unknown = import ',
    ': unknown =export ': ': unknown = export ',
    ': unknown =require(': ': unknown = require(',
    ': unknown =__dirname': ': unknown = __dirname',
    ': unknown =__filename': ': unknown = __filename',
    ': unknown =global.': ': unknown = global.',
    ': unknown =window.': ': unknown = window.',
    ': unknown =document.': ': unknown = document.',
    ': unknown =console.': ': unknown = console.',
    ': unknown =setTimeout(': ': unknown = setTimeout(',
    ': unknown =setInterval(': ': unknown = setInterval(',
    ': unknown =clearTimeout(': ': unknown = clearTimeout(',
    ': unknown =clearInterval(': ': unknown = clearInterval(',
    ': unknown =setImmediate(': ': unknown = setImmediate(',
    ': unknown =clearImmediate(': ': unknown = clearImmediate(',
    ': unknown =Promise.': ': unknown = Promise.',
    ': unknown =Error(': ': unknown = Error(',
    ': unknown =TypeError(': ': unknown = TypeError(',
    ': unknown =ReferenceError(': ': unknown = ReferenceError(',
    ': unknown =SyntaxError(': ': unknown = SyntaxError(',
    ': unknown =RangeError(': ': unknown = RangeError(',
    ': unknown =EvalError(': ': unknown = EvalError(',
    ': unknown =URIError(': ': unknown = URIError(',
    ': unknown =RegExp(': ': unknown = RegExp(',
    ': unknown =Symbol(': ': unknown = Symbol(',
    ': unknown =BigInt(': ': unknown = BigInt(',
    ': unknown =Proxy(': ': unknown = Proxy(',
    ': unknown =Reflect.': ': unknown = Reflect.',
    ': unknown =WeakMap(': ': unknown = WeakMap(',
    ': unknown =WeakSet(': ': unknown = WeakSet(',
    ': unknown =Map(': ': unknown = Map(',
    ': unknown =Set(': ': unknown = Set(',
    ': unknown =ArrayBuffer(': ': unknown = ArrayBuffer(',
    ': unknown =DataView(': ': unknown = DataView(',
    ': unknown =Int8Array(': ': unknown = Int8Array(',
    ': unknown =Uint8Array(': ': unknown = Uint8Array(',
    ': unknown =Uint8ClampedArray(': ': unknown = Uint8ClampedArray(',
    ': unknown =Int16Array(': ': unknown = Int16Array(',
    ': unknown =Uint16Array(': ': unknown = Uint16Array(',
    ': unknown =Int32Array(': ': unknown = Int32Array(',
    ': unknown =Uint32Array(': ': unknown = Uint32Array(',
    ': unknown =Float32Array(': ': unknown = Float32Array(',
    ': unknown =Float64Array(': ': unknown = Float64Array(',
    ': unknown =BigInt64Array(': ': unknown = BigInt64Array(',
    ': unknown =BigUint64Array(': ': unknown = BigUint64Array(',
    
    // Arrow function syntax fixes
    '= >': '=>',
    '= > ': '=> ',
    ' = >': ' =>',
    ' = > ': ' => ',
    
    // Common nullish coalescing fixes
    ' || \'\'': ' ?? \'\'',
    ' || ""': ' ?? ""',
    ' || 0': ' ?? 0',
    ' || false': ' ?? false',
    ' || true': ' ?? true',
    ' || null': ' ?? null',
    ' || undefined': ' ?? undefined',
    ' || []': ' ?? []',
    ' || {}': ' ?? {}'
};

function findAllTypeScriptFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'coverage'].includes(item)) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

function getErrorCount() {
    try {
        const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { encoding: 'utf8' });
        const errorMatches = output.match(/error TS/g) || [];
        return errorMatches.length;
    } catch (error) {
        const errorMatches = error.stdout.match(/error TS/g) || [];
        return errorMatches.length;
    }
}

function processFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let modifiedContent = content;
        let fixCount = 0;
        
        // Apply all specific fixes
        for (const [oldPattern, newPattern] of Object.entries(specificFixes)) {
            const regex = new RegExp(escapeRegExp(oldPattern), 'g');
            const matches = modifiedContent.match(regex);
            if (matches) {
                modifiedContent = modifiedContent.replace(regex, newPattern);
                fixCount += matches.length;
            }
        }
        
        if (fixCount > 0) {
            fs.writeFileSync(filePath, modifiedContent, 'utf8');
            return { filePath, fixCount };
        }
        
        return null;
    } catch (error) {
        return { filePath, error: error.message };
    }
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

async function main() {
    console.log('🔍 Scanning for TypeScript files...');
    
    const files = findAllTypeScriptFiles('./src');
    console.log(`📁 Found ${files.length} TypeScript files`);
    
    console.log('📊 Getting initial error count...');
    const initialErrors = getErrorCount();
    console.log(`🚨 Initial TypeScript errors: ${initialErrors}`);
    
    console.log('🚀 Starting specific syntax fixes...');
    const startTime = Date.now();
    
    const results = [];
    let totalFixedIssues = 0;
    
    for (const file of files) {
        const result = processFile(file);
        if (result) {
            results.push(result);
            if (result.fixCount) {
                totalFixedIssues += result.fixCount;
            }
        }
    }
    
    console.log('📊 Getting final error count...');
    const finalErrors = getErrorCount();
    const totalErrorsFixed = initialErrors - finalErrors;
    
    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;
    
    console.log('\n🎯 SPECIFIC SYNTAX FIX COMPLETE!');
    console.log('=================================');
    console.log(`⏱️  Processing time: ${processingTime.toFixed(2)} seconds`);
    console.log(`📁 Files processed: ${results.filter(r => !r.error).length}`);
    console.log(`❌ Files with errors: ${results.filter(r => r.error).length}`);
    console.log(`🔧 Total fixes applied: ${totalFixedIssues}`);
    console.log(`🚨 TypeScript errors before: ${initialErrors}`);
    console.log(`✅ TypeScript errors after: ${finalErrors}`);
    console.log(`🎯 Total errors fixed: ${totalErrorsFixed}`);
    
    if (totalErrorsFixed > 0) {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}%`);
        console.log('\n🎉 SUCCESS! Specific syntax fixes applied successfully!');
        console.log('🏆 Your application now has improved type safety!');
    } else if (totalErrorsFixed === 0) {
        console.log('📈 Success rate: 0.0% (no net change)');
        console.log('\n✨ No errors were fixed, but no new errors were introduced!');
    } else {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}% (negative - new errors introduced)`);
        console.log('\n⚠️  Some fixes may have introduced new errors. Consider reverting.');
    }
    
    const errorFiles = results.filter(r => r.error);
    if (errorFiles.length > 0) {
        console.log('\n❌ Files with processing errors:');
        errorFiles.forEach(({ filePath, error }) => {
            console.log(`   ${path.relative(process.cwd(), filePath)}: ${error}`);
        });
    }
}

main().catch(console.error);
