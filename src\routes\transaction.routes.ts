// jscpd:ignore-file
import express from "express";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth as any).middleware';
import { Merchant as ImportedMerchant } from '../types';
import {
    getAllTransactions,
    getTransactionById,
    getTransactionByReference,
    createTransaction,
    updateTransactionStatus,
    verifyPayment,
    getMerchantTransactions,
    getTransactionStats
} from "../controllers/refactored/(transaction as any).controller.ts";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth as any).middleware';
import { Merchant as ImportedMerchant } from '../types';


const router: any =(express as any).Router();

// All routes require authentication
(router as any).use(authenticate);

// Routes accessible by ADMIN and SUPER_ADMIN
(router as any).get("/", authorize(["ADMIN", "SUPER_ADMIN"]), getAllTransactions);
(router as any).get("/stats", authorize(["ADMIN", "SUPER_ADMIN"]), getTransactionStats);

// Routes accessible by ADMIN, SUPER_ADMIN, and MERCHANT
(router as any).get("/:id", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getTransactionById);
(router as any).get("/reference/:reference", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getTransactionByReference);
(router as any).post("/", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), createTransaction);
(router as any).put("/:id/status", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), updateTransactionStatus);
(router as any).post("/verify", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), verifyPayment);

// Merchant-specific routes
(router as any).get("/merchant/:merchantId", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getMerchantTransactions);
(router as any).get("/merchant/:merchantId/stats", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getTransactionStats);

export default router;
