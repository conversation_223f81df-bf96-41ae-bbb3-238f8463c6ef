// jscpd:ignore-file
import express from 'express';
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth as any).middleware';
import { IdentityVerificationController as ImportedIdentityVerificationController } from '../controllers/identity-verification';

// Create controller instance
const identityVerificationController = new IdentityVerificationController();

// Extract methods from controller
const {
  verifyEthereumSignature,
  verifyERC1484Identity,
  verifyERC725Identity,
  verifyENS,
  verifyPolygonID,
  verifyWorldcoin,
  verifyUnstoppableDomains,
  getVerificationById,
  getVerificationsForUser,
  getVerificationsForMerchant,
  addClaim,
  revokeClaim,
  setVerificationExpiration,
  checkVerificationExpiration,
  getVerificationStats,
  createBlockchainVerificationRequest,
  completeBlockchainVerification,
  getSupportedNetworks,
  verifyENSDomain,
  completeENSVerification,
  verifyUnstoppableDomain,
  completeUnstoppableDomainVerification,
  createPolygonIDVerificationRequest,
  handlePolygonIDCallback,
  checkPolygonIDVerificationStatus,
} = identityVerificationController;

const router: any = (express as any).Router();

// All routes require authentication
(router as any).use(authenticate);

// Verification endpoints
(router as any).post('/ethereum-signature', verifyEthereumSignature);
(router as any).post('/erc1484', verifyERC1484Identity);
(router as any).post('/erc725', verifyERC725Identity);
(router as any).post('/ens', verifyENS);
(router as any).post('/polygon-id', verifyPolygonID);
(router as any).post('/worldcoin', verifyWorldcoin);
(router as any).post('/unstoppable-domains', verifyUnstoppableDomains);

// Claim management
(router as any).post('/claims', authorize(['ADMIN', 'MERCHANT']), addClaim);
(router as any).delete('/claims/:claimId', authorize(['ADMIN', 'MERCHANT']), revokeClaim);

// Expiration management
(router as any).post('/expiration', authorize(['ADMIN']), setVerificationExpiration);
(router as any).post('/check-expiration', authorize(['ADMIN']), checkVerificationExpiration);

// Blockchain-based verification
(router as any).post(
  '/blockchain/request',
  authorize(['USER', 'MERCHANT']),
  createBlockchainVerificationRequest
);
(router as any).post('/blockchain/verify', authorize(['USER', 'MERCHANT']), completeBlockchainVerification);
(router as any).get('/blockchain/networks', getSupportedNetworks);

// ENS verification
(router as any).post('/ens/verify', authorize(['USER', 'MERCHANT']), verifyENSDomain);
(router as any).post('/ens/complete', authorize(['USER', 'MERCHANT']), completeENSVerification);

// Unstoppable Domains verification
(router as any).post('/unstoppable/verify', authorize(['USER', 'MERCHANT']), verifyUnstoppableDomain);
(router as any).post(
  '/unstoppable/complete',
  authorize(['USER', 'MERCHANT']),
  completeUnstoppableDomainVerification
);

// Polygon ID verification
(router as any).post(
  '/polygon-id/request',
  authorize(['USER', 'MERCHANT']),
  createPolygonIDVerificationRequest
);
(router as any).post('/polygon-id/callback', handlePolygonIDCallback);
(router as any).get(
  '/polygon-id/status/:verificationId',
  authorize(['USER', 'MERCHANT']),
  checkPolygonIDVerificationStatus
);

// Worldcoin verification
(router as any).post('/worldcoin/verify', authorize(['USER', 'MERCHANT']), verifyWorldcoinIdentity);

// Statistics
(router as any).get('/stats', authorize(['ADMIN']), getVerificationStats);

// Get verification by ID
(router as any).get('/:id', getVerificationById);

// Get verifications for user
(router as any).get('/user/me', authorize(['USER']), getVerificationsForUser);

// Get verifications for merchant
(router as any).get('/merchant/me', authorize(['MERCHANT']), getVerificationsForMerchant);

export default router;
