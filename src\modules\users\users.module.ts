// jscpd:ignore-file
/**
 * Users Module
 * 
 * This module handles user management.
 */

import { Router } from 'express';
import { BaseModule } from '../../factories/ModuleFactory';
import { logger } from '../../utils/logger';
import { BaseModule } from '../../factories/ModuleFactory';
import { logger } from '../../utils/logger';

/**
 * Users Module
 */
class UsersModule extends BaseModule {
  /**
   * Constructor
   */
  constructor() {
    super('UsersModule');
  }
  
  /**
   * Initialize the module
   */
  initialize(): void {
    logger.info('Initializing UsersModule');
    
    // Get controllers
    const userController: unknown = this.controllerFactory.getController('user');
    
    // Set up routes
    this.router.get('/', userController.getAll);
    this.router.get('/:id', userController.getById);
    this.router.post('/', userController.create);
    this.router.put('/:id', userController.update);
    this.router.delete('/:id', userController.delete);
    this.router.get('/me', userController.getProfile);
    this.router.put('/me', userController.updateProfile);
    this.router.put('/me/password', userController.changePassword);
    
    logger.info('UsersModule initialized');
  }
}

// Export the module
export default new UsersModule();
