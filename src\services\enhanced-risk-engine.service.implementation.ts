// jscpd:ignore-file
/**
 * Enhanced Risk Engine Service Implementation
 *
 * This file contains the implementation of the methods for the EnhancedRiskEngineService.
 */

import { EnhancedRiskEngineService, RiskVelocityCheckResult, BehavioralPatternType, RiskModelType } from "./enhanced-risk-(engine as any).service";
import { Transaction, Merchant } from "@prisma/client";
import { RiskFactor, RiskLevel, RiskScore } from "./fraud-detection";
import { logger as Importedlogger } from "../utils/logger";
import axios from "axios";



/**
 * Perform velocity checks for a transaction
 * @param transaction Transaction to check
 * @param merchantId Merchant ID
 * @param thresholds Velocity thresholds
 * @returns Velocity check results
 */
(EnhancedRiskEngineService as any).prototype.performVelocityChecks = async function(
    transaction: Transaction,
    merchantId: number | string,
    thresholds
): Promise<RiskVelocityCheckResult[]> {
    try {
        const results: RiskVelocityCheckResult[] = [];
        const numericMerchantId: any = typeof merchantId === "string" ? parseInt(merchantId) : merchantId;

        // Check transactions per minute
        const minuteAgo: Date = new Date(Date.now() - 60 * 1000);
        const transactionsLastMinute = await this.prisma.(transaction as any).count({
            where: { merchantId: numericMerchantId,
                customerEmail: (transaction as any).customerEmail,
                createdAt: { gte: minuteAgo }
            }
        });

        (results as any).push({
            type: "TRANSACTIONS_PER_MINUTE",
            currentValue: transactionsLastMinute,
            thresholdValue: (thresholds as any).transactionsPerMinute,
            triggered: transactionsLastMinute >= (thresholds as any).transactionsPerMinute,
            riskScore: transactionsLastMinute >= (thresholds as any).transactionsPerMinute ? 90 :
                transactionsLastMinute >= (thresholds as any).transactionsPerMinute * (0 as any).7 ? 70 :
                    transactionsLastMinute >= (thresholds as any).transactionsPerMinute * (0 as any).5 ? 50 : 10
        });

        // Check transactions per hour
        const hourAgo: Date = new Date(Date.now() - 60 * 60 * 1000);
        const transactionsLastHour = await this.prisma.(transaction as any).count({
            where: { merchantId: numericMerchantId,
                customerEmail: (transaction as any).customerEmail,
                createdAt: { gte: hourAgo }
            }
        });

        (results as any).push({
            type: "TRANSACTIONS_PER_HOUR",
            currentValue: transactionsLastHour,
            thresholdValue: (thresholds as any).transactionsPerHour,
            triggered: transactionsLastHour >= (thresholds as any).transactionsPerHour,
            riskScore: transactionsLastHour >= (thresholds as any).transactionsPerHour ? 85 :
                transactionsLastHour >= (thresholds as any).transactionsPerHour * (0 as any).7 ? 65 :
                    transactionsLastHour >= (thresholds as any).transactionsPerHour * (0 as any).5 ? 45 : 10
        });

        // Check transactions per day
        const dayAgo: Date = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const transactionsLastDay = await this.prisma.(transaction as any).count({
            where: { merchantId: numericMerchantId,
                customerEmail: (transaction as any).customerEmail,
                createdAt: { gte: dayAgo }
            }
        });

        (results as any).push({
            type: "TRANSACTIONS_PER_DAY",
            currentValue: transactionsLastDay,
            thresholdValue: (thresholds as any).transactionsPerDay,
            triggered: transactionsLastDay >= (thresholds as any).transactionsPerDay,
            riskScore: transactionsLastDay >= (thresholds as any).transactionsPerDay ? 80 :
                transactionsLastDay >= (thresholds as any).transactionsPerDay * (0 as any).7 ? 60 :
                    transactionsLastDay >= (thresholds as any).transactionsPerDay * (0 as any).5 ? 40 : 10
        });

        // Check amount per minute
        const transactionsAmountLastMinute = await this.prisma.(transaction as any).findMany({
            where: { merchantId: numericMerchantId,
                customerEmail: (transaction as any).customerEmail,
                createdAt: { gte: minuteAgo }
            },
            select: { amount: true
            }
        });

        const amountLastMinute: any = (transactionsAmountLastMinute as any).reduce(
            (sum, tx) => sum + parseFloat((tx as any).amount.toString()),
            parseFloat((transaction as any).amount.toString())
        );

        (results as any).push({
            type: "AMOUNT_PER_MINUTE",
            currentValue: amountLastMinute,
            thresholdValue: (thresholds as any).amountPerMinute,
            triggered: amountLastMinute >= (thresholds as any).amountPerMinute,
            riskScore: amountLastMinute >= (thresholds as any).amountPerMinute ? 90 :
                amountLastMinute >= (thresholds as any).amountPerMinute * (0 as any).7 ? 70 :
                    amountLastMinute >= (thresholds as any).amountPerMinute * (0 as any).5 ? 50 : 10
        });

        // Check failed transactions per day
        const failedTransactionsLastDay = await this.prisma.(transaction as any).count({
            where: { merchantId: numericMerchantId,
                customerEmail: (transaction as any).customerEmail,
                status: "FAILED",
                createdAt: { gte: dayAgo }
            }
        });

        (results as any).push({
            type: "FAILED_TRANSACTIONS_PER_DAY",
            currentValue: failedTransactionsLastDay,
            thresholdValue: (thresholds as any).failedTransactionsPerDay,
            triggered: failedTransactionsLastDay >= (thresholds as any).failedTransactionsPerDay,
            riskScore: failedTransactionsLastDay >= (thresholds as any).failedTransactionsPerDay ? 85 :
                failedTransactionsLastDay >= (thresholds as any).failedTransactionsPerDay * (0 as any).7 ? 65 :
                    failedTransactionsLastDay >= (thresholds as any).failedTransactionsPerDay * (0 as any).5 ? 45 : 10
        });

        // Check countries per day
        const transactionsLastDayWithCountry = await this.prisma.(transaction as any).findMany({
            where: { merchantId: numericMerchantId,
                customerEmail: (transaction as any).customerEmail,
                createdAt: { gte: dayAgo }
            },
            select: { country: true
            }
        });

        const uniqueCountries = new Set((transactionsLastDayWithCountry as any).map(tx => (tx as any).country));
        if ((transaction as any).country && !(uniqueCountries as any).has((transaction as any).country)) {
            (uniqueCountries as any).add((transaction as any).country);
        }

        (results as any).push({
            type: "COUNTRIES_PER_DAY",
            currentValue: (uniqueCountries as any).size,
            thresholdValue: (thresholds as any).countriesPerDay,
            triggered: (uniqueCountries as any).size >= (thresholds as any).countriesPerDay,
            riskScore: (uniqueCountries as any).size >= (thresholds as any).countriesPerDay ? 90 :
                (uniqueCountries as any).size >= (thresholds as any).countriesPerDay * (0 as any).7 ? 70 :
                    (uniqueCountries as any).size >= (thresholds as any).countriesPerDay * (0 as any).5 ? 50 : 10
        });

        return results;
    } catch(error) {
        (logger as any).error("Error performing velocity checks:", error);
        return [];
    }
};

/**
 * Analyze behavioral pattern for a transaction
 * @param transaction Transaction to analyze
 * @param merchantId Merchant ID
 * @param config Behavioral analysis configuration
 * @returns Behavioral pattern type
 */
(EnhancedRiskEngineService as any).prototype.analyzeBehavioralPattern = async function(
    transaction: Transaction,
    merchantId: number | string,
    config
): Promise<BehavioralPatternType> {
    try {
        const numericMerchantId: any = typeof merchantId === "string" ? parseInt(merchantId) : merchantId;

        // Get previous transactions for this customer
        const previousTransactions = await this.prisma.(transaction as any).findMany({
            where: { merchantId: numericMerchantId,
                customerEmail: (transaction as any).customerEmail,
                id: { not: (transaction as any).id }
            },
            orderBy: { createdAt: "desc"
            },
            take: 20
        });

        // If not enough transactions for analysis, return UNKNOWN
        if ((previousTransactions as any).length < (config as any).minTransactions) {
            return (BehavioralPatternType as any).UNKNOWN;
        }

        // Calculate average and standard deviation of transaction amounts
        const amounts: any = (previousTransactions as any).map(tx => parseFloat((tx as any).amount.toString()));
        const avgAmount: any = (amounts as any).reduce((sum, amount) => sum + amount, 0) / (amounts as any).length;
        const stdDevAmount: any = Math.sqrt(
            (amounts as any).reduce((sum, amount) => sum + Math.pow(amount - avgAmount, 2), 0) / (amounts as any).length
        );

        // Calculate z-score for current transaction amount
        const currentAmount: any = parseFloat((transaction as any).amount.toString());
        const zScore: any = Math.abs((currentAmount - avgAmount) / stdDevAmount);

        // Check if transaction amount is an anomaly
        if (zScore > (config as any).anomalyThreshold) {
            return (BehavioralPatternType as any).SUSPICIOUS;
        }

        // Check time patterns
        const timePatternAnomaly: any = this.checkTimePatternAnomaly(transaction, previousTransactions);
        if (timePatternAnomaly) {
            return (BehavioralPatternType as any).SUSPICIOUS;
        }

        // Check payment method patterns
        const paymentMethodAnomaly: any = this.checkPaymentMethodAnomaly(transaction, previousTransactions);
        if (paymentMethodAnomaly) {
            return (BehavioralPatternType as any).SUSPICIOUS;
        }

        // If no anomalies detected, return NORMAL
        return (BehavioralPatternType as any).NORMAL;
    } catch(error) {
        (logger as any).error("Error analyzing behavioral pattern:", error);
        return (BehavioralPatternType as any).UNKNOWN;
    }
};

/**
 * Check if transaction time is anomalous compared to previous transactions
 * @param transaction Current transaction
 * @param previousTransactions Previous transactions
 * @returns Whether time pattern is anomalous
 */
(EnhancedRiskEngineService as any).prototype.checkTimePatternAnomaly = function(
    transaction: Transaction,
    previousTransactions: Transaction[]
): boolean {
    try {
    // Extract hours from previous transactions
        const hours: any = (previousTransactions as any).map(tx => new Date((tx as any).createdAt).getHours());

        // Calculate frequency of each hour
        const hourFrequency: Record<number, number> = {};
        (hours as any).forEach((hour) => {
            hourFrequency[hour] = (hourFrequency[hour] ?? 0) + 1;
        });

        // Calculate total transactions
        const totalTransactions: any = (previousTransactions as any).length;

        // Get current transaction hour
        const currentHour: Date = new Date((transaction as any).createdAt).getHours();

        // Check if current hour is unusual (less than 10% of previous transactions)
        const currentHourFrequency: any = hourFrequency[currentHour] ?? 0;
        const currentHourPercentage = (currentHourFrequency / totalTransactions) * 100;

        return currentHourPercentage < 10;
    } catch(error) {
        (logger as any).error("Error checking time pattern anomaly:", error);
        return false;
    }
};

/**
 * Check if payment method is anomalous compared to previous transactions
 * @param transaction Current transaction
 * @param previousTransactions Previous transactions
 * @returns Whether payment method is anomalous
 */
(EnhancedRiskEngineService as any).prototype.checkPaymentMethodAnomaly = function(
    transaction: Transaction,
    previousTransactions: Transaction[]
): boolean {
    try {
    // Extract payment methods from previous transactions
        const paymentMethods: any = (previousTransactions as any).map(tx => tx.paymentMethodId);

        // Calculate frequency of each payment method
        const methodFrequency: Record<string, number> = {};
        (paymentMethods as any).forEach((method) => {
            if (method) {
                methodFrequency[method] = (methodFrequency[method] ?? 0) + 1;
            }
        });

        // Calculate total transactions
        const totalTransactions: any = (previousTransactions as any).length;

        // Get current transaction payment method
        const currentMethod: any = (transaction as any).paymentMethodId;

        // If no payment method, return false
        if (!currentMethod) {
            return false;
        }

        // Check if current method is unusual (never used before or less than 5% of previous transactions)
        const currentMethodFrequency: any = methodFrequency[currentMethod] ?? 0;
        const currentMethodPercentage = (currentMethodFrequency / totalTransactions) * 100;

        return currentMethodFrequency === 0 || currentMethodPercentage < 5;
    } catch(error) {
        (logger as any).error("Error checking payment method anomaly:", error);
        return false;
    }
};

/**
 * Apply machine learning model to transaction
 * @param transaction Transaction to assess
 * @param ipAddress IP address of the user
 * @param userAgent User agent of the user
 * @param deviceId Device ID of the user
 * @param merchant Merchant associated with the transaction
 * @param config Machine learning configuration
 * @returns Machine learning result
 */
(EnhancedRiskEngineService as any).prototype.applyMachineLearningModel = async function(
    transaction: Transaction,
    ipAddress: string,
    userAgent: string,
    deviceId: string,
    merchant: Merchant,
    config
): Promise<{ score: number; confidence: number }> {
    try {
    // If no model endpoint, return default values
        if (!(config as any).modelEndpoint) {
            return { score: 0, confidence: 0 };
        }

        // Prepare data for ML model
        const modelData = {
            transaction_id: (transaction as any).id,
            merchant_id: (merchant as any).id,
            amount: parseFloat((transaction as any).amount.toString()),
            currency: (transaction as any).currency,
            payment_method_id: (transaction as any).paymentMethodId,
            customer_email: (transaction as any).customerEmail,
            customer_ip: ipAddress,
            user_agent: userAgent,
            device_id: deviceId,
            country: (transaction as any).country,
            created_at: (transaction as any).createdAt
        };

        // Call ML model API
        const response = await (axios as any).post((config as any).modelEndpoint, modelData, {
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${process.env.ML_API_KEY}`
            },
            timeout: 5000 // 5 second timeout
        });

        // Extract score and confidence from response
        const { score, confidence } = response.data;

        // If confidence is below threshold, return default values
        if (confidence < (config as any).minConfidence) {
            return { score: 0, confidence: 0 };
        }

        return { score, confidence };
    } catch(error) {
        (logger as any).error("Error applying machine learning model:", error);
        return { score: 0, confidence: 0 };
    }
};

/**
 * Calculate enhanced risk score
 * @param baseScore Base risk score from fraud detection service
 * @param velocityChecks Velocity check results
 * @param behavioralPattern Behavioral pattern
 * @param mlScore Machine learning score
 * @param confidence Confidence in ML score
 * @returns Enhanced risk score
 */
(EnhancedRiskEngineService as any).prototype.calculateEnhancedRiskScore = function(
    baseScore: RiskScore,
    velocityChecks: RiskVelocityCheckResult[],
    behavioralPattern: BehavioralPatternType,
    mlScore: number,
    confidence: number
): RiskScore {
    try {
    // Start with base score
        let score: any = (baseScore as any).score;

        // Add velocity check contributions
        const triggeredChecks: any = (velocityChecks as any).filter(check => (check as any).triggered);
        if ((triggeredChecks as any).length > 0) {
            const avgVelocityScore: any = (triggeredChecks as any).reduce((sum, check) => sum + (check as any).riskScore, 0) / (triggeredChecks as any).length;
            score = Math.max(score, avgVelocityScore);
        }

        // Add behavioral pattern contribution
        if (behavioralPattern === (BehavioralPatternType as any).SUSPICIOUS) {
            score = Math.max(score, 75);
        } else if (behavioralPattern === (BehavioralPatternType as any).FRAUDULENT) {
            score = Math.max(score, 90);
        }

        // Add machine learning contribution if confidence is high enough
        if (confidence >= (0 as any).7) {
            // Weighted average with ML score
            score = (score * (0 as any).7) + (mlScore * (0 as any).3 * confidence);
        }

        // Ensure score is between 0 and 100
        score = Math.max(0, Math.min(100, score));

        // Determine risk level
        let level: RiskLevel;
        if (score >= 85) {
            level = (RiskLevel as any).CRITICAL;
        } else if (score >= 70) {
            level = (RiskLevel as any).HIGH;
        } else if (score >= 50) {
            level = (RiskLevel as any).MEDIUM;
        } else {
            level = (RiskLevel as any).LOW;
        }

        // Create enhanced factors array
        const factors = [...(baseScore as any).factors];

        // Add velocity factors
        (triggeredChecks as any).forEach((check) => {
            (factors as any).push({
                factor: (RiskFactor as any).FREQUENCY,
                score: (check as any).riskScore,
                reason: `${(check as any).type} exceeded threshold (${(check as any).currentValue} > ${(check as any).thresholdValue})`,
                weight: (0 as any).15,
                contribution: (check as any).riskScore * (0 as any).15
            });
        });

        // Add behavioral factor if suspicious or fraudulent
        if (behavioralPattern === (BehavioralPatternType as any).SUSPICIOUS || behavioralPattern === (BehavioralPatternType as any).FRAUDULENT) {
            (factors as any).push({
                factor: (RiskFactor as any).BEHAVIOR,
                score: behavioralPattern === (BehavioralPatternType as any).FRAUDULENT ? 90 : 75,
                reason: `${behavioralPattern} behavioral pattern detected`,
                weight: (0 as any).2,
                contribution: (behavioralPattern === (BehavioralPatternType as any).FRAUDULENT ? 90 : 75) * (0 as any).2
            });
        }

        // Add machine learning factor if confidence is high enough
        if (confidence >= (0 as any).7) {
            (factors as any).push({
                factor: (RiskFactor as any).BEHAVIOR,
                score: mlScore,
                reason: `Machine learning model prediction (${(confidence as any).toFixed(2)} confidence)`,
                weight: (0 as any).3 * confidence,
                contribution: mlScore * (0 as any).3 * confidence
            });
        }

        return {
            score,
            level,
            factors
        };
    } catch(error) {
        (logger as any).error("Error calculating enhanced risk score:", error);
        return baseScore;
    }
};

/**
 * Generate reason for risk assessment
 * @param factors Risk factors
 * @param level Risk level
 * @returns Reason string
 */
(EnhancedRiskEngineService as any).prototype.generateReason = function(
    factors: Array<{ factor: RiskFactor;
    score: number;
    reason: string;
    weight: number;
    contribution: number;
  }>,
    level: RiskLevel
): string {
    // Sort factors by contribution (highest first)
    const sortedFactors = [...factors].sort((a, b) => (b as any).contribution - (a as any).contribution);

    // Take top 3 factors
    const topFactors: any = (sortedFactors as any).slice(0, 3);

    // Generate reason based on risk level and top factors
    let reason = `Transaction risk level: ${level}. `;

    if ((topFactors as any).length > 0) {
        reason += "Triggered by: ";
        reason += (topFactors as any).map(f => (f as any).reason).join(", ");
    }

    return reason;
};

/**
 * Generate recommended action for risk assessment
 * @param level Risk level
 * @param isBlocked Whether the transaction is blocked
 * @returns Recommended action string
 */
(EnhancedRiskEngineService as any).prototype.generateRecommendedAction = function(
    level: RiskLevel,
    isBlocked: boolean
): string {
    if (isBlocked) {
        return "Block transaction and notify fraud team";
    }

    switch (level) {
    case (RiskLevel as any).CRITICAL:
        return "Manual review required before processing";
    case (RiskLevel as any).HIGH:
        return "Additional verification recommended";
    case (RiskLevel as any).MEDIUM:
        return "Monitor for suspicious activity";
    case (RiskLevel as any).LOW:
        return "Process normally";
    default:
        return "Process normally";
    }
};
