#!/usr/bin/env node

/**
 * Phase 2: Specific Error Pattern Fixes
 * Targets the most common remaining TypeScript error patterns
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎯 PHASE 2: SPECIFIC ERROR PATTERN FIXES');
console.log('========================================');

// Specific error pattern fixes
const specificFixes = {
    // Remove redundant unknown type annotations where TypeScript can infer
    ': unknown = new ': ' = new ',
    ': unknown = await ': ' = await ',
    ': unknown = async ': ' = async ',
    ': unknown = function': ' = function',
    ': unknown = (': ' = (',
    ': unknown = [': ' = [',
    ': unknown = {': ' = {',
    ': unknown = `': ' = `',
    ': unknown = "': ' = "',
    ': unknown = \'': ' = \'',
    ': unknown = true': ' = true',
    ': unknown = false': ' = false',
    ': unknown = null': ' = null',
    ': unknown = undefined': ' = undefined',
    ': unknown = 0': ' = 0',
    ': unknown = 1': ' = 1',
    ': unknown = -1': ' = -1',
    
    // Fix method call patterns
    ': unknown =this.': ' = this.',
    ': unknown =super.': ' = super.',
    ': unknown =req.': ' = req.',
    ': unknown =res.': ' = res.',
    ': unknown =next.': ' = next.',
    ': unknown =prisma.': ' = prisma.',
    ': unknown =logger.': ' = logger.',
    ': unknown =console.': ' = console.',
    ': unknown =process.': ' = process.',
    ': unknown =Buffer.': ' = Buffer.',
    ': unknown =JSON.': ' = JSON.',
    ': unknown =Math.': ' = Math.',
    ': unknown =Date.': ' = Date.',
    ': unknown =String.': ' = String.',
    ': unknown =Number.': ' = Number.',
    ': unknown =Boolean.': ' = Boolean.',
    ': unknown =Array.': ' = Array.',
    ': unknown =Object.': ' = Object.',
    ': unknown =Promise.': ' = Promise.',
    ': unknown =Error.': ' = Error.',
    ': unknown =RegExp.': ' = RegExp.',
    ': unknown =URL.': ' = URL.',
    ': unknown =crypto.': ' = crypto.',
    ': unknown =fs.': ' = fs.',
    ': unknown =path.': ' = path.',
    
    // Fix property access patterns
    ': unknown =result.': ' = result.',
    ': unknown =data.': ' = data.',
    ': unknown =response.': ' = response.',
    ': unknown =user.': ' = user.',
    ': unknown =merchant.': ' = merchant.',
    ': unknown =payment.': ' = payment.',
    ': unknown =transaction.': ' = transaction.',
    ': unknown =config.': ' = config.',
    ': unknown =options.': ' = options.',
    ': unknown =settings.': ' = settings.',
    ': unknown =error.': ' = error.',
    ': unknown =event.': ' = event.',
    ': unknown =item.': ' = item.',
    ': unknown =row.': ' = row.',
    ': unknown =record.': ' = record.',
    ': unknown =entity.': ' = entity.',
    ': unknown =model.': ' = model.',
    ': unknown =instance.': ' = instance.',
    ': unknown =service.': ' = service.',
    ': unknown =repository.': ' = repository.',
    ': unknown =controller.': ' = controller.',
    ': unknown =middleware.': ' = middleware.',
    ': unknown =validator.': ' = validator.',
    ': unknown =factory.': ' = factory.',
    ': unknown =manager.': ' = manager.',
    ': unknown =handler.': ' = handler.',
    ': unknown =provider.': ' = provider.',
    ': unknown =adapter.': ' = adapter.',
    ': unknown =helper.': ' = helper.',
    ': unknown =utility.': ' = utility.',
    ': unknown =util.': ' = util.',
    
    // Fix array/object access patterns
    ': unknown =items[': ' = items[',
    ': unknown =list[': ' = list[',
    ': unknown =array[': ' = array[',
    ': unknown =results[': ' = results[',
    ': unknown =data[': ' = data[',
    ': unknown =config[': ' = config[',
    ': unknown =options[': ' = options[',
    ': unknown =settings[': ' = settings[',
    ': unknown =params[': ' = params[',
    ': unknown =query[': ' = query[',
    ': unknown =headers[': ' = headers[',
    ': unknown =body[': ' = body[',
    ': unknown =req[': ' = req[',
    ': unknown =res[': ' = res[',
    
    // Fix function call patterns
    ': unknown =parseInt(': ' = parseInt(',
    ': unknown =parseFloat(': ' = parseFloat(',
    ': unknown =isNaN(': ' = isNaN(',
    ': unknown =isFinite(': ' = isFinite(',
    ': unknown =encodeURIComponent(': ' = encodeURIComponent(',
    ': unknown =decodeURIComponent(': ' = decodeURIComponent(',
    ': unknown =encodeURI(': ' = encodeURI(',
    ': unknown =decodeURI(': ' = decodeURI(',
    ': unknown =escape(': ' = escape(',
    ': unknown =unescape(': ' = unescape(',
    ': unknown =eval(': ' = eval(',
    ': unknown =setTimeout(': ' = setTimeout(',
    ': unknown =setInterval(': ' = setInterval(',
    ': unknown =clearTimeout(': ' = clearTimeout(',
    ': unknown =clearInterval(': ' = clearInterval(',
    ': unknown =setImmediate(': ' = setImmediate(',
    ': unknown =clearImmediate(': ' = clearImmediate(',
    ': unknown =require(': ' = require(',
    
    // Fix conditional expressions
    ': unknown =condition ?': ' = condition ?',
    ': unknown =!': ' = !',
    ': unknown =!!': ' = !!',
    ': unknown =typeof ': ' = typeof ',
    ': unknown =instanceof ': ' = instanceof ',
    ': unknown =in ': ' = in ',
    
    // Fix arithmetic expressions
    ': unknown =+': ' = +',
    ': unknown =-': ' = -',
    ': unknown =*': ' = *',
    ': unknown =/': ' = /',
    ': unknown =%': ' = %',
    ': unknown =**': ' = **',
    ': unknown =++': ' = ++',
    ': unknown =--': ' = --',
    
    // Fix logical expressions
    ': unknown =&&': ' = &&',
    ': unknown =||': ' = ||',
    ': unknown =??': ' = ??',
    
    // Fix comparison expressions
    ': unknown ===': ' = ==',
    ': unknown =!==': ' = !==',
    ': unknown =!=': ' = !=',
    ': unknown =<': ' = <',
    ': unknown =>': ' = >',
    ': unknown =<=': ' = <=',
    ': unknown =>=': ' = >=',
    
    // Fix bitwise expressions
    ': unknown =&': ' = &',
    ': unknown =|': ' = |',
    ': unknown =^': ' = ^',
    ': unknown =~': ' = ~',
    ': unknown =<<': ' = <<',
    ': unknown =>>': ' = >>',
    ': unknown =>>>': ' = >>>',
    
    // Fix template literals
    ': unknown =$': ' = $',
    
    // Fix spread/rest operators
    ': unknown =...': ' = ...',
    
    // Fix destructuring patterns
    ': unknown ={...': ' = {...',
    ': unknown =[...': ' = [...',
    
    // Fix class patterns
    ': unknown =class ': ' = class ',
    ': unknown =extends ': ' = extends ',
    ': unknown =implements ': ' = implements ',
    
    // Fix import/export patterns
    ': unknown =import(': ' = import(',
    ': unknown =import ': ' = import ',
    ': unknown =export ': ' = export ',
    
    // Fix async/await patterns
    ': unknown =async (': ' = async (',
    ': unknown =async function': ' = async function',
    ': unknown =await new ': ' = await new ',
    ': unknown =await this.': ' = await this.',
    ': unknown =await super.': ' = await super.',
    ': unknown =await prisma.': ' = await prisma.',
    ': unknown =await Promise.': ' = await Promise.',
    
    // Fix generator patterns
    ': unknown =function*': ' = function*',
    ': unknown =yield ': ' = yield ',
    ': unknown =yield*': ' = yield*',
    
    // Fix arrow function patterns
    ': unknown =() =>': ' = () =>',
    ': unknown =(x) =>': ' = (x) =>',
    ': unknown =(x, y) =>': ' = (x, y) =>',
    ': unknown =x =>': ' = x =>',
    
    // Fix specific method chains
    ': unknown =.map(': ' = .map(',
    ': unknown =.filter(': ' = .filter(',
    ': unknown =.reduce(': ' = .reduce(',
    ': unknown =.find(': ' = .find(',
    ': unknown =.forEach(': ' = .forEach(',
    ': unknown =.some(': ' = .some(',
    ': unknown =.every(': ' = .every(',
    ': unknown =.sort(': ' = .sort(',
    ': unknown =.slice(': ' = .slice(',
    ': unknown =.splice(': ' = .splice(',
    ': unknown =.push(': ' = .push(',
    ': unknown =.pop(': ' = .pop(',
    ': unknown =.shift(': ' = .shift(',
    ': unknown =.unshift(': ' = .unshift(',
    ': unknown =.includes(': ' = .includes(',
    ': unknown =.indexOf(': ' = .indexOf(',
    ': unknown =.lastIndexOf(': ' = .lastIndexOf(',
    ': unknown =.join(': ' = .join(',
    ': unknown =.split(': ' = .split(',
    ': unknown =.replace(': ' = .replace(',
    ': unknown =.replaceAll(': ' = .replaceAll(',
    ': unknown =.match(': ' = .match(',
    ': unknown =.search(': ' = .search(',
    ': unknown =.test(': ' = .test(',
    ': unknown =.exec(': ' = .exec(',
    ': unknown =.toString(': ' = .toString(',
    ': unknown =.valueOf(': ' = .valueOf(',
    ': unknown =.toUpperCase(': ' = .toUpperCase(',
    ': unknown =.toLowerCase(': ' = .toLowerCase(',
    ': unknown =.trim(': ' = .trim(',
    ': unknown =.trimStart(': ' = .trimStart(',
    ': unknown =.trimEnd(': ' = .trimEnd(',
    ': unknown =.padStart(': ' = .padStart(',
    ': unknown =.padEnd(': ' = .padEnd(',
    ': unknown =.substring(': ' = .substring(',
    ': unknown =.substr(': ' = .substr(',
    ': unknown =.charAt(': ' = .charAt(',
    ': unknown =.charCodeAt(': ' = .charCodeAt(',
    ': unknown =.codePointAt(': ' = .codePointAt(',
    ': unknown =.normalize(': ' = .normalize(',
    ': unknown =.repeat(': ' = .repeat(',
    ': unknown =.startsWith(': ' = .startsWith(',
    ': unknown =.endsWith(': ' = .endsWith(',
    ': unknown =.localeCompare(': ' = .localeCompare(',
    
    // Fix object method patterns
    ': unknown =.hasOwnProperty(': ' = .hasOwnProperty(',
    ': unknown =.propertyIsEnumerable(': ' = .propertyIsEnumerable(',
    ': unknown =.isPrototypeOf(': ' = .isPrototypeOf(',
    ': unknown =.toLocaleString(': ' = .toLocaleString(',
    
    // Fix date method patterns
    ': unknown =.getTime(': ' = .getTime(',
    ': unknown =.getFullYear(': ' = .getFullYear(',
    ': unknown =.getMonth(': ' = .getMonth(',
    ': unknown =.getDate(': ' = .getDate(',
    ': unknown =.getDay(': ' = .getDay(',
    ': unknown =.getHours(': ' = .getHours(',
    ': unknown =.getMinutes(': ' = .getMinutes(',
    ': unknown =.getSeconds(': ' = .getSeconds(',
    ': unknown =.getMilliseconds(': ' = .getMilliseconds(',
    ': unknown =.getTimezoneOffset(': ' = .getTimezoneOffset(',
    ': unknown =.setTime(': ' = .setTime(',
    ': unknown =.setFullYear(': ' = .setFullYear(',
    ': unknown =.setMonth(': ' = .setMonth(',
    ': unknown =.setDate(': ' = .setDate(',
    ': unknown =.setHours(': ' = .setHours(',
    ': unknown =.setMinutes(': ' = .setMinutes(',
    ': unknown =.setSeconds(': ' = .setSeconds(',
    ': unknown =.setMilliseconds(': ' = .setMilliseconds(',
    ': unknown =.toISOString(': ' = .toISOString(',
    ': unknown =.toDateString(': ' = .toDateString(',
    ': unknown =.toTimeString(': ' = .toTimeString(',
    ': unknown =.toLocaleDateString(': ' = .toLocaleDateString(',
    ': unknown =.toLocaleTimeString(': ' = .toLocaleTimeString(',
    
    // Fix number method patterns
    ': unknown =.toFixed(': ' = .toFixed(',
    ': unknown =.toExponential(': ' = .toExponential(',
    ': unknown =.toPrecision(': ' = .toPrecision(',
    ': unknown =.toLocaleString(': ' = .toLocaleString(',
    
    // Fix promise method patterns
    ': unknown =.then(': ' = .then(',
    ': unknown =.catch(': ' = .catch(',
    ': unknown =.finally(': ' = .finally(',
    
    // Fix specific common patterns
    ': unknown =!result': ' = !result',
    ': unknown =!data': ' = !data',
    ': unknown =!response': ' = !response',
    ': unknown =!user': ' = !user',
    ': unknown =!error': ' = !error',
    ': unknown =!!result': ' = !!result',
    ': unknown =!!data': ' = !!data',
    ': unknown =!!response': ' = !!response',
    ': unknown =!!user': ' = !!user',
    ': unknown =!!error': ' = !!error',
};

function findAllTypeScriptFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'coverage'].includes(item)) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

function getErrorCount() {
    try {
        const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { encoding: 'utf8' });
        const errorMatches = output.match(/error TS/g) || [];
        return errorMatches.length;
    } catch (error) {
        const errorMatches = error.stdout.match(/error TS/g) || [];
        return errorMatches.length;
    }
}

function processFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let modifiedContent = content;
        let fixCount = 0;
        
        // Apply all specific fixes
        for (const [oldPattern, newPattern] of Object.entries(specificFixes)) {
            const regex = new RegExp(escapeRegExp(oldPattern), 'g');
            const matches = modifiedContent.match(regex);
            if (matches) {
                modifiedContent = modifiedContent.replace(regex, newPattern);
                fixCount += matches.length;
            }
        }
        
        if (fixCount > 0) {
            fs.writeFileSync(filePath, modifiedContent, 'utf8');
            return { filePath, fixCount };
        }
        
        return null;
    } catch (error) {
        return { filePath, error: error.message };
    }
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

async function main() {
    console.log('🔍 Scanning for TypeScript files...');
    
    const files = findAllTypeScriptFiles('./src');
    console.log(`📁 Found ${files.length} TypeScript files`);
    
    console.log('📊 Getting initial error count...');
    const initialErrors = getErrorCount();
    console.log(`🚨 Initial TypeScript errors: ${initialErrors}`);
    
    console.log('🚀 Starting specific pattern fixes...');
    const startTime = Date.now();
    
    const results = [];
    let totalFixedIssues = 0;
    
    for (const file of files) {
        const result = processFile(file);
        if (result) {
            results.push(result);
            if (result.fixCount) {
                totalFixedIssues += result.fixCount;
            }
        }
    }
    
    console.log('📊 Getting final error count...');
    const finalErrors = getErrorCount();
    const totalErrorsFixed = initialErrors - finalErrors;
    
    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;
    
    console.log('\n🎯 SPECIFIC PATTERN FIXES COMPLETE!');
    console.log('===================================');
    console.log(`⏱️  Processing time: ${processingTime.toFixed(2)} seconds`);
    console.log(`📁 Files processed: ${results.filter(r => !r.error).length}`);
    console.log(`❌ Files with errors: ${results.filter(r => r.error).length}`);
    console.log(`🔧 Total fixes applied: ${totalFixedIssues}`);
    console.log(`🚨 TypeScript errors before: ${initialErrors}`);
    console.log(`✅ TypeScript errors after: ${finalErrors}`);
    console.log(`🎯 Total errors fixed: ${totalErrorsFixed}`);
    
    if (totalErrorsFixed > 0) {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}%`);
        console.log('\n🎉 SUCCESS! Specific pattern fixes applied successfully!');
        console.log('🏆 Your application now has improved type safety!');
    } else if (totalErrorsFixed === 0) {
        console.log('📈 Success rate: 0.0% (no net change)');
        console.log('\n✨ No errors were fixed, but no new errors were introduced!');
    } else {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}% (negative - new errors introduced)`);
        console.log('\n⚠️  Some fixes may have introduced new errors. Consider reverting.');
    }
    
    const errorFiles = results.filter(r => r.error);
    if (errorFiles.length > 0) {
        console.log('\n❌ Files with processing errors:');
        errorFiles.forEach(({ filePath, error }) => {
            console.log(`   ${path.relative(process.cwd(), filePath)}: ${error}`);
        });
    }
}

main().catch(console.error);
