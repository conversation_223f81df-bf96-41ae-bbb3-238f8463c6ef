// jscpd:ignore-file
import express, { Application } from "express";
import { RouteTestSuite } from "../suites/RouteTestSuite";
import { Container } from "../../core/Container";
import { ContainerBootstrap } from "../../core/ContainerBootstrap";
import { logger } from "../../lib/logger";
import { Container } from "../../core/Container";
import { ContainerBootstrap } from "../../core/ContainerBootstrap";
import { logger } from "../../lib/logger";

/**
 * Route test runner
 * This class runs route tests
 */
export class RouteTestRunner {
  private app: Application;
  private container: Container;
  private routeTestSuite: RouteTestSuite;
  
  /**
   * Create a new route test runner
   */
  constructor() {
    // Create Express application
    this.app = express();
    
    // Create container
    this.container = Container.getInstance();
    
    // Bootstrap container
    ContainerBootstrap.bootstrap(this.container);
    
    // Create route test suite
    this.routeTestSuite = new RouteTestSuite(this.app);
  }
  
  /**
   * Run all tests
   */
  public async runAllTests(): Promise<void> {
    logger.info("Running all route tests...");
    
    try {
      await this.routeTestSuite.runAllTests();
      logger.info("All route tests passed");
    } catch (error) {
      logger.error("Route tests failed:", error);
      throw error;
    }
  }
  
  /**
   * Get the Express application
   * @returns Express application
   */
  public getApp(): Application {
    return this.app;
  }
  
  /**
   * Get the container
   * @returns Container
   */
  public getContainer(): Container {
    return this.container;
  }
  
  /**
   * Get the route test suite
   * @returns Route test suite
   */
  public getRouteTestSuite(): RouteTestSuite {
    return this.routeTestSuite;
  }
}

export default RouteTestRunner;
