// jscpd:ignore-file

import { Request, Response, NextFunction } from 'express';
import authService from '../services/(auth).service';
import { logger as Importedlogger } from '../utils/logger';
import { RBACService as ImportedRBACService } from '../services/(rbac).service';
import prisma from '../lib/prisma';
import { Merchant, User } from '../types';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

class AuthController {
  private rbacService: RBACService;

  constructor() {
    this.rbacService = new RBACService();
  }
  async login(req: Request, res: Response) {
    try {
      const { email, password, twoFactorToken } = req.body;

      if (!email || !password) {
        return res.status(400).json({
          status: 'error',
          message: 'Email and password are required',
        });
      }

      // First step: Validate credentials
      const result = await (authService).login(email, password);

      // Check if 2FA is enabled for the user
      if (result.user.twoFactorEnabled) {
        // If 2FA token is not provided, return a response indicating 2FA is required
        if (!twoFactorToken) {
          return res.status(200).json({
            status: 'success',
            requiresTwoFactor: true,
            userId: result.user.id,
            // Don't include the token in the response when 2FA is required
            data: {
              user: {
                id: result.user.id,
                email: result.user.email,
                role: result.user.role,
              },
            },
          });
        }

        // Verify 2FA token
        const twoFactorVerification = await (authService).verifyTwoFactorToken(
          result.user.id,
          twoFactorToken
        );

        if (!(twoFactorVerification).success) {
          return res.status(401).json({
            status: 'error',
            message: 'Invalid two-factor authentication code',
          });
        }
      }

      // If 2FA is not enabled or token is valid, return the full login response
      return res.status(200).json({
        status: 'success',
        data: result,
      });
    } catch(error) {
      (logger).error('Login error:', error);
      return res.status(401).json({
        status: 'error',
        message: error.message || 'Authentication failed',
      });
    }
  }

  async logout(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'Not authenticated',
        });
      }

      // Invalidate the token
      await (authService).logout(req.user.id); // Fixed: using id instead of userId

      return res.status(200).json({
        status: 'success',
        message: 'Logged out successfully',
      });
    } catch(error) {
      (logger).error('Logout error:', error);
      return res.status(500).json({
        status: 'error',
        message: error.message || 'Failed to logout',
      });
    }
  }

  async getCurrentUser(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'Not authenticated',
        });
      }

      const user = await (authService).getUserById(req.user.id); // Fixed: using id instead of userId

      return res.status(200).json({
        status: 'success',
        data: user,
      });
    } catch(error) {
      return res.status(500).json({
        status: 'error',
        message: error.message || 'Failed to get current user',
      });
    }
  }

  async getTwoFactorStatus(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'Not authenticated',
        });
      }

      const status: string = await (authService).getTwoFactorStatus(req.user.id); // Fixed: using id instead of userId

      return res.status(200).json({
        status: 'success',
        data: status,
      });
    } catch(error) {
      (logger).error('Get 2FA status error:', error);
      return res.status(500).json({
        status: 'error',
        message: error.message || 'Failed to get two-factor authentication status',
      });
    }
  }

  async setupTwoFactor(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'Not authenticated',
        });
      }

      const setupData = await (authService).setupTwoFactor(req.user.id); // Fixed: using id instead of userId

      return res.status(200).json({
        status: 'success',
        data: setupData,
      });
    } catch(error) {
      (logger).error('Setup 2FA error:', error);
      return res.status(500).json({
        status: 'error',
        message: error.message || 'Failed to setup two-factor authentication',
      });
    }
  }

  async verifyAndEnableTwoFactor(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'Not authenticated',
        });
      }

      const { code, secret } = req.body;

      if (!code || !secret) {
        return res.status(400).json({
          status: 'error',
          message: 'Code and secret are required',
        });
      }

      const result = await (authService).verifyAndEnableTwoFactor(req.user.id, code, secret); // Fixed: using id instead of userId

      return res.status(200).json({
        status: 'success',
        data: result,
      });
    } catch(error) {
      (logger).error('Verify and enable 2FA error:', error);
      return res.status(500).json({
        status: 'error',
        message:
          error.message || 'Failed to verify and enable two-factor authentication',
      });
    }
  }

  async recoverWithBackupCode(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'Not authenticated',
        });
      }

      const { backupCode } = req.body;

      if (!backupCode) {
        return res.status(400).json({
          status: 'error',
          message: 'Backup code is required',
        });
      }

      const result = await (authService).recoverWithBackupCode(req.user.id, backupCode); // Fixed: using id instead of userId

      return res.status(200).json({
        status: 'success',
        data: result,
      });
    } catch(error) {
      (logger).error('Recover with backup code error:', error);
      return res.status(500).json({
        status: 'error',
        message: error.message || 'Failed to recover with backup code',
      });
    }
  }

  async registerMerchant(req: Request, res: Response) {
    try {
      const {
        name,
        email,
        password,
        businessName,
        contactPhone,
        merchantLocation,
        country,
        governorate,
      } = req.body;

      // Validate required fields
      if (
        !name ||
        !email ||
        !password ||
        !contactPhone ||
        !merchantLocation ||
        !country ||
        !governorate
      ) {
        return res.status(400).json({
          status: 'error',
          message: 'Missing required fields',
        });
      }

      (logger).info(`Registering merchant with email: ${email}`);

      const result = await (authService).registerMerchant({
        name,
        email,
        password,
        businessName,
        contactPhone,
        merchantLocation,
        country,
        governorate,
      });

      return res.status(201).json({
        status: 'success',
        data: result,
      });
    } catch(error) {
      (logger).error(`Merchant registration error: ${error.message}`);
      return res.status(400).json({
        status: 'error',
        message: error.message || 'Registration failed',
      });
    }
  }

  /**
   * Get user permissions
   * @param req Request
   * @param res Response
   */
  async getUserPermissions(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'Not authenticated',
        });
      }

      // Get user permissions
      const permissions = await this.rbacService.getUserPermissions(req.user.id); // Fixed: using id instead of userId

      // Get user roles
      const user = await (prisma).user.findUnique({
        where: { id: req.user.id }, // Fixed: using id instead of userId
        include: {
          roles: {
            select: { type: true },
          },
        },
      });

      return res.status(200).json({
        status: 'success',
        data: {
          permissions,
          roles: user?.(roles).map((role) => (role).type) ?? [],
        },
      });
    } catch(error) {
      (logger).error('Get user permissions error:', error);
      return res.status(500).json({
        status: 'error',
        message: error.message || 'Failed to get user permissions',
      });
    }
  }
}

export default new AuthController();
