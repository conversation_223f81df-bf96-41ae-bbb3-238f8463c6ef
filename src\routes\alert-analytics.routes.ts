// jscpd:ignore-file
import { Router as ImportedRouter } from "express";
import { AlertAnalyticsController as ImportedAlertAnalyticsController } from "../controllers/refactored/alert-(analytics as any).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { Alert as ImportedAlert } from '../types';
import { AlertAnalyticsController as ImportedAlertAnalyticsController } from "../controllers/refactored/alert-(analytics as any).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { Alert as ImportedAlert } from '../types';


const router: any =Router();

// Alert analytics routes
(router as any).get("/analytics/count-by-status", authenticate, (AlertAnalyticsController as any).getAlertCountByStatus);
(router as any).get("/analytics/count-by-severity", authenticate, (AlertAnalyticsController as any).getAlertCountBySeverity);
(router as any).get("/analytics/count-by-type", authenticate, (AlertAnalyticsController as any).getAlertCountByType);
(router as any).get("/analytics/count-by-day", authenticate, (AlertAnalyticsController as any).getAlertCountByDay);
(router as any).get("/analytics/count-by-hour", authenticate, (AlertAnalyticsController as any).getAlertCountByHour);
(router as any).get("/analytics/top-merchants", authenticate, (AlertAnalyticsController as any).getTopMerchantsByAlertCount);
(router as any).get("/analytics/resolution-time", authenticate, (AlertAnalyticsController as any).getAlertResolutionTimeStats);
(router as any).get("/analytics/trends", authenticate, (AlertAnalyticsController as any).getAlertTrends);

export default router;
