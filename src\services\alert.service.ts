// jscpd:ignore-file
import { BaseService } from '../shared/modules/services/BaseService';
import { logger } from '../utils/logger';
import { config } from '../config';
import prisma from '../lib/prisma';
import { WebhookService, WebhookEventType } from './webhook.service';
import { EmailService } from './email.service';
import { SmsService } from './sms.service';
import { NotificationService, NotificationChannel, NotificationPriority } from './notification.service';
import { User } from '../types';
import {
  Alert,
  AlertType,
  AlertSeverity,
  AlertStatus,
  AlertNotificationMethod,
  AlertData
} from '../types/alert.types';

// Using imported AlertSeverity, AlertType, AlertStatus, and AlertNotificationMethod from types/alert.types.ts

/**
 * Alert data
 */
export interface AlertData {
  type: AlertType;
  severity: AlertSeverity;
  title: string;
  message: string;
  source?: string;
  details?: Record<string, any>;
  merchantId?: string;
  notificationMethods?: AlertNotificationMethod[];
}

/**
 * Alert service
 */
export class AlertService extends BaseService {
  private webhookService: WebhookService;
  private emailService: EmailService;
  private smsService: SmsService;
  private notificationService: NotificationService;

  /**
   * Create a new alert service
   */
  constructor() {
    super();
    this.webhookService = new WebhookService();
    this.emailService = new EmailService();
    this.smsService = new SmsService();
    this.notificationService = new NotificationService();
  }

  /**
   * Create a new alert
   * @param data Alert data
   * @returns Alert ID
   */
  public async createAlert(data: AlertData): Promise<string> {
    try {
      // Create alert in database
      const alert = await prisma.alert.create({
        data: { type: data.type,
          severity: data.severity,
          title: data.title,
          message: data.message,
          source: data.source || 'system',
          details: data.details ?? {},
          merchantId: data.merchantId,
          status: AlertStatus.ACTIVE,
        },
      });

      // Send notifications
      await this.sendAlertNotifications(alert.id, data);

      return alert.id;
    } catch (error) {
      logger.error('Error creating alert', { error, data });
      throw new Error('Failed to create alert');
    }
  }

  /**
   * Send alert notifications
   * @param alertId Alert ID
   * @param data Alert data
   */
  private async sendAlertNotifications(alertId: string, data: AlertData): Promise<void> {
    try {
      const notificationMethods: unknown = data.notificationMethods || [AlertNotificationMethod.DASHBOARD];

      // Send email notification
      if (notificationMethods.includes(AlertNotificationMethod.EMAIL)) {
        await this.sendEmailNotification(alertId, data);
      }

      // Send SMS notification
      if (notificationMethods.includes(AlertNotificationMethod.SMS)) {
        await this.sendSmsNotification(alertId, data);
      }

      // Send webhook notification
      if (notificationMethods.includes(AlertNotificationMethod.WEBHOOK) && data.merchantId) {
        await this.sendWebhookNotification(alertId, data);
      }

      // Send Telegram notification
      if (notificationMethods.includes(AlertNotificationMethod.TELEGRAM)) {
        await this.sendTelegramNotification(alertId, data);
      }

      // Update alert with notification status
      await prisma.alert.update({
        where: { id: alertId },
        data: { notifiedAt: new Date(),
          notificationMethods: notificationMethods,
        },
      });
    } catch (error) {
      logger.error('Error sending alert notifications', { error, alertId, data });
    }
  }

  /**
   * Send email notification
   * @param alertId Alert ID
   * @param data Alert data
   */
  private async sendEmailNotification(alertId: string, data: AlertData): Promise<void> {
    try {
      // Get admin emails
      const adminEmails = await this.emailService.getAdminEmails();

      // Get merchant email if merchantId is provided
      let merchantEmail: string = '';
      if (data.merchantId) {
        const merchant = await prisma.merchant.findUnique({
          where: { id: data.merchantId },
          select: { email: true },
        });
        if (merchant) {
          merchantEmail = merchant.email;
        }
      }

      // Determine recipients based on severity and merchant
      let recipients: string[] = [];
      switch (data.severity) {
        case AlertSeverity.CRITICAL:
          // Send to all admins for critical alerts
          recipients = adminEmails;
          // Also send to merchant if it's merchant-specific
          if (merchantEmail) {
            recipients.push(merchantEmail);
          }
          break;
        case AlertSeverity.ERROR:
          // Send to all admins for error alerts
          recipients = adminEmails;
          // Also send to merchant if it's merchant-specific
          if (merchantEmail) {
            recipients.push(merchantEmail);
          }
          break;
        case AlertSeverity.WARNING:
          // Send to merchant if it's merchant-specific
          if (merchantEmail) {
            recipients.push(merchantEmail);
          }
          // Also send to admins for system warnings
          if (!data.merchantId) {
            recipients = [...recipients, ...adminEmails];
          }
          break;
        case AlertSeverity.INFO:
          // Only send info alerts to merchant if it's merchant-specific
          if (merchantEmail) {
            recipients.push(merchantEmail);
          }
          break;
      }

      // Skip if no recipients
      if (recipients.length === 0) {
        logger.debug('No recipients for email notification', { alertId, data });
        return;
      }

      // Get alert from database to ensure we have all fields
      const alert = await prisma.alert.findUnique({
        where: { id: alertId },
      });

      if (!alert) {
        logger.warn('Alert not found for email notification', { alertId });
        return;
      }

      // Send alert email
      const success: boolean = await this.emailService.sendAlertEmail(
        {
          id: alertId,
          type: data.type,
          severity: data.severity,
          title: data.title,
          message: data.message,
          source: data.source,
          details: data.details,
          merchantId: data.merchantId,
          createdAt: alert.createdAt,
        },
        recipients
      );

      if (success) {
        logger.info('Email notification sent', { alertId, recipients });
      } else {
        logger.warn('Failed to send email notification', { alertId, recipients });
      }
    } catch (error) {
      logger.error('Error sending email notification', { error, alertId, data });
    }
  }

  /**
   * Send SMS notification
   * @param alertId Alert ID
   * @param data Alert data
   */
  private async sendSmsNotification(alertId: string, data: AlertData): Promise<void> {
    try {
      // Get admin phone numbers
      const adminPhoneNumbers = await this.smsService.getAdminPhoneNumbers();

      // Get merchant phone number if merchantId is provided
      let merchantPhoneNumber: string | null = null;
      if (data.merchantId) {
        merchantPhoneNumber = await this.smsService.getMerchantPhoneNumber(data.merchantId);
      }

      // Determine recipients based on severity and merchant
      let recipients: string[] = [];
      switch (data.severity) {
        case AlertSeverity.CRITICAL:
          // Send to all admins for critical alerts
          recipients = adminPhoneNumbers;
          // Also send to merchant if it's merchant-specific
          if (merchantPhoneNumber) {
            recipients.push(merchantPhoneNumber);
          }
          break;
        case AlertSeverity.ERROR:
          // Send to all admins for error alerts
          recipients = adminPhoneNumbers;
          // Also send to merchant if it's merchant-specific
          if (merchantPhoneNumber) {
            recipients.push(merchantPhoneNumber);
          }
          break;
        case AlertSeverity.WARNING:
          // Send to merchant if it's merchant-specific
          if (merchantPhoneNumber) {
            recipients.push(merchantPhoneNumber);
          }
          // Also send to admins for system warnings
          if (!data.merchantId) {
            recipients = [...recipients, ...adminPhoneNumbers];
          }
          break;
        case AlertSeverity.INFO:
          // Only send info alerts to merchant if it's merchant-specific
          if (merchantPhoneNumber) {
            recipients.push(merchantPhoneNumber);
          }
          break;
      }

      // Skip if no recipients
      if (recipients.length === 0) {
        logger.debug('No recipients for SMS notification', { alertId, data });
        return;
      }

      // Get alert from database to ensure we have all fields
      const alert = await prisma.alert.findUnique({
        where: { id: alertId },
      });

      if (!alert) {
        logger.warn('Alert not found for SMS notification', { alertId });
        return;
      }

      // Send SMS to each recipient
      for (const phoneNumber of recipients) {
        const success: boolean = await this.smsService.sendAlertSms(
          {
            id: alertId,
            type: data.type,
            severity: data.severity,
            title: data.title,
            message: data.message,
            merchantId: data.merchantId,
          },
          phoneNumber
        );

        if (success) {
          logger.info('SMS notification sent', { alertId, phoneNumber });
        } else {
          logger.warn('Failed to send SMS notification', { alertId, phoneNumber });
        }
      }
    } catch (error) {
      logger.error('Error sending SMS notification', { error, alertId, data });
    }
  }

  /**
   * Send webhook notification
   * @param alertId Alert ID
   * @param data Alert data
   */
  private async sendWebhookNotification(alertId: string, data: AlertData): Promise<void> {
    try {
      if (!data.merchantId) {
        logger.warn('No merchant ID provided for webhook notification', { alertId, data });
        return;
      }

      // Create webhook event
      await this.webhookService.createWebhookEvent(
        data.merchantId,
        WebhookEventType.ALERT_CREATED,
        {
          alertId,
          type: data.type,
          severity: data.severity,
          title: data.title,
          message: data.message,
          source: data.source,
          details: data.details,
          createdAt: new Date().toISOString(),
        }
      );

      logger.info('Webhook notification sent', { alertId, merchantId: data.merchantId });
    } catch (error) {
      logger.error('Error sending webhook notification', { error, alertId, data });
    }
  }

  /**
   * Send Telegram notification
   * @param alertId Alert ID
   * @param data Alert data
   */
  private async sendTelegramNotification(alertId: string, data: AlertData): Promise<void> {
    try {
      // Map alert severity to notification priority
      let priority: NotificationPriority;
      switch (data.severity) {
        case AlertSeverity.CRITICAL:
          priority = NotificationPriority.CRITICAL;
          break;
        case AlertSeverity.ERROR:
          priority = NotificationPriority.HIGH;
          break;
        case AlertSeverity.WARNING:
          priority = NotificationPriority.MEDIUM;
          break;
        case AlertSeverity.INFO:
          priority = NotificationPriority.LOW;
          break;
        default:
          priority = NotificationPriority.MEDIUM;
      }

      // Format message
      const subject = `${data.severity.toUpperCase()}: ${data.title}`;
      const message: string = `*${data.title}*\n\n${data.message}\n\n*Type:* ${data.type}\n*Severity:* ${data.severity}\n*Alert ID:* ${alertId}`;

      // Send notification
      await this.notificationService.sendNotification({
        userId: undefined,
        merchantId: data.merchantId,
        channels: [NotificationChannel.TELEGRAM],
        priority,
        subject,
        message,
        metadata: {
          alertId,
          alertType: data.type,
          alertSeverity: data.severity,
        },
      });

      logger.info('Telegram notification sent', { alertId, merchantId: data.merchantId });
    } catch (error) {
      logger.error('Error sending Telegram notification', { error, alertId, merchantId: data.merchantId });
    }
  }


  /**
   * Get alerts with filtering and pagination
   * @param options Alert query options
   * @returns Alerts and total count
   */
  public async getAlerts(options: {
    merchantId?: string;
    status?: AlertStatus;
    severity?: AlertSeverity;
    type?: AlertType;
    startDate?: Date;
    endDate?: Date;
    search?: string;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ alerts: any[]; total: number }> {
    try {
      const {
        merchantId,
        status,
        severity,
        type,
        startDate,
        endDate,
        search,
        limit = 10,
        offset = 0,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = options;

      // Build where clause
      const where = {};

      // Add merchant filter
      if (merchantId) {
        where.merchantId = merchantId;
      }

      // Add status filter
      if (status) {
        where.status = status;
      }

      // Add severity filter
      if (severity) {
        where.severity = severity;
      }

      // Add type filter
      if (type) {
        where.type = type;
      }

      // Add date range filter
      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) {
          where.createdAt.gte = startDate;
        }
        if (endDate) {
          where.createdAt.lte = endDate;
        }
      }

      // Add search filter
      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { message: { contains: search, mode: 'insensitive' } },
          { source: { contains: search, mode: 'insensitive' } },
        ];
      }

      // Get total count
      const total: number = await prisma.alert.count({ where });

      // Build order by
      const orderBy = {};
      orderBy[sortBy] = sortOrder;

      // Get alerts
      const alerts = await prisma.alert.findMany({
        where,
        orderBy,
        take: limit,
        skip: offset,
      });

      return { alerts, total };
    } catch (error) {
      logger.error('Error getting alerts', { error, options });
      throw new Error('Failed to get alerts');
    }
  }

  /**
   * Update alert status
   * @param alertId Alert ID
   * @param status Alert status
   * @param userId User ID
   * @returns Updated alert
   */
  public async updateAlertStatus(
    alertId: string,
    status: AlertStatus,
    userId: string
  ): Promise<any> {
    try {
      const alert = await prisma.alert.update({
        where: { id: alertId },
        data: {
          status,
          updatedAt: new Date(),
          resolvedBy: status === AlertStatus.RESOLVED ? userId : undefined,
          resolvedAt: status === AlertStatus.RESOLVED ? new Date() : undefined,
          acknowledgedBy: status === AlertStatus.ACKNOWLEDGED ? userId : undefined,
          acknowledgedAt: status === AlertStatus.ACKNOWLEDGED ? new Date() : undefined,
        },
      });

      return alert;
    } catch (error) {
      logger.error('Error updating alert status', { error, alertId, status });
      throw new Error('Failed to update alert status');
    }
  }

  /**
   * Get alert by ID
   * @param alertId Alert ID
   * @returns Alert
   */
  public async getAlert(alertId: string): Promise<any> {
    try {
      const alert = await prisma.alert.findUnique({
        where: { id: alertId },
      });

      if (!alert) {
        throw new Error('Alert not found');
      }

      return alert;
    } catch (error) {
      logger.error('Error getting alert', { error, alertId });
      throw new Error('Failed to get alert');
    }
  }
}