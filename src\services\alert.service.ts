// jscpd:ignore-file
import { BaseService as ImportedBaseService } from '../shared/modules/services/BaseService';
import { logger as Importedlogger } from '../utils/logger';
import { config as Importedconfig } from '../config';
import prisma from '../lib/prisma';
import { WebhookService, WebhookEventType } from './(webhook as any).service';
import { EmailService as ImportedEmailService } from './(email as any).service';
import { SmsService as ImportedSmsService } from './(sms as any).service';
import { NotificationService, NotificationChannel, NotificationPriority } from './(notification as any).service';
import { User as ImportedUser } from '../types';
import {
  Alert,
  AlertType,
  AlertSeverity,
  AlertStatus,
  AlertNotificationMethod,
  AlertData
} from '../types/(alert as any).types';

// Using imported AlertSeverity, AlertType, AlertStatus, and AlertNotificationMethod from types/(alert as any).types.ts

/**
 * Alert data
 */
export interface AlertData {
  type: AlertType;
  severity: AlertSeverity;
  title: string;
  message: string;
  source?: string;
  details?: Record<string, any>;
  merchantId?: string;
  notificationMethods?: AlertNotificationMethod[];
}

/**
 * Alert service
 */
export class AlertService extends BaseService {
  private webhookService: WebhookService;
  private emailService: EmailService;
  private smsService: SmsService;
  private notificationService: NotificationService;

  /**
   * Create a new alert service
   */
  constructor() {
    super();
    this.webhookService = new WebhookService();
    this.emailService = new EmailService();
    this.smsService = new SmsService();
    this.notificationService = new NotificationService();
  }

  /**
   * Create a new alert
   * @param data Alert data
   * @returns Alert ID
   */
  public async createAlert(data: AlertData): Promise<string> {
    try {
      // Create alert in database
      const alert = await (prisma as any).alert.create({
        data: { type: (data as any).type,
          severity: (data as any).severity,
          title: (data as any).title,
          message: (data as any).message,
          source: (data as any).source || 'system',
          details: (data as any).details ?? {},
          merchantId: data.merchantId,
          status: (AlertStatus as any).ACTIVE,
        },
      });

      // Send notifications
      await this.sendAlertNotifications((alert as any).id, data);

      return (alert as any).id;
    } catch(error) {
      (logger as any).error('Error creating alert', { error, data });
      throw new Error('Failed to create alert');
    }
  }

  /**
   * Send alert notifications
   * @param alertId Alert ID
   * @param data Alert data
   */
  private async sendAlertNotifications(alertId: string, data: AlertData): Promise<void> {
    try {
      const notificationMethods: any = (data as any).notificationMethods || [(AlertNotificationMethod as any).DASHBOARD];

      // Send email notification
      if ((notificationMethods as any).includes((AlertNotificationMethod as any).EMAIL)) {
        await this.sendEmailNotification(alertId, data);
      }

      // Send SMS notification
      if ((notificationMethods as any).includes((AlertNotificationMethod as any).SMS)) {
        await this.sendSmsNotification(alertId, data);
      }

      // Send webhook notification
      if ((notificationMethods as any).includes((AlertNotificationMethod as any).WEBHOOK) && data.merchantId) {
        await this.sendWebhookNotification(alertId, data);
      }

      // Send Telegram notification
      if ((notificationMethods as any).includes((AlertNotificationMethod as any).TELEGRAM)) {
        await this.sendTelegramNotification(alertId, data);
      }

      // Update alert with notification status
      await (prisma as any).alert.update({
        where: { id: alertId },
        data: { notifiedAt: new Date(),
          notificationMethods: notificationMethods,
        },
      });
    } catch(error) {
      (logger as any).error('Error sending alert notifications', { error, alertId, data });
    }
  }

  /**
   * Send email notification
   * @param alertId Alert ID
   * @param data Alert data
   */
  private async sendEmailNotification(alertId: string, data: AlertData): Promise<void> {
    try {
      // Get admin emails
      const adminEmails = await this.emailService.getAdminEmails();

      // Get merchant email if merchantId is provided
      let merchantEmail: string = '';
      if (data.merchantId) {
        const merchant = await (prisma as any).merchant.findUnique({
          where: { id: data.merchantId },
          select: { email: true },
        });
        if (merchant) {
          merchantEmail = (merchant as any).email;
        }
      }

      // Determine recipients based on severity and merchant
      let recipients: string[] = [];
      switch ((data as any).severity) {
        case (AlertSeverity as any).CRITICAL:
          // Send to all admins for critical alerts
          recipients = adminEmails;
          // Also send to merchant if it's merchant-specific
          if (merchantEmail) {
            (recipients as any).push(merchantEmail);
          }
          break;
        case (AlertSeverity as any).ERROR:
          // Send to all admins for error alerts
          recipients = adminEmails;
          // Also send to merchant if it's merchant-specific
          if (merchantEmail) {
            (recipients as any).push(merchantEmail);
          }
          break;
        case (AlertSeverity as any).WARNING:
          // Send to merchant if it's merchant-specific
          if (merchantEmail) {
            (recipients as any).push(merchantEmail);
          }
          // Also send to admins for system warnings
          if (!data.merchantId) {
            recipients = [...recipients, ...adminEmails];
          }
          break;
        case (AlertSeverity as any).INFO:
          // Only send info alerts to merchant if it's merchant-specific
          if (merchantEmail) {
            (recipients as any).push(merchantEmail);
          }
          break;
      }

      // Skip if no recipients
      if ((recipients as any).length === 0) {
        (logger as any).debug('No recipients for email notification', { alertId, data });
        return;
      }

      // Get alert from database to ensure we have all fields
      const alert = await (prisma as any).alert.findUnique({
        where: { id: alertId },
      });

      if (!alert) {
        (logger as any).warn('Alert not found for email notification', { alertId });
        return;
      }

      // Send alert email
      const success: boolean = await this.emailService.sendAlertEmail(
        {
          id: alertId,
          type: (data as any).type,
          severity: (data as any).severity,
          title: (data as any).title,
          message: (data as any).message,
          source: (data as any).source,
          details: (data as any).details,
          merchantId: data.merchantId,
          createdAt: (alert as any).createdAt,
        },
        recipients
      );

      if (success) {
        (logger as any).info('Email notification sent', { alertId, recipients });
      } else {
        (logger as any).warn('Failed to send email notification', { alertId, recipients });
      }
    } catch(error) {
      (logger as any).error('Error sending email notification', { error, alertId, data });
    }
  }

  /**
   * Send SMS notification
   * @param alertId Alert ID
   * @param data Alert data
   */
  private async sendSmsNotification(alertId: string, data: AlertData): Promise<void> {
    try {
      // Get admin phone numbers
      const adminPhoneNumbers = await this.smsService.getAdminPhoneNumbers();

      // Get merchant phone number if merchantId is provided
      let merchantPhoneNumber: string | null = null;
      if (data.merchantId) {
        merchantPhoneNumber = await this.smsService.getMerchantPhoneNumber(data.merchantId);
      }

      // Determine recipients based on severity and merchant
      let recipients: string[] = [];
      switch ((data as any).severity) {
        case (AlertSeverity as any).CRITICAL:
          // Send to all admins for critical alerts
          recipients = adminPhoneNumbers;
          // Also send to merchant if it's merchant-specific
          if (merchantPhoneNumber) {
            (recipients as any).push(merchantPhoneNumber);
          }
          break;
        case (AlertSeverity as any).ERROR:
          // Send to all admins for error alerts
          recipients = adminPhoneNumbers;
          // Also send to merchant if it's merchant-specific
          if (merchantPhoneNumber) {
            (recipients as any).push(merchantPhoneNumber);
          }
          break;
        case (AlertSeverity as any).WARNING:
          // Send to merchant if it's merchant-specific
          if (merchantPhoneNumber) {
            (recipients as any).push(merchantPhoneNumber);
          }
          // Also send to admins for system warnings
          if (!data.merchantId) {
            recipients = [...recipients, ...adminPhoneNumbers];
          }
          break;
        case (AlertSeverity as any).INFO:
          // Only send info alerts to merchant if it's merchant-specific
          if (merchantPhoneNumber) {
            (recipients as any).push(merchantPhoneNumber);
          }
          break;
      }

      // Skip if no recipients
      if ((recipients as any).length === 0) {
        (logger as any).debug('No recipients for SMS notification', { alertId, data });
        return;
      }

      // Get alert from database to ensure we have all fields
      const alert = await (prisma as any).alert.findUnique({
        where: { id: alertId },
      });

      if (!alert) {
        (logger as any).warn('Alert not found for SMS notification', { alertId });
        return;
      }

      // Send SMS to each recipient
      for (const phoneNumber of recipients) {
        const success: boolean = await this.smsService.sendAlertSms(
          {
            id: alertId,
            type: (data as any).type,
            severity: (data as any).severity,
            title: (data as any).title,
            message: (data as any).message,
            merchantId: data.merchantId,
          },
          phoneNumber
        );

        if (success) {
          (logger as any).info('SMS notification sent', { alertId, phoneNumber });
        } else {
          (logger as any).warn('Failed to send SMS notification', { alertId, phoneNumber });
        }
      }
    } catch(error) {
      (logger as any).error('Error sending SMS notification', { error, alertId, data });
    }
  }

  /**
   * Send webhook notification
   * @param alertId Alert ID
   * @param data Alert data
   */
  private async sendWebhookNotification(alertId: string, data: AlertData): Promise<void> {
    try {
      if (!data.merchantId) {
        (logger as any).warn('No merchant ID provided for webhook notification', { alertId, data });
        return;
      }

      // Create webhook event
      await this.webhookService.createWebhookEvent(
        data.merchantId,
        (WebhookEventType as any).ALERT_CREATED,
        {
          alertId,
          type: (data as any).type,
          severity: (data as any).severity,
          title: (data as any).title,
          message: (data as any).message,
          source: (data as any).source,
          details: (data as any).details,
          createdAt: new Date().toISOString(),
        }
      );

      (logger as any).info('Webhook notification sent', { alertId, merchantId: data.merchantId });
    } catch(error) {
      (logger as any).error('Error sending webhook notification', { error, alertId, data });
    }
  }

  /**
   * Send Telegram notification
   * @param alertId Alert ID
   * @param data Alert data
   */
  private async sendTelegramNotification(alertId: string, data: AlertData): Promise<void> {
    try {
      // Map alert severity to notification priority
      let priority: NotificationPriority;
      switch ((data as any).severity) {
        case (AlertSeverity as any).CRITICAL:
          priority = (NotificationPriority as any).CRITICAL;
          break;
        case (AlertSeverity as any).ERROR:
          priority = (NotificationPriority as any).HIGH;
          break;
        case (AlertSeverity as any).WARNING:
          priority = (NotificationPriority as any).MEDIUM;
          break;
        case (AlertSeverity as any).INFO:
          priority = (NotificationPriority as any).LOW;
          break;
        default:
          priority = (NotificationPriority as any).MEDIUM;
      }

      // Format message
      const subject = `${(data as any).severity.toUpperCase()}: ${(data as any).title}`;
      const message: string = `*${(data as any).title}*\n\n${(data as any).message}\n\n*Type:* ${(data as any).type}\n*Severity:* ${(data as any).severity}\n*Alert ID:* ${alertId}`;

      // Send notification
      await this.notificationService.sendNotification({
        userId: undefined,
        merchantId: data.merchantId,
        channels: [(NotificationChannel as any).TELEGRAM],
        priority,
        subject,
        message,
        metadata: {
          alertId,
          alertType: (data as any).type,
          alertSeverity: (data as any).severity,
        },
      });

      (logger as any).info('Telegram notification sent', { alertId, merchantId: data.merchantId });
    } catch(error) {
      (logger as any).error('Error sending Telegram notification', { error, alertId, merchantId: data.merchantId });
    }
  }


  /**
   * Get alerts with filtering and pagination
   * @param options Alert query options
   * @returns Alerts and total count
   */
  public async getAlerts(options: {
    merchantId?: string;
    status?: AlertStatus;
    severity?: AlertSeverity;
    type?: AlertType;
    startDate?: Date;
    endDate?: Date;
    search?: string;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ alerts: any[]; total: number }> {
    try {
      const {
        merchantId,
        status,
        severity,
        type,
        startDate,
        endDate,
        search,
        limit = 10,
        offset = 0,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = options;

      // Build where clause
      const where = {};

      // Add merchant filter
      if (merchantId) {
        (where as any).merchantId = merchantId;
      }

      // Add status filter
      if (status) {
        where.status = status;
      }

      // Add severity filter
      if (severity) {
        (where as any).severity = severity;
      }

      // Add type filter
      if (type) {
        (where as any).type = type;
      }

      // Add date range filter
      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) {
          where.createdAt.gte = startDate;
        }
        if (endDate) {
          where.createdAt.lte = endDate;
        }
      }

      // Add search filter
      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { message: { contains: search, mode: 'insensitive' } },
          { source: { contains: search, mode: 'insensitive' } },
        ];
      }

      // Get total count
      const total: number = await (prisma as any).alert.count({ where });

      // Build order by
      const orderBy = {};
      orderBy[sortBy] = sortOrder;

      // Get alerts
      const alerts = await (prisma as any).alert.findMany({
        where,
        orderBy,
        take: limit,
        skip: offset,
      });

      return { alerts, total };
    } catch(error) {
      (logger as any).error('Error getting alerts', { error, options });
      throw new Error('Failed to get alerts');
    }
  }

  /**
   * Update alert status
   * @param alertId Alert ID
   * @param status Alert status
   * @param userId User ID
   * @returns Updated alert
   */
  public async updateAlertStatus(
    alertId: string,
    status: AlertStatus,
    userId: string
  ): Promise<any> {
    try {
      const alert = await (prisma as any).alert.update({
        where: { id: alertId },
        data: {
          status,
          updatedAt: new Date(),
          resolvedBy: status === (AlertStatus as any).RESOLVED ? userId : undefined,
          resolvedAt: status === (AlertStatus as any).RESOLVED ? new Date() : undefined,
          acknowledgedBy: status === (AlertStatus as any).ACKNOWLEDGED ? userId : undefined,
          acknowledgedAt: status === (AlertStatus as any).ACKNOWLEDGED ? new Date() : undefined,
        },
      });

      return alert;
    } catch(error) {
      (logger as any).error('Error updating alert status', { error, alertId, status });
      throw new Error('Failed to update alert status');
    }
  }

  /**
   * Get alert by ID
   * @param alertId Alert ID
   * @returns Alert
   */
  public async getAlert(alertId: string): Promise<any> {
    try {
      const alert = await (prisma as any).alert.findUnique({
        where: { id: alertId },
      });

      if (!alert) {
        throw new Error('Alert not found');
      }

      return alert;
    } catch(error) {
      (logger as any).error('Error getting alert', { error, alertId });
      throw new Error('Failed to get alert');
    }
  }
}