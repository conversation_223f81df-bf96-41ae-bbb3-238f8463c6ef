// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { asyncHandler } from '../middlewares/error.middleware';
import { asyncHandler } from '../middlewares/error.middleware';

// Country data
const COUNTRIES: unknown = [
    { code: "AE", name: "United Arab Emirates" },
    { code: "BH", name: "Bahrain" },
    { code: "EG", name: "Egypt" },
    { code: "IQ", name: "Iraq" },
    { code: "JO", name: "Jordan" },
    { code: "KW", name: "Kuwait" },
    { code: "LB", name: "Lebanon" },
    { code: "OM", name: "Oman" },
    { code: "QA", name: "Qatar" },
    { code: "SA", name: "Saudi Arabia" },
    { code: "SY", name: "Syria" },
    { code: "YE", name: "Yemen" }
];

// Governorate data by country
const GOVERNORATES: unknown = {
    "EG": [
        { code: "CAI", name: "Cairo", countryCode: "EG" },
        { code: "ALX", name: "Alexandria", countryCode: "EG" },
        { code: "GIZ", name: "<PERSON><PERSON>", countryCode: "EG" },
        { code: "ASW", name: "Aswan", countryCode: "EG" },
        { code: "ASY", name: "Asyut", countryCode: "EG" },
        { code: "BNS", name: "Beni Suef", countryCode: "EG" },
        { code: "DAK", name: "Dakahlia", countryCode: "EG" },
        { code: "DAM", name: "Damietta", countryCode: "EG" },
        { code: "FAY", name: "Faiyum", countryCode: "EG" },
        { code: "GHA", name: "Gharbia", countryCode: "EG" },
        { code: "ISM", name: "Ismailia", countryCode: "EG" },
        { code: "KFS", name: "Kafr El Sheikh", countryCode: "EG" },
        { code: "LUX", name: "Luxor", countryCode: "EG" },
        { code: "MAT", name: "Matruh", countryCode: "EG" },
        { code: "MIN", name: "Minya", countryCode: "EG" },
        { code: "MNF", name: "Monufia", countryCode: "EG" },
        { code: "NEV", name: "New Valley", countryCode: "EG" },
        { code: "NSI", name: "North Sinai", countryCode: "EG" },
        { code: "PSD", name: "Port Said", countryCode: "EG" },
        { code: "QAL", name: "Qalyubia", countryCode: "EG" },
        { code: "QEN", name: "Qena", countryCode: "EG" },
        { code: "RSE", name: "Red Sea", countryCode: "EG" },
        { code: "SHA", name: "Sharqia", countryCode: "EG" },
        { code: "SOH", name: "Sohag", countryCode: "EG" },
        { code: "SSI", name: "South Sinai", countryCode: "EG" },
        { code: "SUZ", name: "Suez", countryCode: "EG" }
    ],
    "SA": [
        { code: "RIY", name: "Riyadh", countryCode: "SA" },
        { code: "MEC", name: "Mecca", countryCode: "SA" },
        { code: "MED", name: "Medina", countryCode: "SA" },
        { code: "EAS", name: "Eastern Province", countryCode: "SA" },
        { code: "ASI", name: "Asir", countryCode: "SA" },
        { code: "TAB", name: "Tabuk", countryCode: "SA" },
        { code: "HAI", name: "Hail", countryCode: "SA" },
        { code: "QAS", name: "Al-Qassim", countryCode: "SA" },
        { code: "JIZ", name: "Jizan", countryCode: "SA" },
        { code: "NAJ", name: "Najran", countryCode: "SA" },
        { code: "BAH", name: "Al Bahah", countryCode: "SA" },
        { code: "JOF", name: "Al Jawf", countryCode: "SA" },
        { code: "NBD", name: "Northern Borders", countryCode: "SA" }
    ],
    "AE": [
        { code: "ABU", name: "Abu Dhabi", countryCode: "AE" },
        { code: "DUB", name: "Dubai", countryCode: "AE" },
        { code: "SHJ", name: "Sharjah", countryCode: "AE" },
        { code: "AJM", name: "Ajman", countryCode: "AE" },
        { code: "UAQ", name: "Umm Al Quwain", countryCode: "AE" },
        { code: "RAK", name: "Ras Al Khaimah", countryCode: "AE" },
        { code: "FUJ", name: "Fujairah", countryCode: "AE" }
    ]
};

// Get all countries
export const getCountries: unknown =asyncHandler(async (_req: Request, res: Response) => {
  
    res.status(200).json(COUNTRIES);
});

// Get governorates by country code
export const getGovernoratesByCountry: unknown =asyncHandler(async (req: Request, res: Response) => {
  
  

    const { countryCode } = req.params;

    if (!countryCode) {
    
        return res.status(400).json({ error: "Country code is required" });
    }

  
    const governorates: unknown =GOVERNORATES[countryCode as keyof typeof GOVERNORATES] ?? [];
  

    res.status(200).json(governorates);
});
