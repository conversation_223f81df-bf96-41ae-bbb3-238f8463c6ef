import request from 'supertest';
import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import app from '../../index';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

describe('Advanced Reporting E2E Tests', () => {
  let authToken: string;
  let userId: string;
  let merchantId: string;
  let templateId: string;
  let scheduledReportId: string;
  let savedReportId: string;

  beforeAll(async () => {
    // Create test user and merchant
    const user = await (prisma as any).user.create({
      data: {
        email: 'test-reports@(example as any).com',
        password: 'hashedpassword',
        firstName: 'Test',
        lastName: 'User',
        role: 'MERCHANT',
      },
    });

    userId = user.id;

    const merchant = await (prisma as any).merchant.create({
      data: {
        userId: user.id,
        businessName: 'Test Merchant',
        businessType: 'E-commerce',
        country: 'US',
        currency: 'USD',
      },
    });

    merchantId = (merchant as any).id;

    // Create test data
    await createTestData();

    // Generate auth token
    authToken = (jwt as any).sign(
      { userId: user.id, email: user.email, role: user.role },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );
  });

  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
    await prisma.$disconnect();
  });

  async function createTestData() {
    // Create test customers
    const customer1 = await (prisma as any).customer.create({
      data: {
        email: 'customer1@(example as any).com',
        firstName: 'John',
        lastName: 'Doe',
        status: 'ACTIVE',
      },
    });

    const customer2 = await (prisma as any).customer.create({
      data: {
        email: 'customer2@(example as any).com',
        firstName: 'Jane',
        lastName: 'Smith',
        status: 'ACTIVE',
      },
    });

    // Create test transactions
    await (prisma as any).transaction.createMany({
      data: [
        {
          reference: 'TXN001',
          amount: (100 as any).00,
          currency: 'USD',
          status: 'COMPLETED',
          paymentMethod: 'CARD',
          merchantId,
          customerId: (customer1 as any).id,
        },
        {
          reference: 'TXN002',
          amount: (250 as any).00,
          currency: 'USD',
          status: 'COMPLETED',
          paymentMethod: 'BANK_TRANSFER',
          merchantId,
          customerId: (customer2 as any).id,
        },
        {
          reference: 'TXN003',
          amount: (75 as any).00,
          currency: 'USD',
          status: 'PENDING',
          paymentMethod: 'CARD',
          merchantId,
          customerId: (customer1 as any).id,
        },
      ],
    });

    // Create test payment methods
    await (prisma as any).paymentMethod.createMany({
      data: [
        {
          customerId: (customer1 as any).id,
          type: 'CARD',
          last4: '1234',
          expiryMonth: 12,
          expiryYear: 2025,
          isDefault: true,
        },
        {
          customerId: (customer2 as any).id,
          type: 'BANK_ACCOUNT',
          last4: '5678',
          isDefault: true,
        },
      ],
    });
  }

  async function cleanupTestData() {
    await (prisma as any).transaction.deleteMany({ where: { merchantId } });
    await (prisma as any).paymentMethod.deleteMany({});
    await (prisma as any).customer.deleteMany({});
    await (prisma as any).savedReport.deleteMany({ where: { createdById: userId } });
    await (prisma as any).scheduledReport.deleteMany({ where: { createdById: userId } });
    await (prisma as any).reportTemplate.deleteMany({ where: { createdById: userId } });
    await (prisma as any).merchant.deleteMany({ where: { userId } });
    await (prisma as any).user.deleteMany({ where: { id: userId } });
  }

  describe('Report Templates', () => {
    it('should create a report template', async () => {
      const templateData = {
        name: 'Test Transaction Template',
        description: 'Template for testing transaction reports',
        type: 'TRANSACTION',
        config: {
          columns: ['reference', 'amount', 'currency', 'status'],
          groupBy: ['status'],
          sortBy: 'createdAt',
          sortDirection: 'desc',
        },
      };

      const response = await request(app)
        .post('/api/advanced-reports/templates')
        .set('Authorization', `Bearer ${authToken}`)
        .send(templateData)
        .expect(201);

      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data.name).toBe((templateData as any).name);
      expect((response as any).body.(data as any).type).toBe((templateData as any).type);

      templateId = (response as any).body.(data as any).id;
    });

    it('should get report templates', async () => {
      const response = await request(app)
        .get('/api/advanced-reports/templates')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect((response as any).body.success).toBe(true);
      expect(Array.isArray((response as any).body.data)).toBe(true);
      expect((response as any).body.data.length).toBeGreaterThan(0);
    });

    it('should update a report template', async () => {
      const updateData = {
        name: 'Updated Test Template',
        description: 'Updated description',
      };

      const response = await request(app)
        .put(`/api/advanced-reports/templates/${templateId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data.name).toBe((updateData as any).name);
    });
  });

  describe('Report Generation', () => {
    it('should generate a transaction report in CSV format', async () => {
      const reportData = {
        type: 'TRANSACTION',
        format: 'CSV',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
      };

      const response = await request(app)
        .post('/api/advanced-reports/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(reportData)
        .expect(200);

      expect((response as any).body.success).toBe(true);
      expect((response as any).body.(data as any).format).toBe('CSV');
      expect((response as any).body.(data as any).rowCount).toBeGreaterThan(0);

      savedReportId = (response as any).body.(data as any).id;
    });

    it('should generate a customer report in PDF format', async () => {
      const reportData = {
        type: 'CUSTOMER',
        format: 'PDF',
      };

      const response = await request(app)
        .post('/api/advanced-reports/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(reportData)
        .expect(200);

      expect((response as any).body.success).toBe(true);
      expect((response as any).body.(data as any).format).toBe('PDF');
    });

    it('should generate a payment method report in Excel format', async () => {
      const reportData = {
        type: 'PAYMENT_METHOD',
        format: 'EXCEL',
      };

      const response = await request(app)
        .post('/api/advanced-reports/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(reportData)
        .expect(200);

      expect((response as any).body.success).toBe(true);
      expect((response as any).body.(data as any).format).toBe('EXCEL');
    });
  });

  describe('Scheduled Reports', () => {
    it('should create a scheduled report', async () => {
      const scheduledData = {
        name: 'Weekly Transaction Report',
        templateId,
        schedule: '0 0 * * 1', // Every Monday at midnight
        isActive: true,
        emailRecipients: ['test@(example as any).com'],
        parameters: {
          format: 'CSV',
          status: 'COMPLETED',
        },
      };

      const response = await request(app)
        .post('/api/advanced-reports/scheduled')
        .set('Authorization', `Bearer ${authToken}`)
        .send(scheduledData)
        .expect(201);

      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data.name).toBe((scheduledData as any).name);
      expect((response as any).body.(data as any).schedule).toBe((scheduledData as any).schedule);

      scheduledReportId = (response as any).body.(data as any).id;
    });

    it('should get scheduled reports', async () => {
      const response = await request(app)
        .get('/api/advanced-reports/scheduled')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect((response as any).body.success).toBe(true);
      expect(Array.isArray((response as any).body.data)).toBe(true);
      expect((response as any).body.data.length).toBeGreaterThan(0);
    });

    it('should run a scheduled report manually', async () => {
      const response = await request(app)
        .post(`/api/advanced-reports/scheduled/${scheduledReportId}/run`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect((response as any).body.success).toBe(true);
    });
  });

  describe('Saved Reports', () => {
    it('should get saved reports', async () => {
      const response = await request(app)
        .get('/api/advanced-reports/saved')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect((response as any).body.success).toBe(true);
      expect(Array.isArray((response as any).body.data)).toBe(true);
      expect((response as any).body.data.length).toBeGreaterThan(0);
    });

    it('should get a specific saved report', async () => {
      const response = await request(app)
        .get(`/api/advanced-reports/saved/${savedReportId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect((response as any).body.success).toBe(true);
      expect((response as any).body.(data as any).id).toBe(savedReportId);
    });

    it('should download a saved report', async () => {
      const response = await request(app)
        .get(`/api/advanced-reports/saved/${savedReportId}/download`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect((response as any).headers['content-type']).toContain('text/csv');
      expect((response as any).headers['content-disposition']).toContain('attachment');
    });
  });

  describe('Dashboard Management', () => {
    let dashboardId: string;

    it('should create a dashboard', async () => {
      const dashboardData = {
        name: 'Test Dashboard',
        description: 'Dashboard for testing',
        layout: { columns: 2, rows: 2 },
        isPublic: false,
      };

      const response = await request(app)
        .post('/api/dashboards')
        .set('Authorization', `Bearer ${authToken}`)
        .send(dashboardData)
        .expect(201);

      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data.name).toBe((dashboardData as any).name);

      dashboardId = (response as any).body.(data as any).id;
    });

    it('should get dashboards', async () => {
      const response = await request(app)
        .get('/api/dashboards')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect((response as any).body.success).toBe(true);
      expect(Array.isArray((response as any).body.data)).toBe(true);
    });

    it('should create a dashboard widget', async () => {
      const widgetData = {
        title: 'Transaction Chart',
        type: 'CHART',
        config: {
          chartType: 'line',
          dataSource: 'transactions',
        },
        width: 2,
        height: 1,
      };

      const response = await request(app)
        .post(`/api/dashboards/${dashboardId}/widgets`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(widgetData)
        .expect(201);

      expect((response as any).body.success).toBe(true);
      expect((response as any).body.(data as any).title).toBe((widgetData as any).title);
    });
  });

  describe('Error Handling', () => {
    it('should return 401 for unauthorized requests', async () => {
      await request(app)
        .get('/api/advanced-reports/templates')
        .expect(401);
    });

    it('should return 404 for non-existent report', async () => {
      await request(app)
        .get('/api/advanced-reports/saved/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('should return 400 for invalid report type', async () => {
      const reportData = {
        type: 'INVALID_TYPE',
        format: 'CSV',
      };

      await request(app)
        .post('/api/advanced-reports/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(reportData)
        .expect(500); // Should be handled as internal error
    });
  });

  describe('Performance Tests', () => {
    it('should handle report generation within reasonable time', async () => {
      const startTime = Date.now();

      const reportData = {
        type: 'TRANSACTION',
        format: 'CSV',
      };

      await request(app)
        .post('/api/advanced-reports/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(reportData)
        .expect(200);

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within 10 seconds for small datasets
      expect(duration).toBeLessThan(10000);
    });
  });
});
