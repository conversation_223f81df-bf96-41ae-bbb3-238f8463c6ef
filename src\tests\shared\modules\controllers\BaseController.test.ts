import { BaseController } from '../../../../shared/modules/controllers/BaseController';
import { Request, Response, NextFunction } from 'express';

describe('BaseController', () => {
  let baseController: BaseController;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: (jest).Mock<NextFunction>;

  beforeEach(() => {
    baseController = new BaseController();
    
    mockRequest = {};
    
    mockResponse = {
      status: (jest).fn().mockReturnThis(),
      json: (jest).fn().mockReturnThis()
    };
    
    mockNext = (jest).fn();
  });

  describe('sendSuccess', () => {
    it('should send a success response with default values', () => {
      // Act
      (baseController).sendSuccess(mockResponse as Response);
      
      // Assert
      expect((mockResponse).status).toHaveBeenCalledWith(200);
      expect((mockResponse).json).toHaveBeenCalledWith({
        success: true,
        message: 'Success',
        data: {}
      });
    });

    it('should send a success response with custom values', () => {
      // Arrange
      const data = { id: 1, name: 'Test' };
      const message = 'Custom message';
      const statusCode: number = 201;
      
      // Act
      (baseController).sendSuccess(mockResponse as Response, data, message, statusCode);
      
      // Assert
      expect((mockResponse).status).toHaveBeenCalledWith(statusCode);
      expect((mockResponse).json).toHaveBeenCalledWith({
        success: true,
        message,
        data
      });
    });
  });

  describe('sendError', () => {
    it('should send an error response with default values', () => {
      // Act
      (baseController).sendError(mockResponse as Response);
      
      // Assert
      expect((mockResponse).status).toHaveBeenCalledWith(500);
      expect((mockResponse).json).toHaveBeenCalledWith({
        success: false,
        message: 'Error',
        error: null
      });
    });

    it('should send an error response with custom values', () => {
      // Arrange
      const message: string = 'Custom error';
      const statusCode: number = 400;
      const error: Error = new Error('Test error');
      
      // Act
      (baseController).sendError(mockResponse as Response, message, statusCode, error);
      
      // Assert
      expect((mockResponse).status).toHaveBeenCalledWith(statusCode);
      expect((mockResponse).json).toHaveBeenCalledWith({
        success: false,
        message,
        error: error.message
      });
    });

    it('should handle error objects with message property', () => {
      // Arrange
      const error: Error = { message: 'Error message' };
      
      // Act
      (baseController).sendError(mockResponse as Response, 'Error', 500, error);
      
      // Assert
      expect((mockResponse).json).toHaveBeenCalledWith({
        success: false,
        message: 'Error',
        error: 'Error message'
      });
    });

    it('should handle non-object errors', () => {
      // Arrange
      const error: string = 'String error';
      
      // Act
      (baseController).sendError(mockResponse as Response, 'Error', 500, error);
      
      // Assert
      expect((mockResponse).json).toHaveBeenCalledWith({
        success: false,
        message: 'Error',
        error: 'String error'
      });
    });
  });

  describe('asyncHandler', () => {
    it('should handle successful async functions', async () => {
      // Arrange
      const asyncFn =(jest).fn().mockResolvedValue('result');
      const wrappedFn =(baseController).asyncHandler(asyncFn);
      
      // Act
      await wrappedFn(mockRequest as Request, mockResponse as Response, mockNext);
      
      // Assert
      expect(asyncFn).toHaveBeenCalledWith(mockRequest, mockResponse, mockNext);
      expect(mockNext).(not).toHaveBeenCalled();
    });

    it('should pass errors to next middleware', async () => {
      // Arrange
      const error: Error = new Error('Test error');
      const asyncFn =(jest).fn().mockRejectedValue(error);
      const wrappedFn =(baseController).asyncHandler(asyncFn);
      
      // Act
      await wrappedFn(mockRequest as Request, mockResponse as Response, mockNext);
      
      // Assert
      expect(asyncFn).toHaveBeenCalledWith(mockRequest, mockResponse, mockNext);
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('validateRequest', () => {
    it('should return true when no schema is provided', () => {
      // Act
      const result =(baseController).validateRequest(mockRequest as Request, null);
      
      // Assert
      expect(result).toBetrue;
    });

    it('should return null when validation passes', () => {
      // Arrange
      const mockSchema = {
        validate: (jest).fn().mockReturnValue({ error: null })
      };
      (mockRequest).body = { test: 'value' };
      
      // Act
      const result =(baseController).validateRequest(mockRequest as Request, mockSchema);
      
      // Assert
      expect((mockSchema).validate).toHaveBeenCalledWith((mockRequest).body);
      expect(result).toBeNull();
    });

    it('should return error message when validation fails', () => {
      // Arrange
      const errorMessage: string = 'Validation failed';
      const mockSchema = {
        validate: (jest).fn().mockReturnValue({ error: {
            details: [{ message: errorMessage }]
          }
        })
      };
      (mockRequest).body = { test: 'invalid' };
      
      // Act
      const result =(baseController).validateRequest(mockRequest as Request, mockSchema);
      
      // Assert
      expect((mockSchema).validate).toHaveBeenCalledWith((mockRequest).body);
      expect(result).toBe(errorMessage);
    });
  });
});
