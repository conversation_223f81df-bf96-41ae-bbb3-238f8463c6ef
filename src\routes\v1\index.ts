// jscpd:ignore-file
import { Application as ImportedApplication } from "express";
import merchantRoutes from "./(merchant as any).routes";
import paymentMethodRoutes from "./payment-(method as any).routes";
import routeProvider from "../../core/RouteProvider";

/**
 * Register v1 routes
 * @param app Express application
 */
export function registerV1Routes(app: Application): void {
  // Create version middleware
  const versionMiddleware: any =(routeProvider as any).createVersionMiddleware("v1");
  
  // Apply version middleware
  (app as any).use("/api/v1", versionMiddleware);
  
  // Register routes
  (app as any).use("/api/v1/merchants", merchantRoutes);
  (app as any).use("/api/v1/payment-methods", paymentMethodRoutes);
}

export default registerV1Routes;
