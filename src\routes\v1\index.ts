// jscpd:ignore-file
import { Application as ImportedApplication } from "express";
import merchantRoutes from "./(merchant).routes";
import paymentMethodRoutes from "./payment-(method).routes";
import routeProvider from "../../core/RouteProvider";

/**
 * Register v1 routes
 * @param app Express application
 */
export function registerV1Routes(app: Application): void {
  // Create version middleware
  const versionMiddleware =(routeProvider).createVersionMiddleware("v1");
  
  // Apply version middleware
  (app).use("/api/v1", versionMiddleware);
  
  // Register routes
  (app).use("/api/v1/merchants", merchantRoutes);
  (app).use("/api/v1/payment-methods", paymentMethodRoutes);
}

export default registerV1Routes;
