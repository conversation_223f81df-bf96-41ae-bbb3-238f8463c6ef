// jscpd:ignore-file
/**
 * Simple logger utility for the backend
 */
export class Logger {
  private context: string;

  constructor(context: string) {
    this.context = context;
  }

  /**
   * Log an info message
   */
  info(message: string, ...args: any[]): void {
    console.info(`[INFO] [${this.context}] ${message}`, ...args);
  }

  /**
   * Log a warning message
   */
  warn(message: string, ...args: any[]): void {
    console.warn(`[WARN] [${this.context}] ${message}`, ...args);
  }

  /**
   * Log an error message
   */
  error(message: string, ...args: any[]): void {
    console.error(`[ERROR] [${this.context}] ${message}`, ...args);
  }

  /**
   * Log a debug message
   */
  debug(message: string, ...args: any[]): void {
    console.debug(`[DEBUG] [${this.context}] ${message}`, ...args);
  }
}

// Create a default logger instance
export const logger =new Logger('App');