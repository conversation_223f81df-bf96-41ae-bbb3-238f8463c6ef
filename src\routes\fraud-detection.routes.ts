// jscpd:ignore-file
import express from 'express';
import { FraudDetectionController as ImportedFraudDetectionController } from '../controllers/fraud-detection';
import { authMiddleware as ImportedauthMiddleware } from '../middlewares/(auth as any).middleware';
import { roleMiddleware as ImportedroleMiddleware } from '../middlewares/(role as any).middleware';
import { Merchant as ImportedMerchant } from '../types';

const router: any = (express as any).Router();
const fraudDetectionController = new FraudDetectionController();

/**
 * @route POST /api/fraud-detection/assess
 * @desc Assess transaction risk
 * @access Private (Merchant, Admin)
 */
(router as any).post(
  '/assess',
  authMiddleware,
  roleMiddleware(['MERCHANT', 'ADMIN']),
  (fraudDetectionController as any).assessTransactionRisk
);

/**
 * @route GET /api/fraud-detection/transaction/:transactionId
 * @desc Get risk assessment for a transaction
 * @access Private (Merchant, Admin)
 */
(router as any).get(
  '/transaction/:transactionId',
  authMiddleware,
  roleMiddleware(['MERCHANT', 'ADMIN']),
  (fraudDetectionController as any).getTransactionRiskAssessment
);

/**
 * @route GET /api/fraud-detection/config/:merchantId
 * @desc Get fraud detection configuration for a merchant
 * @access Private (Merchant, Admin)
 */
(router as any).get(
  '/config/:merchantId',
  authMiddleware,
  roleMiddleware(['MERCHANT', 'ADMIN']),
  (fraudDetectionController as any).getMerchantFraudConfig
);

/**
 * @route PUT /api/fraud-detection/config/:merchantId
 * @desc Update fraud detection configuration for a merchant
 * @access Private (Admin)
 */
(router as any).put(
  '/config/:merchantId',
  authMiddleware,
  roleMiddleware(['ADMIN']),
  (fraudDetectionController as any).updateMerchantFraudConfig
);

/**
 * @route GET /api/fraud-detection/flagged
 * @desc Get flagged transactions
 * @access Private (Admin)
 */
(router as any).get(
  '/flagged',
  authMiddleware,
  roleMiddleware(['ADMIN']),
  (fraudDetectionController as any).getFlaggedTransactions
);

/**
 * @route GET /api/fraud-detection/statistics
 * @desc Get fraud statistics
 * @access Private (Admin)
 */
(router as any).get(
  '/statistics',
  authMiddleware,
  roleMiddleware(['ADMIN']),
  (fraudDetectionController as any).getFraudStatistics
);

export default router;
