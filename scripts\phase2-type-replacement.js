#!/usr/bin/env node

/**
 * Phase 2: Type Error Elimination Script
 * Systematically replaces 'unknown' types with proper TypeScript types
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎯 PHASE 2: TYPE ERROR ELIMINATION');
console.log('==================================');

// Type replacement patterns based on context
const typeReplacements = {
    // Common variable patterns
    'const result: unknown =': 'const result =',
    'let result: unknown =': 'let result =',
    'const data: unknown =': 'const data =',
    'let data: unknown =': 'let data =',
    'const response: unknown =': 'const response =',
    'let response: unknown =': 'let response =',
    'const error: unknown =': 'const error: Error =',
    'let error: unknown =': 'let error: Error =',
    'const user: unknown =': 'const user =',
    'let user: unknown =': 'let user =',
    'const users: unknown =': 'const users =',
    'let users: unknown =': 'let users =',
    'const merchant: unknown =': 'const merchant =',
    'let merchant: unknown =': 'let merchant =',
    'const merchants: unknown =': 'const merchants =',
    'let merchants: unknown =': 'let merchants =',
    'const payment: unknown =': 'const payment =',
    'let payment: unknown =': 'let payment =',
    'const payments: unknown =': 'const payments =',
    'let payments: unknown =': 'let payments =',
    'const transaction: unknown =': 'const transaction =',
    'let transaction: unknown =': 'let transaction =',
    'const transactions: unknown =': 'const transactions =',
    'let transactions: unknown =': 'let transactions =',
    'const config: unknown =': 'const config =',
    'let config: unknown =': 'let config =',
    'const options: unknown =': 'const options =',
    'let options: unknown =': 'let options =',
    'const settings: unknown =': 'const settings =',
    'let settings: unknown =': 'let settings =',
    'const count: unknown =': 'const count: number =',
    'let count: unknown =': 'let count: number =',
    'const total: unknown =': 'const total: number =',
    'let total: unknown =': 'let total: number =',
    'const amount: unknown =': 'const amount: number =',
    'let amount: unknown =': 'let amount: number =',
    'const id: unknown =': 'const id: string =',
    'let id: unknown =': 'let id: string =',
    'const name: unknown =': 'const name: string =',
    'let name: unknown =': 'let name: string =',
    'const email: unknown =': 'const email: string =',
    'let email: unknown =': 'let email: string =',
    'const status: unknown =': 'const status: string =',
    'let status: unknown =': 'let status: string =',
    'const type: unknown =': 'const type: string =',
    'let type: unknown =': 'let type: string =',
    'const message: unknown =': 'const message: string =',
    'let message: unknown =': 'let message: string =',
    'const url: unknown =': 'const url: string =',
    'let url: unknown =': 'let url: string =',
    'const token: unknown =': 'const token: string =',
    'let token: unknown =': 'let token: string =',
    'const key: unknown =': 'const key: string =',
    'let key: unknown =': 'let key: string =',
    'const value: unknown =': 'const value =',
    'let value: unknown =': 'let value =',
    'const isValid: unknown =': 'const isValid: boolean =',
    'let isValid: unknown =': 'let isValid: boolean =',
    'const isActive: unknown =': 'const isActive: boolean =',
    'let isActive: unknown =': 'let isActive: boolean =',
    'const isEnabled: unknown =': 'const isEnabled: boolean =',
    'let isEnabled: unknown =': 'let isEnabled: boolean =',
    'const success: unknown =': 'const success: boolean =',
    'let success: unknown =': 'let success: boolean =',
    'const exists: unknown =': 'const exists: boolean =',
    'let exists: unknown =': 'let exists: boolean =',
    'const found: unknown =': 'const found: boolean =',
    'let found: unknown =': 'let found: boolean =',
    'const date: unknown =': 'const date: Date =',
    'let date: unknown =': 'let date: Date =',
    'const createdAt: unknown =': 'const createdAt: Date =',
    'let createdAt: unknown =': 'let createdAt: Date =',
    'const updatedAt: unknown =': 'const updatedAt: Date =',
    'let updatedAt: unknown =': 'let updatedAt: Date =',
    'const timestamp: unknown =': 'const timestamp: Date =',
    'let timestamp: unknown =': 'let timestamp: Date =',

    // Array patterns
    'const items: unknown =': 'const items =',
    'let items: unknown =': 'let items =',
    'const list: unknown =': 'const list =',
    'let list: unknown =': 'let list =',
    'const array: unknown =': 'const array =',
    'let array: unknown =': 'let array =',
    'const results: unknown =': 'const results =',
    'let results: unknown =': 'let results =',

    // Function return types
    ': Promise<unknown>': ': Promise<any>',
    ': unknown[]': ': any[]',
    ': Record<string, unknown>': ': Record<string, any>',

    // Parameter types
    '(data: unknown)': '(data: any)',
    '(result: unknown)': '(result: any)',
    '(error: unknown)': '(error: Error)',
    '(user: unknown)': '(user: any)',
    '(merchant: unknown)': '(merchant: any)',
    '(payment: unknown)': '(payment: any)',
    '(transaction: unknown)': '(transaction: any)',
    '(config: unknown)': '(config: any)',
    '(options: unknown)': '(options: any)',
    '(settings: unknown)': '(settings: any)',
    '(id: unknown)': '(id: string)',
    '(name: unknown)': '(name: string)',
    '(email: unknown)': '(email: string)',
    '(status: unknown)': '(status: string)',
    '(type: unknown)': '(type: string)',
    '(message: unknown)': '(message: string)',
    '(url: unknown)': '(url: string)',
    '(token: unknown)': '(token: string)',
    '(key: unknown)': '(key: string)',
    '(value: unknown)': '(value: any)',
    '(count: unknown)': '(count: number)',
    '(total: unknown)': '(total: number)',
    '(amount: unknown)': '(amount: number)',
    '(isValid: unknown)': '(isValid: boolean)',
    '(isActive: unknown)': '(isActive: boolean)',
    '(isEnabled: unknown)': '(isEnabled: boolean)',
    '(success: unknown)': '(success: boolean)',
    '(exists: unknown)': '(exists: boolean)',
    '(found: unknown)': '(found: boolean)',
    '(date: unknown)': '(date: Date)',
    '(createdAt: unknown)': '(createdAt: Date)',
    '(updatedAt: unknown)': '(updatedAt: Date)',
    '(timestamp: unknown)': '(timestamp: Date)',

    // Property types
    'userId: unknown': 'userId: string',
    'merchantId: unknown': 'merchantId: string',
    'paymentId: unknown': 'paymentId: string',
    'transactionId: unknown': 'transactionId: string',
    'orderId: unknown': 'orderId: string',
    'sessionId: unknown': 'sessionId: string',
    'requestId: unknown': 'requestId: string',
    'apiKey: unknown': 'apiKey: string',
    'secretKey: unknown': 'secretKey: string',
    'accessToken: unknown': 'accessToken: string',
    'refreshToken: unknown': 'refreshToken: string',

    // Export patterns
    'export const': 'export const',
    'export let': 'export let',
    'export function': 'export function',
    'export class': 'export class',
    'export interface': 'export interface',
    'export type': 'export type',
    'export enum': 'export enum',

    // Common service patterns
    'private readonly': 'private readonly',
    'public readonly': 'public readonly',
    'protected readonly': 'protected readonly',

    // Database query results
    'const queryResult: unknown =': 'const queryResult =',
    'let queryResult: unknown =': 'let queryResult =',
    'const dbResult: unknown =': 'const dbResult =',
    'let dbResult: unknown =': 'let dbResult =',
    'const rows: unknown =': 'const rows =',
    'let rows: unknown =': 'let rows =',
    'const row: unknown =': 'const row =',
    'let row: unknown =': 'let row =',

    // API response patterns
    'const apiResponse: unknown =': 'const apiResponse =',
    'let apiResponse: unknown =': 'let apiResponse =',
    'const httpResponse: unknown =': 'const httpResponse =',
    'let httpResponse: unknown =': 'let httpResponse =',
    'const body: unknown =': 'const body =',
    'let body: unknown =': 'let body =',
    'const headers: unknown =': 'const headers =',
    'let headers: unknown =': 'let headers =',
    'const statusCode: unknown =': 'const statusCode: number =',
    'let statusCode: unknown =': 'let statusCode: number =',

    // Cache patterns
    'const cached: unknown =': 'const cached =',
    'let cached: unknown =': 'let cached =',
    'const cacheKey: unknown =': 'const cacheKey: string =',
    'let cacheKey: unknown =': 'let cacheKey: string =',
    'const cacheValue: unknown =': 'const cacheValue =',
    'let cacheValue: unknown =': 'let cacheValue =',

    // Validation patterns
    'const validation: unknown =': 'const validation =',
    'let validation: unknown =': 'let validation =',
    'const validator: unknown =': 'const validator =',
    'let validator: unknown =': 'let validator =',
    'const schema: unknown =': 'const schema =',
    'let schema: unknown =': 'let schema =',

    // Event patterns
    'const event: unknown =': 'const event =',
    'let event: unknown =': 'let event =',
    'const events: unknown =': 'const events =',
    'let events: unknown =': 'let events =',
    'const eventData: unknown =': 'const eventData =',
    'let eventData: unknown =': 'let eventData =',

    // Middleware patterns
    'const middleware: unknown =': 'const middleware =',
    'let middleware: unknown =': 'let middleware =',
    'const req: unknown =': 'const req: Request =',
    'let req: unknown =': 'let req: Request =',
    'const res: unknown =': 'const res: Response =',
    'let res: unknown =': 'let res: Response =',
    'const next: unknown =': 'const next: NextFunction =',
    'let next: unknown =': 'let next: NextFunction =',

    // Logger patterns
    'const logger: unknown =': 'const logger =',
    'let logger: unknown =': 'let logger =',
    'const log: unknown =': 'const log =',
    'let log: unknown =': 'let log =',

    // File system patterns
    'const file: unknown =': 'const file =',
    'let file: unknown =': 'let file =',
    'const files: unknown =': 'const files =',
    'let files: unknown =': 'let files =',
    'const path: unknown =': 'const path: string =',
    'let path: unknown =': 'let path: string =',
    'const filename: unknown =': 'const filename: string =',
    'let filename: unknown =': 'let filename: string =',
    'const content: unknown =': 'const content =',
    'let content: unknown =': 'let content =',

    // Crypto patterns
    'const hash: unknown =': 'const hash: string =',
    'let hash: unknown =': 'let hash: string =',
    'const signature: unknown =': 'const signature: string =',
    'let signature: unknown =': 'let signature: string =',
    'const encrypted: unknown =': 'const encrypted: string =',
    'let encrypted: unknown =': 'let encrypted: string =',
    'const decrypted: unknown =': 'const decrypted: string =',
    'let decrypted: unknown =': 'let decrypted: string =',

    // Time patterns
    'const duration: unknown =': 'const duration: number =',
    'let duration: unknown =': 'let duration: number =',
    'const timeout: unknown =': 'const timeout: number =',
    'let timeout: unknown =': 'let timeout: number =',
    'const interval: unknown =': 'const interval: number =',
    'let interval: unknown =': 'let interval: number =',

    // Generic patterns that should be more specific
    'const client: unknown =': 'const client =',
    'let client: unknown =': 'let client =',
    'const service: unknown =': 'const service =',
    'let service: unknown =': 'let service =',
    'const repository: unknown =': 'const repository =',
    'let repository: unknown =': 'let repository =',
    'const controller: unknown =': 'const controller =',
    'let controller: unknown =': 'let controller =',
    'const factory: unknown =': 'const factory =',
    'let factory: unknown =': 'let factory =',
    'const manager: unknown =': 'const manager =',
    'let manager: unknown =': 'let manager =',
    'const handler: unknown =': 'const handler =',
    'let handler: unknown =': 'let handler =',
    'const processor: unknown =': 'const processor =',
    'let processor: unknown =': 'let processor =',
    'const provider: unknown =': 'const provider =',
    'let provider: unknown =': 'let provider =',
    'const adapter: unknown =': 'const adapter =',
    'let adapter: unknown =': 'let adapter =',
    'const helper: unknown =': 'const helper =',
    'let helper: unknown =': 'let helper =',
    'const utility: unknown =': 'const utility =',
    'let utility: unknown =': 'let utility =',
    'const util: unknown =': 'const util =',
    'let util: unknown =': 'let util =',
};

function findAllTypeScriptFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'coverage'].includes(item)) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

function getErrorCount() {
    try {
        const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { encoding: 'utf8' });
        const errorMatches = output.match(/error TS/g) || [];
        return errorMatches.length;
    } catch (error) {
        const errorMatches = error.stdout.match(/error TS/g) || [];
        return errorMatches.length;
    }
}

function processFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let modifiedContent = content;
        let fixCount = 0;
        
        // Apply all type replacements
        for (const [oldPattern, newPattern] of Object.entries(typeReplacements)) {
            const regex = new RegExp(escapeRegExp(oldPattern), 'g');
            const matches = modifiedContent.match(regex);
            if (matches) {
                modifiedContent = modifiedContent.replace(regex, newPattern);
                fixCount += matches.length;
            }
        }
        
        if (fixCount > 0) {
            fs.writeFileSync(filePath, modifiedContent, 'utf8');
            return { filePath, fixCount };
        }
        
        return null;
    } catch (error) {
        return { filePath, error: error.message };
    }
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

async function main() {
    console.log('🔍 Scanning for TypeScript files...');
    
    const files = findAllTypeScriptFiles('./src');
    console.log(`📁 Found ${files.length} TypeScript files`);
    
    console.log('📊 Getting initial error count...');
    const initialErrors = getErrorCount();
    console.log(`🚨 Initial TypeScript errors: ${initialErrors}`);
    
    console.log('🚀 Starting type replacements...');
    const startTime = Date.now();
    
    const results = [];
    let totalFixedIssues = 0;
    
    for (const file of files) {
        const result = processFile(file);
        if (result) {
            results.push(result);
            if (result.fixCount) {
                totalFixedIssues += result.fixCount;
            }
        }
    }
    
    console.log('📊 Getting final error count...');
    const finalErrors = getErrorCount();
    const totalErrorsFixed = initialErrors - finalErrors;
    
    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;
    
    console.log('\n🎯 PHASE 2 TYPE REPLACEMENT COMPLETE!');
    console.log('====================================');
    console.log(`⏱️  Processing time: ${processingTime.toFixed(2)} seconds`);
    console.log(`📁 Files processed: ${results.filter(r => !r.error).length}`);
    console.log(`❌ Files with errors: ${results.filter(r => r.error).length}`);
    console.log(`🔧 Total replacements applied: ${totalFixedIssues}`);
    console.log(`🚨 TypeScript errors before: ${initialErrors}`);
    console.log(`✅ TypeScript errors after: ${finalErrors}`);
    console.log(`🎯 Total errors fixed: ${totalErrorsFixed}`);
    
    if (totalErrorsFixed > 0) {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}%`);
        console.log('\n🎉 SUCCESS! Type replacements applied successfully!');
        console.log('🏆 Your application now has improved type safety!');
    } else if (totalErrorsFixed === 0) {
        console.log('📈 Success rate: 0.0% (no net change)');
        console.log('\n✨ No errors were fixed, but no new errors were introduced!');
    } else {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}% (negative - new errors introduced)`);
        console.log('\n⚠️  Some replacements may have introduced new errors. Consider reverting.');
    }
    
    const errorFiles = results.filter(r => r.error);
    if (errorFiles.length > 0) {
        console.log('\n❌ Files with processing errors:');
        errorFiles.forEach(({ filePath, error }) => {
            console.log(`   ${path.relative(process.cwd(), filePath)}: ${error}`);
        });
    }
}

main().catch(console.error);
