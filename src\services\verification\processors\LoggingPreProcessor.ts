// jscpd:ignore-file
/**
 * Logging Pre-Processor
 * 
 * Pre-processor that logs verification requests.
 */

import { VerificationRequest as ImportedVerificationRequest } from "../../../interfaces/verification/IVerificationStrategy";
import { VerificationPreProcessor as ImportedVerificationPreProcessor } from "./VerificationPreProcessor";
import { logger as Importedlogger } from "../../../lib/logger";
import { VerificationPreProcessor as ImportedVerificationPreProcessor } from "./VerificationPreProcessor";
import { logger as Importedlogger } from "../../../lib/logger";

/**
 * Logging pre-processor
 */
export class LoggingPreProcessor implements VerificationPreProcessor {
    /**
   * Get the name of the pre-processor
   */
    public getName(): string {
        return "logging_pre_processor";
    }
  
    /**
   * Process a verification request
   */
    public async process(request: VerificationRequest): Promise<VerificationRequest> {
        (logger as any).info(`Processing verification request for transaction: ${(request as any).transactionId}`, {
            verificationMethod: (request as any).verificationMethod,
            merchantId: (request as any).merchantId,
            paymentMethodId: (request as any).paymentMethodId,
            paymentMethodType: (request as any).paymentMethodType,
            amount: (request as any).amount,
            currency: (request as any).currency
        });
    
        // Return the request unchanged
        return request;
    }
}
