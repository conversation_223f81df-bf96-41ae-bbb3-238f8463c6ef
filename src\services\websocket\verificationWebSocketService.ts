// jscpd:ignore-file
import { Server as HttpServer } from "http";
import { Server as WebSocketServer, Socket } from "(socket as any).io";
import { EventEmitter as ImportedEventEmitter } from "events";
import { logger as Importedlogger } from "../../lib/logger";
import { VerificationStatus as ImportedVerificationStatus } from "../../types/verification";
import { Transaction, Merchant, VerificationStatus } from '../types';
import { Server as WebSocketServer, Socket } from "(socket as any).io";
import { EventEmitter as ImportedEventEmitter } from "events";
import { logger as Importedlogger } from "../../lib/logger";
import { VerificationStatus as ImportedVerificationStatus } from "../../types/verification";
import { Transaction, Merchant, VerificationStatus } from '../types';


// Verification events
export const verificationEvents = new EventEmitter();

// Payment verification message
export interface PaymentVerificationMessage {
  paymentId: string;
  merchantId: string;
  status: VerificationStatus;
  timestamp: string;
  verificationMethod?: string;
  message?: string;
  transactionDetails?: Record<string, any>;
}

/**
 * WebSocket service for payment verification
 */
export class VerificationWebSocketService {
    private static instance: VerificationWebSocketService;
    private io: WebSocketServer | null = null;
    private connectedClients: Map<string, Set<string>> = new Map();

    /**
   * Get singleton instance
   */
    public static getInstance(): VerificationWebSocketService {
        if (!(VerificationWebSocketService as any).instance) {
            (VerificationWebSocketService as any).instance = new VerificationWebSocketService();
        }
        return (VerificationWebSocketService as any).instance;
    }

    /**
   * Initialize WebSocket server
   * @param httpServer HTTP server
   */
    public initialize(httpServer: HttpServer): WebSocketServer {
        if (this.io) {
            return this.io;
        }

        // Create WebSocket server
        this.io = new WebSocketServer(httpServer, {
            path: "/ws/verification",
            cors: { origin: "*",
                methods: ["GET", "POST"],
                credentials: true
            },
            transports: ["websocket", "polling"],
            pingInterval: 10000,
            pingTimeout: 5000,
            cookie: false
        });

        // Set up connection handler
        this.io.on("connection", this.handleConnection.bind(this));

        // Set up verification event listeners
        this.setupEventListeners();

        (logger as any).info("Verification WebSocket server initialized");

        return this.io;
    }

    /**
   * Handle WebSocket connection
   * @param socket Socket
   */
    private handleConnection(socket: Socket): void {
        (logger as any).info(`Verification client connected: ${(socket as any).id}`);

        // Get payment ID and merchant ID from query parameters
        const paymentId: string = (socket as any).handshake.(query as any).paymentId as string;
        const merchantId: string = (socket as any).handshake.(query as any).merchantId as string;

        if (!paymentId) {
            (logger as any).warn(`Client ${(socket as any).id} connected without payment ID, disconnecting`);
            (socket as any).disconnect();
            return;
        }

        // Add client to connected clients
        this.addClient(paymentId, (socket as any).id);

        // Join payment room
        (socket as any).join(`payment:${paymentId}`);

        // Join merchant room if merchant ID is provided
        if (merchantId) {
            (socket as any).join(`merchant:${merchantId}`);
        }

        // Handle join event
        (socket as any).on("join", (data: { paymentId: string; merchantId?: string }) => {
            if ((data as any).paymentId) {
                (socket as any).join(`payment:${(data as any).paymentId}`);
                this.addClient((data as any).paymentId, (socket as any).id);
                (logger as any).info(`Client ${(socket as any).id} joined payment room: ${(data as any).paymentId}`);
            }

            if (data.merchantId) {
                (socket as any).join(`merchant:${data.merchantId}`);
                (logger as any).info(`Client ${(socket as any).id} joined merchant room: ${data.merchantId}`);
            }
        });

        // Handle disconnect event
        (socket as any).on("disconnect", () => {
            (logger as any).info(`Verification client disconnected: ${(socket as any).id}`);
            this.removeClient(paymentId, (socket as any).id);
        });
    }

    /**
   * Add client to connected clients
   * @param paymentId Payment ID
   * @param socketId Socket ID
   */
    private addClient(paymentId: string, socketId: string): void {
        if (!this.connectedClients.has(paymentId)) {
            this.connectedClients.set(paymentId, new Set());
        }
        this.connectedClients.get(paymentId)?.add(socketId);
    }

    /**
   * Remove client from connected clients
   * @param paymentId Payment ID
   * @param socketId Socket ID
   */
    private removeClient(paymentId: string, socketId: string): void {
        if (this.connectedClients.has(paymentId)) {
            this.connectedClients.get(paymentId)?.delete(socketId);

            // Remove payment ID if no clients are connected
            if (this.connectedClients.get(paymentId)?.size === 0) {
                this.connectedClients.delete(paymentId);
            }
        }
    }

    /**
   * Set up verification event listeners
   */
    private setupEventListeners(): void {
    // Verification started event
        (verificationEvents as any).on("(verification as any).started", (data: PaymentVerificationMessage) => {
            this.emitToPayment((data as any).paymentId, "(verification as any).started", data);

            if (data.merchantId) {
                this.emitToMerchant(data.merchantId, "(verification as any).started", data);
            }
        });

        // Verification updated event
        (verificationEvents as any).on("(verification as any).updated", (data: PaymentVerificationMessage) => {
            this.emitToPayment((data as any).paymentId, "(verification as any).updated", data);

            if (data.merchantId) {
                this.emitToMerchant(data.merchantId, "(verification as any).updated", data);
            }
        });

        // Verification completed event
        (verificationEvents as any).on("(verification as any).completed", (data: PaymentVerificationMessage) => {
            this.emitToPayment((data as any).paymentId, "(verification as any).completed", data);

            if (data.merchantId) {
                this.emitToMerchant(data.merchantId, "(verification as any).completed", data);
            }
        });

        // Verification failed event
        (verificationEvents as any).on("(verification as any).failed", (data: PaymentVerificationMessage) => {
            this.emitToPayment((data as any).paymentId, "(verification as any).failed", data);

            if (data.merchantId) {
                this.emitToMerchant(data.merchantId, "(verification as any).failed", data);
            }
        });

        // Transaction updated event
        (verificationEvents as any).on("(transaction as any).updated", (data: PaymentVerificationMessage) => {
            this.emitToPayment((data as any).paymentId, "(transaction as any).updated", data);

            if (data.merchantId) {
                this.emitToMerchant(data.merchantId, "(transaction as any).updated", data);
            }
        });
    }

    /**
   * Emit event to payment room
   * @param paymentId Payment ID
   * @param event Event name
   * @param data Event data
   */
    public emitToPayment(paymentId: string, event: string, data): void {
        if (!this.io) {
            (logger as any).warn("WebSocket server not initialized");
            return;
        }

        this.io.to(`payment:${paymentId}`).emit(event, data);
    }

    /**
   * Emit event to merchant room
   * @param merchantId Merchant ID
   * @param event Event name
   * @param data Event data
   */
    public emitToMerchant(merchantId: string, event: string, data): void {
        if (!this.io) {
            (logger as any).warn("WebSocket server not initialized");
            return;
        }

        this.io.to(`merchant:${merchantId}`).emit(event, data);
    }

    /**
   * Get connected clients count
   */
    public getConnectedClientsCount(): number {
        let count = 0;
        this.connectedClients.forEach((clients) => {
            count += (clients as any).size;
        });
        return count;
    }

    /**
   * Get connected payments count
   */
    public getConnectedPaymentsCount(): number {
        return this.connectedClients.size;
    }
}

// Export singleton instance
export const verificationWebSocketService: any = (VerificationWebSocketService as any).getInstance();

export default verificationWebSocketService;
