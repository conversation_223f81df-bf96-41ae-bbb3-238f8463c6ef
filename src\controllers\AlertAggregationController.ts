// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from "../base/BaseController";
import { asyncHandler } from "../../utils/asyncHandler";
import { AppError } from "../../utils/appError";
import prisma from "../../lib/prisma";
import { AlertType, AlertSeverity } from "../../services/alert.service";
import { Alert, AlertType, AlertSeverity } from '../types';
import { BaseController } from "../base/BaseController";
import { asyncHandler } from "../../utils/asyncHandler";
import { AppError } from "../../utils/appError";
import { AlertType, AlertSeverity } from "../../services/alert.service";
import { Alert, AlertType, AlertSeverity } from '../types';


/**
 * Alert aggregation controller
 */
export class AlertAggregationController extends BaseController {
  constructor() {
    super();
  }

  /**
   * Get aggregation rules
   * @route GET /api/alerts/aggregation/rules
   */
  getAggregationRules = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization and admin role
    const { userRole } = this.checkAuthorization(req);
    this.checkAdminRole(userRole);

    // Get aggregation rules
    const rules = await prisma.alertAggregationRule.findMany({
      orderBy: { createdAt: "desc" }
    });

    // Return rules
    return this.sendSuccess(res, rules);
  });

  /**
   * Get aggregation rule by ID
   * @route GET /api/alerts/aggregation/rules/:id
   */
  getAggregationRule = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization and admin role
    const { userRole } = this.checkAuthorization(req);
    this.checkAdminRole(userRole);

    // Get rule ID from params
    const ruleId: unknown = req.params.id;
    if (!ruleId) {
      throw new AppError({
            message: "Rule ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get aggregation rule
    const rule = await prisma.alertAggregationRule.findUnique({
      where: { id: ruleId }
    });

    // Check if rule exists
    if (!rule) {
      throw new AppError({
            message: "Rule not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
    }

    // Return rule
    return this.sendSuccess(res, rule);
  });

  /**
   * Create aggregation rule
   * @route POST /api/alerts/aggregation/rules
   */
  createAggregationRule = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization and admin role
    const { userRole } = this.checkAuthorization(req);
    this.checkAdminRole(userRole);

    // Get rule data from body
    const {
      name,
      description,
      type,
      severity,
      timeWindow,
      threshold,
      groupBy,
      enabled
    } = req.body;

    // Validate required fields
    if (!name || !description || !type || !severity || !timeWindow || !threshold || !groupBy) {
      throw new AppError({
            message: "Missing required fields",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Validate type
    if (type !== "ANY" && !Object.values(AlertType).includes(type as AlertType)) {
      throw new AppError({
            message: "Invalid alert type",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }

    // Validate severity
    if (severity !== "ANY" && !Object.values(AlertSeverity).includes(severity as AlertSeverity)) {
      throw new AppError({
            message: "Invalid alert severity",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }

    // Validate timeWindow
    if (timeWindow <= 0) {
      throw new AppError({
            message: "Time window must be greater than 0",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }

    // Validate threshold
    if (threshold <= 0) {
      throw new AppError({
            message: "Threshold must be greater than 0",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }

    // Validate groupBy
    if (!Array.isArray(groupBy) || groupBy.length === 0) {
      throw new AppError({
            message: "Group by must be a non-empty array",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }

    // Create aggregation rule
    const rule = await prisma.alertAggregationRule.create({
      data: {
        name,
        description,
        type,
        severity,
        timeWindow,
        threshold,
        groupBy,
        enabled: enabled !== undefined ? enabled : true
      }
    });

    // Return created rule
    return this.sendSuccess(res, rule, 201);
  });

  /**
   * Update aggregation rule
   * @route PUT /api/alerts/aggregation/rules/:id
   */
  updateAggregationRule = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization and admin role
    const { userRole } = this.checkAuthorization(req);
    this.checkAdminRole(userRole);

    // Get rule ID from params
    const ruleId: unknown = req.params.id;
    if (!ruleId) {
      throw new AppError({
            message: "Rule ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get rule data from body
    const {
      name,
      description,
      type,
      severity,
      timeWindow,
      threshold,
      groupBy,
      enabled
    } = req.body;

    // Check if rule exists
    const existingRule = await prisma.alertAggregationRule.findUnique({
      where: { id: ruleId }
    });

    if (!existingRule) {
      throw new AppError({
            message: "Rule not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
    }

    // Validate type if provided
    if (type !== undefined && type !== "ANY" && !Object.values(AlertType).includes(type as AlertType)) {
      throw new AppError({
            message: "Invalid alert type",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }

    // Validate severity if provided
    if (severity !== undefined && severity !== "ANY" && !Object.values(AlertSeverity).includes(severity as AlertSeverity)) {
      throw new AppError({
            message: "Invalid alert severity",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }

    // Validate timeWindow if provided
    if (timeWindow !== undefined && timeWindow <= 0) {
      throw new AppError({
            message: "Time window must be greater than 0",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }

    // Validate threshold if provided
    if (threshold !== undefined && threshold <= 0) {
      throw new AppError({
            message: "Threshold must be greater than 0",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }

    // Validate groupBy if provided
    if (groupBy !== undefined && (!Array.isArray(groupBy) || groupBy.length === 0)) {
      throw new AppError({
            message: "Group by must be a non-empty array",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
    }

    // Update aggregation rule
    const rule = await prisma.alertAggregationRule.update({
      where: { id: ruleId },
      data: { name: name !== undefined ? name : undefined,
        description: description !== undefined ? description : undefined,
        type: type !== undefined ? type : undefined,
        severity: severity !== undefined ? severity : undefined,
        timeWindow: timeWindow !== undefined ? timeWindow : undefined,
        threshold: threshold !== undefined ? threshold : undefined,
        groupBy: groupBy !== undefined ? groupBy : undefined,
        enabled: enabled !== undefined ? enabled : undefined
      }
    });

    // Return updated rule
    return this.sendSuccess(res, rule);
  });

  /**
   * Delete aggregation rule
   * @route DELETE /api/alerts/aggregation/rules/:id
   */
  deleteAggregationRule = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization and admin role
    const { userRole } = this.checkAuthorization(req);
    this.checkAdminRole(userRole);

    // Get rule ID from params
    const ruleId: unknown = req.params.id;
    if (!ruleId) {
      throw new AppError({
            message: "Rule ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Check if rule exists
    const existingRule = await prisma.alertAggregationRule.findUnique({
      where: { id: ruleId }
    });

    if (!existingRule) {
      throw new AppError({
            message: "Rule not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
    }

    // Delete aggregation rule
    await prisma.alertAggregationRule.delete({
      where: { id: ruleId }
    });

    // Return success
    return this.sendSuccess(res, { success: true, message: "Rule deleted successfully" });
  });

  /**
   * Get correlation rules
   * @route GET /api/alerts/correlation/rules
   */
  getCorrelationRules = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization and admin role
    const { userRole } = this.checkAuthorization(req);
    this.checkAdminRole(userRole);

    // Get correlation rules
    const rules = await prisma.alertCorrelationRule.findMany({
      orderBy: { createdAt: "desc" }
    });

    // Return rules
    return this.sendSuccess(res, rules);
  });

  /**
   * Get correlation rule by ID
   * @route GET /api/alerts/correlation/rules/:id
   */
  getCorrelationRule = asyncHandler(async (req: Request, res: Response) => {
    // Check authorization and admin role
    const { userRole } = this.checkAuthorization(req);
    this.checkAdminRole(userRole);

    // Get rule ID from params
    const ruleId: unknown = req.params.id;
    if (!ruleId) {
      throw new AppError({
            message: "Rule ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get correlation rule
    const rule = await prisma.alertCorrelationRule.findUnique({
      where: { id: ruleId }
    });

    // Check if rule exists
    if (!rule) {
      throw new AppError({
            message: "Rule not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
    }

    // Return rule
    return this.sendSuccess(res, rule);
  });
}
