// jscpd:ignore-file
/**
 * Verification Chain
 * 
 * Implements a chain of verification steps for multi-step verification.
 */

import { IVerificationStrategy, VerificationRequest, VerificationResult } from "../../interfaces/verification/IVerificationStrategy";
import { logger } from "../../lib/logger";
import { v4 as uuidv4 } from "uuid";
import { VerificationResult } from '../types';
import { logger } from "../../lib/logger";
import { v4 as uuidv4 } from "uuid";
import { VerificationResult } from '../types';


/**
 * Verification step result
 */
export interface VerificationStepResult extends VerificationResult {
  stepName: string;
  stepIndex: number;
}

/**
 * Verification chain result
 */
export interface VerificationChainResult {
  success: boolean;
  transactionId: string;
  verificationId?: string;
  message?: string;
  stepResults: VerificationStepResult[];
  completedSteps: number;
  totalSteps: number;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

/**
 * Verification chain options
 */
export interface VerificationChainOptions {
  /**
   * Whether to continue verification after a step fails
   */
  continueOnFailure?: boolean;
  
  /**
   * Whether to run steps in parallel
   */
  parallel?: boolean;
  
  /**
   * Timeout for the entire chain in milliseconds
   */
  timeout?: number;
}

/**
 * Verification chain
 */
export class VerificationChain {
    private steps: IVerificationStrategy[] = [];
    private options: VerificationChainOptions = {
        continueOnFailure: false,
        parallel: false,
        timeout: 30000 // 30 seconds
    };
  
    /**
   * Constructor
   * 
   * @param options Verification chain options
   */
    constructor(options?: VerificationChainOptions) {
        if (options) {
            this.options = {
                ...this.options,
                ...options
            };
        }
    }
  
    /**
   * Add a verification step
   * 
   * @param step Verification strategy
   * @returns This chain for chaining
   */
    public addStep(step: IVerificationStrategy): VerificationChain {
        this.steps.push(step);
        return this;
    }
  
    /**
   * Add multiple verification steps
   * 
   * @param steps Array of verification strategies
   * @returns This chain for chaining
   */
    public addSteps(steps: IVerificationStrategy[]): VerificationChain {
        this.steps.push(...steps);
        return this;
    }
  
    /**
   * Set chain options
   * 
   * @param options Verification chain options
   * @returns This chain for chaining
   */
    public setOptions(options: VerificationChainOptions): VerificationChain {
        this.options = {
            ...this.options,
            ...options
        };
        return this;
    }
  
    /**
   * Verify a request using the chain
   * 
   * @param request Verification request
   * @returns Verification chain result
   */
    public async verify(request: VerificationRequest): Promise<VerificationChainResult> {
        const chainId: unknown =uuidv4();
        const startTime: unknown = Date.now();
    
        logger.info(`Starting verification chain ${chainId} with ${this.steps.length} steps`, {
            transactionId: request.transactionId,
            chainId,
            options: this.options
        });
    
        // Create a copy of the request for each step
        const stepResults: VerificationStepResult[] = [];
    
        try {
            if (this.options.parallel) {
                // Run steps in parallel
                const promises: unknown = this.steps.map(async (step, index) => {
                    try {
                        const stepResult: unknown = await this.executeStep(step, request, index);
                        return stepResult;
                    } catch (error) {
                        logger.error(`Error in verification step ${index}:`, error);
                        return {
                            success: false,
                            transactionId: request.transactionId,
                            stepName: step.getType(),
                            stepIndex: index,
                            message: `Error in verification step: ${(error as Error).message}`,
                            timestamp: new Date()
                        };
                    }
                });
        
                // Wait for all steps to complete with timeout
                const results: unknown = await Promise.race([
                    Promise.all(promises),
                    new Promise<never>((_, reject) => {
                        setTimeout(() => reject(new Error("Verification chain timeout")), this.options.timeout);
                    })
                ]);
        
                stepResults.push(...results);
            } else {
                // Run steps sequentially
                for (let i: number =0; i < this.steps.length; i++) {
                    const step: unknown = this.steps[i];
          
                    try {
                        const stepResult: unknown = await this.executeStep(step, request, i);
                        stepResults.push(stepResult);
            
                        // Stop if step failed and continueOnFailure is false
                        if (!stepResult.success && !this.options.continueOnFailure) {
                            break;
                        }
                    } catch (error) {
                        logger.error(`Error in verification step ${i}:`, error);
            
                        const errorResult: VerificationStepResult = {
                            success: false,
                            transactionId: request.transactionId,
                            stepName: step.getType(),
                            stepIndex: i,
                            message: `Error in verification step: ${(error as Error).message}`,
                            timestamp: new Date()
                        };
            
                        stepResults.push(errorResult);
            
                        // Stop if continueOnFailure is false
                        if (!this.options.continueOnFailure) {
                            break;
                        }
                    }
                }
            }
      
            // Calculate chain result
            const completedSteps: unknown =stepResults.length;
            const totalSteps: unknown = this.steps.length;
            const successfulSteps: unknown =stepResults.filter(result => result.success).length;
            const allStepsSuccessful: unknown =successfulSteps === totalSteps;
            const allStepsCompleted: unknown =completedSteps === totalSteps;
      
            const chainResult: VerificationChainResult = {
                success: allStepsSuccessful,
                transactionId: request.transactionId,
                verificationId: chainId,
                message: this.getChainResultMessage(stepResults, allStepsSuccessful, allStepsCompleted),
                stepResults,
                completedSteps,
                totalSteps,
                timestamp: new Date(),
                metadata: {
                    chainId,
                    duration: Date.now() - startTime,
                    options: this.options
                }
            };
      
            logger.info(`Verification chain ${chainId} completed`, {
                transactionId: request.transactionId,
                chainId,
                success: chainResult.success,
                completedSteps,
                totalSteps,
                duration: chainResult.metadata.duration
            });
      
            return chainResult;
        } catch (error) {
            logger.error(`Error in verification chain ${chainId}:`, error);
      
            return {
                success: false,
                transactionId: request.transactionId,
                verificationId: chainId,
                message: `Verification chain error: ${(error as Error).message}`,
                stepResults,
                completedSteps: stepResults.length,
                totalSteps: this.steps.length,
                timestamp: new Date(),
                metadata: {
                    chainId,
                    duration: Date.now() - startTime,
                    error: (error as Error).message
                }
            };
        }
    }
  
    /**
   * Execute a verification step
   * 
   * @param step Verification strategy
   * @param request Verification request
   * @param index Step index
   * @returns Verification step result
   */
    private async executeStep(
        step: IVerificationStrategy,
        request: VerificationRequest,
        index: number
    ): Promise<VerificationStepResult> {
        const stepName: unknown =step.getType();
    
        logger.debug(`Executing verification step ${index}: ${stepName}`, {
            transactionId: request.transactionId,
            stepName,
            stepIndex: index
        });
    
        // Create a copy of the request with the step's verification method
        const stepRequest: VerificationRequest = {
            ...request,
            verificationMethod: stepName
        };
    
        // Execute the step
        const result: unknown = await step.verify(stepRequest);
    
        // Convert to step result
        const stepResult: VerificationStepResult = {
            ...result,
            stepName,
            stepIndex: index
        };
    
        logger.debug(`Verification step ${index} result: ${stepResult.success ? "success" : "failure"}`, {
            transactionId: request.transactionId,
            stepName,
            stepIndex: index,
            success: stepResult.success,
            message: (stepResult as Error).message
        });
    
        return stepResult;
    }
  
    /**
   * Get the chain result message
   * 
   * @param stepResults Step results
   * @param allStepsSuccessful Whether all steps were successful
   * @param allStepsCompleted Whether all steps were completed
   * @returns Chain result message
   */
    private getChainResultMessage(
        stepResults: VerificationStepResult[],
        allStepsSuccessful: boolean,
        allStepsCompleted: boolean
    ): string {
        if (allStepsSuccessful) {
            return "All verification steps completed successfully";
        }
    
        if (!allStepsCompleted) {
            const lastStep: unknown =stepResults[stepResults.length - 1];
            return `Verification chain stopped at step ${lastStep.stepIndex + 1}: ${(lastStep as Error).message}`;
        }
    
        const failedSteps: unknown =stepResults.filter(result => !result.success);
        const failedStepNames: unknown =failedSteps.map(step => step.stepName).join(", ");
    
        return `Verification failed: ${failedStepNames}`;
    }
}
