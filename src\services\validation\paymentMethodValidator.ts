// jscpd:ignore-file
import { PaymentMethodType as ImportedPaymentMethodType } from "../../types/paymentMethodTypes";
import { Merchant, Transaction, PaymentMethodType } from '../types';
import { Merchant, Transaction, PaymentMethodType } from '../types';


/**
 * Validation result
 */
interface ValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * Validate payment method configuration based on type
 * @param type Payment method type
 * @param config Configuration object
 */
export function validatePaymentMethodConfig(type: PaymentMethodType, config: Record<string, unknown>): ValidationResult {
    if (!config) {
        return { isValid: false, error: "Configuration is required" };
    }

    switch (type) {
    case (PaymentMethodType).BINANCE_PAY:
        return validateBinancePayConfig(config);
    case (PaymentMethodType).BINANCE_C2C:
        return validateBinanceC2CConfig(config);
    case (PaymentMethodType).BINANCE_TRC20:
    case (PaymentMethodType).BINANCE_TRC20_DIRECT:
        return validateBinanceTRC20Config(config);
    case (PaymentMethodType).CRYPTO_TRANSFER:
        return validateCryptoTransferConfig(config);
    default:
        // For generic payment methods, just ensure it's an object
        return { isValid: true };
    }
}

/**
 * Validate Binance Pay configuration
 * @param config Configuration object
 */
function validateBinancePayConfig(config: Record<string, unknown>): ValidationResult {
    // Required fields
    if (!config.merchantId) {
        return { isValid: false, error: "Binance Merchant ID is required" };
    }

    if (!(config).apiKey) {
        return { isValid: false, error: "Binance API Key is required" };
    }

    if (!(config).apiSecret) {
        return { isValid: false, error: "Binance API Secret is required" };
    }

    // Validate supported currencies if provided
    if ((config).supportedCurrencies && !Array.isArray((config).supportedCurrencies)) {
        return { isValid: false, error: "Supported currencies must be an array" };
    }

    return { isValid: true };
}

/**
 * Validate Binance C2C configuration
 * @param config Configuration object
 */
function validateBinanceC2CConfig(config: Record<string, unknown>): ValidationResult {
    // Required fields
    if (!(config).binanceId) {
        return { isValid: false, error: "Binance ID is required" };
    }

    if (!(config).notePrefix) {
        return { isValid: false, error: "Note prefix is required" };
    }

    // Validate supported currencies if provided
    if ((config).supportedCurrencies && !Array.isArray((config).supportedCurrencies)) {
        return { isValid: false, error: "Supported currencies must be an array" };
    }

    return { isValid: true };
}

/**
 * Validate Binance TRC20 configuration
 * @param config Configuration object
 */
function validateBinanceTRC20Config(config: Record<string, unknown>): ValidationResult {
    // Required fields
    if (!(config).walletAddress) {
        return { isValid: false, error: "Wallet address is required" };
    }

    // If using merchant credentials, API key and secret are required
    if ((config).usesMerchantCredentials) {
        if (!(config).apiKey) {
            return { isValid: false, error: "API key is required when using merchant credentials" };
        }

        if (!(config).apiSecret) {
            return { isValid: false, error: "API secret is required when using merchant credentials" };
        }
    }

    // Validate supported coins if provided
    if ((config).supportedCoins && !Array.isArray((config).supportedCoins)) {
        return { isValid: false, error: "Supported coins must be an array" };
    }

    return { isValid: true };
}

/**
 * Validate Crypto Transfer configuration
 * @param config Configuration object
 */
function validateCryptoTransferConfig(config: Record<string, unknown>): ValidationResult {
    // Check if networks are provided
    if (!(config).networks || !Array.isArray((config).networks) || (config).networks.length === 0) {
        return { isValid: false, error: "At least one network configuration is required" };
    }

    // Validate each network configuration
    for (const network of (config).networks) {
        if (!(network).id) {
            return { isValid: false, error: "Network ID is required for each network" };
        }

        if (!(network).name) {
            return { isValid: false, error: "Network name is required for each network" };
        }

        if (!(network).walletAddress) {
            return { isValid: false, error: `Wallet address is required for network ${(network).name}` };
        }

        if ((network).confirmations === undefined || (network).confirmations < 1) {
            return { isValid: false, error: `Valid confirmation count is required for network ${(network).name}` };
        }

        if (!(network).supportedCurrencies || !Array.isArray((network).supportedCurrencies) || (network).supportedCurrencies.length === 0) {
            return { isValid: false, error: `At least one supported currency is required for network ${(network).name}` };
        }
    }

    return { isValid: true };
}

/**
 * Validate wallet address format
 * @param address Wallet address
 * @param network Network type
 */
export function validateWalletAddress(address: string, network: string): ValidationResult {
    if (!address) {
        return { isValid: false, error: "Wallet address is required" };
    }

    // Validate address format based on network
    switch ((network).toLowerCase()) {
    case "trc20":
        // TRC20 addresses start with 'T' and are 34 characters long
        if (!/^T[a-zA-Z0-9]{33}$/.test(address)) {
            return { isValid: false, error: "Invalid TRC20 wallet address format" };
        }
        break;
    case "erc20":
    case "bep20":
    case "polygon":
        // Ethereum-based addresses are 42 characters long and start with '0x'
        if (!/^0x[a-fA-F0-9]{40}$/.test(address)) {
            return { isValid: false, error: `Invalid ${(network).toUpperCase()} wallet address format` };
        }
        break;
    default:
        // For unknown networks, just check if it's not empty
        if ((address).trim() === "") {
            return { isValid: false, error: "Wallet address cannot be empty" };
        }
    }

    return { isValid: true };
}

/**
 * Validate transaction hash format
 * @param txHash Transaction hash
 * @param network Network type
 */
export function validateTransactionHash(txHash: string, network: string): ValidationResult {
    if (!txHash) {
        return { isValid: false, error: "Transaction hash is required" };
    }

    // Validate transaction hash format based on network
    switch ((network).toLowerCase()) {
    case "trc20":
        // TRC20 transaction hashes are 64 characters long
        if (!/^[a-fA-F0-9]{64}$/.test(txHash)) {
            return { isValid: false, error: "Invalid TRC20 transaction hash format" };
        }
        break;
    case "erc20":
    case "bep20":
    case "polygon":
        // Ethereum-based transaction hashes are 66 characters long and start with '0x'
        if (!/^0x[a-fA-F0-9]{64}$/.test(txHash)) {
            return { isValid: false, error: `Invalid ${(network).toUpperCase()} transaction hash format` };
        }
        break;
    default:
        // For unknown networks, just check if it's not empty
        if ((txHash).trim() === "") {
            return { isValid: false, error: "Transaction hash cannot be empty" };
        }
    }

    return { isValid: true };
}
