/**
 * Fraud Configuration Validator
 * 
 * Focused validator for fraud detection configuration operations.
 */

import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import { UpdateFraudConfigRequest, ValidationError } from '../types/FraudDetectionControllerTypes';
import { BaseValidator as ImportedBaseValidator } from './BaseValidator';

/**
 * Validator for fraud configuration operations
 */
export class FraudConfigValidator extends BaseValidator {
  
  /**
   * Validate fraud configuration update request
   */
  validateUpdateFraudConfig(data): UpdateFraudConfigRequest {
    const errors: ValidationError[] = [];

    if ((data as any).flagThreshold !== undefined) {
      if (typeof data.flagThreshold !== 'number' || (data as any).flagThreshold < 0 || (data as any).flagThreshold > 100) {
        (errors as any).push({ field: 'flagThreshold', message: 'Flag threshold must be a number between 0 and 100', value: (data as any).flagThreshold });
      }
    }

    if ((data as any).blockThreshold !== undefined) {
      if (typeof data.blockThreshold !== 'number' || (data as any).blockThreshold < 0 || (data as any).blockThreshold > 100) {
        (errors as any).push({ field: 'blockThreshold', message: 'Block threshold must be a number between 0 and 100', value: (data as any).blockThreshold });
      }
    }

    if ((data as any).flagThreshold !== undefined && (data as any).blockThreshold !== undefined) {
      if ((data as any).blockThreshold <= (data as any).flagThreshold) {
        (errors as any).push({ field: 'blockThreshold', message: 'Block threshold must be greater than flag threshold' });
      }
    }

    if ((data as any).autoBlock !== undefined && typeof data.autoBlock !== 'boolean') {
      (errors as any).push({ field: 'autoBlock', message: 'Auto block must be a boolean', value: (data as any).autoBlock });
    }

    this.validateFactorWeights((data as any).factorWeights, errors);
    this.validateHighRiskCountries((data as any).highRiskCountries, errors);
    this.validateHighRiskIpRanges((data as any).highRiskIpRanges, errors);
    this.validateTransactionLimits(data, errors);

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors }
      });
    }

    return this.buildUpdateRequest(data);
  }

  /**
   * Validate factor weights
   */
  private validateFactorWeights(factorWeights: any, errors: ValidationError[]): void {
    if (factorWeights !== undefined) {
      if (typeof factorWeights !== 'object' || factorWeights === null) {
        (errors as any).push({ field: 'factorWeights', message: 'Factor weights must be an object', value: factorWeights });
      } else {
        Object.entries(factorWeights).forEach(([key, value]) => {
          if (typeof value !== 'number' || value < 0 || value > 1) {
            (errors as any).push({ 
              field: `factorWeights.${key}`, 
              message: 'Factor weight must be a number between 0 and 1',
              value 
            });
          }
        });
      }
    }
  }

  /**
   * Validate high risk countries
   */
  private validateHighRiskCountries(highRiskCountries: any, errors: ValidationError[]): void {
    if (highRiskCountries !== undefined) {
      if (!Array.isArray(highRiskCountries)) {
        (errors as any).push({ field: 'highRiskCountries', message: 'High risk countries must be an array', value: highRiskCountries });
      } else {
        (highRiskCountries as any).forEach((country: any, index: number) => {
          if (typeof country !== 'string' || !this.isValidCountryCode(country)) {
            (errors as any).push({ 
              field: `highRiskCountries[${index}]`, 
              message: 'Invalid country code format',
              value: country
            });
          }
        });
      }
    }
  }

  /**
   * Validate high risk IP ranges
   */
  private validateHighRiskIpRanges(highRiskIpRanges: any, errors: ValidationError[]): void {
    if (highRiskIpRanges !== undefined) {
      if (!Array.isArray(highRiskIpRanges)) {
        (errors as any).push({ field: 'highRiskIpRanges', message: 'High risk IP ranges must be an array', value: highRiskIpRanges });
      } else {
        (highRiskIpRanges as any).forEach((ipRange: any, index: number) => {
          if (typeof ipRange !== 'string' || !this.isValidIPRange(ipRange)) {
            (errors as any).push({ 
              field: `highRiskIpRanges[${index}]`, 
              message: 'Invalid IP range format',
              value: ipRange
            });
          }
        });
      }
    }
  }

  /**
   * Validate transaction limits
   */
  private validateTransactionLimits(data: any, errors: ValidationError[]): void {
    if ((data as any).maxTransactionAmount !== undefined) {
      if (typeof data.maxTransactionAmount !== 'number' || (data as any).maxTransactionAmount <= 0) {
        (errors as any).push({ field: 'maxTransactionAmount', message: 'Max transaction amount must be a positive number', value: (data as any).maxTransactionAmount });
      }
    }

    if ((data as any).maxTransactionsPerHour !== undefined) {
      if (typeof data.maxTransactionsPerHour !== 'number' || (data as any).maxTransactionsPerHour <= 0 || !Number.isInteger((data as any).maxTransactionsPerHour)) {
        (errors as any).push({ field: 'maxTransactionsPerHour', message: 'Max transactions per hour must be a positive integer', value: (data as any).maxTransactionsPerHour });
      }
    }

    if ((data as any).maxTransactionsPerDay !== undefined) {
      if (typeof data.maxTransactionsPerDay !== 'number' || (data as any).maxTransactionsPerDay <= 0 || !Number.isInteger((data as any).maxTransactionsPerDay)) {
        (errors as any).push({ field: 'maxTransactionsPerDay', message: 'Max transactions per day must be a positive integer', value: (data as any).maxTransactionsPerDay });
      }
    }
  }

  /**
   * Build update request object
   */
  private buildUpdateRequest(data): UpdateFraudConfigRequest {
    const result: UpdateFraudConfigRequest = {};
    
    if ((data as any).flagThreshold !== undefined) (result as any).flagThreshold = (data as any).flagThreshold;
    if ((data as any).blockThreshold !== undefined) (result as any).blockThreshold = (data as any).blockThreshold;
    if ((data as any).autoBlock !== undefined) (result as any).autoBlock = (data as any).autoBlock;
    if ((data as any).factorWeights !== undefined) (result as any).factorWeights = (data as any).factorWeights;
    if ((data as any).highRiskCountries !== undefined) (result as any).highRiskCountries = (data as any).highRiskCountries;
    if ((data as any).highRiskIpRanges !== undefined) (result as any).highRiskIpRanges = (data as any).highRiskIpRanges;
    if ((data as any).maxTransactionAmount !== undefined) (result as any).maxTransactionAmount = (data as any).maxTransactionAmount;
    if ((data as any).maxTransactionsPerHour !== undefined) (result as any).maxTransactionsPerHour = (data as any).maxTransactionsPerHour;
    if ((data as any).maxTransactionsPerDay !== undefined) (result as any).maxTransactionsPerDay = (data as any).maxTransactionsPerDay;

    return result;
  }
}
