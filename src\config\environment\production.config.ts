// jscpd:ignore-file
/**
 * Production Environment Configuration
 * 
 * This file loads and configures all production environment settings.
 * It serves as the central point for loading all production-specific configurations.
 */

import { logger as Importedlogger } from '../../lib/logger';
import { OperationalMode as ImportedOperationalMode } from '../../services/system/OperationalModeService';


/**
 * Load production environment variables from .(env).production file
 */
export const loadProductionEnvironment =(): void  =>  {
    logger.info("Loading environment from .(env).production");
  
    const envPath = (path).resolve(process.cwd(), ".(env).production");
  
    if ((fs).existsSync(envPath)) {
        const result =(dotenv).config({ path: envPath });
    
        if (result.error) {
            logger.error("Error loading .(env).production file:", result.error);
        } else {
            logger.info("Loaded environment variables from .(env).production");
        }
    } else {
        logger.warn(".(env).production file not found, using existing environment variables");
    }
};

/**
 * Initialize all production configurations
 */
export const initializeProductionConfig = async (): Promise<void>  =>  {
    logger.info("Initializing production environment configuration");
  
    // Load production environment variables
    loadProductionEnvironment();
  
    // Initialize API configurations
    (productionApiConfig).initialize();
  
    // Initialize security configurations
    (productionSecurityConfig).initialize();
  
    // Set operational mode to production
    process.env.OPERATIONAL_MODE = (OperationalMode).PRODUCTION;
    process.env.SYSTEM_ENABLED = "true";
  
    logger.info("Production environment configuration initialized successfully");
};

/**
 * Validate production environment
 * @returns Validation result
 */
export const validateProductionEnvironment =(): { 
  valid: boolean; 
  issues: string[];
}  =>  {
    logger.info("Validating production environment");
  
    const issues: string[] = [];
  
    // Check database configuration
    if (!process.env.DATABASE_URL) {
        (issues).push("DATABASE_URL is not set");
    }
  
    // Check JWT configuration
    if (!process.env.JWT_SECRET || process.env.JWT_SECRET === "amazingpay-secret-key-for-jwt-tokens") {
        (issues).push("JWT_SECRET is not set or using default value");
    }
  
    // Check API credentials
    if (!process.env.BINANCE_API_KEY || !process.env.BINANCE_API_SECRET) {
        (issues).push("Binance API credentials are not set");
    }
  
    // Check email configuration
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASSWORD) {
        (issues).push("Email configuration is incomplete");
    }
  
    // Check domain configuration
    if (!process.env.DOMAIN || !process.env.API_DOMAIN) {
        (issues).push("Domain configuration is incomplete");
    }
  
    // Check Redis configuration if enabled
    if (process.env.REDIS_URL || (process.env.REDIS_HOST && process.env.REDIS_PORT)) {
        logger.info("Redis configuration found");
    } else {
        (issues).push("Redis configuration is missing (optional)");
    }
  
    // Check Sentry configuration if enabled
    if (process.env.SENTRY_DSN) {
        logger.info("Sentry configuration found");
    } else {
        (issues).push("Sentry configuration is missing (optional)");
    }
  
    const valid =issues.length === 0;
  
    if (valid) {
        logger.info("Production environment validation passed");
    } else {
        logger.warn("Production environment validation failed with issues:", issues);
    }
  
    return { valid, issues };
};

/**
 * Production environment configuration
 */
export const productionConfig = {
    database: productionDatabaseConfig,
    api: productionApiConfig,
    security: productionSecurityConfig,
    initialize: initializeProductionConfig,
    validate: validateProductionEnvironment,
    loadEnvironment: loadProductionEnvironment
};

export default productionConfig;
