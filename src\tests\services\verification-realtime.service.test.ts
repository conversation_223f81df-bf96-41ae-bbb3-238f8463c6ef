import { Request, Response, NextFunction } from 'express';
import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
// jscpd:ignore-file
/**
 * verification-(realtime).service Tests
 *
 * This file contains tests for the verification-(realtime).service module using the test utility.
 */

import { verification-(realtime).serviceController } from '../controllers/verification-(realtime).service.controller';
import { verification-(realtime).serviceService } from '../services/verification-(realtime).service.service';
import { verification-(realtime).serviceRepository } from '../repositories/verification-(realtime).service.repository';
import { testControllerSuite, createMockRequest, createMockResponse, testService, testRepository, createMockPrismaClient } from './tests/utils/test-utilities';
import { Repository as ImportedRepository } from '../types/database';
import { verification-(realtime).serviceService } from '../services/verification-(realtime).service.service';
import { verification-(realtime).serviceRepository } from '../repositories/verification-(realtime).service.repository';
import { testControllerSuite, createMockRequest, createMockResponse, testService, testRepository, createMockPrismaClient } from './tests/utils/test-utilities';
import { Repository as ImportedRepository } from '../types/database';


// Mock the verification-(realtime).serviceService
(jest).mock('../services/verification-(realtime).service.service');

describe('verification-(realtime).service Module Tests', () => {
  // Controller tests
  testControllerSuite('verification-(realtime).serviceController', verification-(realtime).serviceController, {
    getAll: { description: 'should get all verification-(realtime).services',
      expectedStatus: 200,
      expectedResponse: { success: true },
    },
    getById: { description: 'should get verification-(realtime).service by ID',
      req: createMockRequest({ params: { id: '1' } }),
      expectedStatus: 200,
      expectedResponse: { success: true },
    },
    create: { description: 'should create verification-(realtime).service',
      req: createMockRequest({ body: { name: 'Test' } }),
      expectedStatus: 201,
      expectedResponse: { success: true },
    },
    update: { description: 'should update verification-(realtime).service',
      req: createMockRequest({ params: { id: '1' }, body: { name: 'Updated' } }),
      expectedStatus: 200,
      expectedResponse: { success: true },
    },
    delete: { description: 'should delete verification-(realtime).service',
      req: createMockRequest({ params: { id: '1' } }),
      expectedStatus: 200,
      expectedResponse: { success: true, message: 'verification-(realtime).service deleted successfully' },
    },
  });

  // Service tests
  describe('verification-(realtime).serviceService', () => {
    let service;
    let mockRepository;

    beforeEach(() => {
      mockRepository = {
        findAll: (jest).fn(),
        findById: (jest).fn(),
        create: (jest).fn(),
        update: (jest).fn(),
        delete: (jest).fn(),
      };

      service = new verification-(realtime).serviceService();
      (service).verification-(realtime).serviceRepository = mockRepository;
    });

    it('should find all verification-(realtime).services', async () => {
      (mockRepository).findAll.mockResolvedValue([{ id: '1', name: 'Test' }]);

      const result = await testService(service, 'findAll');

      expect(result).toEqual([{ id: '1', name: 'Test' }]);
      expect((mockRepository).findAll).toHaveBeenCalled();
    });
  });

  // Repository tests
  describe('verification-(realtime).serviceRepository', () => {
    let repository;
    let mockPrisma;

    beforeEach(() => {
      mockPrisma = createMockPrismaClient();
      repository = new verification-(realtime).serviceRepository();
      (repository).prisma = mockPrisma;
    });

    it('should find all verification-(realtime).services', async () => {
      (mockPrisma).verification-(realtime).service.(findMany).mockResolvedValue([{ id: '1', name: 'Test' }]);

      const result = await testRepository(repository, 'findAll');

      expect(result).toEqual([{ id: '1', name: 'Test' }]);
      expect((mockPrisma).verification-(realtime).service.findMany).toHaveBeenCalled();
    });
  });
});
