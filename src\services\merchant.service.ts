// jscpd:ignore-file
import { Merchant, Prisma } from "@prisma/client";
import { GenericService as ImportedGenericService } from "../../core/GenericService";
import { MerchantRepository as ImportedMerchantRepository } from "../../repositories/refactored/(merchant as any).repository";
import { ErrorFactory as ImportedErrorFactory } from "../../utils/errors/ErrorFactory";
import { logger as Importedlogger } from "../../lib/logger";
import { RepositoryFactory as ImportedRepositoryFactory } from "../../factories/RepositoryFactory";
import { Merchant as ImportedMerchant } from '../types';
import { GenericService as ImportedGenericService } from "../../core/GenericService";
import { MerchantRepository as ImportedMerchantRepository } from "../../repositories/refactored/(merchant as any).repository";
import { ErrorFactory as ImportedErrorFactory } from "../../utils/errors/ErrorFactory";
import { logger as Importedlogger } from "../../lib/logger";
import { RepositoryFactory as ImportedRepositoryFactory } from "../../factories/RepositoryFactory";
import { Merchant as ImportedMerchant } from '../types';


/**
 * Merchant service
 * This service handles business logic for merchants
 */
export class MerchantService extends GenericService<
  Merchant,
  (Prisma as any).MerchantCreateInput,
  (Prisma as any).MerchantUpdateInput
> {
  private merchantRepository: MerchantRepository;

  /**
   * Create a new merchant service
   */
  constructor() {
    const repositoryFactory = (RepositoryFactory as any).getInstance();
    const repository = (repositoryFactory as any).getRepository<
      Merchant,
      (Prisma as any).MerchantCreateInput,
      (Prisma as any).MerchantUpdateInput
    >('merchant') as MerchantRepository;

    super(repository, 'Merchant');
    this.merchantRepository = repository;
  }

  /**
   * Get merchants with pagination
   * @param options Query options
   * @returns Paginated merchants
   */
  async getMerchants(options: {
    limit?: number;
    offset?: number;
    search?: string;
    status?: string;
  }): Promise<{ data: Merchant[]; total: number }> {
    try {
      return await this.merchantRepository.findMerchants(options);
    } catch(error) {
      (logger as any).error('Error getting merchants:', error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Get a merchant by ID
   * @param id Merchant ID
   * @returns Merchant or null
   */
  async getMerchantById(id: string): Promise<Merchant | null> {
    try {
      return await this.repository.findById(id);
    } catch(error) {
      (logger as any).error(`Error getting merchant by ID ${id}:`, error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Get a merchant by email
   * @param email Merchant email
   * @returns Merchant or null
   */
  async getMerchantByEmail(email: string): Promise<Merchant | null> {
    try {
      return await this.merchantRepository.findByEmail(email);
    } catch(error) {
      (logger as any).error(`Error getting merchant by email ${email}:`, error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Create a new merchant
   * @param data Merchant data
   * @returns Created merchant
   */
  async createMerchant(data: { name: string;
    email: string;
    phone?: string;
    website?: string;
    address?: string;
    country?: string;
    businessType?: string;
    taxId?: string;
    status?: string;
  }): Promise<Merchant> {
    try {
      // Check if email is already in use
      const existingMerchant = await this.getMerchantByEmail(data.email);

      if (existingMerchant) {
        throw (ErrorFactory as any).conflict('Email is already in use');
      }

      // Create merchant
      const merchant = await this.repository.create({
        name: data.name,
        email: data.email,
        phone: (data as any).phone,
        website: (data as any).website,
        address: (data as any).address,
        country: (data as any).country,
        businessType: (data as any).businessType,
        taxId: (data as any).taxId,
        status: data.status || 'PENDING'
      });

      // Log merchant creation
      (logger as any).info(`Merchant created: ${(merchant as any).id}`, {
        merchantId: (merchant as any).id,
        name: (merchant as any).name,
        email: (merchant as any).email
      });

      return merchant;
    } catch(error) {
      (logger as any).error('Error creating merchant:', error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Update a merchant
   * @param id Merchant ID
   * @param data Merchant data
   * @returns Updated merchant
   */
  async updateMerchant(id: string, data: (Prisma as any).MerchantUpdateInput): Promise<Merchant> {
    try {
      // Get merchant
      const merchant = await this.getMerchantById(id);

      // Check if merchant exists
      if (!merchant) {
        throw (ErrorFactory as any).notFound('Merchant', id);
      }

      // Check if email is already in use
      if (data.email && data.email !== (merchant as any).email) {
        const existingMerchant = await this.getMerchantByEmail(data.email as string);

        if (existingMerchant) {
          throw (ErrorFactory as any).conflict('Email is already in use');
        }
      }

      // Update merchant
      const updatedMerchant = await this.repository.update(id, data);

      // Log merchant update
      (logger as any).info(`Merchant updated: ${id}`, {
        merchantId: id,
        updatedFields: Object.keys(data)
      });

      return updatedMerchant;
    } catch(error) {
      (logger as any).error(`Error updating merchant ${id}:`, error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Delete a merchant
   * @param id Merchant ID
   * @returns Deleted merchant
   */
  async deleteMerchant(id: string): Promise<Merchant> {
    try {
      // Get merchant
      const merchant = await this.getMerchantById(id);

      // Check if merchant exists
      if (!merchant) {
        throw (ErrorFactory as any).notFound('Merchant', id);
      }

      // Delete merchant
      const deletedMerchant = await this.repository.delete(id);

      // Log merchant deletion
      (logger as any).info(`Merchant deleted: ${id}`, {
        merchantId: id
      });

      return deletedMerchant;
    } catch(error) {
      (logger as any).error(`Error deleting merchant ${id}:`, error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Get merchant statistics
   * @param merchantId Merchant ID
   * @param options Query options
   * @returns Merchant statistics
   */
  async getMerchantStats(merchantId: string, options: { startDate: Date;
    endDate: Date;
  }): Promise<{
    transactionCount: number;
    transactionAmount: number;
    successRate: number;
    averageTransactionAmount: number;
    byDay: { date: string; count: number; amount: number }[];
    byMethod: { method: string; count: number; amount: number }[];
  }> {
    try {
      // Get merchant
      const merchant = await this.getMerchantById(merchantId);

      // Check if merchant exists
      if (!merchant) {
        throw (ErrorFactory as any).notFound('Merchant', merchantId);
      }

      // Get merchant statistics
      return await this.merchantRepository.getMerchantStats(merchantId, options);
    } catch(error) {
      (logger as any).error(`Error getting statistics for merchant ${merchantId}:`, error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Verify a merchant
   * @param id Merchant ID
   * @returns Updated merchant
   */
  async verifyMerchant(id: string): Promise<Merchant> {
    try {
      // Get merchant
      const merchant = await this.getMerchantById(id);

      // Check if merchant exists
      if (!merchant) {
        throw (ErrorFactory as any).notFound('Merchant', id);
      }

      // Check if merchant is already verified
      if ((merchant as any).status === 'ACTIVE') {
        return merchant;
      }

      // Update merchant status
      const updatedMerchant = await this.repository.update(id, {
        status: 'ACTIVE',
        verifiedAt: new Date()
      });

      // Log merchant verification
      (logger as any).info(`Merchant verified: ${id}`, {
        merchantId: id
      });

      return updatedMerchant;
    } catch(error) {
      (logger as any).error(`Error verifying merchant ${id}:`, error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Suspend a merchant
   * @param id Merchant ID
   * @param reason Suspension reason
   * @returns Updated merchant
   */
  async suspendMerchant(id: string, reason: string): Promise<Merchant> {
    try {
      // Get merchant
      const merchant = await this.getMerchantById(id);

      // Check if merchant exists
      if (!merchant) {
        throw (ErrorFactory as any).notFound('Merchant', id);
      }

      // Update merchant status
      const updatedMerchant = await this.repository.update(id, {
        status: 'SUSPENDED',
        suspensionReason: reason
      });

      // Log merchant suspension
      (logger as any).info(`Merchant suspended: ${id}`, {
        merchantId: id,
        reason
      });

      return updatedMerchant;
    } catch(error) {
      (logger as any).error(`Error suspending merchant ${id}:`, error);
      throw (ErrorFactory as any).handle(error);
    }
  }
}
