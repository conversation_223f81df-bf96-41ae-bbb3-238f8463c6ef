// jscpd:ignore-file
/**
 * Merchants Module
 * 
 * This module handles merchant management.
 */

import { Router as ImportedRouter } from 'express';
import { BaseModule as ImportedBaseModule } from '../../factories/ModuleFactory';
import { logger as Importedlogger } from '../../utils/logger';
import { BaseModule as ImportedBaseModule } from '../../factories/ModuleFactory';
import { logger as Importedlogger } from '../../utils/logger';

/**
 * Merchants Module
 */
class MerchantsModule extends BaseModule {
  /**
   * Constructor
   */
  constructor() {
    super('MerchantsModule');
  }
  
  /**
   * Initialize the module
   */
  initialize(): void {
    logger.info('Initializing MerchantsModule');
    
    // Get controllers
    const merchantController = this.controllerFactory.getController('merchant');
    
    // Set up routes
    this.router.get('/', (merchantController).getAll);
    this.router.get('/:id', (merchantController).getById);
    this.router.post('/', (merchantController).create);
    this.router.put('/:id', (merchantController).update);
    this.router.delete('/:id', (merchantController).delete);
    this.router.get('/:id/payment-methods', (merchantController).getPaymentMethods);
    this.router.get('/:id/transactions', (merchantController).getTransactions);
    this.router.get('/:id/statistics', (merchantController).getStatistics);
    
    logger.info('MerchantsModule initialized');
  }
}

// Export the module
export default new MerchantsModule();
