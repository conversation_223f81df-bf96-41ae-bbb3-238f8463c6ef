#!/usr/bin/env node

/**
 * Phase 6: Systematic Error Resolution Script
 * Systematically fixes remaining TypeScript errors by category
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎯 PHASE 6: SYSTEMATIC ERROR RESOLUTION');
console.log('=======================================');

// Systematic error resolution patterns
const errorResolutionPatterns = {
    // Fix malformed Prisma calls
    'this.prisma.(user)': 'this.prisma.user',
    'this.prisma.(merchant)': 'this.prisma.merchant',
    'this.prisma.(payment)': 'this.prisma.payment',
    'this.prisma.(transaction)': 'this.prisma.transaction',
    'this.prisma.(admin)': 'this.prisma.admin',
    'this.prisma.(paymentMethod)': 'this.prisma.paymentMethod',
    'this.prisma.(role)': 'this.prisma.role',
    'this.prisma.(permission)': 'this.prisma.permission',
    'this.prisma.(enhancedRiskAssessment)': 'this.prisma.enhancedRiskAssessment',
    'this.prisma.(enhancedRiskConfig)': 'this.prisma.enhancedRiskConfig',
    'this.prisma.(alertAggregationRule)': 'this.prisma.alertAggregationRule',
    'this.prisma.(alertCorrelationRule)': 'this.prisma.alertCorrelationRule',
    'this.prisma.(auditLog)': 'this.prisma.auditLog',
    'this.prisma.(notification)': 'this.prisma.notification',
    'this.prisma.(webhook)': 'this.prisma.webhook',
    'this.prisma.(apiKey)': 'this.prisma.apiKey',
    'this.prisma.(session)': 'this.prisma.session',
    'this.prisma.(setting)': 'this.prisma.setting',
    
    // Fix malformed property access
    '(user).id': 'user.id',
    '(user).email': 'user.email',
    '(user).firstName': 'user.firstName',
    '(user).lastName': 'user.lastName',
    '(user).role': 'user.role',
    '(user).isActive': 'user.isActive',
    '(user).createdAt': 'user.createdAt',
    '(user).updatedAt': 'user.updatedAt',
    
    '(merchant).id': 'merchant.id',
    '(merchant).userId': 'merchant.userId',
    '(merchant).businessName': 'merchant.businessName',
    '(merchant).businessType': 'merchant.businessType',
    '(merchant).email': 'merchant.email',
    '(merchant).status': 'merchant.status',
    '(merchant).isActive': 'merchant.isActive',
    '(merchant).createdAt': 'merchant.createdAt',
    '(merchant).updatedAt': 'merchant.updatedAt',
    
    '(payment).id': 'payment.id',
    '(payment).merchantId': 'payment.merchantId',
    '(payment).amount': 'payment.amount',
    '(payment).currency': 'payment.currency',
    '(payment).status': 'payment.status',
    '(payment).paymentMethodId': 'payment.paymentMethodId',
    '(payment).description': 'payment.description',
    '(payment).metadata': 'payment.metadata',
    '(payment).createdAt': 'payment.createdAt',
    '(payment).updatedAt': 'payment.updatedAt',
    
    '(transaction).id': 'transaction.id',
    '(transaction).paymentId': 'transaction.paymentId',
    '(transaction).merchantId': 'transaction.merchantId',
    '(transaction).amount': 'transaction.amount',
    '(transaction).currency': 'transaction.currency',
    '(transaction).status': 'transaction.status',
    '(transaction).type': 'transaction.type',
    '(transaction).reference': 'transaction.reference',
    '(transaction).createdAt': 'transaction.createdAt',
    '(transaction).updatedAt': 'transaction.updatedAt',
    
    '(admin).id': 'admin.id',
    '(admin).userId': 'admin.userId',
    '(admin).department': 'admin.department',
    '(admin).permissions': 'admin.permissions',
    '(admin).isActive': 'admin.isActive',
    '(admin).user': 'admin.user',
    '(admin).createdAt': 'admin.createdAt',
    '(admin).updatedAt': 'admin.updatedAt',
    
    '(assessment).id': 'assessment.id',
    '(assessment).transactionId': 'assessment.transactionId',
    '(assessment).score': 'assessment.score',
    '(assessment).level': 'assessment.level',
    '(assessment).isFlagged': 'assessment.isFlagged',
    '(assessment).isBlocked': 'assessment.isBlocked',
    '(assessment).behavioralPattern': 'assessment.behavioralPattern',
    '(assessment).riskFactors': 'assessment.riskFactors',
    '(assessment).createdAt': 'assessment.createdAt',
    
    '(config).id': 'config.id',
    '(config).merchantId': 'config.merchantId',
    '(config).settings': 'config.settings',
    '(config).thresholds': 'config.thresholds',
    '(config).isActive': 'config.isActive',
    '(config).createdAt': 'config.createdAt',
    '(config).updatedAt': 'config.updatedAt',
    
    '(rule).id': 'rule.id',
    '(rule).name': 'rule.name',
    '(rule).description': 'rule.description',
    '(rule).conditions': 'rule.conditions',
    '(rule).actions': 'rule.actions',
    '(rule).isActive': 'rule.isActive',
    '(rule).priority': 'rule.priority',
    '(rule).createdAt': 'rule.createdAt',
    '(rule).updatedAt': 'rule.updatedAt',
    
    '(result).id': 'result.id',
    '(result).data': 'result.data',
    '(result).success': 'result.success',
    '(result).error': 'result.error',
    '(result).message': 'result.message',
    '(result).count': 'result.count',
    '(result).total': 'result.total',
    '(result).users': 'result.users',
    '(result).merchants': 'result.merchants',
    '(result).payments': 'result.payments',
    '(result).transactions': 'result.transactions',
    '(result).admins': 'result.admins',
    
    '(data).id': 'data.id',
    '(data).name': 'data.name',
    '(data).email': 'data.email',
    '(data).password': 'data.password',
    '(data).firstName': 'data.firstName',
    '(data).lastName': 'data.lastName',
    '(data).role': 'data.role',
    '(data).roleId': 'data.roleId',
    '(data).status': 'data.status',
    '(data).isActive': 'data.isActive',
    '(data).department': 'data.department',
    '(data).permissions': 'data.permissions',
    '(data).businessName': 'data.businessName',
    '(data).businessType': 'data.businessType',
    '(data).amount': 'data.amount',
    '(data).currency': 'data.currency',
    '(data).description': 'data.description',
    '(data).metadata': 'data.metadata',
    
    '(error).message': 'error.message',
    '(error).stack': 'error.stack',
    '(error).name': 'error.name',
    '(error).code': 'error.code',
    
    '(req).user': 'req.user',
    '(req).body': 'req.body',
    '(req).params': 'req.params',
    '(req).query': 'req.query',
    '(req).headers': 'req.headers',
    '(req).method': 'req.method',
    '(req).url': 'req.url',
    '(req).ip': 'req.ip',
    
    '(res).status': 'res.status',
    '(res).json': 'res.json',
    '(res).send': 'res.send',
    '(res).success': 'res.success',
    '(res).error': 'res.error',
    '(res).sendError': 'res.sendError',
    '(res).sendSuccess': 'res.sendSuccess',
    
    '(filters).status': 'filters?.status',
    '(filters).roleId': 'filters?.roleId',
    '(filters).search': 'filters?.search',
    '(filters).dateFrom': 'filters?.dateFrom',
    '(filters).dateTo': 'filters?.dateTo',
    '(filters).email': 'filters?.email',
    '(filters).name': 'filters?.name',
    '(filters).department': 'filters?.department',
    '(filters).merchantId': 'filters?.merchantId',
    '(filters).paymentMethodId': 'filters?.paymentMethodId',
    '(filters).amount': 'filters?.amount',
    '(filters).currency': 'filters?.currency',
    
    '(pagination).page': 'pagination?.page',
    '(pagination).limit': 'pagination?.limit',
    '(pagination).offset': 'pagination?.offset',
    '(pagination).sortBy': 'pagination?.sortBy',
    '(pagination).sortOrder': 'pagination?.sortOrder',
    
    '(validation).isValid': 'validation.isValid',
    '(validation).errors': 'validation.errors',
    '(validation).data': 'validation.data',
    
    '(permission).allowed': 'permission.allowed',
    '(permission).denied': 'permission.denied',
    '(permission).reason': 'permission.reason',
    '(permission).resource': 'permission.resource',
    '(permission).action': 'permission.action',
    
    // Fix array operations
    '(users).length': 'users.length',
    '(users).map': 'users.map',
    '(users).filter': 'users.filter',
    '(users).find': 'users.find',
    '(users).forEach': 'users.forEach',
    '(users).reduce': 'users.reduce',
    '(users).some': 'users.some',
    '(users).every': 'users.every',
    '(users).sort': 'users.sort',
    '(users).slice': 'users.slice',
    '(users).includes': 'users.includes',
    '(users).indexOf': 'users.indexOf',
    
    '(merchants).length': 'merchants.length',
    '(merchants).map': 'merchants.map',
    '(merchants).filter': 'merchants.filter',
    '(merchants).find': 'merchants.find',
    '(merchants).forEach': 'merchants.forEach',
    '(merchants).reduce': 'merchants.reduce',
    '(merchants).some': 'merchants.some',
    '(merchants).every': 'merchants.every',
    '(merchants).sort': 'merchants.sort',
    '(merchants).slice': 'merchants.slice',
    
    '(payments).length': 'payments.length',
    '(payments).map': 'payments.map',
    '(payments).filter': 'payments.filter',
    '(payments).find': 'payments.find',
    '(payments).forEach': 'payments.forEach',
    '(payments).reduce': 'payments.reduce',
    '(payments).some': 'payments.some',
    '(payments).every': 'payments.every',
    '(payments).sort': 'payments.sort',
    '(payments).slice': 'payments.slice',
    
    '(transactions).length': 'transactions.length',
    '(transactions).map': 'transactions.map',
    '(transactions).filter': 'transactions.filter',
    '(transactions).find': 'transactions.find',
    '(transactions).forEach': 'transactions.forEach',
    '(transactions).reduce': 'transactions.reduce',
    '(transactions).some': 'transactions.some',
    '(transactions).every': 'transactions.every',
    '(transactions).sort': 'transactions.sort',
    '(transactions).slice': 'transactions.slice',
    
    '(admins).length': 'admins.length',
    '(admins).map': 'admins.map',
    '(admins).filter': 'admins.filter',
    '(admins).find': 'admins.find',
    '(admins).forEach': 'admins.forEach',
    '(admins).reduce': 'admins.reduce',
    '(admins).some': 'admins.some',
    '(admins).every': 'admins.every',
    '(admins).sort': 'admins.sort',
    '(admins).slice': 'admins.slice',
    
    '(results).length': 'results.length',
    '(results).map': 'results.map',
    '(results).filter': 'results.filter',
    '(results).find': 'results.find',
    '(results).forEach': 'results.forEach',
    '(results).reduce': 'results.reduce',
    '(results).some': 'results.some',
    '(results).every': 'results.every',
    '(results).sort': 'results.sort',
    '(results).slice': 'results.slice',
    
    '(items).length': 'items.length',
    '(items).map': 'items.map',
    '(items).filter': 'items.filter',
    '(items).find': 'items.find',
    '(items).forEach': 'items.forEach',
    '(items).reduce': 'items.reduce',
    '(items).some': 'items.some',
    '(items).every': 'items.every',
    '(items).sort': 'items.sort',
    '(items).slice': 'items.slice',
    
    '(list).length': 'list.length',
    '(list).map': 'list.map',
    '(list).filter': 'list.filter',
    '(list).find': 'list.find',
    '(list).forEach': 'list.forEach',
    '(list).reduce': 'list.reduce',
    '(list).some': 'list.some',
    '(list).every': 'list.every',
    '(list).sort': 'list.sort',
    '(list).slice': 'list.slice',
    
    '(array).length': 'array.length',
    '(array).map': 'array.map',
    '(array).filter': 'array.filter',
    '(array).find': 'array.find',
    '(array).forEach': 'array.forEach',
    '(array).reduce': 'array.reduce',
    '(array).some': 'array.some',
    '(array).every': 'array.every',
    '(array).sort': 'array.sort',
    '(array).slice': 'array.slice',
    
    '(riskAssessments).length': 'riskAssessments.length',
    '(riskAssessments).map': 'riskAssessments.map',
    '(riskAssessments).filter': 'riskAssessments.filter',
    '(riskAssessments).find': 'riskAssessments.find',
    '(riskAssessments).forEach': 'riskAssessments.forEach',
    '(riskAssessments).reduce': 'riskAssessments.reduce',
    '(riskAssessments).some': 'riskAssessments.some',
    '(riskAssessments).every': 'riskAssessments.every',
    '(riskAssessments).sort': 'riskAssessments.sort',
    '(riskAssessments).slice': 'riskAssessments.slice',
    
    // Fix aggregate operations
    '(totalRevenue)._sum': 'totalRevenue._sum',
    '(monthlyRevenue)._sum': 'monthlyRevenue._sum',
    '(aggregateResult)._sum': 'aggregateResult._sum',
    '(aggregateResult)._count': 'aggregateResult._count',
    '(aggregateResult)._avg': 'aggregateResult._avg',
    '(aggregateResult)._max': 'aggregateResult._max',
    '(aggregateResult)._min': 'aggregateResult._min',
    
    // Fix where clause building
    '(where).status': 'where.status',
    '(where).email': 'where.email',
    '(where).name': 'where.name',
    '(where).role': 'where.role',
    '(where).isActive': 'where.isActive',
    '(where).createdAt': 'where.createdAt',
    '(where).updatedAt': 'where.updatedAt',
    '(where).OR': 'where.OR',
    '(where).AND': 'where.AND',
    '(where).user': 'where.user',
    '(where).merchant': 'where.merchant',
    '(where).payment': 'where.payment',
    '(where).transaction': 'where.transaction',
    
    // Fix query options building
    '(queryOptions).skip': 'queryOptions.skip',
    '(queryOptions).take': 'queryOptions.take',
    '(queryOptions).orderBy': 'queryOptions.orderBy',
    '(queryOptions).where': 'queryOptions.where',
    '(queryOptions).include': 'queryOptions.include',
    '(queryOptions).select': 'queryOptions.select',
    
    // Fix specific method calls
    '(AdminResponseMapper).sendError': 'AdminResponseMapper.sendError',
    '(AdminResponseMapper).sendSuccess': 'AdminResponseMapper.sendSuccess',
    '(AdminResponseMapper).sendAdminUsersList': 'AdminResponseMapper.sendAdminUsersList',
    '(AdminResponseMapper).sendAdminUser': 'AdminResponseMapper.sendAdminUser',
    '(AdminResponseMapper).sendAdminUserCreated': 'AdminResponseMapper.sendAdminUserCreated',
    '(AdminResponseMapper).sendAdminUserUpdated': 'AdminResponseMapper.sendAdminUserUpdated',
    '(AdminResponseMapper).sendAdminUserDeleted': 'AdminResponseMapper.sendAdminUserDeleted',
    '(AdminResponseMapper).sendSystemHealth': 'AdminResponseMapper.sendSystemHealth',
    '(AdminResponseMapper).sendDashboardData': 'AdminResponseMapper.sendDashboardData',
    
    // Fix specific service calls
    '(AdminBusinessService).getAdminUsers': 'AdminBusinessService.getAdminUsers',
    '(AdminBusinessService).getAdminUserById': 'AdminBusinessService.getAdminUserById',
    '(AdminBusinessService).createAdminUser': 'AdminBusinessService.createAdminUser',
    '(AdminBusinessService).updateAdminUser': 'AdminBusinessService.updateAdminUser',
    '(AdminBusinessService).deleteAdminUser': 'AdminBusinessService.deleteAdminUser',
    '(AdminBusinessService).getDashboardData': 'AdminBusinessService.getDashboardData',
    '(AdminBusinessService).getSystemHealth': 'AdminBusinessService.getSystemHealth',
    
    '(AdminValidationService).validateCreateAdminUser': 'AdminValidationService.validateCreateAdminUser',
    '(AdminValidationService).validateUpdateAdminUser': 'AdminValidationService.validateUpdateAdminUser',
    '(AdminValidationService).validateAdminUserFilters': 'AdminValidationService.validateAdminUserFilters',
    '(AdminValidationService).validatePagination': 'AdminValidationService.validatePagination',
    
    '(AdminAuthorizationService).checkAdminPermission': 'AdminAuthorizationService.checkAdminPermission',
    '(AdminAuthorizationService).checkAdminRole': 'AdminAuthorizationService.checkAdminRole',
    '(AdminAuthorizationService).validateAdminAccess': 'AdminAuthorizationService.validateAdminAccess',
    
    // Fix number literal patterns
    '(1).0.0': '1.0.0',
    '(0).0.1': '0.0.1',
    '(2).0.0': '2.0.0',
    '(3).0.0': '3.0.0',
    '(4).0.0': '4.0.0',
    '(5).0.0': '5.0.0',
    
    // Fix boolean patterns
    '(true)': 'true',
    '(false)': 'false',
    '(null)': 'null',
    '(undefined)': 'undefined',
    
    // Fix string patterns
    '("ADMIN")': '"ADMIN"',
    '("USER")': '"USER"',
    '("MERCHANT")': '"MERCHANT"',
    '("ACTIVE")': '"ACTIVE"',
    '("INACTIVE")': '"INACTIVE"',
    '("PENDING")': '"PENDING"',
    '("SUCCESS")': '"SUCCESS"',
    '("FAILED")': '"FAILED"',
    '("CANCELLED")': '"CANCELLED"',
    
    '(\'ADMIN\')': '\'ADMIN\'',
    '(\'USER\')': '\'USER\'',
    '(\'MERCHANT\')': '\'MERCHANT\'',
    '(\'ACTIVE\')': '\'ACTIVE\'',
    '(\'INACTIVE\')': '\'INACTIVE\'',
    '(\'PENDING\')': '\'PENDING\'',
    '(\'SUCCESS\')': '\'SUCCESS\'',
    '(\'FAILED\')': '\'FAILED\'',
    '(\'CANCELLED\')': '\'CANCELLED\'',
    
    // Fix environment patterns
    '(process.env).NODE_ENV': 'process.env.NODE_ENV',
    '(process.env).PORT': 'process.env.PORT',
    '(process.env).DATABASE_URL': 'process.env.DATABASE_URL',
    '(process.env).JWT_SECRET': 'process.env.JWT_SECRET',
    '(process.env).REDIS_URL': 'process.env.REDIS_URL',
    
    // Fix specific error codes
    '(ErrorCode).VALIDATION_ERROR': 'ErrorCode.VALIDATION_ERROR',
    '(ErrorCode).RESOURCE_NOT_FOUND': 'ErrorCode.RESOURCE_NOT_FOUND',
    '(ErrorCode).UNAUTHORIZED': 'ErrorCode.UNAUTHORIZED',
    '(ErrorCode).FORBIDDEN': 'ErrorCode.FORBIDDEN',
    '(ErrorCode).INTERNAL_SERVER_ERROR': 'ErrorCode.INTERNAL_SERVER_ERROR',
    '(ErrorCode).DUPLICATE_RESOURCE': 'ErrorCode.DUPLICATE_RESOURCE',
    
    '(ErrorType).VALIDATION': 'ErrorType.VALIDATION',
    '(ErrorType).NOT_FOUND': 'ErrorType.NOT_FOUND',
    '(ErrorType).UNAUTHORIZED': 'ErrorType.UNAUTHORIZED',
    '(ErrorType).FORBIDDEN': 'ErrorType.FORBIDDEN',
    '(ErrorType).INTERNAL': 'ErrorType.INTERNAL',
    '(ErrorType).CONFLICT': 'ErrorType.CONFLICT',
};

function findAllTypeScriptFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'coverage'].includes(item)) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

function getErrorCount() {
    try {
        const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { encoding: 'utf8' });
        const errorMatches = output.match(/error TS/g) || [];
        return errorMatches.length;
    } catch (error) {
        const errorMatches = error.stdout.match(/error TS/g) || [];
        return errorMatches.length;
    }
}

function processFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let modifiedContent = content;
        let fixCount = 0;
        
        // Apply all error resolution patterns
        for (const [oldPattern, newPattern] of Object.entries(errorResolutionPatterns)) {
            const originalContent = modifiedContent;
            modifiedContent = modifiedContent.split(oldPattern).join(newPattern);
            if (modifiedContent !== originalContent) {
                fixCount++;
            }
        }
        
        if (fixCount > 0) {
            fs.writeFileSync(filePath, modifiedContent, 'utf8');
            return { filePath, fixCount };
        }
        
        return null;
    } catch (error) {
        return { filePath, error: error.message };
    }
}

async function main() {
    console.log('🔍 Scanning for TypeScript files...');
    
    const files = findAllTypeScriptFiles('./src');
    console.log(`📁 Found ${files.length} TypeScript files`);
    
    console.log('📊 Getting initial error count...');
    const initialErrors = getErrorCount();
    console.log(`🚨 Initial TypeScript errors: ${initialErrors}`);
    
    console.log('🚀 Starting systematic error resolution...');
    const startTime = Date.now();
    
    const results = [];
    let totalFixedIssues = 0;
    
    for (const file of files) {
        const result = processFile(file);
        if (result) {
            results.push(result);
            if (result.fixCount) {
                totalFixedIssues += result.fixCount;
            }
        }
    }
    
    console.log('📊 Getting final error count...');
    const finalErrors = getErrorCount();
    const totalErrorsFixed = initialErrors - finalErrors;
    
    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;
    
    console.log('\n🎯 SYSTEMATIC ERROR RESOLUTION COMPLETE!');
    console.log('=========================================');
    console.log(`⏱️  Processing time: ${processingTime.toFixed(2)} seconds`);
    console.log(`📁 Files processed: ${results.filter(r => !r.error).length}`);
    console.log(`❌ Files with errors: ${results.filter(r => r.error).length}`);
    console.log(`🔧 Total fixes applied: ${totalFixedIssues}`);
    console.log(`🚨 TypeScript errors before: ${initialErrors}`);
    console.log(`✅ TypeScript errors after: ${finalErrors}`);
    console.log(`🎯 Total errors fixed: ${totalErrorsFixed}`);
    
    if (totalErrorsFixed > 0) {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}%`);
        console.log('\n🎉 SUCCESS! Systematic error resolution completed successfully!');
        console.log('🏆 Your application now has fewer TypeScript errors!');
    } else if (totalErrorsFixed === 0) {
        console.log('📈 Success rate: 0.0% (no net change)');
        console.log('\n✨ No errors were fixed, but patterns were applied!');
    } else {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}% (negative - new errors introduced)`);
        console.log('\n⚠️  Some fixes may have introduced new errors. Consider reverting.');
    }
    
    const errorFiles = results.filter(r => r.error);
    if (errorFiles.length > 0) {
        console.log('\n❌ Files with processing errors:');
        errorFiles.forEach(({ filePath, error }) => {
            console.log(`   ${path.relative(process.cwd(), filePath)}: ${error}`);
        });
    }
    
    const successFiles = results.filter(r => r.fixCount && r.fixCount > 0);
    if (successFiles.length > 0) {
        console.log(`\n✅ Successfully fixed issues in ${successFiles.length} files`);
        console.log('Top files with most fixes:');
        successFiles
            .sort((a, b) => b.fixCount - a.fixCount)
            .slice(0, 10)
            .forEach(({ filePath, fixCount }) => {
                console.log(`   ${path.relative(process.cwd(), filePath)}: ${fixCount} fixes`);
            });
    }
}

main().catch(console.error);
