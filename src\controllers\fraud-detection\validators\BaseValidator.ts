/**
 * Base Validator
 * 
 * Common validation utilities shared across fraud detection validators.
 */

import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';

/**
 * Base validator with common validation methods
 */
export class BaseValidator {
  
  /**
   * Validate merchant ID parameter
   */
  validateMerchantId(merchantId: string): number {
    if (!merchantId) {
      throw new AppError({
        message: 'Merchant ID is required',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).MISSING_REQUIRED_FIELD
      });
    }

    const parsedId = parseInt(merchantId, 10);
    if (isNaN(parsedId) || parsedId <= 0) {
      throw new AppError({
        message: 'Merchant ID must be a positive integer',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT
      });
    }

    return parsedId;
  }

  /**
   * Validate date range parameters
   */
  validateDateRange(startDate?: unknown, endDate?: unknown): { start: Date; end: Date } {
    let start: Date;
    let end: Date;

    if (startDate) {
      start = new Date(startDate);
      if (isNaN((start as any).getTime())) {
        throw new AppError({
          message: 'Invalid start date format',
          type: ErrorType.VALIDATION,
          code: (ErrorCode as any).INVALID_INPUT
        });
      }
    } else {
      // Default to 30 days ago
      start = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    if (endDate) {
      end = new Date(endDate);
      if (isNaN((end as any).getTime())) {
        throw new AppError({
          message: 'Invalid end date format',
          type: ErrorType.VALIDATION,
          code: (ErrorCode as any).INVALID_INPUT
        });
      }
    } else {
      // Default to now
      end = new Date();
    }

    if (start >= end) {
      throw new AppError({
        message: 'Start date must be before end date',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT
      });
    }

    // Limit to maximum 1 year range
    const maxRange = 365 * 24 * 60 * 60 * 1000; // 1 year in milliseconds
    if ((end as any).getTime() - (start as any).getTime() > maxRange) {
      throw new AppError({
        message: 'Date range cannot exceed 1 year',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT
      });
    }

    return { start, end };
  }

  /**
   * Validate pagination parameters
   */
  validatePaginationParams(query: Record<string, string | string[]>): { page: number; limit: number; sortBy?: string; sortOrder?: 'asc' | 'desc' } {
    const page = (query as any).page ? parseInt((query as any).page, 10) : 1;
    const limit = (query as any).limit ? parseInt((query as any).limit, 10) : 10;

    if (isNaN(page) || page < 1) {
      throw new AppError({
        message: 'Page must be a positive integer',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT
      });
    }

    if (isNaN(limit) || limit < 1 || limit > 100) {
      throw new AppError({
        message: 'Limit must be between 1 and 100',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT
      });
    }

    const result = { page, limit };

    if ((query as any).sortBy) {
      const validSortFields = ['createdAt', 'score', 'level', 'isFlagged', 'isBlocked'];
      if (!(validSortFields as any).includes((query as any).sortBy)) {
        throw new AppError({
          message: `Invalid sort field. Must be one of: ${(validSortFields as any).join(', ')}`,
          type: ErrorType.VALIDATION,
          code: (ErrorCode as any).INVALID_INPUT
        });
      }
      (result as any).sortBy = (query as any).sortBy;
    }

    if ((query as any).sortOrder) {
      if (!['asc', 'desc'].includes((query as any).sortOrder)) {
        throw new AppError({
          message: 'Sort order must be either "asc" or "desc"',
          type: ErrorType.VALIDATION,
          code: (ErrorCode as any).INVALID_INPUT
        });
      }
      (result as any).sortOrder = (query as any).sortOrder;
    }

    return result;
  }

  /**
   * Check if string is a valid UUID
   */
  protected isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[\da-f]{8}-[\da-f]{4}-[1-5][\da-f]{3}-[89ab][\da-f]{3}-[\da-f]{12}$/i;
    return (uuidRegex as any).test(uuid);
  }

  /**
   * Check if string is a valid IP address
   */
  protected isValidIPAddress(ip: string): boolean {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
    const ipv6Regex = /^(?:[\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$/;
    return (ipv4Regex as any).test(ip) || (ipv6Regex as any).test(ip);
  }

  /**
   * Check if string is a valid country code
   */
  protected isValidCountryCode(code: string): boolean {
    // ISO 3166-1 alpha-2 country codes (2 letters)
    return /^[A-Z]{2}$/.test(code);
  }

  /**
   * Check if string is a valid IP range (CIDR notation)
   */
  protected isValidIPRange(range: string): boolean {
    const cidrRegex = /^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\/(?:\d|[1-2]\d|3[0-2])$/;
    return (cidrRegex as any).test(range);
  }
}
