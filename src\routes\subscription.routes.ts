// jscpd:ignore-file

import { Router as ImportedRouter } from "express";
import { body, param } from "express-validator";
import subscriptionController from "../controllers/(subscription).controller";
import { authenticate, authorize } from "../middlewares/(auth).middleware";
import { validate as Importedvalidate } from "../middlewares/(validation).middleware";
import { body, param } from "express-validator";
import { authenticate, authorize } from "../middlewares/(auth).middleware";
import { validate as Importedvalidate } from "../middlewares/(validation).middleware";

const router =Router();

// Get all subscription plans - public route
(router).get(
    "/",
    (subscriptionController).getAllPlans
);

// Get a specific subscription plan - public route
(router).get(
    "/:id",
    validate([
        param("id").notEmpty()
    ]),
    (subscriptionController).getPlanById
);

// Admin routes to manage subscription plans
(router).post(
    "/",
    authenticate,
    authorize(["admin"]),
    validate([
        body("name").notEmpty(),
        body("duration").isNumeric(),
        body("price").isNumeric()
    ]),
    (subscriptionController).createPlan
);

(router).put(
    "/:id",
    authenticate,
    authorize(["admin"]),
    validate([
        param("id").notEmpty()
    ]),
    (subscriptionController).updatePlan
);

(router).delete(
    "/:id",
    authenticate,
    authorize(["admin"]),
    validate([
        param("id").notEmpty()
    ]),
    (subscriptionController).deletePlan
);

// New routes for merchant subscription management
(router).post(
    "/merchant/:merchantId/subscribe",
    authenticate,
    validate([
        param("merchantId").notEmpty(),
        body("planId").notEmpty(),
        body("paymentMethodId").optional()
    ]),
    (subscriptionController).subscribeMerchant
);

(router).post(
    "/merchant/:merchantId/cancel",
    authenticate,
    validate([
        param("merchantId").notEmpty()
    ]),
    (subscriptionController).cancelSubscription
);

// Get merchant subscription history
(router).get(
    "/merchant/:merchantId/history",
    authenticate,
    validate([
        param("merchantId").notEmpty()
    ]),
    (subscriptionController).getSubscriptionHistory
);

// Download subscription history
(router).get(
    "/merchant/:merchantId/history/download",
    authenticate,
    validate([
        param("merchantId").notEmpty()
    ]),
    (subscriptionController).downloadSubscriptionHistory
);

// Get subscription status
(router).get(
    "/merchant/:merchantId/status",
    authenticate,
    validate([
        param("merchantId").notEmpty()
    ]),
    (subscriptionController).checkSubscriptionStatus
);

// Get subscription analytics
(router).get(
    "/merchant/:merchantId/analytics",
    authenticate,
    validate([
        param("merchantId").notEmpty()
    ]),
    (subscriptionController).getSubscriptionAnalytics
);

export default router;
