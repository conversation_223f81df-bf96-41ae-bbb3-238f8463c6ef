// jscpd:ignore-file

import { Router as ImportedRouter } from "express";
import { body, param } from "express-validator";
import subscriptionController from "../controllers/(subscription as any).controller";
import { authenticate, authorize } from "../middlewares/(auth as any).middleware";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { body, param } from "express-validator";
import { authenticate, authorize } from "../middlewares/(auth as any).middleware";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";

const router: any =Router();

// Get all subscription plans - public route
(router as any).get(
    "/",
    (subscriptionController as any).getAllPlans
);

// Get a specific subscription plan - public route
(router as any).get(
    "/:id",
    validate([
        param("id").notEmpty()
    ]),
    (subscriptionController as any).getPlanById
);

// Admin routes to manage subscription plans
(router as any).post(
    "/",
    authenticate,
    authorize(["admin"]),
    validate([
        body("name").notEmpty(),
        body("duration").isNumeric(),
        body("price").isNumeric()
    ]),
    (subscriptionController as any).createPlan
);

(router as any).put(
    "/:id",
    authenticate,
    authorize(["admin"]),
    validate([
        param("id").notEmpty()
    ]),
    (subscriptionController as any).updatePlan
);

(router as any).delete(
    "/:id",
    authenticate,
    authorize(["admin"]),
    validate([
        param("id").notEmpty()
    ]),
    (subscriptionController as any).deletePlan
);

// New routes for merchant subscription management
(router as any).post(
    "/merchant/:merchantId/subscribe",
    authenticate,
    validate([
        param("merchantId").notEmpty(),
        body("planId").notEmpty(),
        body("paymentMethodId").optional()
    ]),
    (subscriptionController as any).subscribeMerchant
);

(router as any).post(
    "/merchant/:merchantId/cancel",
    authenticate,
    validate([
        param("merchantId").notEmpty()
    ]),
    (subscriptionController as any).cancelSubscription
);

// Get merchant subscription history
(router as any).get(
    "/merchant/:merchantId/history",
    authenticate,
    validate([
        param("merchantId").notEmpty()
    ]),
    (subscriptionController as any).getSubscriptionHistory
);

// Download subscription history
(router as any).get(
    "/merchant/:merchantId/history/download",
    authenticate,
    validate([
        param("merchantId").notEmpty()
    ]),
    (subscriptionController as any).downloadSubscriptionHistory
);

// Get subscription status
(router as any).get(
    "/merchant/:merchantId/status",
    authenticate,
    validate([
        param("merchantId").notEmpty()
    ]),
    (subscriptionController as any).checkSubscriptionStatus
);

// Get subscription analytics
(router as any).get(
    "/merchant/:merchantId/analytics",
    authenticate,
    validate([
        param("merchantId").notEmpty()
    ]),
    (subscriptionController as any).getSubscriptionAnalytics
);

export default router;
