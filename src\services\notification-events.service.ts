// jscpd:ignore-file
import { EventEmitter } from 'events';
import { logger } from '../utils/logging/logger';
import { User } from '../types';
import { logger } from '../utils/logging/logger';
import { User } from '../types';

/**
 * Notification priority levels
 */
export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Notification channels
 */
export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  DASHBOARD = 'dashboard',
  TELEGRAM = 'telegram',
}

/**
 * Notification event data
 */
export interface NotificationEvent {
  id: string;
  userId?: string;
  merchantId?: string;
  title: string;
  message: string;
  type: 'transaction' | 'merchant' | 'subscription' | 'system';
  priority: NotificationPriority;
  channels: NotificationChannel[];
  metadata?: Record<string, any>;
  createdAt: Date;
}

/**
 * Event emitter for notification events
 */
export const notificationEvents = new EventEmitter();

/**
 * Notification events service
 */
export class NotificationEventsService {
  /**
   * Emit notification created event
   * @param notification Notification data
   */
  public static emitNotificationCreated(notification: NotificationEvent): void {
    try {
      notificationEvents.emit('notification.created', notification);

      logger.info(`Emitted notification.created event for notification ${notification.id}`, {
        type: notification.type,
        userId: notification.userId,
        merchantId: notification.merchantId,
      });
    } catch (error) {
      logger.error('Error emitting notification.created event:', error);
    }
  }

  /**
   * Emit notification delivered event
   * @param notificationId Notification ID
   * @param channel Delivery channel
   * @param success Whether delivery was successful
   * @param error Error message if delivery failed
   */
  public static emitNotificationDelivered(
    notificationId: string,
    channel: NotificationChannel,
    success: boolean,
    error?: string
  ): void {
    try {
      const event = {
        notificationId,
        channel,
        success,
        error,
        timestamp: new Date(),
      };

      notificationEvents.emit('notification.delivered', event);

      logger.info(`Emitted notification.delivered event for notification ${notificationId}`, {
        channel,
        success,
      });
    } catch (error) {
      logger.error('Error emitting notification.delivered event:', error);
    }
  }

  /**
   * Emit notification read event
   * @param notificationId Notification ID
   * @param userId User ID
   */
  public static emitNotificationRead(notificationId: string, userId: string): void {
    try {
      const event = {
        notificationId,
        userId,
        timestamp: new Date(),
      };

      notificationEvents.emit('notification.read', event);

      logger.info(`Emitted notification.read event for notification ${notificationId}`, {
        userId,
      });
    } catch (error) {
      logger.error('Error emitting notification.read event:', error);
    }
  }
}
