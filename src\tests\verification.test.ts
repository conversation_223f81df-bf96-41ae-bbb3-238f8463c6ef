import { PrismaClient } from '@prisma/client';
// jscpd:ignore-file
/**
 * verification Tests
 *
 * This file contains tests for the verification module using the test utility.
 */

import { verificationController } from '../controllers/verification.controller';
import { verificationService } from '../services/verification.service';
import { verificationRepository } from '../repositories/verification.repository';
import { testControllerSuite, createMockRequest, createMockResponse, testService, testRepository, createMockPrismaClient } from './tests/utils/test-utilities';
import { Repository } from '../types/database';
import { verificationService } from '../services/verification.service';
import { verificationRepository } from '../repositories/verification.repository';
import { testControllerSuite, createMockRequest, createMockResponse, testService, testRepository, createMockPrismaClient } from './tests/utils/test-utilities';
import { Repository } from '../types/database';


// Mock the verificationService
jest.mock('../services/verification.service');

describe('verification Module Tests', () => {
  // Controller tests
  testControllerSuite('verificationController', verificationController, {
    getAll: { description: 'should get all verifications',
      expectedStatus: 200,
      expectedResponse: { success: true },
    },
    getById: { description: 'should get verification by ID',
      req: createMockRequest({ params: { id: '1' } }),
      expectedStatus: 200,
      expectedResponse: { success: true },
    },
    create: { description: 'should create verification',
      req: createMockRequest({ body: { name: 'Test' } }),
      expectedStatus: 201,
      expectedResponse: { success: true },
    },
    update: { description: 'should update verification',
      req: createMockRequest({ params: { id: '1' }, body: { name: 'Updated' } }),
      expectedStatus: 200,
      expectedResponse: { success: true },
    },
    delete: { description: 'should delete verification',
      req: createMockRequest({ params: { id: '1' } }),
      expectedStatus: 200,
      expectedResponse: { success: true, message: 'verification deleted successfully' },
    },
  });

  // Service tests
  describe('verificationService', () => {
    let service;
    let mockRepository;

    beforeEach(() => {
      mockRepository = {
        findAll: jest.fn(),
        findById: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      };

      service = new verificationService();
      service.verificationRepository = mockRepository;
    });

    it('should find all verifications', async () => {
      mockRepository.findAll.mockResolvedValue([{ id: '1', name: 'Test' }]);

      const result: unknown = await testService(service, 'findAll');

      expect(result).toEqual([{ id: '1', name: 'Test' }]);
      expect(mockRepository.findAll).toHaveBeenCalled();
    });
  });

  // Repository tests
  describe('verificationRepository', () => {
    let repository;
    let mockPrisma;

    beforeEach(() => {
      mockPrisma = createMockPrismaClient();
      repository = new verificationRepository();
      repository.prisma = mockPrisma;
    });

    it('should find all verifications', async () => {
      mockPrisma.verification.findMany.mockResolvedValue([{ id: '1', name: 'Test' }]);

      const result: unknown = await testRepository(repository, 'findAll');

      expect(result).toEqual([{ id: '1', name: 'Test' }]);
      expect(mockPrisma.verification.findMany).toHaveBeenCalled();
    });
  });
});
