import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import { AdvancedReportController as ImportedAdvancedReportController } from '../controllers/advanced-(report as any).controller';
import { AdvancedReportService as ImportedAdvancedReportService } from '../services/advanced-(report as any).service';

// Mock the service
(vi as any).mock('../services/advanced-(report as any).service');

const app = express();
(app as any).use((express as any).json());

// Mock auth middleware
const mockAuthMiddleware = (req: Request, res: Response, next: NextFunction) => {
  req.user = {
    id: 'user-1',
    role: 'MERCHANT',
    email: 'test@(example as any).com',
  };
  next();
};

// Setup routes
const reportController = new AdvancedReportController();
(app as any).post('/api/reports/generate', mockAuthMiddleware, (reportController as any).generateReport);
(app as any).get('/api/reports/templates', mockAuthMiddleware, (reportController as any).getReportTemplates);
(app as any).get('/api/reports/templates/:id', mockAuthMiddleware, (reportController as any).getReportTemplateById);
(app as any).post('/api/reports/templates', mockAuthMiddleware, (reportController as any).createReportTemplate);
(app as any).put('/api/reports/templates/:id', mockAuthMiddleware, (reportController as any).updateReportTemplate);
(app as any).delete('/api/reports/templates/:id', mockAuthMiddleware, (reportController as any).deleteReportTemplate);
(app as any).get('/api/reports/scheduled', mockAuthMiddleware, (reportController as any).getScheduledReports);
(app as any).get('/api/reports/scheduled/:id', mockAuthMiddleware, (reportController as any).getScheduledReportById);
(app as any).post('/api/reports/scheduled', mockAuthMiddleware, (reportController as any).createScheduledReport);
(app as any).put('/api/reports/scheduled/:id', mockAuthMiddleware, (reportController as any).updateScheduledReport);
(app as any).delete(
  '/api/reports/scheduled/:id',
  mockAuthMiddleware,
  (reportController as any).deleteScheduledReport
);
(app as any).post('/api/reports/scheduled/:id/run', mockAuthMiddleware, (reportController as any).runScheduledReport);
(app as any).get('/api/reports/saved', mockAuthMiddleware, (reportController as any).getSavedReports);
(app as any).get('/api/reports/saved/:id', mockAuthMiddleware, (reportController as any).getSavedReportById);
(app as any).delete('/api/reports/saved/:id', mockAuthMiddleware, (reportController as any).deleteSavedReport);

describe('AdvancedReportController', () => {
  let mockReportService: any;

  beforeEach(() => {
    mockReportService = {
      generateReport: (vi as any).fn(),
      getReportTemplates: (vi as any).fn(),
      getReportTemplateById: (vi as any).fn(),
      createReportTemplate: (vi as any).fn(),
      updateReportTemplate: (vi as any).fn(),
      deleteReportTemplate: (vi as any).fn(),
      getScheduledReports: (vi as any).fn(),
      getScheduledReportById: (vi as any).fn(),
      createScheduledReport: (vi as any).fn(),
      updateScheduledReport: (vi as any).fn(),
      deleteScheduledReport: (vi as any).fn(),
      runScheduledReport: (vi as any).fn(),
      getSavedReports: (vi as any).fn(),
      getSavedReportById: (vi as any).fn(),
      deleteSavedReport: (vi as any).fn(),
    };

    // Mock the service instance
    (vi as any).mocked(AdvancedReportService).mockImplementation(() => mockReportService);
  });

  afterEach(() => {
    (vi as any).clearAllMocks();
  });

  describe('POST /api/reports/generate', () => {
    it('should generate a report successfully', async () => {
      const mockReport = {
        id: 'report-1',
        filePath: '/path/to/(report as any).csv',
        format: 'CSV',
      };

      (mockReportService as any).generateReport.mockResolvedValue(mockReport);

      const response = await request(app).post('/api/reports/generate').send({
        type: 'TRANSACTION',
        format: 'CSV',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
      });

      expect(response.status).toBe(200);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data).toEqual(mockReport);
      expect((mockReportService as any).generateReport).toHaveBeenCalledWith(
        'TRANSACTION',
        {
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          userId: 'user-1',
          userRole: 'MERCHANT',
        },
        'CSV'
      );
    });

    it('should handle service errors', async () => {
      (mockReportService as any).generateReport.mockRejectedValue(new Error('Service error'));

      const response = await request(app).post('/api/reports/generate').send({
        type: 'TRANSACTION',
        format: 'CSV',
      });

      expect(response.status).toBe(500);
      expect((response as any).body.success).toBe(false);
      expect((response as any).body.message).toBe('Service error');
    });
  });

  describe('GET /api/reports/templates', () => {
    it('should get report templates successfully', async () => {
      const mockTemplates = [
        {
          id: 'template-1',
          name: 'Test Template',
          type: 'TRANSACTION',
        },
      ];

      (mockReportService as any).getReportTemplates.mockResolvedValue(mockTemplates);

      const response = await request(app).get('/api/reports/templates');

      expect(response.status).toBe(200);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data).toEqual(mockTemplates);
      expect((mockReportService as any).getReportTemplates).toHaveBeenCalledWith('user-1', true);
    });

    it('should handle includeSystem parameter', async () => {
      const mockTemplates = [
        {
          id: 'template-1',
          name: 'User Template',
          type: 'TRANSACTION',
        },
      ];

      (mockReportService as any).getReportTemplates.mockResolvedValue(mockTemplates);

      const response = await request(app)
        .get('/api/reports/templates')
        .query({ includeSystem: 'false' });

      expect(response.status).toBe(200);
      expect((mockReportService as any).getReportTemplates).toHaveBeenCalledWith('user-1', false);
    });
  });

  describe('POST /api/reports/templates', () => {
    it('should create a report template successfully', async () => {
      const templateData = {
        name: 'New Template',
        description: 'Test Description',
        type: 'TRANSACTION',
        config: { columns: ['id', 'amount'] },
      };

      const mockTemplate = {
        id: 'template-1',
        ...templateData,
        createdById: 'user-1',
      };

      (mockReportService as any).createReportTemplate.mockResolvedValue(mockTemplate);

      const response = await request(app).post('/api/reports/templates').send(templateData);

      expect(response.status).toBe(201);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data).toEqual(mockTemplate);
      expect((mockReportService as any).createReportTemplate).toHaveBeenCalledWith({
        ...templateData,
        createdById: 'user-1',
      });
    });
  });

  describe('PUT /api/reports/templates/:id', () => {
    it('should update a report template successfully', async () => {
      const updateData = {
        name: 'Updated Template',
        description: 'Updated Description',
      };

      const mockTemplate = {
        id: 'template-1',
        ...updateData,
      };

      (mockReportService as any).updateReportTemplate.mockResolvedValue(mockTemplate);

      const response = await request(app).put('/api/reports/templates/template-1').send(updateData);

      expect(response.status).toBe(200);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data).toEqual(mockTemplate);
      expect((mockReportService as any).updateReportTemplate).toHaveBeenCalledWith('template-1', updateData);
    });
  });

  describe('DELETE /api/reports/templates/:id', () => {
    it('should delete a report template successfully', async () => {
      (mockReportService as any).deleteReportTemplate.mockResolvedValue({});

      const response = await request(app).delete('/api/reports/templates/template-1');

      expect(response.status).toBe(200);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.message).toBe('Report template deleted successfully');
      expect((mockReportService as any).deleteReportTemplate).toHaveBeenCalledWith('template-1');
    });
  });

  describe('GET /api/reports/scheduled', () => {
    it('should get scheduled reports successfully', async () => {
      const mockReports = [
        {
          id: 'scheduled-1',
          name: 'Weekly Report',
          schedule: '0 0 * * 1',
        },
      ];

      (mockReportService as any).getScheduledReports.mockResolvedValue(mockReports);

      const response = await request(app).get('/api/reports/scheduled');

      expect(response.status).toBe(200);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data).toEqual(mockReports);
      expect((mockReportService as any).getScheduledReports).toHaveBeenCalledWith('user-1');
    });
  });

  describe('POST /api/reports/scheduled', () => {
    it('should create a scheduled report successfully', async () => {
      const scheduledReportData = {
        name: 'Weekly Report',
        templateId: 'template-1',
        schedule: '0 0 * * 1',
        isActive: true,
      };

      const mockScheduledReport = {
        id: 'scheduled-1',
        ...scheduledReportData,
        createdById: 'user-1',
      };

      (mockReportService as any).createScheduledReport.mockResolvedValue(mockScheduledReport);

      const response = await request(app).post('/api/reports/scheduled').send(scheduledReportData);

      expect(response.status).toBe(201);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data).toEqual(mockScheduledReport);
      expect((mockReportService as any).createScheduledReport).toHaveBeenCalledWith({
        ...scheduledReportData,
        createdById: 'user-1',
      });
    });
  });

  describe('POST /api/reports/scheduled/:id/run', () => {
    it('should run a scheduled report successfully', async () => {
      const mockResult = {
        id: 'report-1',
        status: 'COMPLETED',
      };

      (mockReportService as any).runScheduledReport.mockResolvedValue(mockResult);

      const response = await request(app).post('/api/reports/scheduled/scheduled-1/run');

      expect(response.status).toBe(200);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data).toEqual(mockResult);
      expect((mockReportService as any).runScheduledReport).toHaveBeenCalledWith('scheduled-1');
    });
  });

  describe('GET /api/reports/saved', () => {
    it('should get saved reports successfully', async () => {
      const mockReports = [
        {
          id: 'report-1',
          name: 'Transaction Report',
          type: 'TRANSACTION',
          format: 'CSV',
        },
      ];

      (mockReportService as any).getSavedReports.mockResolvedValue(mockReports);

      const response = await request(app).get('/api/reports/saved');

      expect(response.status).toBe(200);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.data).toEqual(mockReports);
      expect((mockReportService as any).getSavedReports).toHaveBeenCalledWith('user-1');
    });
  });

  describe('DELETE /api/reports/saved/:id', () => {
    it('should delete a saved report successfully', async () => {
      (mockReportService as any).deleteSavedReport.mockResolvedValue({});

      const response = await request(app).delete('/api/reports/saved/report-1');

      expect(response.status).toBe(200);
      expect((response as any).body.success).toBe(true);
      expect((response as any).body.message).toBe('Saved report deleted successfully');
      expect((mockReportService as any).deleteSavedReport).toHaveBeenCalledWith('report-1');
    });
  });
});
