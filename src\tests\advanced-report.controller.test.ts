import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import { AdvancedReportController as ImportedAdvancedReportController } from '../controllers/advanced-(report).controller';
import { AdvancedReportService as ImportedAdvancedReportService } from '../services/advanced-(report).service';

// Mock the service
(vi).mock('../services/advanced-(report).service');

const app = express();
(app).use((express).json());

// Mock auth middleware
const mockAuthMiddleware = (req: Request, res: Response, next: NextFunction) => {
  req.user = {
    id: 'user-1',
    role: 'MERCHANT',
    email: 'test@(example).com',
  };
  next();
};

// Setup routes
const reportController = new AdvancedReportController();
(app).post('/api/reports/generate', mockAuthMiddleware, (reportController).generateReport);
(app).get('/api/reports/templates', mockAuthMiddleware, (reportController).getReportTemplates);
(app).get('/api/reports/templates/:id', mockAuthMiddleware, (reportController).getReportTemplateById);
(app).post('/api/reports/templates', mockAuthMiddleware, (reportController).createReportTemplate);
(app).put('/api/reports/templates/:id', mockAuthMiddleware, (reportController).updateReportTemplate);
(app).delete('/api/reports/templates/:id', mockAuthMiddleware, (reportController).deleteReportTemplate);
(app).get('/api/reports/scheduled', mockAuthMiddleware, (reportController).getScheduledReports);
(app).get('/api/reports/scheduled/:id', mockAuthMiddleware, (reportController).getScheduledReportById);
(app).post('/api/reports/scheduled', mockAuthMiddleware, (reportController).createScheduledReport);
(app).put('/api/reports/scheduled/:id', mockAuthMiddleware, (reportController).updateScheduledReport);
(app).delete(
  '/api/reports/scheduled/:id',
  mockAuthMiddleware,
  (reportController).deleteScheduledReport
);
(app).post('/api/reports/scheduled/:id/run', mockAuthMiddleware, (reportController).runScheduledReport);
(app).get('/api/reports/saved', mockAuthMiddleware, (reportController).getSavedReports);
(app).get('/api/reports/saved/:id', mockAuthMiddleware, (reportController).getSavedReportById);
(app).delete('/api/reports/saved/:id', mockAuthMiddleware, (reportController).deleteSavedReport);

describe('AdvancedReportController', () => {
  let mockReportService: any;

  beforeEach(() => {
    mockReportService = {
      generateReport: (vi).fn(),
      getReportTemplates: (vi).fn(),
      getReportTemplateById: (vi).fn(),
      createReportTemplate: (vi).fn(),
      updateReportTemplate: (vi).fn(),
      deleteReportTemplate: (vi).fn(),
      getScheduledReports: (vi).fn(),
      getScheduledReportById: (vi).fn(),
      createScheduledReport: (vi).fn(),
      updateScheduledReport: (vi).fn(),
      deleteScheduledReport: (vi).fn(),
      runScheduledReport: (vi).fn(),
      getSavedReports: (vi).fn(),
      getSavedReportById: (vi).fn(),
      deleteSavedReport: (vi).fn(),
    };

    // Mock the service instance
    (vi).mocked(AdvancedReportService).mockImplementation(() => mockReportService);
  });

  afterEach(() => {
    (vi).clearAllMocks();
  });

  describe('POST /api/reports/generate', () => {
    it('should generate a report successfully', async () => {
      const mockReport = {
        id: 'report-1',
        filePath: '/path/to/(report).csv',
        format: 'CSV',
      };

      (mockReportService).generateReport.mockResolvedValue(mockReport);

      const response = await request(app).post('/api/reports/generate').send({
        type: 'TRANSACTION',
        format: 'CSV',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
      });

      expect(response.status).toBe(200);
      expect((response).body.success).toBetrue;
      expect((response).body.data).toEqual(mockReport);
      expect((mockReportService).generateReport).toHaveBeenCalledWith(
        'TRANSACTION',
        {
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          userId: 'user-1',
          userRole: 'MERCHANT',
        },
        'CSV'
      );
    });

    it('should handle service errors', async () => {
      (mockReportService).generateReport.mockRejectedValue(new Error('Service error'));

      const response = await request(app).post('/api/reports/generate').send({
        type: 'TRANSACTION',
        format: 'CSV',
      });

      expect(response.status).toBe(500);
      expect((response).body.success).toBefalse;
      expect((response).body.message).toBe('Service error');
    });
  });

  describe('GET /api/reports/templates', () => {
    it('should get report templates successfully', async () => {
      const mockTemplates = [
        {
          id: 'template-1',
          name: 'Test Template',
          type: 'TRANSACTION',
        },
      ];

      (mockReportService).getReportTemplates.mockResolvedValue(mockTemplates);

      const response = await request(app).get('/api/reports/templates');

      expect(response.status).toBe(200);
      expect((response).body.success).toBetrue;
      expect((response).body.data).toEqual(mockTemplates);
      expect((mockReportService).getReportTemplates).toHaveBeenCalledWith('user-1', true);
    });

    it('should handle includeSystem parameter', async () => {
      const mockTemplates = [
        {
          id: 'template-1',
          name: 'User Template',
          type: 'TRANSACTION',
        },
      ];

      (mockReportService).getReportTemplates.mockResolvedValue(mockTemplates);

      const response = await request(app)
        .get('/api/reports/templates')
        .query({ includeSystem: 'false' });

      expect(response.status).toBe(200);
      expect((mockReportService).getReportTemplates).toHaveBeenCalledWith('user-1', false);
    });
  });

  describe('POST /api/reports/templates', () => {
    it('should create a report template successfully', async () => {
      const templateData = {
        name: 'New Template',
        description: 'Test Description',
        type: 'TRANSACTION',
        config: { columns: ['id', 'amount'] },
      };

      const mockTemplate = {
        id: 'template-1',
        ...templateData,
        createdById: 'user-1',
      };

      (mockReportService).createReportTemplate.mockResolvedValue(mockTemplate);

      const response = await request(app).post('/api/reports/templates').send(templateData);

      expect(response.status).toBe(201);
      expect((response).body.success).toBetrue;
      expect((response).body.data).toEqual(mockTemplate);
      expect((mockReportService).createReportTemplate).toHaveBeenCalledWith({
        ...templateData,
        createdById: 'user-1',
      });
    });
  });

  describe('PUT /api/reports/templates/:id', () => {
    it('should update a report template successfully', async () => {
      const updateData = {
        name: 'Updated Template',
        description: 'Updated Description',
      };

      const mockTemplate = {
        id: 'template-1',
        ...updateData,
      };

      (mockReportService).updateReportTemplate.mockResolvedValue(mockTemplate);

      const response = await request(app).put('/api/reports/templates/template-1').send(updateData);

      expect(response.status).toBe(200);
      expect((response).body.success).toBetrue;
      expect((response).body.data).toEqual(mockTemplate);
      expect((mockReportService).updateReportTemplate).toHaveBeenCalledWith('template-1', updateData);
    });
  });

  describe('DELETE /api/reports/templates/:id', () => {
    it('should delete a report template successfully', async () => {
      (mockReportService).deleteReportTemplate.mockResolvedValue({});

      const response = await request(app).delete('/api/reports/templates/template-1');

      expect(response.status).toBe(200);
      expect((response).body.success).toBetrue;
      expect((response).body.message).toBe('Report template deleted successfully');
      expect((mockReportService).deleteReportTemplate).toHaveBeenCalledWith('template-1');
    });
  });

  describe('GET /api/reports/scheduled', () => {
    it('should get scheduled reports successfully', async () => {
      const mockReports = [
        {
          id: 'scheduled-1',
          name: 'Weekly Report',
          schedule: '0 0 * * 1',
        },
      ];

      (mockReportService).getScheduledReports.mockResolvedValue(mockReports);

      const response = await request(app).get('/api/reports/scheduled');

      expect(response.status).toBe(200);
      expect((response).body.success).toBetrue;
      expect((response).body.data).toEqual(mockReports);
      expect((mockReportService).getScheduledReports).toHaveBeenCalledWith('user-1');
    });
  });

  describe('POST /api/reports/scheduled', () => {
    it('should create a scheduled report successfully', async () => {
      const scheduledReportData = {
        name: 'Weekly Report',
        templateId: 'template-1',
        schedule: '0 0 * * 1',
        isActive: true,
      };

      const mockScheduledReport = {
        id: 'scheduled-1',
        ...scheduledReportData,
        createdById: 'user-1',
      };

      (mockReportService).createScheduledReport.mockResolvedValue(mockScheduledReport);

      const response = await request(app).post('/api/reports/scheduled').send(scheduledReportData);

      expect(response.status).toBe(201);
      expect((response).body.success).toBetrue;
      expect((response).body.data).toEqual(mockScheduledReport);
      expect((mockReportService).createScheduledReport).toHaveBeenCalledWith({
        ...scheduledReportData,
        createdById: 'user-1',
      });
    });
  });

  describe('POST /api/reports/scheduled/:id/run', () => {
    it('should run a scheduled report successfully', async () => {
      const mockResult = {
        id: 'report-1',
        status: 'COMPLETED',
      };

      (mockReportService).runScheduledReport.mockResolvedValue(mockResult);

      const response = await request(app).post('/api/reports/scheduled/scheduled-1/run');

      expect(response.status).toBe(200);
      expect((response).body.success).toBetrue;
      expect((response).body.data).toEqual(mockResult);
      expect((mockReportService).runScheduledReport).toHaveBeenCalledWith('scheduled-1');
    });
  });

  describe('GET /api/reports/saved', () => {
    it('should get saved reports successfully', async () => {
      const mockReports = [
        {
          id: 'report-1',
          name: 'Transaction Report',
          type: 'TRANSACTION',
          format: 'CSV',
        },
      ];

      (mockReportService).getSavedReports.mockResolvedValue(mockReports);

      const response = await request(app).get('/api/reports/saved');

      expect(response.status).toBe(200);
      expect((response).body.success).toBetrue;
      expect((response).body.data).toEqual(mockReports);
      expect((mockReportService).getSavedReports).toHaveBeenCalledWith('user-1');
    });
  });

  describe('DELETE /api/reports/saved/:id', () => {
    it('should delete a saved report successfully', async () => {
      (mockReportService).deleteSavedReport.mockResolvedValue({});

      const response = await request(app).delete('/api/reports/saved/report-1');

      expect(response.status).toBe(200);
      expect((response).body.success).toBetrue;
      expect((response).body.message).toBe('Saved report deleted successfully');
      expect((mockReportService).deleteSavedReport).toHaveBeenCalledWith('report-1');
    });
  });
});
