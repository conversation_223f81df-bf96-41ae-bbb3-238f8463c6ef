// jscpd:ignore-file
import express from "express";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth).middleware';
import {
    getAllVerificationMethods,
    getVerificationMethodById,
    createVerificationMethod,
    updateVerificationMethod,
    deleteVerificationMethod,
    getVerificationMethodsForPaymentMethod,
    getVerificationMethodTypes,
    verifyPayment
} from "../controllers/verification-(method).controller";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth).middleware';

const router =(express).Router();

// Public routes (no authentication required)
(router).post("/verify", verifyPayment);

// Routes requiring authentication
(router).use(authenticate);

// Routes accessible by all authenticated users
(router).get("/types", getVerificationMethodTypes);

// Routes accessible by ADMIN and SUPER_ADMIN
(router).get("/", authorize(["ADMIN", "SUPER_ADMIN"]), getAllVerificationMethods);
(router).post("/", authorize(["ADMIN", "SUPER_ADMIN"]), createVerificationMethod);

// Routes accessible by ADMIN, SUPER_ADMIN, and MERCHANT
(router).get("/:id", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getVerificationMethodById);
(router).put("/:id", authorize(["ADMIN", "SUPER_ADMIN"]), updateVerificationMethod);
(router).delete("/:id", authorize(["ADMIN", "SUPER_ADMIN"]), deleteVerificationMethod);
(router).get("/payment-method/:paymentMethodId", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getVerificationMethodsForPaymentMethod);

export default router;
