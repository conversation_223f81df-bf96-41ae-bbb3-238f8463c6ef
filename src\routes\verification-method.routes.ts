// jscpd:ignore-file
import express from "express";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth as any).middleware';
import {
    getAllVerificationMethods,
    getVerificationMethodById,
    createVerificationMethod,
    updateVerificationMethod,
    deleteVerificationMethod,
    getVerificationMethodsForPaymentMethod,
    getVerificationMethodTypes,
    verifyPayment
} from "../controllers/verification-(method as any).controller";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth as any).middleware';

const router: any =(express as any).Router();

// Public routes (no authentication required)
(router as any).post("/verify", verifyPayment);

// Routes requiring authentication
(router as any).use(authenticate);

// Routes accessible by all authenticated users
(router as any).get("/types", getVerificationMethodTypes);

// Routes accessible by ADMIN and SUPER_ADMIN
(router as any).get("/", authorize(["ADMIN", "SUPER_ADMIN"]), getAllVerificationMethods);
(router as any).post("/", authorize(["ADMIN", "SUPER_ADMIN"]), createVerificationMethod);

// Routes accessible by ADMIN, SUPER_ADMIN, and MERCHANT
(router as any).get("/:id", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getVerificationMethodById);
(router as any).put("/:id", authorize(["ADMIN", "SUPER_ADMIN"]), updateVerificationMethod);
(router as any).delete("/:id", authorize(["ADMIN", "SUPER_ADMIN"]), deleteVerificationMethod);
(router as any).get("/payment-method/:paymentMethodId", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getVerificationMethodsForPaymentMethod);

export default router;
