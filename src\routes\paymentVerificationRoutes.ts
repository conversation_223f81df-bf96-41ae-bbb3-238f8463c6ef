// jscpd:ignore-file
import { Router as ImportedRouter } from "express";
import { PaymentVerificationController as ImportedPaymentVerificationController } from "../controllers/paymentVerificationController";
import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { authenticateJWT as ImportedauthenticateJWT } from '../middlewares/authMiddleware';
import { PaymentVerificationController as ImportedPaymentVerificationController } from "../controllers/paymentVerificationController";
import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { authenticateJWT as ImportedauthenticateJWT } from '../middlewares/authMiddleware';

const router: any =Router();
const prisma = new PrismaClient();
const paymentVerificationController = new PaymentVerificationController(prisma);

/**
 * @route POST /api/verify
 * @desc Verify a payment
 * @access Private
 */
(router as any).post(
    "/",
    authenticateJWT,
    (paymentVerificationController as any).verifyPayment.bind(paymentVerificationController)
);

/**
 * @route GET /api/verify/:id
 * @desc Get a transaction by ID
 * @access Private
 */
(router as any).get(
    "/:id",
    authenticateJWT,
    (paymentVerificationController as any).getTransaction.bind(paymentVerificationController)
);

/**
 * @route PUT /api/verify/:id/status
 * @desc Update a transaction status
 * @access Private
 */
(router as any).put(
    "/:id/status",
    authenticateJWT,
    (paymentVerificationController as any).updateTransactionStatus.bind(paymentVerificationController)
);

export default router;
