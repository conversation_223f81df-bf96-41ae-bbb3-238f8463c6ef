/**
 * CRUD Controller
 * 
 * This is a base controller class that provides CRUD functionality
 * for all controllers in the application.
 */

import { Request, Response, NextFunction } from 'express';
import { BaseController } from './BaseController';
import { BaseController } from './BaseController';

export class CrudController extends BaseController {
  /**
   * Get all items
   */
  protected async getAll(req: Request, res: Response, service: any, message: string = 'Items retrieved successfully') {
    try {
      const items = await (service).findAll(req.query);
      return this.sendSuccess(res, items, message);
    } catch (error) {
      return this.sendError(res, 'Error retrieving items', 500, error);
    }
  }

  /**
   * Get item by ID
   */
  protected async getById(req: Request, res: Response, service: any, message: string = 'Item retrieved successfully') {
    try {
      const { id } = req.params;
      const item = await (service).findById(id);
      
      if (!item) {
        return this.sendError(res, 'Item not found', 404);
      }
      
      return this.sendSuccess(res, item, message);
    } catch (error) {
      return this.sendError(res, 'Error retrieving item', 500, error);
    }
  }

  /**
   * Create item
   */
  protected async create(req: Request, res: Response, service: any, validationSchema = null, message: string = 'Item created successfully') {
    try {
      // Validate request
      const validationError = this.validateRequest(req, validationSchema);
      if (validationError) {
        return this.sendError(res, validationError, 400);
      }
      
      const item = await (service).create(req.body);
      return this.sendSuccess(res, item, message, 201);
    } catch (error) {
      return this.sendError(res, 'Error creating item', 500, error);
    }
  }

  /**
   * Update item
   */
  protected async update(req: Request, res: Response, service: any, validationSchema = null, message: string = 'Item updated successfully') {
    try {
      // Validate request
      const validationError = this.validateRequest(req, validationSchema);
      if (validationError) {
        return this.sendError(res, validationError, 400);
      }
      
      const { id } = req.params;
      const item = await (service).update(id, req.body);
      
      if (!item) {
        return this.sendError(res, 'Item not found', 404);
      }
      
      return this.sendSuccess(res, item, message);
    } catch (error) {
      return this.sendError(res, 'Error updating item', 500, error);
    }
  }

  /**
   * Delete item
   */
  protected async delete(req: Request, res: Response, service: any, message: string = 'Item deleted successfully') {
    try {
      const { id } = req.params;
      const result = await (service).delete(id);
      
      if (!result) {
        return this.sendError(res, 'Item not found', 404);
      }
      
      return this.sendSuccess(res, {}, message);
    } catch (error) {
      return this.sendError(res, 'Error deleting item', 500, error);
    }
  }
}