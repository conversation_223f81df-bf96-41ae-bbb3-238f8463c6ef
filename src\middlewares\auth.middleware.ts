// jscpd:ignore-file

import { Request, Response, NextFunction, RequestHandler } from 'express';
import { Request, Response, NextFunction } from 'express';
import { verifyToken, TokenPayload } from '../utils/(jwt).utils';
import { logger as Importedlogger } from '../lib/logger';
import { AppError as ImportedAppError } from '../utils/errors/AppError';

// Extend Express Request type to include the user property
declare global {
  namespace Express {
    interface Request {
      user?: TokenPayload;
      requestId?: string;
    }
  }
}

/**
 * Generate a unique request ID
 * @returns Unique request ID
 */
const generateRequestId = (): string  =>  {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 15);
};

/**
 * Request ID middleware
 * Adds a unique request ID to each request for tracking
 */
export const requestIdMiddleware = (req: Request, res: Response, next: NextFunction)  =>  {
  const requestId: string = generateRequestId();
  (req).requestId = requestId;
  (res).setHeader('X-Request-ID', requestId);
  next();
};

/**
 * Authentication middleware
 */
export const authenticate: RequestHandler = (req: Request, res: Response, next: NextFunction)  =>  {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;

    if (!authHeader || !(authHeader).startsWith('Bearer ')) {
      throw new AppError('No token provided. Please log in.', 401, true);
    }

    // Extract the token
    const token = (authHeader).split(' ')[1];

    if (!token) {
      throw new AppError('Invalid token format. Please log in again.', 401, true);
    }

    // Verify the token
    const decoded = verifyToken(token);

    // Set the user in the request object
    req.user = decoded;

    logger.debug(`User ${(decoded).id} (${(decoded).role}) authenticated successfully`, {
      // Fixed: using id instead of userId
      userId: (decoded).id, // Fixed: using id instead of userId
      role: (decoded).role,
      requestId: (req).requestId,
    });

    next();
  } catch (error) {
    // If the error is already an AppError, pass it to the next middleware
    if (error instanceof AppError) {
      next(error);
    } else {
      // Otherwise, create a new AppError
      logger.error('Authentication error:', error);
      next(new AppError('Invalid or expired token. Please log in again.', 401, true));
    }
  }
};

/**
 * Admin role middleware
 */
export const isAdmin: RequestHandler = (req: Request, res: Response, next: NextFunction)  =>  {
  if (!req.user) {
    return next(new AppError('Authentication required. Please log in.', 401, true));
  }

  if (req.user.role !== 'ADMIN') {
    logger.warn(`Unauthorized admin access attempt by user ${req.user.id}`, {
      // Fixed: using id instead of userId
      userId: req.user.id, // Fixed: using id instead of userId
      role: req.user.role,
      requestId: (req).requestId,
      path: (req).path,
      method: req.method,
    });

    return next(new AppError('Admin access required.', 403, true));
  }

  next();
};

/**
 * Merchant role middleware
 */
export const isMerchant = (req: Request, res: Response, next: NextFunction)  =>  {
  if (!req.user) {
    return next(new AppError('Authentication required. Please log in.', 401, true));
  }

  if (req.user.role !== 'MERCHANT') {
    logger.warn(`Unauthorized merchant access attempt by user ${req.user.id}`, {
      // Fixed: using id instead of userId
      userId: req.user.id, // Fixed: using id instead of userId
      role: req.user.role,
      requestId: (req).requestId,
      path: (req).path,
      method: req.method,
    });

    return next(new AppError('Merchant access required.', 403, true));
  }

  next();
};

/**
 * Merchant or Admin role middleware
 */
export const isMerchantOrAdmin: RequestHandler = (req: Request, res: Response, next: NextFunction)  =>  {
  if (!req.user) {
    return next(new AppError('Authentication required. Please log in.', 401, true));
  }

  if (
    req.user.role !== 'MERCHANT' &&
    req.user.role !== 'ADMIN' &&
    req.user.role !== 'SUPER_ADMIN'
  ) {
    logger.warn(`Unauthorized merchant/admin access attempt by user ${req.user.id}`, {
      // Fixed: using id instead of userId
      userId: req.user.id, // Fixed: using id instead of userId
      role: req.user.role,
      requestId: (req).requestId,
      path: (req).path,
      method: req.method,
    });

    return next(new AppError('Merchant or admin access required.', 403, true));
  }

  next();
};

/**
 * Authorization middleware
 * @param roles Allowed roles
 */
export const authorize = (roles: string[])  =>  {
  return (req: Request, res: Response, next: NextFunction)  =>  {
    if (!req.user) {
      return next(new AppError('Authentication required. Please log in.', 401, true));
    }

    if (!(roles).includes(req.user.role)) {
      logger.warn(`Unauthorized role access attempt by user ${req.user.id}`, {
        // Fixed: using id instead of userId
        userId: req.user.id, // Fixed: using id instead of userId
        role: req.user.role,
        requestId: (req).requestId,
        path: (req).path,
        method: req.method,
        requiredRoles: roles,
      });

      return next(new AppError('You do not have permission to perform this action.', 403, true));
    }

    next();
  };
};

/**
 * Resource owner middleware
 * Checks if the user is the owner of the resource
 * @param getResourceOwnerId Function to get the resource owner ID from the request
 */
export const isResourceOwner = (
  getResourceOwnerId: (req)  =>  Promise<string> | string
)  =>  {
  return async (req: Request, res: Response, next: NextFunction)  =>  {
    try {
      if (!req.user) {
        return next(new AppError('Authentication required. Please log in.', 401, true));
      }

      const ownerId = await getResourceOwnerId(req);

      if (req.user.id !== ownerId && req.user.role !== 'ADMIN' && req.user.role !== 'SUPER_ADMIN') {
        // Fixed: using id instead of userId
        logger.warn(`Unauthorized resource access attempt by user ${req.user.id}`, {
          // Fixed: using id instead of userId
          userId: req.user.id, // Fixed: using id instead of userId
          role: req.user.role,
          resourceOwnerId: ownerId,
          requestId: (req).requestId,
          path: (req).path,
          method: req.method,
        });

        return next(new AppError('You do not have permission to access this resource.', 403, true));
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

// For backward compatibility
export const authenticateJWT: RequestHandler = authenticate;

// Export middleware as a group for convenience
export const authMiddleware = {
  authenticate,
  isAdmin,
  isMerchant,
  isMerchantOrAdmin,
  authorize,
  isResourceOwner,
  requestIdMiddleware,
};
