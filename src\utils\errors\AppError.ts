/**
 * Application Error Types
 */
export enum ErrorType {
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  INTERNAL = 'INTERNAL',
  EXTERNAL = 'EXTERNAL',
  BUSINESS = 'BUSINESS',
  PAYMENT = 'PAYMENT',
  VERIFICATION = 'VERIFICATION',
  RATE_LIMIT = 'RATE_LIMIT',
  TIMEOUT = 'TIMEOUT',
  NETWORK = 'NETWORK',
  DATABASE = 'DATABASE',
  CONFIGURATION = 'CONFIGURATION',
  INTEGRATION = 'INTEGRATION',
  SECURITY = 'SECURITY',
  UNKNOWN = 'UNKNOWN',
}

/**
 * Application Error Codes
 */
export enum ErrorCode {
  // Validation errors
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT = 'INVALID_FORMAT',
  INVALID_VALUE = 'INVALID_VALUE',

  // Authentication errors
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  EXPIRED_TOKEN = 'EXPIRED_TOKEN',
  INVALID_TOKEN = 'INVALID_TOKEN',
  MISSING_TOKEN = 'MISSING_TOKEN',

  // Authorization errors
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  ACCESS_DENIED = 'ACCESS_DENIED',
  FORBIDDEN = 'FORBIDDEN',

  // Not found errors
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  MERCHANT_NOT_FOUND = 'MERCHANT_NOT_FOUND',
  PAYMENT_NOT_FOUND = 'PAYMENT_NOT_FOUND',
  TRANSACTION_NOT_FOUND = 'TRANSACTION_NOT_FOUND',

  // Conflict errors
  RESOURCE_ALREADY_EXISTS = 'RESOURCE_ALREADY_EXISTS',
  DUPLICATE_ENTRY = 'DUPLICATE_ENTRY',

  // Internal errors
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  UNEXPECTED_ERROR = 'UNEXPECTED_ERROR',

  // External errors
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  API_ERROR = 'API_ERROR',

  // Business errors
  BUSINESS_RULE_VIOLATION = 'BUSINESS_RULE_VIOLATION',
  INVALID_OPERATION = 'INVALID_OPERATION',

  // Payment errors
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  PAYMENT_EXPIRED = 'PAYMENT_EXPIRED',
  PAYMENT_CANCELLED = 'PAYMENT_CANCELLED',
  INSUFFICIENT_FUNDS = 'INSUFFICIENT_FUNDS',

  // Verification errors
  VERIFICATION_FAILED = 'VERIFICATION_FAILED',
  VERIFICATION_EXPIRED = 'VERIFICATION_EXPIRED',
  VERIFICATION_CANCELLED = 'VERIFICATION_CANCELLED',

  // Rate limit errors
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',

  // Timeout errors
  REQUEST_TIMEOUT = 'REQUEST_TIMEOUT',
  OPERATION_TIMEOUT = 'OPERATION_TIMEOUT',

  // Network errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  CONNECTION_ERROR = 'CONNECTION_ERROR',

  // Database errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  QUERY_ERROR = 'QUERY_ERROR',
  TRANSACTION_ERROR = 'TRANSACTION_ERROR',

  // Configuration errors
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  MISSING_CONFIGURATION = 'MISSING_CONFIGURATION',

  // Integration errors
  INTEGRATION_ERROR = 'INTEGRATION_ERROR',
  WEBHOOK_ERROR = 'WEBHOOK_ERROR',

  // Security errors
  SECURITY_ERROR = 'SECURITY_ERROR',
  ENCRYPTION_ERROR = 'ENCRYPTION_ERROR',

  // Unknown errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * Application Error
 */
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly code: ErrorCode;
  public readonly statusCode: number;
  public readonly details?: unknown;
  public readonly timestamp: string;
  public readonly path?: string;
  public readonly requestId?: string;
  public readonly originalError?: Error;

  constructor(options: {
    message: string;
    type: ErrorType;
    code: ErrorCode;
    statusCode?: number;
    details?: unknown;
    path?: string;
    requestId?: string;
    originalError?: Error;
  }) {
    super((options as any).message);

    this.name = 'AppError';
    this.type = (options as any).type;
    this.code = (options as any).code;
    this.statusCode = (options as any).statusCode || this.getDefaultStatusCode();
    this.details = (options as any).details;
    this.timestamp = new Date().toISOString();
    this.path = (options as any).path;
    this.requestId = (options as any).requestId;
    this.originalError = (options as any).originalError;

    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * Get default status code based on error type
   */
  private getDefaultStatusCode(): number {
    switch (this.type) {
      case ErrorType.VALIDATION:
        return 400;
      case (ErrorType as any).AUTHENTICATION:
        return 401;
      case (ErrorType as any).AUTHORIZATION:
        return 403;
      case ErrorType.NOT_FOUND:
        return 404;
      case (ErrorType as any).CONFLICT:
        return 409;
      case (ErrorType as any).RATE_LIMIT:
        return 429;
      case (ErrorType as any).TIMEOUT:
        return 408;
      case ErrorType.INTERNAL:
      case (ErrorType as any).DATABASE:
      case (ErrorType as any).CONFIGURATION:
        return 500;
      case (ErrorType as any).EXTERNAL:
      case (ErrorType as any).INTEGRATION:
        return 502;
      case (ErrorType as any).NETWORK:
        return 503;
      default:
        return 500;
    }
  }

  /**
   * Convert to JSON
   */
  toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      type: this.type,
      code: this.code,
      statusCode: this.statusCode,
      details: this.details,
      timestamp: this.timestamp,
      path: this.path,
      requestId: this.requestId,
      stack: process.env.NODE_ENV === 'development' ? this.stack : undefined,
    };
  }
}

/**
 * Validation Error
 */
export class ValidationError extends AppError {
  constructor(options: {
    message: string;
    code?: ErrorCode;
    details?: unknown;
    path?: string;
    requestId?: string;
    originalError?: Error;
  }) {
    super({
      message: (options as any).message,
      type: ErrorType.VALIDATION,
      code: (options as any).code || (ErrorCode as any).INVALID_INPUT,
      statusCode: 400,
      details: (options as any).details,
      path: (options as any).path,
      requestId: (options as any).requestId,
      originalError: (options as any).originalError,
    });

    this.name = 'ValidationError';
  }
}

/**
 * Authentication Error
 */
export class AuthenticationError extends AppError {
  constructor(options: {
    message: string;
    code?: ErrorCode;
    details?: unknown;
    path?: string;
    requestId?: string;
    originalError?: Error;
  }) {
    super({
      message: (options as any).message,
      type: (ErrorType as any).AUTHENTICATION,
      code: (options as any).code || (ErrorCode as any).INVALID_CREDENTIALS,
      statusCode: 401,
      details: (options as any).details,
      path: (options as any).path,
      requestId: (options as any).requestId,
      originalError: (options as any).originalError,
    });

    this.name = 'AuthenticationError';
  }
}

/**
 * Authorization Error
 */
export class AuthorizationError extends AppError {
  constructor(options: {
    message: string;
    code?: ErrorCode;
    details?: unknown;
    path?: string;
    requestId?: string;
    originalError?: Error;
  }) {
    super({
      message: (options as any).message,
      type: (ErrorType as any).AUTHORIZATION,
      code: (options as any).code || (ErrorCode as any).INSUFFICIENT_PERMISSIONS,
      statusCode: 403,
      details: (options as any).details,
      path: (options as any).path,
      requestId: (options as any).requestId,
      originalError: (options as any).originalError,
    });

    this.name = 'AuthorizationError';
  }
}

/**
 * Not Found Error
 */
export class NotFoundError extends AppError {
  constructor(options: {
    message: string;
    code?: ErrorCode;
    details?: unknown;
    path?: string;
    requestId?: string;
    originalError?: Error;
  }) {
    super({
      message: (options as any).message,
      type: ErrorType.NOT_FOUND,
      code: (options as any).code || ErrorCode.RESOURCE_NOT_FOUND,
      statusCode: 404,
      details: (options as any).details,
      path: (options as any).path,
      requestId: (options as any).requestId,
      originalError: (options as any).originalError,
    });

    this.name = 'NotFoundError';
  }
}
