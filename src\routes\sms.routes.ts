// jscpd:ignore-file
import { Router as ImportedRouter } from "express";
import { SmsController as ImportedSmsController } from "../controllers/refactored/(sms as any).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { SmsController as ImportedSmsController } from "../controllers/refactored/(sms as any).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';

const router: any =Router();

// SMS routes
(router as any).post("/test", authenticate, (SmsController as any).testSmsService);
(router as any).post("/send", authenticate, (SmsController as any).sendCustomSms);
(router as any).get("/admin-phone-numbers", authenticate, (SmsController as any).getAdminPhoneNumbers);

export default router;
