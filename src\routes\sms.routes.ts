// jscpd:ignore-file
import { Router as ImportedRouter } from "express";
import { SmsController as ImportedSmsController } from "../controllers/refactored/(sms).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { SmsController as ImportedSmsController } from "../controllers/refactored/(sms).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';

const router =Router();

// SMS routes
(router).post("/test", authenticate, (SmsController).testSmsService);
(router).post("/send", authenticate, (SmsController).sendCustomSms);
(router).get("/admin-phone-numbers", authenticate, (SmsController).getAdminPhoneNumbers);

export default router;
