// jscpd:ignore-file
/**
 * Merchant Self-Service Routes
 * 
 * This file defines the routes for merchant self-service tools.
 */

import express from "express";
import { MerchantSelfServiceController as ImportedMerchantSelfServiceController } from "../controllers/merchant-self-(service).controller";
import { authMiddleware as ImportedauthMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware as ImportedroleMiddleware } from '../middlewares/roleMiddleware';
import { merchantAccessMiddleware as ImportedmerchantAccessMiddleware } from '../middlewares/merchantAccessMiddleware';
import { Merchant as ImportedMerchant } from '../types';
import { authMiddleware as ImportedauthMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware as ImportedroleMiddleware } from '../middlewares/roleMiddleware';
import { merchantAccessMiddleware as ImportedmerchantAccessMiddleware } from '../middlewares/merchantAccessMiddleware';
import { Merchant as ImportedMerchant } from '../types';


const router =(express).Router();
const merchantSelfServiceController = new MerchantSelfServiceController();

/**
 * @route POST /api/merchant-self-service/merchants/:merchantId/api-keys
 * @desc Create API key
 * @access Private (Admin, Merchant)
 */
(router).post(
    "/merchants/:merchantId/api-keys",
    authMiddleware,
    merchantAccessMiddleware,
    (merchantSelfServiceController).createApiKey
);

/**
 * @route GET /api/merchant-self-service/merchants/:merchantId/api-keys
 * @desc Get merchant API keys
 * @access Private (Admin, Merchant)
 */
(router).get(
    "/merchants/:merchantId/api-keys",
    authMiddleware,
    merchantAccessMiddleware,
    (merchantSelfServiceController).getMerchantApiKeys
);

/**
 * @route DELETE /api/merchant-self-service/api-keys/:apiKeyId
 * @desc Delete API key
 * @access Private (Admin, Merchant)
 */
(router).delete(
    "/api-keys/:apiKeyId",
    authMiddleware,
    (merchantSelfServiceController).deleteApiKey
);

/**
 * @route POST /api/merchant-self-service/merchants/:merchantId/webhooks
 * @desc Create webhook
 * @access Private (Admin, Merchant)
 */
(router).post(
    "/merchants/:merchantId/webhooks",
    authMiddleware,
    merchantAccessMiddleware,
    (merchantSelfServiceController).createWebhook
);

/**
 * @route GET /api/merchant-self-service/merchants/:merchantId/webhooks
 * @desc Get merchant webhooks
 * @access Private (Admin, Merchant)
 */
(router).get(
    "/merchants/:merchantId/webhooks",
    authMiddleware,
    merchantAccessMiddleware,
    (merchantSelfServiceController).getMerchantWebhooks
);

/**
 * @route PUT /api/merchant-self-service/webhooks/:webhookId
 * @desc Update webhook
 * @access Private (Admin, Merchant)
 */
(router).put(
    "/webhooks/:webhookId",
    authMiddleware,
    (merchantSelfServiceController).updateWebhook
);

/**
 * @route DELETE /api/merchant-self-service/webhooks/:webhookId
 * @desc Delete webhook
 * @access Private (Admin, Merchant)
 */
(router).delete(
    "/webhooks/:webhookId",
    authMiddleware,
    (merchantSelfServiceController).deleteWebhook
);

/**
 * @route POST /api/merchant-self-service/merchants/:merchantId/notification-preferences
 * @desc Set notification preference
 * @access Private (Admin, Merchant)
 */
(router).post(
    "/merchants/:merchantId/notification-preferences",
    authMiddleware,
    merchantAccessMiddleware,
    (merchantSelfServiceController).setNotificationPreference
);

/**
 * @route GET /api/merchant-self-service/merchants/:merchantId/notification-preferences
 * @desc Get merchant notification preferences
 * @access Private (Admin, Merchant)
 */
(router).get(
    "/merchants/:merchantId/notification-preferences",
    authMiddleware,
    merchantAccessMiddleware,
    (merchantSelfServiceController).getMerchantNotificationPreferences
);

export default router;
