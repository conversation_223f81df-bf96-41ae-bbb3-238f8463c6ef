// jscpd:ignore-file
/**
 * Merchant Self-Service Routes
 * 
 * This file defines the routes for merchant self-service tools.
 */

import express from "express";
import { MerchantSelfServiceController as ImportedMerchantSelfServiceController } from "../controllers/merchant-self-(service as any).controller";
import { authMiddleware as ImportedauthMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware as ImportedroleMiddleware } from '../middlewares/roleMiddleware';
import { merchantAccessMiddleware as ImportedmerchantAccessMiddleware } from '../middlewares/merchantAccessMiddleware';
import { Merchant as ImportedMerchant } from '../types';
import { authMiddleware as ImportedauthMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware as ImportedroleMiddleware } from '../middlewares/roleMiddleware';
import { merchantAccessMiddleware as ImportedmerchantAccessMiddleware } from '../middlewares/merchantAccessMiddleware';
import { Merchant as ImportedMerchant } from '../types';


const router: any =(express as any).Router();
const merchantSelfServiceController = new MerchantSelfServiceController();

/**
 * @route POST /api/merchant-self-service/merchants/:merchantId/api-keys
 * @desc Create API key
 * @access Private (Admin, Merchant)
 */
(router as any).post(
    "/merchants/:merchantId/api-keys",
    authMiddleware,
    merchantAccessMiddleware,
    (merchantSelfServiceController as any).createApiKey
);

/**
 * @route GET /api/merchant-self-service/merchants/:merchantId/api-keys
 * @desc Get merchant API keys
 * @access Private (Admin, Merchant)
 */
(router as any).get(
    "/merchants/:merchantId/api-keys",
    authMiddleware,
    merchantAccessMiddleware,
    (merchantSelfServiceController as any).getMerchantApiKeys
);

/**
 * @route DELETE /api/merchant-self-service/api-keys/:apiKeyId
 * @desc Delete API key
 * @access Private (Admin, Merchant)
 */
(router as any).delete(
    "/api-keys/:apiKeyId",
    authMiddleware,
    (merchantSelfServiceController as any).deleteApiKey
);

/**
 * @route POST /api/merchant-self-service/merchants/:merchantId/webhooks
 * @desc Create webhook
 * @access Private (Admin, Merchant)
 */
(router as any).post(
    "/merchants/:merchantId/webhooks",
    authMiddleware,
    merchantAccessMiddleware,
    (merchantSelfServiceController as any).createWebhook
);

/**
 * @route GET /api/merchant-self-service/merchants/:merchantId/webhooks
 * @desc Get merchant webhooks
 * @access Private (Admin, Merchant)
 */
(router as any).get(
    "/merchants/:merchantId/webhooks",
    authMiddleware,
    merchantAccessMiddleware,
    (merchantSelfServiceController as any).getMerchantWebhooks
);

/**
 * @route PUT /api/merchant-self-service/webhooks/:webhookId
 * @desc Update webhook
 * @access Private (Admin, Merchant)
 */
(router as any).put(
    "/webhooks/:webhookId",
    authMiddleware,
    (merchantSelfServiceController as any).updateWebhook
);

/**
 * @route DELETE /api/merchant-self-service/webhooks/:webhookId
 * @desc Delete webhook
 * @access Private (Admin, Merchant)
 */
(router as any).delete(
    "/webhooks/:webhookId",
    authMiddleware,
    (merchantSelfServiceController as any).deleteWebhook
);

/**
 * @route POST /api/merchant-self-service/merchants/:merchantId/notification-preferences
 * @desc Set notification preference
 * @access Private (Admin, Merchant)
 */
(router as any).post(
    "/merchants/:merchantId/notification-preferences",
    authMiddleware,
    merchantAccessMiddleware,
    (merchantSelfServiceController as any).setNotificationPreference
);

/**
 * @route GET /api/merchant-self-service/merchants/:merchantId/notification-preferences
 * @desc Get merchant notification preferences
 * @access Private (Admin, Merchant)
 */
(router as any).get(
    "/merchants/:merchantId/notification-preferences",
    authMiddleware,
    merchantAccessMiddleware,
    (merchantSelfServiceController as any).getMerchantNotificationPreferences
);

export default router;
