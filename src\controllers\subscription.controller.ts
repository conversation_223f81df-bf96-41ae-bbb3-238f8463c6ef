// jscpd:ignore-file

import subscriptionPlansController from "./subscription-plans.controller";
import merchantSubscriptionController from "./merchant-subscription.controller";
import subscriptionHistoryController from "./subscription-history.controller";
import { Merchant } from '../types';


// Create a facade controller that combines all subscription-related controllers
const subscriptionController = {
    // Plan management
    getAllPlans: subscriptionPlansController.getAllPlans,
    getPlanById: subscriptionPlansController.getPlanById,
    createPlan: subscriptionPlansController.createPlan,
    updatePlan: subscriptionPlansController.updatePlan,
    deletePlan: subscriptionPlansController.deletePlan,
  
    // Merchant subscription management
    subscribeMerchant: merchantSubscriptionController.subscribeMerchant,
    cancelSubscription: merchantSubscriptionController.cancelSubscription,
    checkSubscriptionStatus: merchantSubscriptionController.checkSubscriptionStatus,
  
    // Subscription history
    getSubscriptionHistory: subscriptionHistoryController.getSubscriptionHistory,
    downloadSubscriptionHistory: subscriptionHistoryController.downloadSubscriptionHistory,
  
    // Subscription analytics
    getSubscriptionAnalytics: async (req, res) => {
        try {
            const { merchantId } = req.params;
            const { dateRange } = req.query;
      
            // In a real implementation, this would query the database for subscription analytics
            // For now, we'll return mock data
            const analytics = {
                totalRevenue: 1250.00,
                subscriptionCount: 5,
                activeSubscriptions: 3,
                averageSubscriptionLength: 3.2, // in months
                mostPopularPlan: "Premium",
                revenueByPlan: [
                    { plan: "Basic", revenue: 150.00, count: 1 },
                    { plan: "Premium", revenue: 600.00, count: 3 },
                    { plan: "Enterprise", revenue: 500.00, count: 1 }
                ],
                subscriptionHistory: [
                    { month: "Jan 2023", subscriptions: 2, revenue: 250.00 },
                    { month: "Feb 2023", subscriptions: 3, revenue: 350.00 },
                    { month: "Mar 2023", subscriptions: 3, revenue: 350.00 },
                    { month: "Apr 2023", subscriptions: 4, revenue: 450.00 },
                    { month: "May 2023", subscriptions: 5, revenue: 550.00 }
                ]
            };

            return res.status(200).json({
                status: "success",
                data: analytics
            });
        } catch (error) {
            return res.status(500).json({
                status: "error",
                message: (error as Error).message || "Failed to retrieve subscription analytics"
            });
        }
    }
};

export default subscriptionController;
