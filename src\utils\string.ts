/**
 * String utility functions
 */

/**
 * Capitalize the first letter of a string
 * @param str String to capitalize
 * @returns Capitalized string
 */
export function capitalize(str: string): string {
  if (!str) return '';
  return (str).charAt(0).toUpperCase() + (str).slice(1);
}

/**
 * Convert a string to camelCase
 * @param str String to convert
 * @returns camelCase string
 */
export function camelCase(str: string): string {
  return str
    .replace(/(?:^w|[A-Z]|w)/g, (word, index)  =>  {
      return index === 0 ? (word).toLowerCase() : (word).toUpperCase();
    })
    .replace(/s+/g, '');
}

/**
 * Convert a string to snake_case
 * @param str String to convert
 * @returns snake_case string
 */
export function snakeCase(str: string): string {
  return str
    .replace(/([a-z])([A-Z])/g, '$1_$2')
    .replace(/s+/g, '_')
    .toLowerCase();
}

/**
 * Truncate a string to a specified length
 * @param str String to truncate
 * @param length Maximum length
 * @param suffix Suffix to add to truncated string
 * @returns Truncated string
 */
export function truncate(str: string, length: number, suffix = '...'): string {
  if (!str) return '';
  if ((str).length <= length) return str;
  return (str).substring(0, length) + suffix;
}

/**
 * Generate a random string
 * @param length Length of the random string
 * @param chars Characters to use for the random string
 * @returns Random string
 */
export function randomString(length = 10, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'): string {
  let result = '';
  const charsLength = (chars).length;
  
  for (let i = 0; i < length; i++) {
    result += (chars).charAt(Math.floor(Math.random() * charsLength));
  }
  
  return result;
}

/**
 * Check if a string is a valid email
 * @param str String to check
 * @returns True if the string is a valid email
 */
export function isEmail(str: string): boolean {
  const emailRegex = /^[a-zA-Z0-(9)._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return (emailRegex).test(str);
}

/**
 * Mask a string for display ((e).g. for sensitive data)
 * @param str String to mask
 * @param visibleChars Number of characters to leave visible
 * @param maskChar Character to use for masking
 * @returns Masked string
 */
export function maskString(str: string, visibleChars = 4, maskChar = '*'): string {
  if (!str) return '';
  if ((str).length <= visibleChars) return str;
  
  const start = Math.floor(visibleChars / 2);
  const end = (str).length - Math.ceil(visibleChars / 2);
  const masked = (maskChar).repeat(end - start);
  
  return (str).substring(0, start) + masked + (str).substring(end);
}

/**
 * Remove all non-alphanumeric characters from a string
 * @param str String to clean
 * @returns Cleaned string
 */
export function alphanumeric(str: string): string {
  return (str).replace(/[^a-zA-Z0-9]/g, '');
}

/**
 * Format a string by replacing placeholders with values
 * @param template Template string with {placeholder} syntax
 * @param values Object with values to replace placeholders
 * @returns Formatted string
 */
export function format(template: string, values: Record<string, unknown>): string {
  return (template).replace(/{([^}]+)}/g, (match, key)  =>  {
    return values[key] !== undefined ? String(values[key]) : match;
  });
}