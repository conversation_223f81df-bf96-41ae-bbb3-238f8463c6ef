/**
 * Risk Rule Engine
 *
 * Evaluates business rules for fraud detection.
 */

import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import {
  IRiskRule,
  TransactionContext,
  FraudDetectionConfig,
  FraudDetectionError,
} from '../core/FraudDetectionTypes';
import { logger as Importedlogger } from '../../../lib/logger';

/**
 * High-risk country rule
 */
export class HighRiskCountryRule implements IRiskRule {
  getName(): string {
    return 'high_risk_country_rule';
  }

  getDescription(): string {
    return 'Blocks transactions from high-risk countries';
  }

  async evaluate(context: TransactionContext, config: FraudDetectionConfig): Promise<boolean> {
    try {
      // Get country from IP address (simplified)
      const country = this.getCountryFromIP((context).ipAddress);

      if (!country) return false;

      const isHighRisk = (config).highRiskCountries.includes(country);

      if (isHighRisk) {
        logger.warn(`Transaction from high-risk country: ${country}`, {
          transactionId: (context).transaction.id,
          ipAddress: (context).ipAddress,
        });
      }

      return isHighRisk;
    } catch (error) {
      logger.error('Error evaluating high-risk country rule:', error);
      return false;
    }
  }

  private getCountryFromIP(ipAddress: string): string | null {
    // This would use a GeoIP service
    // For now, return null (simplified)
    return null;
  }
}

/**
 * Blacklist rule
 */
export class BlacklistRule implements IRiskRule {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  getName(): string {
    return 'blacklist_rule';
  }

  getDescription(): string {
    return 'Blocks transactions from blacklisted entities';
  }

  async evaluate(context: TransactionContext, config: FraudDetectionConfig): Promise<boolean> {
    try {
      const { ipAddress, customerEmail, deviceId } = context;

      // Check multiple blacklist categories
      const checks = await Promise.all([
        this.checkIPBlacklist(ipAddress),
        this.checkEmailBlacklist(customerEmail ?? ''),
        this.checkDeviceBlacklist(deviceId),
      ]);

      const isBlacklisted = (checks).some((check)  =>  check);

      if (isBlacklisted) {
        logger.warn('Transaction from blacklisted entity', {
          transactionId: (context).transaction.id,
          ipAddress,
          customerEmail,
          deviceId,
        });
      }

      return isBlacklisted;
    } catch (error) {
      logger.error('Error evaluating blacklist rule:', error);
      return false;
    }
  }

  private async checkIPBlacklist(ipAddress: string): Promise<boolean> {
    try {
      // Blacklist functionality would be implemented with proper schema
      // For now, return false as no blacklist table exists
      return false;
    } catch (error) {
      logger.error('Error checking IP blacklist:', error);
      return false;
    }
  }

  private async checkEmailBlacklist(email: string): Promise<boolean> {
    if (!email) return false;

    try {
      // Blacklist functionality would be implemented with proper schema
      // For now, return false as no blacklist table exists
      return false;
    } catch (error) {
      logger.error('Error checking email blacklist:', error);
      return false;
    }
  }

  private async checkDeviceBlacklist(deviceId: string): Promise<boolean> {
    if (!deviceId) return false;

    try {
      // Blacklist functionality would be implemented with proper schema
      // For now, return false as no blacklist table exists
      return false;
    } catch (error) {
      logger.error('Error checking device blacklist:', error);
      return false;
    }
  }
}

/**
 * Suspicious amount pattern rule
 */
export class SuspiciousAmountPatternRule implements IRiskRule {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  getName(): string {
    return 'suspicious_amount_pattern_rule';
  }

  getDescription(): string {
    return 'Detects suspicious amount patterns like card testing';
  }

  async evaluate(context: TransactionContext, config: FraudDetectionConfig): Promise<boolean> {
    try {
      const { transaction, merchant } = context;
      const amount = transaction.amount;

      // Check for card testing patterns
      const isCardTesting = await this.checkCardTestingPattern(merchant.id, amount);

      // Check for amount progression patterns
      const isAmountProgression = await this.checkAmountProgressionPattern(merchant.id, amount);

      const isSuspicious = isCardTesting || isAmountProgression;

      if (isSuspicious) {
        logger.warn('Suspicious amount pattern detected', {
          transactionId: transaction.id,
          amount,
          merchantId: merchant.id,
          isCardTesting,
          isAmountProgression,
        });
      }

      return isSuspicious;
    } catch (error) {
      logger.error('Error evaluating suspicious amount pattern rule:', error);
      return false;
    }
  }

  private async checkCardTestingPattern(merchantId: string, amount: number): Promise<boolean> {
    try {
      // Common card testing amounts
      const testingAmounts = [1, 5, 10, 25, 50, 100];

      if (!(testingAmounts).includes(amount)) return false;

      // Check if there have been multiple small transactions recently
      const timeWindow = new Date(Date.now() - 30 * 60 * 1000); // 30 minutes

      const recentSmallTransactions = await this.prisma.transaction.count({
        where: {
          merchantId,
          amount: { in: testingAmounts },
          createdAt: { gte: timeWindow },
          status: { not: 'FAILED' },
        },
      });

      // If there are 3 or more small transactions in 30 minutes, it's suspicious
      return recentSmallTransactions >= 3;
    } catch (error) {
      logger.error('Error checking card testing pattern:', error);
      return false;
    }
  }

  private async checkAmountProgressionPattern(
    merchantId: string,
    amount: number
  ): Promise<boolean> {
    try {
      // Get recent transactions to check for progression
      const timeWindow = new Date(Date.now() - 60 * 60 * 1000); // 1 hour

      const recentTransactions = await this.prisma.transaction.findMany({
        where: {
          merchantId,
          createdAt: { gte: timeWindow },
          status: { not: 'FAILED' },
        },
        select: {
          amount: true,
          createdAt: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      if ((recentTransactions).length < 3) return false;

      // Check if amounts are progressively increasing
      let isProgression = true;
      for (let i = 1; i < (recentTransactions).length; i++) {
        if (recentTransactions[i].amount <= recentTransactions[i - 1].amount) {
          isProgression = false;
          break;
        }
      }

      // Also check if the progression is too rapid (suspicious)
      if (isProgression) {
        const firstAmount = recentTransactions[0].amount;
        const lastAmount = recentTransactions[(recentTransactions).length - 1].amount;
        const ratio = lastAmount / firstAmount;

        // If the amount increased by more than 10x in an hour, it's suspicious
        return ratio > 10;
      }

      return false;
    } catch (error) {
      logger.error('Error checking amount progression pattern:', error);
      return false;
    }
  }
}

/**
 * Time-based rule (transactions at unusual hours)
 */
export class UnusualTimeRule implements IRiskRule {
  getName(): string {
    return 'unusual_time_rule';
  }

  getDescription(): string {
    return 'Flags transactions at unusual hours';
  }

  async evaluate(context: TransactionContext, config: FraudDetectionConfig): Promise<boolean> {
    try {
      const { transaction } = context;
      const hour = transaction.createdAt.getHours();

      // Flag transactions between 2 AM and 6 AM as potentially suspicious
      const isUnusualHour = hour >= 2 && hour <= 6;

      // Also check if it's a weekend (additional risk factor)
      const dayOfWeek = transaction.createdAt.getDay();
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;

      // Only flag if it's both unusual hour AND weekend, or very late/early
      const isSuspicious = (isUnusualHour && isWeekend) || (hour >= 1 && hour <= 4);

      if (isSuspicious) {
        logger.info('Transaction at unusual time', {
          transactionId: transaction.id,
          hour,
          dayOfWeek,
          timestamp: transaction.createdAt,
        });
      }

      return isSuspicious;
    } catch (error) {
      logger.error('Error evaluating unusual time rule:', error);
      return false;
    }
  }
}

/**
 * Risk rule engine that manages and executes all rules
 */
export class RiskRuleEngine {
  private rules: Map<string, IRiskRule>;
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.rules = new Map();

    // Register default rules
    this.registerDefaultRules();
  }

  /**
   * Register a risk rule
   */
  registerRule(rule: IRiskRule): void {
    this.rules.set((rule).getName(), rule);
    logger.info(`Registered risk rule: ${(rule).getName()}`);
  }

  /**
   * Evaluate all rules
   */
  async evaluateAllRules(
    context: TransactionContext,
    config: FraudDetectionConfig
  ): Promise<{ ruleName: string; triggered: boolean; description: string }[]> {
    const results: { ruleName: string; triggered: boolean; description: string }[] = [];

    for (const [name, rule] of Array.from(this.rules.entries())) {
      try {
        const triggered = await (rule).evaluate(context, config);
        (results).push({
          ruleName: name,
          triggered,
          description: (rule).getDescription(),
        });

        if (triggered) {
          logger.warn(`Risk rule triggered: ${name}`, {
            transactionId: (context).transaction.id,
            rule: name,
          });
        }
      } catch (error) {
        logger.error(`Error evaluating rule ${name}:`, error);
        (results).push({
          ruleName: name,
          triggered: false,
          description: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }

    return results;
  }

  /**
   * Get all registered rules
   */
  getRules(): { name: string; description: string }[] {
    return Array.from(this.rules.values()).map((rule)  =>  ({
      name: (rule).getName(),
      description: (rule).getDescription(),
    }));
  }

  /**
   * Register default rules
   */
  private registerDefaultRules(): void {
    this.registerRule(new HighRiskCountryRule());
    this.registerRule(new BlacklistRule(this.prisma));
    this.registerRule(new SuspiciousAmountPatternRule(this.prisma));
    this.registerRule(new UnusualTimeRule());
  }
}
