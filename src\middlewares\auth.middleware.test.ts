// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import { 
    authenticate, 
    requestIdMiddleware,
    isAdmin,
    isSuper<PERSON>dmin,
    isMerchant,
    isMerchantOrAdmin,
    authorize,
    isResourceOwner
} from "./auth.middleware";
import { verifyToken } from "../utils/jwt.utils";
import { AppError } from "./error.middleware";
import { logger } from "../lib/logger";
import { Merchant } from '../types';
import { Middleware } from '../types/express';
import { 
    authenticate, 
    requestIdMiddleware,
    isAdmin,
    isSuperAdmin,
    isMerchant,
    isMerchantOrAdmin,
    authorize,
    isResourceOwner
} from "./auth.middleware";
import { verifyToken } from "../utils/jwt.utils";
import { AppError } from "./error.middleware";
import { logger } from "../lib/logger";
import { Merchant } from '../types';
import { Middleware } from '../types/express';



// Mock dependencies
jest.mock("../utils/jwt.utils", () => ({
    verifyToken: jest.fn()
}));

jest.mock("../lib/logger", () => ({
    logger: { error: jest.fn(),
        warn: jest.fn(),
        info: jest.fn(),
        debug: jest.fn()
    }
}));

describe("Auth Middleware", () => {
    let mockRequest: Partial<Request>;
    let mockResponse: Partial<Response>;
    let nextFunction: NextFunction;

    beforeEach(() => {
        mockRequest = {
            headers: {},
            path: "/test",
            method: "GET",
            ip: "127.0.0.1"
        };
    
        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn(),
            setHeader: jest.fn()
        };
    
        nextFunction = jest.fn();
    
        jest.clearAllMocks();
    });

    describe("requestIdMiddleware", () => {
        it("should add a request ID to the request and response headers", () => {
            requestIdMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(mockRequest.requestId).toBeDefined();
            expect(mockResponse.setHeader).toHaveBeenCalledWith("X-Request-ID", mockRequest.requestId);
            expect(nextFunction).toHaveBeenCalled();
        });
    });

    describe("authenticate", () => {
        it("should authenticate a valid token", () => {
            // Mock request with authorization header
            mockRequest.headers = {
                authorization: "Bearer valid-token"
            };
      
            // Mock verifyToken to return a valid user
            const mockUser: unknown = { userId: "user123", role: "MERCHANT" };
            (verifyToken as jest.Mock).mockReturnValue(mockUser);
      
            authenticate(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(verifyToken).toHaveBeenCalledWith("valid-token");
            expect(mockRequest.user).toEqual(mockUser);
            expect(logger.debug).toHaveBeenCalled();
            expect(nextFunction).toHaveBeenCalled();
        });

        it("should throw an error if no authorization header is provided", () => {
            authenticate(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalledWith(expect.any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "Authentication required. Please log in.",
                    statusCode: 401
                })
            );
        });

        it("should throw an error if the authorization header is invalid", () => {
            mockRequest.headers = {
                authorization: "InvalidHeader"
            };
      
            authenticate(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalledWith(expect.any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "Authentication required. Please log in.",
                    statusCode: 401
                })
            );
        });

        it("should handle token verification errors", () => {
            mockRequest.headers = {
                authorization: "Bearer invalid-token"
            };
      
            // Mock verifyToken to throw an error
            (verifyToken as jest.Mock).mockImplementation(() => {
                throw new Error("Invalid token");
            });
      
            authenticate(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(verifyToken).toHaveBeenCalledWith("invalid-token");
            expect(logger.error).toHaveBeenCalled();
            expect(nextFunction).toHaveBeenCalledWith(expect.any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "Invalid or expired token. Please log in again.",
                    statusCode: 401
                })
            );
        });
    });

    describe("isAdmin", () => {
        it("should allow admin users", () => {
            mockRequest.user = { userId: "admin123", role: "ADMIN" };
      
            isAdmin(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).not.toHaveBeenCalledWith(expect.any(AppError));
        });

        it("should reject non-admin users", () => {
            mockRequest.user = { userId: "user123", role: "MERCHANT" };
      
            isAdmin(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(logger.warn).toHaveBeenCalled();
            expect(nextFunction).toHaveBeenCalledWith(expect.any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "Admin access required.",
                    statusCode: 403
                })
            );
        });

        it("should reject unauthenticated users", () => {
            isAdmin(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalledWith(expect.any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "Authentication required. Please log in.",
                    statusCode: 401
                })
            );
        });
    });

    describe("isMerchant", () => {
        it("should allow merchant users", () => {
            mockRequest.user = { userId: "merchant123", role: "MERCHANT" };
      
            isMerchant(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).not.toHaveBeenCalledWith(expect.any(AppError));
        });

        it("should reject non-merchant users", () => {
            mockRequest.user = { userId: "admin123", role: "ADMIN" };
      
            isMerchant(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(logger.warn).toHaveBeenCalled();
            expect(nextFunction).toHaveBeenCalledWith(expect.any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "Merchant access required.",
                    statusCode: 403
                })
            );
        });
    });

    describe("isMerchantOrAdmin", () => {
        it("should allow merchant users", () => {
            mockRequest.user = { userId: "merchant123", role: "MERCHANT" };
      
            isMerchantOrAdmin(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).not.toHaveBeenCalledWith(expect.any(AppError));
        });

        it("should allow admin users", () => {
            mockRequest.user = { userId: "admin123", role: "ADMIN" };
      
            isMerchantOrAdmin(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).not.toHaveBeenCalledWith(expect.any(AppError));
        });

        it("should allow super admin users", () => {
            mockRequest.user = { userId: "superadmin123", role: "SUPER_ADMIN" };
      
            isMerchantOrAdmin(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).not.toHaveBeenCalledWith(expect.any(AppError));
        });

        it("should reject other users", () => {
            mockRequest.user = { userId: "user123", role: "USER" };
      
            isMerchantOrAdmin(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(logger.warn).toHaveBeenCalled();
            expect(nextFunction).toHaveBeenCalledWith(expect.any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "Merchant or Admin access required.",
                    statusCode: 403
                })
            );
        });
    });

    describe("authorize", () => {
        it("should allow users with the specified role", () => {
            mockRequest.user = { userId: "user123", role: "MERCHANT" };
      
            const authorizeMiddleware: unknown =authorize(["MERCHANT", "ADMIN"]);
            authorizeMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).not.toHaveBeenCalledWith(expect.any(AppError));
        });

        it("should reject users without the specified role", () => {
            mockRequest.user = { userId: "user123", role: "USER" };
      
            const authorizeMiddleware: unknown =authorize(["MERCHANT", "ADMIN"]);
            authorizeMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(logger.warn).toHaveBeenCalled();
            expect(nextFunction).toHaveBeenCalledWith(expect.any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "You do not have permission to perform this action.",
                    statusCode: 403
                })
            );
        });
    });

    describe("isResourceOwner", () => {
        it("should allow resource owners", async () => {
            mockRequest.user = { userId: "user123", role: "MERCHANT" };
      
            const getResourceOwnerId: unknown =jest.fn().mockResolvedValue("user123");
            const resourceOwnerMiddleware: unknown =isResourceOwner(getResourceOwnerId);
      
            await resourceOwnerMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(getResourceOwnerId).toHaveBeenCalledWith(mockRequest);
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).not.toHaveBeenCalledWith(expect.any(AppError));
        });

        it("should allow admins to access any resource", async () => {
            mockRequest.user = { userId: "admin123", role: "ADMIN" };
      
            const getResourceOwnerId: unknown =jest.fn().mockResolvedValue("user123");
            const resourceOwnerMiddleware: unknown =isResourceOwner(getResourceOwnerId);
      
            await resourceOwnerMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(getResourceOwnerId).toHaveBeenCalledWith(mockRequest);
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).not.toHaveBeenCalledWith(expect.any(AppError));
        });

        it("should reject non-owners and non-admins", async () => {
            mockRequest.user = { userId: "user456", role: "MERCHANT" };
      
            const getResourceOwnerId: unknown =jest.fn().mockResolvedValue("user123");
            const resourceOwnerMiddleware: unknown =isResourceOwner(getResourceOwnerId);
      
            await resourceOwnerMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(getResourceOwnerId).toHaveBeenCalledWith(mockRequest);
            expect(logger.warn).toHaveBeenCalled();
            expect(nextFunction).toHaveBeenCalledWith(expect.any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: "You do not have permission to access this resource.",
                    statusCode: 403
                })
            );
        });
    });
});
