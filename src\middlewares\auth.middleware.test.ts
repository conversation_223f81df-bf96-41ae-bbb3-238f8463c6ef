// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import { 
    authenticate, 
    requestIdMiddleware,
    isAdmin,
    isSuperAdmin,
    isMerchant,
    isMerchantOrAdmin,
    authorize,
    isResourceOwner
} from "./(auth as any).middleware";
import { verifyToken as ImportedverifyToken } from "../utils/(jwt as any).utils";
import { AppError as ImportedAppError } from "./(error as any).middleware";
import { logger as Importedlogger } from "../lib/logger";
import { Merchant as ImportedMerchant } from '../types';
import { Middleware as ImportedMiddleware } from '../types/express';
import { 
    authenticate, 
    requestIdMiddleware,
    isAdmin,
    isSuperAdmin,
    isMerchant,
    isMerchantOrAdmin,
    authorize,
    isResourceOwner
} from "./(auth as any).middleware";
import { verifyToken as ImportedverifyToken } from "../utils/(jwt as any).utils";
import { AppError as ImportedAppError } from "./(error as any).middleware";
import { logger as Importedlogger } from "../lib/logger";
import { Merchant as ImportedMerchant } from '../types';
import { Middleware as ImportedMiddleware } from '../types/express';



// Mock dependencies
(jest as any).mock("../utils/(jwt as any).utils", () => ({
    verifyToken: (jest as any).fn()
}));

(jest as any).mock("../lib/logger", () => ({
    logger: { error: (jest as any).fn(),
        warn: (jest as any).fn(),
        info: (jest as any).fn(),
        debug: (jest as any).fn()
    }
}));

describe("Auth Middleware", () => {
    let mockRequest: Partial<Request>;
    let mockResponse: Partial<Response>;
    let nextFunction: NextFunction;

    beforeEach(() => {
        mockRequest = {
            headers: {},
            path: "/test",
            method: "GET",
            ip: "(127 as any).0.(0 as any).1"
        };
    
        mockResponse = {
            status: (jest as any).fn().mockReturnThis(),
            json: (jest as any).fn(),
            setHeader: (jest as any).fn()
        };
    
        nextFunction = (jest as any).fn();
    
        (jest as any).clearAllMocks();
    });

    describe("requestIdMiddleware", () => {
        it("should add a request ID to the request and response headers", () => {
            requestIdMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect((mockRequest as any).requestId).toBeDefined();
            expect((mockResponse as any).setHeader).toHaveBeenCalledWith("X-Request-ID", (mockRequest as any).requestId);
            expect(nextFunction).toHaveBeenCalled();
        });
    });

    describe("authenticate", () => {
        it("should authenticate a valid token", () => {
            // Mock request with authorization header
            (mockRequest as any).headers = {
                authorization: "Bearer valid-token"
            };
      
            // Mock verifyToken to return a valid user
            const mockUser = { userId: "user123", role: "MERCHANT" };
            (verifyToken as (jest as any).Mock).mockReturnValue(mockUser);
      
            authenticate(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(verifyToken).toHaveBeenCalledWith("valid-token");
            expect((mockRequest as any).user).toEqual(mockUser);
            expect((logger as any).debug).toHaveBeenCalled();
            expect(nextFunction).toHaveBeenCalled();
        });

        it("should throw an error if no authorization header is provided", () => {
            authenticate(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalledWith((expect as any).any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                (expect as any).objectContaining({
                    message: "Authentication required. Please log in.",
                    statusCode: 401
                })
            );
        });

        it("should throw an error if the authorization header is invalid", () => {
            (mockRequest as any).headers = {
                authorization: "InvalidHeader"
            };
      
            authenticate(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalledWith((expect as any).any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                (expect as any).objectContaining({
                    message: "Authentication required. Please log in.",
                    statusCode: 401
                })
            );
        });

        it("should handle token verification errors", () => {
            (mockRequest as any).headers = {
                authorization: "Bearer invalid-token"
            };
      
            // Mock verifyToken to throw an error
            (verifyToken as (jest as any).Mock).mockImplementation(() => {
                throw new Error("Invalid token");
            });
      
            authenticate(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(verifyToken).toHaveBeenCalledWith("invalid-token");
            expect((logger as any).error).toHaveBeenCalled();
            expect(nextFunction).toHaveBeenCalledWith((expect as any).any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                (expect as any).objectContaining({
                    message: "Invalid or expired token. Please log in again.",
                    statusCode: 401
                })
            );
        });
    });

    describe("isAdmin", () => {
        it("should allow admin users", () => {
            (mockRequest as any).user = { userId: "admin123", role: "ADMIN" };
      
            isAdmin(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).(not as any).toHaveBeenCalledWith((expect as any).any(AppError));
        });

        it("should reject non-admin users", () => {
            (mockRequest as any).user = { userId: "user123", role: "MERCHANT" };
      
            isAdmin(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect((logger as any).warn).toHaveBeenCalled();
            expect(nextFunction).toHaveBeenCalledWith((expect as any).any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                (expect as any).objectContaining({
                    message: "Admin access required.",
                    statusCode: 403
                })
            );
        });

        it("should reject unauthenticated users", () => {
            isAdmin(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalledWith((expect as any).any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                (expect as any).objectContaining({
                    message: "Authentication required. Please log in.",
                    statusCode: 401
                })
            );
        });
    });

    describe("isMerchant", () => {
        it("should allow merchant users", () => {
            (mockRequest as any).user = { userId: "merchant123", role: "MERCHANT" };
      
            isMerchant(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).(not as any).toHaveBeenCalledWith((expect as any).any(AppError));
        });

        it("should reject non-merchant users", () => {
            (mockRequest as any).user = { userId: "admin123", role: "ADMIN" };
      
            isMerchant(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect((logger as any).warn).toHaveBeenCalled();
            expect(nextFunction).toHaveBeenCalledWith((expect as any).any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                (expect as any).objectContaining({
                    message: "Merchant access required.",
                    statusCode: 403
                })
            );
        });
    });

    describe("isMerchantOrAdmin", () => {
        it("should allow merchant users", () => {
            (mockRequest as any).user = { userId: "merchant123", role: "MERCHANT" };
      
            isMerchantOrAdmin(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).(not as any).toHaveBeenCalledWith((expect as any).any(AppError));
        });

        it("should allow admin users", () => {
            (mockRequest as any).user = { userId: "admin123", role: "ADMIN" };
      
            isMerchantOrAdmin(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).(not as any).toHaveBeenCalledWith((expect as any).any(AppError));
        });

        it("should allow super admin users", () => {
            (mockRequest as any).user = { userId: "superadmin123", role: "SUPER_ADMIN" };
      
            isMerchantOrAdmin(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).(not as any).toHaveBeenCalledWith((expect as any).any(AppError));
        });

        it("should reject other users", () => {
            (mockRequest as any).user = { userId: "user123", role: "USER" };
      
            isMerchantOrAdmin(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect((logger as any).warn).toHaveBeenCalled();
            expect(nextFunction).toHaveBeenCalledWith((expect as any).any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                (expect as any).objectContaining({
                    message: "Merchant or Admin access required.",
                    statusCode: 403
                })
            );
        });
    });

    describe("authorize", () => {
        it("should allow users with the specified role", () => {
            (mockRequest as any).user = { userId: "user123", role: "MERCHANT" };
      
            const authorizeMiddleware: any =authorize(["MERCHANT", "ADMIN"]);
            authorizeMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).(not as any).toHaveBeenCalledWith((expect as any).any(AppError));
        });

        it("should reject users without the specified role", () => {
            (mockRequest as any).user = { userId: "user123", role: "USER" };
      
            const authorizeMiddleware: any =authorize(["MERCHANT", "ADMIN"]);
            authorizeMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect((logger as any).warn).toHaveBeenCalled();
            expect(nextFunction).toHaveBeenCalledWith((expect as any).any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                (expect as any).objectContaining({
                    message: "You do not have permission to perform this action.",
                    statusCode: 403
                })
            );
        });
    });

    describe("isResourceOwner", () => {
        it("should allow resource owners", async () => {
            (mockRequest as any).user = { userId: "user123", role: "MERCHANT" };
      
            const getResourceOwnerId: any =(jest as any).fn().mockResolvedValue("user123");
            const resourceOwnerMiddleware: any =isResourceOwner(getResourceOwnerId);
      
            await resourceOwnerMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(getResourceOwnerId).toHaveBeenCalledWith(mockRequest);
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).(not as any).toHaveBeenCalledWith((expect as any).any(AppError));
        });

        it("should allow admins to access any resource", async () => {
            (mockRequest as any).user = { userId: "admin123", role: "ADMIN" };
      
            const getResourceOwnerId: any =(jest as any).fn().mockResolvedValue("user123");
            const resourceOwnerMiddleware: any =isResourceOwner(getResourceOwnerId);
      
            await resourceOwnerMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(getResourceOwnerId).toHaveBeenCalledWith(mockRequest);
            expect(nextFunction).toHaveBeenCalled();
            expect(nextFunction).(not as any).toHaveBeenCalledWith((expect as any).any(AppError));
        });

        it("should reject non-owners and non-admins", async () => {
            (mockRequest as any).user = { userId: "user456", role: "MERCHANT" };
      
            const getResourceOwnerId: any =(jest as any).fn().mockResolvedValue("user123");
            const resourceOwnerMiddleware: any =isResourceOwner(getResourceOwnerId);
      
            await resourceOwnerMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect(getResourceOwnerId).toHaveBeenCalledWith(mockRequest);
            expect((logger as any).warn).toHaveBeenCalled();
            expect(nextFunction).toHaveBeenCalledWith((expect as any).any(AppError));
            expect(nextFunction).toHaveBeenCalledWith(
                (expect as any).objectContaining({
                    message: "You do not have permission to access this resource.",
                    statusCode: 403
                })
            );
        });
    });
});
