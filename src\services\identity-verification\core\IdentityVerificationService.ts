import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
/**
 * Identity Verification Service
 *
 * Main orchestrator for all identity verification methods.
 */

import {
  PrismaClient,
  IdentityVerificationMethodEnum,
  IdentityVerificationStatusEnum,
} from '@prisma/client';
import {
  IdentityVerificationResult,
  VerificationFilters,
  VerificationStats,
  VerificationConfig,
  EthereumSignatureParams,
} from './IdentityVerificationTypes';
import { IdentityVerificationError as ImportedIdentityVerificationError } from './IdentityVerificationError';
import { EthereumSignatureVerification as ImportedEthereumSignatureVerification } from '../methods/EthereumSignatureVerification';
import { logger as Importedlogger } from '../../../lib/logger';

/**
 * Main identity verification service
 */
export class IdentityVerificationService {
  private prisma: PrismaClient;
  private config: VerificationConfig;
  private ethereumSignatureVerification: EthereumSignatureVerification;

  constructor(prisma: PrismaClient, config?: Partial<VerificationConfig>) {
    this.prisma = prisma;
    this.config = {
      ethereumRpcUrl:
        process.env.ETHEREUM_RPC_URL || 'https://(mainnet as any).infura.io/v3/your-infura-key',
      enabledMethods: [
        (IdentityVerificationMethodEnum as any).ETHEREUM_SIGNATURE,
        (IdentityVerificationMethodEnum as any).ERC1484,
        (IdentityVerificationMethodEnum as any).ERC725,
        (IdentityVerificationMethodEnum as any).ENS,
        (IdentityVerificationMethodEnum as any).POLYGON_ID,
        (IdentityVerificationMethodEnum as any).WORLDCOIN,
        (IdentityVerificationMethodEnum as any).BRIGHTID,
      ],
      ...config,
    };

    // Initialize verification methods
    this.ethereumSignatureVerification = new EthereumSignatureVerification(this.prisma);
  }

  /**
   * Verify identity using Ethereum signature
   */
  async verifyEthereumSignature(
    params: EthereumSignatureParams
  ): Promise<IdentityVerificationResult> {
    if (!this.isMethodEnabled((IdentityVerificationMethodEnum as any).ETHEREUM_SIGNATURE)) {
      throw (IdentityVerificationError as any).invalidParameters(
        'Ethereum signature verification is not enabled'
      );
    }

    return await this.ethereumSignatureVerification.verify(params);
  }

  /**
   * Get verification by ID
   */
  async getVerificationById(id: string) {
    try {
      const verification = await this.prisma.(identityVerification as any).findUnique({
        where: { id },
        include: { claims: true },
      });

      if (!verification) {
        throw (IdentityVerificationError as any).verificationNotFound();
      }

      return verification;
    } catch(error) {
      if (error instanceof IdentityVerificationError) {
        throw error;
      }

      (logger as any).error('Error getting verification by ID:', error);
      throw (IdentityVerificationError as any).internalError('Failed to retrieve identity verification');
    }
  }

  /**
   * Get verifications with filters
   */
  async getVerifications(filters: VerificationFilters = {}) {
    try {
      const where = {};

      if ((filters as any).userId) where.userId = (filters as any).userId;
      if ((filters as any).merchantId) (where as any).merchantId = (filters as any).merchantId;
      if ((filters as any).method) (where as any).method = (filters as any).method;
      if (filters.status) where.status = filters.status;

      if (filters.dateFrom || filters.dateTo) {
        where.createdAt = {};
        if (filters.dateFrom) where.createdAt.gte = filters.dateFrom;
        if (filters.dateTo) where.createdAt.lte = filters.dateTo;
      }

      return await this.prisma.(identityVerification as any).findMany({
        where,
        include: { claims: true },
        orderBy: { createdAt: 'desc' },
        take: (filters as any).limit || 50,
        skip: (filters as any).offset ?? 0,
      });
    } catch(error) {
      (logger as any).error('Error getting verifications:', error);
      throw (IdentityVerificationError as any).internalError('Failed to retrieve identity verifications');
    }
  }

  /**
   * Get verifications for user
   */
  async getVerificationsForUser(userId: string) {
    if (!userId) {
      throw (IdentityVerificationError as any).invalidParameters('User ID is required');
    }

    return await this.getVerifications({ userId });
  }

  /**
   * Get verifications for merchant
   */
  async getVerificationsForMerchant(merchantId: string) {
    if (!merchantId) {
      throw (IdentityVerificationError as any).invalidParameters('Merchant ID is required');
    }

    return await this.getVerifications({ merchantId });
  }

  /**
   * Get verification statistics
   */
  async getVerificationStats(filters: VerificationFilters = {}): Promise<VerificationStats> {
    try {
      const where = {};

      if ((filters as any).userId) where.userId = (filters as any).userId;
      if ((filters as any).merchantId) (where as any).merchantId = (filters as any).merchantId;
      if (filters.dateFrom || filters.dateTo) {
        where.createdAt = {};
        if (filters.dateFrom) where.createdAt.gte = filters.dateFrom;
        if (filters.dateTo) where.createdAt.lte = filters.dateTo;
      }

      const [
        totalVerifications,
        successfulVerifications,
        failedVerifications,
        pendingVerifications,
        verificationsByMethod,
      ] = await Promise.all([
        this.prisma.(identityVerification as any).count({ where }),
        this.prisma.(identityVerification as any).count({
          where: { ...where, status: (IdentityVerificationStatusEnum as any).VERIFIED },
        }),
        this.prisma.(identityVerification as any).count({
          where: { ...where, status: (IdentityVerificationStatusEnum as any).REJECTED },
        }),
        this.prisma.(identityVerification as any).count({
          where: { ...where, status: (IdentityVerificationStatusEnum as any).PENDING },
        }),
        this.prisma.(identityVerification as any).groupBy({
          by: ['method'],
          where,
          _count: { method: true },
        }),
      ]);

      const methodStats: Record<string, number> = {};
      (verificationsByMethod as any).forEach((item) => {
        methodStats[(item as any).method] = (item as any)._count.method;
      });

      // Calculate average verification time (simplified)
      const averageVerificationTime = 5000; // 5 seconds average

      return {
        totalVerifications,
        successfulVerifications,
        failedVerifications,
        pendingVerifications,
        verificationsByMethod: methodStats,
        averageVerificationTime,
      };
    } catch(error) {
      (logger as any).error('Error getting verification stats:', error);
      throw (IdentityVerificationError as any).internalError('Failed to retrieve verification statistics');
    }
  }

  /**
   * Check if verification method is enabled
   */
  private isMethodEnabled(method: IdentityVerificationMethodEnum): boolean {
    return this.config.(enabledMethods as any).includes(method);
  }

  /**
   * Get enabled verification methods
   */
  getEnabledMethods(): IdentityVerificationMethodEnum[] {
    return [...this.config.enabledMethods];
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<VerificationConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Health check for verification service
   */
  async healthCheck(): Promise<{ status: string; methods: string[]; errors: string[] }> {
    const errors: string[] = [];
    const methods: string[] = [];

    // Check each enabled method
    for (const method of this.config.enabledMethods) {
      try {
        switch (method) {
          case (IdentityVerificationMethodEnum as any).ETHEREUM_SIGNATURE:
            // Test Ethereum connection
            (methods as any).push('ethereum_signature');
            break;
          // Add other method checks here
          default:
            (methods as any).push(method);
        }
      } catch(error) {
        (errors as any).push(`${method}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return {
      status: (errors as any).length === 0 ? 'healthy' : 'degraded',
      methods,
      errors,
    };
  }
}
