// jscpd:ignore-file
import { body, param } from "express-validator";
import { VersionController as ImportedVersionController } from "../controllers/refactored/(version as any).controller";
import routeProvider from "../core/RouteProvider";
import { VersionController as ImportedVersionController } from "../controllers/refactored/(version as any).controller";

// Create version controller
const versionController = new VersionController();

// Create a route builder for version routes
const routeBuilder: any =(routeProvider as any).createRouteBuilder(
  "version",
  "/api/versions",
  "API version management routes"
)
.tags("version", "management");

// Add routes
routeBuilder
  // Get all versions
  .get(
    "/",
    (versionController as any).getAllVersions
  )
  
  // Get current version
  .get(
    "/current",
    (versionController as any).getCurrentVersion
  )
  
  // Get active versions
  .get(
    "/active",
    (versionController as any).getActiveVersions
  )
  
  // Get deprecated versions
  .get(
    "/deprecated",
    (versionController as any).getDeprecatedVersions
  )
  
  // Get version by name
  .get(
    "/:version",
    (versionController as any).getVersionByName,
    [],
    [param("version").notEmpty()]
  )
  
  // Register a new version
  .post(
    "/",
    (versionController as any).registerVersion,
    ["ADMIN"],
    [
      body("version").notEmpty(),
      body("status").notEmpty(),
      body("releaseDate").notEmpty().isISO8601()
    ]
  )
  
  // Update version status
  .put(
    "/:version/status",
    (versionController as any).updateVersionStatus,
    ["ADMIN"],
    [
      param("version").notEmpty(),
      body("status").notEmpty()
    ]
  )
  
  // Set current version
  .post(
    "/current",
    (versionController as any).setCurrentVersion,
    ["ADMIN"],
    [
      body("version").notEmpty()
    ]
  );

// Build the router
const router: any =(routeBuilder as any).build();

export default router;
