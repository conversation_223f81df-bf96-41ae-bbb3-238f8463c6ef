// jscpd:ignore-file
/**
 * Merchant Relationship Routes
 * 
 * This file defines the routes for merchant relationship management.
 */

import express from "express";
import { MerchantRelationshipController } from "../controllers/merchant-relationship.controller";
import { authMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant } from '../types';
import { authMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant } from '../types';


const router: unknown =express.Router();
const merchantRelationshipController: unknown = new MerchantRelationshipController();

/**
 * @route POST /api/merchant-relationship/merchants/:merchantId/communications
 * @desc Send communication to merchant
 * @access Private (Admin)
 */
router.post(
    "/merchants/:merchantId/communications",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    merchantRelationshipController.sendCommunication
);

/**
 * @route GET /api/merchant-relationship/merchants/:merchantId/communications
 * @desc Get merchant communications
 * @access Private (Admin, Merchant)
 */
router.get(
    "/merchants/:merchantId/communications",
    authMiddleware,
    merchantRelationshipController.getMerchantCommunications
);

/**
 * @route PUT /api/merchant-relationship/communications/:communicationId/read
 * @desc Mark communication as read
 * @access Private (Merchant)
 */
router.put(
    "/communications/:communicationId/read",
    authMiddleware,
    merchantRelationshipController.markCommunicationAsRead
);

/**
 * @route POST /api/merchant-relationship/merchants/:merchantId/support-tickets
 * @desc Create support ticket
 * @access Private (Admin, Merchant)
 */
router.post(
    "/merchants/:merchantId/support-tickets",
    authMiddleware,
    merchantRelationshipController.createSupportTicket
);

/**
 * @route GET /api/merchant-relationship/merchants/:merchantId/support-tickets
 * @desc Get merchant support tickets
 * @access Private (Admin, Merchant)
 */
router.get(
    "/merchants/:merchantId/support-tickets",
    authMiddleware,
    merchantRelationshipController.getMerchantSupportTickets
);

/**
 * @route POST /api/merchant-relationship/support-tickets/:ticketId/messages
 * @desc Add message to support ticket
 * @access Private (Admin, Merchant)
 */
router.post(
    "/support-tickets/:ticketId/messages",
    authMiddleware,
    merchantRelationshipController.addMessageToSupportTicket
);

/**
 * @route PUT /api/merchant-relationship/support-tickets/:ticketId/status
 * @desc Update support ticket status
 * @access Private (Admin)
 */
router.put(
    "/support-tickets/:ticketId/status",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    merchantRelationshipController.updateSupportTicketStatus
);

/**
 * @route POST /api/merchant-relationship/merchants/:merchantId/onboarding
 * @desc Initialize merchant onboarding
 * @access Private (Admin)
 */
router.post(
    "/merchants/:merchantId/onboarding",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    merchantRelationshipController.initializeOnboarding
);

/**
 * @route GET /api/merchant-relationship/merchants/:merchantId/onboarding
 * @desc Get merchant onboarding
 * @access Private (Admin, Merchant)
 */
router.get(
    "/merchants/:merchantId/onboarding",
    authMiddleware,
    merchantRelationshipController.getMerchantOnboarding
);

/**
 * @route PUT /api/merchant-relationship/onboarding-steps/:stepId/status
 * @desc Update onboarding step status
 * @access Private (Admin, Merchant)
 */
router.put(
    "/onboarding-steps/:stepId/status",
    authMiddleware,
    merchantRelationshipController.updateOnboardingStepStatus
);

export default router;
