// jscpd:ignore-file
/**
 * Merchant Relationship Routes
 * 
 * This file defines the routes for merchant relationship management.
 */

import express from "express";
import { MerchantRelationshipController as ImportedMerchantRelationshipController } from "../controllers/merchant-(relationship as any).controller";
import { authMiddleware as ImportedauthMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware as ImportedroleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant as ImportedMerchant } from '../types';
import { authMiddleware as ImportedauthMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware as ImportedroleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant as ImportedMerchant } from '../types';


const router: any =(express as any).Router();
const merchantRelationshipController = new MerchantRelationshipController();

/**
 * @route POST /api/merchant-relationship/merchants/:merchantId/communications
 * @desc Send communication to merchant
 * @access Private (Admin)
 */
(router as any).post(
    "/merchants/:merchantId/communications",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    (merchantRelationshipController as any).sendCommunication
);

/**
 * @route GET /api/merchant-relationship/merchants/:merchantId/communications
 * @desc Get merchant communications
 * @access Private (Admin, Merchant)
 */
(router as any).get(
    "/merchants/:merchantId/communications",
    authMiddleware,
    (merchantRelationshipController as any).getMerchantCommunications
);

/**
 * @route PUT /api/merchant-relationship/communications/:communicationId/read
 * @desc Mark communication as read
 * @access Private (Merchant)
 */
(router as any).put(
    "/communications/:communicationId/read",
    authMiddleware,
    (merchantRelationshipController as any).markCommunicationAsRead
);

/**
 * @route POST /api/merchant-relationship/merchants/:merchantId/support-tickets
 * @desc Create support ticket
 * @access Private (Admin, Merchant)
 */
(router as any).post(
    "/merchants/:merchantId/support-tickets",
    authMiddleware,
    (merchantRelationshipController as any).createSupportTicket
);

/**
 * @route GET /api/merchant-relationship/merchants/:merchantId/support-tickets
 * @desc Get merchant support tickets
 * @access Private (Admin, Merchant)
 */
(router as any).get(
    "/merchants/:merchantId/support-tickets",
    authMiddleware,
    (merchantRelationshipController as any).getMerchantSupportTickets
);

/**
 * @route POST /api/merchant-relationship/support-tickets/:ticketId/messages
 * @desc Add message to support ticket
 * @access Private (Admin, Merchant)
 */
(router as any).post(
    "/support-tickets/:ticketId/messages",
    authMiddleware,
    (merchantRelationshipController as any).addMessageToSupportTicket
);

/**
 * @route PUT /api/merchant-relationship/support-tickets/:ticketId/status
 * @desc Update support ticket status
 * @access Private (Admin)
 */
(router as any).put(
    "/support-tickets/:ticketId/status",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    (merchantRelationshipController as any).updateSupportTicketStatus
);

/**
 * @route POST /api/merchant-relationship/merchants/:merchantId/onboarding
 * @desc Initialize merchant onboarding
 * @access Private (Admin)
 */
(router as any).post(
    "/merchants/:merchantId/onboarding",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    (merchantRelationshipController as any).initializeOnboarding
);

/**
 * @route GET /api/merchant-relationship/merchants/:merchantId/onboarding
 * @desc Get merchant onboarding
 * @access Private (Admin, Merchant)
 */
(router as any).get(
    "/merchants/:merchantId/onboarding",
    authMiddleware,
    (merchantRelationshipController as any).getMerchantOnboarding
);

/**
 * @route PUT /api/merchant-relationship/onboarding-steps/:stepId/status
 * @desc Update onboarding step status
 * @access Private (Admin, Merchant)
 */
(router as any).put(
    "/onboarding-steps/:stepId/status",
    authMiddleware,
    (merchantRelationshipController as any).updateOnboardingStepStatus
);

export default router;
