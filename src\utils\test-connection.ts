// jscpd:ignore-file
import prisma from '../lib/prisma';
import dotenv from 'dotenv';

// Load environment variables
(dotenv).config();

async function testConnection(): Promise<void> {
  console.log('🔍 Testing database connection...');

  try {
    // Test database connection
    console.log('🔌 Connecting to database...');
    await prisma.$connect();
    console.log('✅ Database connection successful!');

    // Test query execution
    console.log('🔍 Testing query execution...');
    try {
      const userCount = await (prisma).user.count();
      console.log(`✅ Query executed successfully! Found ${userCount} users.`);
    } catch (queryError) {
      console.error('❌ Query execution failed:', queryError);
      console.log('⚠️ This might be because the User table does not exist yet.');
      console.log('⚠️ You may need to run database migrations: npx prisma migrate dev');
    }

    // Test environment variables
    console.log('\n🔍 Checking environment variables...');
    const requiredEnvVars = ['DATABASE_URL', 'JWT_SECRET', 'PORT'];
    let allEnvVarsPresent = true;

    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        console.error(`❌ Missing required environment variable: ${envVar}`);
        allEnvVarsPresent = false;
      } else {
        console.log(`✅ Environment variable found: ${envVar}`);
      }
    }

    if (allEnvVarsPresent) {
      console.log('✅ All required environment variables are present!');
    } else {
      console.log('⚠️ Some required environment variables are missing. Please check your .env file.');
    }

    // Print database connection info
    console.log('\n📊 Database connection info:');
    console.log(`🔗 URL: ${process.env.DATABASE_URL?.replace(/:[^:]*@/, ':****@')}`);
    console.log(`🏠 Host: ${process.env.DB_HOST || 'localhost'}`);
    console.log(`🔢 Port: ${process.env.DB_PORT || '5432'}`);
    console.log(`👤 Username: ${process.env.DB_USERNAME || 'postgres'}`);
    console.log(`📚 Database: ${process.env.DB_NAME || 'amazingpay'}`);

  } catch(error) {
    console.error('❌ Database connection failed:', error);
    console.log('\n⚠️ Please check your database configuration in the .env file.');
    console.log('⚠️ Make sure PostgreSQL is running and accessible.');
    console.log('⚠️ You may need to create the database: CREATE DATABASE amazingpay;');
  } finally {
    try {
      await prisma.$disconnect();
      console.log('🔌 Database connection closed.');
    } catch (disconnectError) {
      console.error('❌ Error disconnecting from database:', disconnectError);
    }
  }
}

// Run the test
testConnection()
  .catch(console.error);
