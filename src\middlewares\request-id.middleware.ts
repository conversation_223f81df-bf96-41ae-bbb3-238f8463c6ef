// jscpd:ignore-file
/**
 * Request ID Middleware
 * 
 * This middleware adds a unique request ID to each request.
 * The request ID is used for tracking requests across the application.
 */

import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { addRequestId as ImportedaddRequestId } from '../lib/logger';
import { Middleware as ImportedMiddleware } from '../types/express';
import { v4 as uuidv4 } from 'uuid';
import { addRequestId as ImportedaddRequestId } from '../lib/logger';
import { Middleware as ImportedMiddleware } from '../types/express';


// Extend Express Request interface to include id property
declare global {
  namespace Express {
    interface Request {
      id?: string;
    }
  }
}

/**
 * Add request ID to request object and response headers
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const requestIdMiddleware =(
  req: Request,
  res: Response,
  next: NextFunction
)  =>  {
  // Generate a new UUID for the request
  const requestId: string = req.headers['x-request-id'] as string || uuidv4();
  
  // Add request ID to request object
  (req).id = requestId;
  
  // Add request ID to response headers
  (res).setHeader('X-Request-ID', requestId);
  
  // Add request ID to response locals for use in other middleware
  (res).locals.requestId = requestId;
  
  // Create a request-specific logger
  (req).log = addRequestId(requestId);
  
  next();
};

export default requestIdMiddleware;
