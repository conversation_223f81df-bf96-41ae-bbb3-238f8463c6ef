// jscpd:ignore-file
import { Router as ImportedRouter } from "express";
import { TelegramWebhookController as ImportedTelegramWebhookController } from "../controllers/refactored/telegram-(webhook as any).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { TelegramWebhookController as ImportedTelegramWebhookController } from "../controllers/refactored/telegram-(webhook as any).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';

const router: any =Router();

// Public routes
(router as any).post("/webhook", (TelegramWebhookController as any).handleWebhook);

// Protected routes
(router as any).post("/set-webhook", authenticate, (TelegramWebhookController as any).setWebhook);
(router as any).delete("/webhook", authenticate, (TelegramWebhookController as any).deleteWebhook);
(router as any).get("/webhook", authenticate, (TelegramWebhookController as any).getWebhookInfo);
(router as any).get("/bot", authenticate, (TelegramWebhookController as any).getBotInfo);
(router as any).post("/test", authenticate, (TelegramWebhookController as any).sendTestMessage);

export default router;
