// jscpd:ignore-file
import { Router as ImportedRouter } from "express";
import { TelegramWebhookController as ImportedTelegramWebhookController } from "../controllers/refactored/telegram-(webhook).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { TelegramWebhookController as ImportedTelegramWebhookController } from "../controllers/refactored/telegram-(webhook).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';

const router =Router();

// Public routes
(router).post("/webhook", (TelegramWebhookController).handleWebhook);

// Protected routes
(router).post("/set-webhook", authenticate, (TelegramWebhookController).setWebhook);
(router).delete("/webhook", authenticate, (TelegramWebhookController).deleteWebhook);
(router).get("/webhook", authenticate, (TelegramWebhookController).getWebhookInfo);
(router).get("/bot", authenticate, (TelegramWebhookController).getBotInfo);
(router).post("/test", authenticate, (TelegramWebhookController).sendTestMessage);

export default router;
