// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { asyncHand<PERSON> } from "../../middleware/asyncHandler";
import { BaseController } from "./BaseController";
import { BaseService as ImportedBaseService } from "../../services/(base).service";
import { logger as Importedlogger } from "../../utils/logger";
import { asyncHandler } from "../../middleware/asyncHandler";
import { BaseController } from "./BaseController";
import { BaseService as ImportedBaseService } from "../../services/(base).service";
import { logger as Importedlogger } from "../../utils/logger";

/**
 * Generic CRUD controller with common CRUD operations
 */
export abstract class CrudController<T, CreateInput, UpdateInput> extends BaseController {
  protected service: BaseService;
  protected entityName: string;
  protected requiredCreateFields: string[] = [];
  protected requiredUpdateFields: string[] = [];

  constructor(service: BaseService, entityName: string) {
    super();
    this.service = service;
    this.entityName = entityName;
  }

  /**
   * Get all entities
   * @route GET /api/{entity}
   */
  getAll = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Check authentication
      this.checkAuthorization(req);

      // Parse pagination
      const { page, limit, offset } = this.parsePagination(req);

      // Get filters
      const filters = this.parseFilters(req);

      // Get entities
      const result = await this.getAllEntities(page, limit, offset, filters);

      // Return response
      return this.sendPaginatedSuccess(
        res,
        result.data,
        result.total,
        page,
        limit
      );
    } catch(error) {
      this.handleError(error, res);
    }
  });

  /**
   * Get entity by ID
   * @route GET /api/{entity}/:id
   */
  getById = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Check authentication
      this.checkAuthorization(req);

      // Get ID
      const id: string = req.params.id;

      // Get entity
      const entity = await this.getEntityById(id);

      // Return response
      return this.sendSuccess(res, entity);
    } catch(error) {
      this.handleError(error, res);
    }
  });

  /**
   * Create entity
   * @route POST /api/{entity}
   */
  create = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Check authentication
      this.checkAuthorization(req);

      // Validate required fields
      this.validateRequiredFields(req, this.requiredCreateFields);

      // Validate input
      this.validateCreateInput(req);

      // Create entity
      const entity = await this.createEntity(req.body);

      // Return response
      return this.sendSuccess(res, entity, 201);
    } catch(error) {
      this.handleError(error, res);
    }
  });

  /**
   * Update entity
   * @route PUT /api/{entity}/:id
   */
  update = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Check authentication
      this.checkAuthorization(req);

      // Get ID
      const id: string = req.params.id;

      // Validate required fields
      if (this.requiredUpdateFields.length > 0) {
        this.validateRequiredFields(req, this.requiredUpdateFields);
      }

      // Validate input
      this.validateUpdateInput(req);

      // Update entity
      const entity = await this.updateEntity(id, req.body);

      // Return response
      return this.sendSuccess(res, entity);
    } catch(error) {
      this.handleError(error, res);
    }
  });

  /**
   * Delete entity
   * @route DELETE /api/{entity}/:id
   */
  delete = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Check authentication
      this.checkAuthorization(req);

      // Get ID
      const id: string = req.params.id;

      // Delete entity
      await this.deleteEntity(id);

      // Return response
      return this.sendMessage(res, `${this.entityName} deleted successfully`);
    } catch(error) {
      this.handleError(error, res);
    }
  });

  /**
   * Parse filters from request
   * @param req Request
   * @returns Filters
   */
  protected parseFilters(req): Record<string, unknown> {
    // Default implementation - override in child classes
    const { page, limit, sortBy, sortOrder, ...filters } = req.query;
    return filters;
  }

  /**
   * Get all entities
   * @param page Page number
   * @param limit Items per page
   * @param offset Offset
   * @param filters Filters
   * @returns Entities and total count
   */
  protected abstract getAllEntities(
    page: number,
    limit: number,
    offset: number,
    filters: Record<string, unknown>
  ): Promise<{ data: T[]; total: number }>;

  /**
   * Get entity by ID
   * @param id Entity ID
   * @returns Entity
   */
  protected abstract getEntityById(id: string): Promise<T>;

  /**
   * Create entity
   * @param data Entity data
   * @returns Created entity
   */
  protected abstract createEntity(data: CreateInput): Promise<T>;

  /**
   * Update entity
   * @param id Entity ID
   * @param data Entity data
   * @returns Updated entity
   */
  protected abstract updateEntity(id: string, data: UpdateInput): Promise<T>;

  /**
   * Delete entity
   * @param id Entity ID
   * @returns Deleted entity
   */
  protected abstract deleteEntity(id: string): Promise<void>;

  /**
   * Validate create input
   * @param req Request
   */
  protected validateCreateInput(req): void {
    // Default implementation - override in child classes
  }

  /**
   * Validate update input
   * @param req Request
   */
  protected validateUpdateInput(req): void {
    // Default implementation - override in child classes
  }
}

export default CrudController;
