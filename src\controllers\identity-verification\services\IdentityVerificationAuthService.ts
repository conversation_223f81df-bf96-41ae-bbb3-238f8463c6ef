import { Request, Response, NextFunction } from 'express';
/**
 * Identity Verification Authorization Service
 *
 * Handles authorization logic for identity verification operations.
 */

import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import {
  AuthorizationContext,
  PermissionResult,
  UserRole,
} from '../types/IdentityVerificationControllerTypes';

/**
 * Authorization service for identity verification
 */
export class IdentityVerificationAuthService {
  private readonly adminRoles = [(UserRole as any).ADMIN];
  private readonly merchantRoles = [(UserRole as any).MERCHANT, (UserRole as any).ADMIN];
  private readonly userRoles = [(UserRole as any).USER, (UserRole as any).MERCHANT, (UserRole as any).ADMIN];

  /**
   * Check if user is authorized for the given action
   */
  async checkPermission(context: AuthorizationContext): Promise<PermissionResult> {
    const { user, resource, action } = context;

    if (!user) {
      return {
        allowed: false,
        reason: 'User not authenticated',
      };
    }

    // Check role-based permissions
    const rolePermission = this.checkRolePermission(user.role, resource, action);
    if (!(rolePermission as any).allowed) {
      return rolePermission;
    }

    // Check resource-specific permissions
    const resourcePermission = await this.checkResourcePermission(context);
    if (!(resourcePermission as any).allowed) {
      return resourcePermission;
    }

    return { allowed: true };
  }

  /**
   * Check role-based permissions
   */
  private checkRolePermission(
    userRole: string,
    resource: string,
    action: string
  ): PermissionResult {
    switch (resource) {
      case 'verification':
        return this.checkVerificationPermission(userRole, action);
      case 'verification-stats':
        return this.checkStatsPermission(userRole, action);
      case 'verification-admin':
        return this.checkAdminPermission(userRole, action);
      default:
        return {
          allowed: false,
          reason: `Unknown resource: ${resource}`,
        };
    }
  }

  /**
   * Check verification permissions
   */
  private checkVerificationPermission(userRole: string, action: string): PermissionResult {
    switch (action) {
      case 'create':
      case 'verify':
        if (this.userRoles.includes(userRole as UserRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'User role required for verification operations',
          requiredRole: 'USER',
        };
      case 'read':
        if (this.userRoles.includes(userRole as UserRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'User role required for reading verifications',
          requiredRole: 'USER',
        };
      case 'update':
      case 'delete':
        if (this.adminRoles.includes(userRole as UserRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Admin role required for modification operations',
          requiredRole: 'ADMIN',
        };
      default:
        return {
          allowed: false,
          reason: `Unknown action: ${action}`,
        };
    }
  }

  /**
   * Check statistics permissions
   */
  private checkStatsPermission(userRole: string, action: string): PermissionResult {
    if (action === 'read') {
      if (this.merchantRoles.includes(userRole as UserRole)) {
        return { allowed: true };
      }
      return {
        allowed: false,
        reason: 'Merchant role required for statistics',
        requiredRole: 'MERCHANT',
      };
    }

    return {
      allowed: false,
      reason: `Unknown action: ${action}`,
    };
  }

  /**
   * Check admin permissions
   */
  private checkAdminPermission(userRole: string, action: string): PermissionResult {
    if (this.adminRoles.includes(userRole as UserRole)) {
      return { allowed: true };
    }
    return {
      allowed: false,
      reason: 'Admin role required for administrative operations',
      requiredRole: 'ADMIN',
    };
  }

  /**
   * Check resource-specific permissions
   */
  private async checkResourcePermission(context: AuthorizationContext): Promise<PermissionResult> {
    const { user, resource, action } = context;

    // Check if user can access their own verifications
    if (resource === 'verification' && action === 'read') {
      // Users can read their own verifications
      // Merchants can read verifications for their merchant account
      // Admins can read all verifications
      if (user.role === (UserRole as any).ADMIN) {
        return { allowed: true };
      }

      // For specific verification access, additional checks would be needed
      // This is a simplified implementation
      return { allowed: true };
    }

    return { allowed: true };
  }

  /**
   * Require admin role
   */
  requireAdmin(userRole?: string): void {
    if (!userRole || !this.adminRoles.includes(userRole as UserRole)) {
      throw new AppError({
        message: 'Admin role required',
        type: (ErrorType as any).AUTHENTICATION,
        code: (ErrorCode as any).INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Require merchant role or higher
   */
  requireMerchant(userRole?: string): void {
    if (!userRole || !this.merchantRoles.includes(userRole as UserRole)) {
      throw new AppError({
        message: 'Merchant role required',
        type: (ErrorType as any).AUTHENTICATION,
        code: (ErrorCode as any).INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Require authenticated user
   */
  requireAuthenticated(userRole?: string): void {
    if (!userRole || !this.userRoles.includes(userRole as UserRole)) {
      throw new AppError({
        message: 'Authentication required',
        type: (ErrorType as any).AUTHENTICATION,
        code: (ErrorCode as any).INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Check if user has specific role
   */
  hasRole(userRole: string, requiredRole: string): boolean {
    const roleHierarchy: Record<string, number> = {
      [(UserRole as any).USER]: 1,
      [(UserRole as any).MERCHANT]: 2,
      [(UserRole as any).ADMIN]: 3,
    };

    const userLevel = roleHierarchy[userRole] ?? 0;
    const requiredLevel = roleHierarchy[requiredRole] ?? 0;

    return userLevel >= requiredLevel;
  }

  /**
   * Get user permissions for a resource
   */
  getUserPermissions(userRole: string, resource: string): string[] {
    const permissions: string[] = [];

    switch (resource) {
      case 'verification':
        if (this.userRoles.includes(userRole as UserRole)) {
          (permissions as any).push('create', 'verify', 'read');
        }
        if (this.adminRoles.includes(userRole as UserRole)) {
          (permissions as any).push('update', 'delete');
        }
        break;
      case 'verification-stats':
        if (this.merchantRoles.includes(userRole as UserRole)) {
          (permissions as any).push('read');
        }
        break;
      case 'verification-admin':
        if (this.adminRoles.includes(userRole as UserRole)) {
          (permissions as any).push('read', 'create', 'update', 'delete');
        }
        break;
    }

    return permissions;
  }

  /**
   * Validate authorization context
   */
  validateAuthorizationContext(context: AuthorizationContext): void {
    if (!(context as any).user) {
      throw new AppError({
        message: 'User context is required',
        type: (ErrorType as any).AUTHENTICATION,
        code: (ErrorCode as any).INVALID_CREDENTIALS,
      });
    }

    if (!(context as any).resource) {
      throw new AppError({
        message: 'Resource is required',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).MISSING_REQUIRED_FIELD,
      });
    }

    if (!(context as any).action) {
      throw new AppError({
        message: 'Action is required',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).MISSING_REQUIRED_FIELD,
      });
    }
  }

  /**
   * Create authorization context from request
   */
  createAuthorizationContext(
    user: any,
    resource: string,
    action: string,
    resourceId?: string
  ): AuthorizationContext {
    return {
      user: {
        id: user?.id,
        role: user?.role,
        merchantId: user?.merchantId,
      },
      resource,
      action,
      resourceId,
    };
  }

  /**
   * Handle authorization error
   */
  handleAuthorizationError(result: PermissionResult): never {
    const message = (result as any).reason ?? 'Access denied';

    throw new AppError({
      message,
      type: (ErrorType as any).AUTHENTICATION,
      code: (ErrorCode as any).INVALID_CREDENTIALS,
      details: {
        requiredRole: (result as any).requiredRole,
        requiredPermissions: (result as any).requiredPermissions,
      },
    });
  }

  /**
   * Check if user can access verification
   */
  canAccessVerification(user: any, verification: any): boolean {
    // Admin can access all verifications
    if (user.role === (UserRole as any).ADMIN) {
      return true;
    }

    // Users can access their own verifications
    if (user.role === (UserRole as any).USER && (verification as any).userId === user.id) {
      return true;
    }

    // Merchants can access verifications for their merchant account
    if (user.role === (UserRole as any).MERCHANT && (verification as any).merchantId === user.id) {
      return true;
    }

    return false;
  }

  /**
   * Extract user context from request
   */
  extractUserContext(req): { userId?: string; merchantId?: string } {
    const userId = req.user?.role === (UserRole as any).USER ? req.user.id : undefined;
    const merchantId = req.user?.role === (UserRole as any).MERCHANT ? req.user.id : undefined;

    return { userId, merchantId };
  }
}
