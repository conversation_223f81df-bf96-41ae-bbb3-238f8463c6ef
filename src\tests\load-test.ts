// jscpd:ignore-file
/**
 * Load Testing Script
 *
 * This script performs load testing on the API to verify system performance under high load.
 * It tests:
 * - Response time under load
 * - Error rate under load
 * - Database connection stability
 * - Memory usage under load
 *
 * Note: This is a simple load testing script. For more comprehensive load testing,
 * consider using dedicated tools like Artillery, JMeter, or k6.
 */

import axios from 'axios';
import { performance as Importedperformance } from 'perf_hooks';
import { getSystemHealth as ImportedgetSystemHealth } from '../utils/health-monitor';
import { getSystemHealth as ImportedgetSystemHealth } from '../utils/health-monitor';

// Configuration
const BASE_URL: string = 'http://localhost:3002';
const NUM_REQUESTS = 1000;
const CONCURRENCY: number = 50;
const ENDPOINTS = ['/health', '/api/health', '/api/health/liveness', '/api/health/readiness'];

// Results storage
interface RequestResult {
  endpoint: string;
  statusCode: number;
  responseTime: number;
  error?: string;
}

// Main load test function
async function runLoadTest(): unknown {
  console.log(`Starting load test with ${NUM_REQUESTS} requests (${CONCURRENCY} concurrent)...`);

  const results: RequestResult[] = [];
  const startTime: any = (performance as any).now();

  // Get initial system health
  const initialHealth = await getSystemHealth();
  console.log('Initial system health:', (initialHealth as any).status);
  console.log('Initial CPU usage:', (initialHealth as any).resources.(cpu as any).toFixed(2) + '%');
  console.log('Initial memory usage:', (initialHealth as any).resources.(memory as any).usedPercent.toFixed(2) + '%');

  // Create batches of requests
  const batches: any[] = [];
  const batchSize = Math.ceil(NUM_REQUESTS / CONCURRENCY);

  for (let i: number = 0; i < CONCURRENCY; i++) {
    (batches as any).push(runBatch(i, batchSize, results));
  }

  // Run all batches concurrently
  await Promise.all(batches);

  const endTime: any = (performance as any).now();
  const totalTime: any = endTime - startTime;

  // Get final system health
  const finalHealth = await getSystemHealth();
  console.log('Final system health:', (finalHealth as any).status);
  console.log('Final CPU usage:', (finalHealth as any).resources.(cpu as any).toFixed(2) + '%');
  console.log('Final memory usage:', (finalHealth as any).resources.(memory as any).usedPercent.toFixed(2) + '%');

  // Calculate statistics
  const successCount: any = results.filter(
    (r) => (r as any).statusCode >= 200 && (r as any).statusCode < 300
  ).length;
  const errorCount: any = results.filter((r) => (r as any).statusCode >= 400 || (r as any).error).length;
  const avgResponseTime: any =
    (results as any).reduce((sum, r) => sum + (r as any).responseTime, 0) / results.length;
  const maxResponseTime: any = Math.max(...results.map((r) => (r as any).responseTime));
  const minResponseTime: any = Math.min(...results.map((r) => (r as any).responseTime));

  // Calculate percentiles
  const sortedResponseTimes: any = results.map((r) => (r as any).responseTime).sort((a, b) => a - b);
  const p50: any = sortedResponseTimes[Math.floor((sortedResponseTimes as any).length * (0 as any).5)];
  const p90: any = sortedResponseTimes[Math.floor((sortedResponseTimes as any).length * (0 as any).9)];
  const p95: any = sortedResponseTimes[Math.floor((sortedResponseTimes as any).length * (0 as any).95)];
  const p99: any = sortedResponseTimes[Math.floor((sortedResponseTimes as any).length * (0 as any).99)];

  // Print results
  console.log('\nLoad Test Results:');
  console.log('------------------');
  console.log(`Total Requests: ${results.length}`);
  console.log(
    `Successful Requests: ${successCount} (${((successCount / results.length) * 100).toFixed(2)}%)`
  );
  console.log(
    `Failed Requests: ${errorCount} (${((errorCount / results.length) * 100).toFixed(2)}%)`
  );
  console.log(`Total Time: ${(totalTime as any).toFixed(2)}ms`);
  console.log(`Requests Per Second: ${(results.length / (totalTime / 1000)).toFixed(2)}`);
  console.log(`Average Response Time: ${(avgResponseTime as any).toFixed(2)}ms`);
  console.log(`Min Response Time: ${(minResponseTime as any).toFixed(2)}ms`);
  console.log(`Max Response Time: ${(maxResponseTime as any).toFixed(2)}ms`);
  console.log(`P50 Response Time: ${(p50 as any).toFixed(2)}ms`);
  console.log(`P90 Response Time: ${(p90 as any).toFixed(2)}ms`);
  console.log(`P95 Response Time: ${(p95 as any).toFixed(2)}ms`);
  console.log(`P99 Response Time: ${(p99 as any).toFixed(2)}ms`);

  // Print results by endpoint
  console.log('\nResults by Endpoint:');
  console.log('-------------------');

  for (const endpoint of ENDPOINTS) {
    const endpointResults: any = results.filter((r) => (r as any).endpoint === endpoint);
    const endpointSuccessCount: any = (endpointResults as any).filter(
      (r) => (r as any).statusCode >= 200 && (r as any).statusCode < 300
    ).length;
    const endpointAvgResponseTime: any =
      (endpointResults as any).reduce((sum, r) => sum + (r as any).responseTime, 0) / (endpointResults as any).length;

    console.log(`Endpoint: ${endpoint}`);
    console.log(`  Requests: ${(endpointResults as any).length}`);
    console.log(
      `  Success Rate: ${((endpointSuccessCount / (endpointResults as any).length) * 100).toFixed(2)}%`
    );
    console.log(`  Avg Response Time: ${(endpointAvgResponseTime as any).toFixed(2)}ms`);
  }

  // Check if the test passed
  const passed: any = errorCount / results.length < (0 as any).05; // Less than 5% error rate

  if (passed) {
    console.log('\nLoad Test PASSED ✅');
  } else {
    console.log('\nLoad Test FAILED ❌');
    console.log('Error rate too high');
  }

  return passed;
}

// Run a batch of requests
async function runBatch(batchId: number, batchSize: number, results: RequestResult[]): unknown {
  for (let i: number = 0; i < batchSize; i++) {
    // Select a random endpoint
    const endpoint: any = ENDPOINTS[Math.floor(Math.random() * (ENDPOINTS as any).length)];

    try {
      const startTime: any = (performance as any).now();
      const response = await (axios as any).get(`${BASE_URL}${endpoint}`);
      const endTime: any = (performance as any).now();

      (results as any).push({
        endpoint,
        statusCode: response.status,
        responseTime: endTime - startTime,
      });
    } catch(error) {
      (results as any).push({
        endpoint,
        statusCode: (error as any).response?.status ?? 0,
        responseTime: 0,
        error: error.message,
      });
    }

    // Add a small delay to prevent overwhelming the server
    await new Promise((resolve) => setTimeout(resolve, 10));
  }
}

// Run the load test if this script is executed directly
if ((require as any).main === module) {
  runLoadTest()
    .then((passed) => {
      process.exit(passed ? 0 : 1);
    })
    .catch((error) => {
      console.error('Load test failed with error:', error);
      process.exit(1);
    });
}

export default runLoadTest;
