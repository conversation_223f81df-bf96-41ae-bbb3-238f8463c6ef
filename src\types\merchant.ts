// Merchant-related types and interfaces
import { Merchant } from '../types/merchant';
import { CreateMerchantData } from '../types/merchant';
import { UpdateMerchantData } from '../types/merchant';

export interface Merchant {
  id: string;
  userId: string;
  businessName: string;
  businessType: string;
  businessCategory: string;
  businessDescription?: string;
  website?: string;
  email: string;
  phoneNumber: string;
  address: Address;
  taxId?: string;
  registrationNumber?: string;
  status: MerchantStatus;
  verificationStatus: VerificationStatus;
  isActive: boolean;
  settings: MerchantSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

export type MerchantStatus = 'PENDING' | 'ACTIVE' | 'SUSPENDED' | 'INACTIVE';
export type VerificationStatus = 'PENDING' | 'VERIFIED' | 'REJECTED' | 'REQUIRES_REVIEW';

export interface MerchantSettings {
  paymentMethods: string[];
  currencies: string[];
  webhookUrl?: string;
  notificationSettings: MerchantNotificationSettings;
  securitySettings: MerchantSecuritySettings;
}

export interface MerchantNotificationSettings {
  emailNotifications: boolean;
  smsNotifications: boolean;
  webhookNotifications: boolean;
  transactionAlerts: boolean;
  securityAlerts: boolean;
}

export interface MerchantSecuritySettings {
  ipWhitelist: string[];
  requireTwoFactor: boolean;
  sessionTimeout: number;
  allowedOrigins: string[];
}

export interface CreateMerchantData {
  businessName: string;
  businessType: string;
  businessCategory: string;
  businessDescription?: string;
  website?: string;
  email: string;
  phoneNumber: string;
  address: Address;
  taxId?: string;
  registrationNumber?: string;
}

export interface UpdateMerchantData {
  businessName?: string;
  businessType?: string;
  businessCategory?: string;
  businessDescription?: string;
  website?: string;
  email?: string;
  phoneNumber?: string;
  address?: Partial<Address>;
  taxId?: string;
  registrationNumber?: string;
}

export interface MerchantVerification {
  id: string;
  merchantId: string;
  documentType: string;
  documentUrl: string;
  status: VerificationStatus;
  reviewedBy?: string;
  reviewedAt?: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface MerchantApiKey {
  id: string;
  merchantId: string;
  name: string;
  keyHash: string;
  permissions: string[];
  isActive: boolean;
  lastUsedAt?: Date;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface MerchantWebhook {
  id: string;
  merchantId: string;
  url: string;
  events: string[];
  secret: string;
  isActive: boolean;
  lastTriggeredAt?: Date;
  failureCount: number;
  createdAt: Date;
  updatedAt: Date;
}