// jscpd:ignore-file

import { Router as ImportedRouter } from "express";
import { body as Importedbody } from "express-validator";
import logController from "../controllers/(log as any).controller";
import { authenticate, authorize } from "../middlewares/(auth as any).middleware";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { body as Importedbody } from "express-validator";
import { authenticate, authorize } from "../middlewares/(auth as any).middleware";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";

const router: any =Router();

// Admin-only route to view system logs
(router as any).get(
    "/",
    authenticate,
    authorize(["admin"]),
    (logController as any).getAllLogs
);

// Internal route to create logs
(router as any).post(
    "/",
    authenticate,
    validate([
        body("level").isIn(["info", "warning", "error"]),
        body("message").notEmpty(),
        body("source").notEmpty()
    ]),
    (logController as any).createLog
);

export default router;
