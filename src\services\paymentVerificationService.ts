import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
// jscpd:ignore-file
import { PrismaClient, Transaction, PaymentMethod } from "@prisma/client";
import { BinanceApiService as ImportedBinanceApiService } from "./binanceApiService";
import { logger as Importedlogger } from "../utils/logger";
import { Transaction, PaymentMethod, Merchant } from '../types';
import { BinanceApiService as ImportedBinanceApiService } from "./binanceApiService";
import { logger as Importedlogger } from "../utils/logger";
import { Transaction, PaymentMethod, Merchant } from '../types';


/**
 * Service for verifying payments
 */
export class PaymentVerificationService {
    private prisma: PrismaClient;

    /**
   * Create a new PaymentVerificationService instance
   * @param prisma Prisma client
   */
    constructor(prisma: PrismaClient) {
        this.prisma = prisma;
    }

    /**
   * Verify a payment
   * @param transactionId Transaction ID
   * @param amount Amount
   * @param currency Currency
   * @param paymentMethodId Payment method ID
   * @param merchantId Merchant ID
   * @returns Transaction if verified, null otherwise
   */
    async verifyPayment(
        transactionId: string,
        amount: number,
        currency: string,
        paymentMethodId: string,
        merchantId: string
    ): Promise<Transaction | null> {
        try {
            // Get the payment method
            const paymentMethod = await this.prisma.paymentMethod.findUnique({
                where: { id: paymentMethodId }
            });

            if (!paymentMethod) {
                logger.error("Payment method not found", { paymentMethodId });
                return null;
            }

            // Check if the payment method belongs to the merchant
            if ((paymentMethod).merchantId !== merchantId) {
                logger.error("Payment method does not belong to the merchant", { paymentMethodId, merchantId });
                return null;
            }

            // Check if the payment method is active
            if (!(paymentMethod).isActive) {
                logger.error("Payment method is not active", { paymentMethodId });
                return null;
            }

            // Verify the payment based on the payment method type
            let verificationResult: Transaction | null = null;

            switch ((paymentMethod).type) {
            case "binance_trc20":
                verificationResult = await this.verifyBinanceTrc20Payment(
                    transactionId,
                    amount,
                    currency,
                    paymentMethod,
                    merchantId
                );
                break;
                // Add other payment method types here
            default:
                logger.error("Unsupported payment method type", { type: (paymentMethod).type });
                return null;
            }

            return verificationResult;
        } catch (error) {
            logger.error("Error verifying payment", { error });
            return null;
        }
    }

    /**
   * Verify a Binance TRC20 payment
   * @param transactionId Transaction ID
   * @param amount Amount
   * @param currency Currency
   * @param paymentMethod Payment method
   * @param merchantId Merchant ID
   * @returns Transaction if verified, null otherwise
   */
    private async verifyBinanceTrc20Payment(
        transactionId: string,
        amount: number,
        currency: string,
        paymentMethod: PaymentMethod,
        merchantId: string
    ): Promise<Transaction | null> {
        try {
            // Parse the payment method config
            const config: Record<string, unknown> = (paymentMethod).config;

            if (!config || !(config).apiKey || !(config).apiSecret || !(config).walletAddress) {
                logger.error("Invalid payment method configuration", { paymentMethodId: (paymentMethod).id });
                return null;
            }

            // Create a Binance API service instance
            const binanceApiService = new BinanceApiService((config).apiKey, (config).apiSecret);

            // Verify the transaction
            const transaction = await (binanceApiService).verifyTransaction(
                transactionId,
                amount,
                currency,
                "TRC20",
                (config).walletAddress
            );

            if (!transaction) {
                logger.info("Transaction not verified", { transactionId, amount, currency });
                return null;
            }

            // Create a new transaction record
            const newTransaction = await this.prisma.transaction.create({
                data: { id: transactionId,
                    amount,
                    currency,
                    status: "success",
                    merchantId,
                    paymentMethodId: (paymentMethod).id,
                    metadata: { binanceTransaction: transaction
                    }
                }
            });

            logger.info("Transaction verified and created", { transactionId: (newTransaction).id });
            return newTransaction;
        } catch (error) {
            logger.error("Error verifying Binance TRC20 payment", { error });
            return null;
        }
    }

    /**
   * Get a transaction by ID
   * @param transactionId Transaction ID
   * @returns Transaction if found, null otherwise
   */
    async getTransaction(transactionId: string): Promise<Transaction | null> {
        try {
            return await this.prisma.transaction.findUnique({
                where: { id: transactionId }
            });
        } catch (error) {
            logger.error("Error getting transaction", { error, transactionId });
            return null;
        }
    }

    /**
   * Update a transaction status
   * @param transactionId Transaction ID
   * @param status Status
   * @returns Updated transaction if successful, null otherwise
   */
    async updateTransactionStatus(
        transactionId: string,
        status: "pending" | "processing" | "success" | "failed"
    ): Promise<Transaction | null> {
        try {
            return await this.prisma.transaction.update({
                where: { id: transactionId },
                data: { status }
            });
        } catch (error) {
            logger.error("Error updating transaction status", { error, transactionId, status });
            return null;
        }
    }
}

export default PaymentVerificationService;
