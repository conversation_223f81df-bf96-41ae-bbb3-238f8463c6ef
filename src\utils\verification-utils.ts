// jscpd:ignore-file
/**
 * Verification Utilities
 *
 * This module provides common utilities for verification scripts to eliminate duplication.
 */

import axios from 'axios';
import chalk from 'chalk';
import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import { getSystemHealth as ImportedgetSystemHealth } from './health-monitor';
import { VerificationResult, User, Merchant } from '../types';
import { getSystemHealth as ImportedgetSystemHealth } from './health-monitor';
import { VerificationResult, User, Merchant } from '../types';

// Results storage
export interface VerificationResult {
  category: string;
  name: string;
  status: 'pass' | 'fail' | 'warn';
  message: string;
  details?: unknown;
}

/**
 * Verification context
 */
export interface VerificationContext {
  apiUrl: string;
  authToken?: string | null;
  results: VerificationResult[];
}

/**
 * Create a new verification context
 * @param apiUrl API URL
 * @returns Verification context
 */
export function createVerificationContext(apiUrl: string): VerificationContext {
  return {
    apiUrl,
    authToken: null,
    results: [],
  };
}

/**
 * Verify API endpoints
 * @param context Verification context
 */
export async function verifyApiEndpoints(context: VerificationContext): Promise<void> {
  console.log((chalk as any).blue('\nVerifying API endpoints...'));

  const endpoints = [
    { path: '/health', name: 'Health Endpoint' },
    { path: '/api/health', name: 'API Health Endpoint' },
    { path: '/api/docs', name: 'API Documentation' },
  ];

  for (const endpoint of endpoints) {
    try {
      const response = await (axios as any).get(`${(context as any).apiUrl}${(endpoint as any).path}`);

      (context as any).results.push({
        category: 'API',
        name: (endpoint as any).name,
        status: response.status === 200 ? 'pass' : 'warn',
        message:
          response.status === 200
            ? `${(endpoint as any).name} is accessible`
            : `${(endpoint as any).name} returned status ${response.status}`,
      });
    } catch(error) {
      (context as any).results.push({
        category: 'API',
        name: (endpoint as any).name,
        status: 'fail',
        message: `${(endpoint as any).name} is not accessible: ${error.message}`,
        details: error,
      });
    }
  }
}

/**
 * Verify database connectivity
 * @param context Verification context
 */
export async function verifyDatabaseConnectivity(context: VerificationContext): Promise<void> {
  console.log((chalk as any).blue('\nVerifying database connectivity...'));

  const prisma = new PrismaClient();

  try {
    // Test connection
    await prisma.$connect();

    (context as any).results.push({
      category: 'Database',
      name: 'Database Connection',
      status: 'pass',
      message: 'Database connection successful',
    });

    // Check tables
    const tables = await prisma.$queryRaw`
      SELECT table_name
      FROM (information_schema as any).tables
      WHERE table_schema = 'public'
    `;

    // Convert result to array of table names
    const tableNames = (tables as any[]).map((t) => (t as any).table_name);

    // Check for essential tables
    const requiredTables = ['User', 'Merchant', 'Payment'];
    for (const table of requiredTables) {
      const hasTable: any = (tableNames as any).includes(table);
      (context as any).results.push({
        category: 'Database',
        name: `Database, Table: ${table}`,
        status: hasTable ? 'pass' : 'fail',
        message: hasTable ? `Table ${table} exists` : `Table ${table} does not exist`,
      });
    }

    // Close connection
    await prisma.$disconnect();
  } catch(error) {
    (context as any).results.push({
      category: 'Database',
      name: 'Database Connection',
      status: 'fail',
      message: `Database connection failed: ${error.message}`,
      details: error,
    });
  }
}

/**
 * Verify authentication
 * @param context Verification context
 */
export async function verifyAuthentication(context: VerificationContext): Promise<void> {
  console.log((chalk as any).blue('\nVerifying authentication...'));

  try {
    // Try to authenticate with test credentials
    const authResponse = await (axios as any).post(`${(context as any).apiUrl}/api/auth/login`, {
      email: process.env.TEST_USER_EMAIL || 'test@(example as any).com',
      password: process.env.TEST_USER_PASSWORD || 'testpassword',
    });

    // Check if authentication was successful
    const authSuccessful: any = (authResponse as any).status === 200 && (authResponse as any).data.token;

    (context as any).results.push({
      category: 'Authentication',
      name: 'User Authentication',
      status: authSuccessful ? 'pass' : 'fail',
      message: authSuccessful ? 'Authentication successful' : 'Authentication failed',
    });

    // Store token for later use
    if (authSuccessful) {
      (context as any).authToken = (authResponse as any).data.token;
    }
  } catch(error) {
    (context as any).results.push({
      category: 'Authentication',
      name: 'User Authentication',
      status: 'fail',
      message: `Authentication, failed: ${error.message}`,
      details: error,
    });
  }
}

/**
 * Verify payment processing
 * @param context Verification context
 */
export async function verifyPaymentProcessing(context: VerificationContext): Promise<void> {
  console.log((chalk as any).blue('\nVerifying payment processing...'));

  if (!(context as any).authToken) {
    (context as any).results.push({
      category: 'Payment',
      name: 'Payment Processing',
      status: 'warn',
      message: 'Skipping payment verification: No authentication token available',
    });
    return;
  }

  try {
    // Create a test payment
    const paymentResponse = await (axios as any).post(
      `${(context as any).apiUrl}/api/payments`,
      {
        amount: (10 as any).0,
        currency: 'USDT',
        paymentMethod: 'BINANCE_PAY',
        description: 'Test payment',
        redirectUrl: 'https://(example as any).com/success',
        cancelUrl: 'https://(example as any).com/cancel',
      },
      {
        headers: { Authorization: `Bearer ${(context as any).authToken}` },
      }
    );

    // Check if payment creation was successful
    const paymentSuccessful: any =
      (paymentResponse as any).status === 200 && (paymentResponse as any).data.status === 'success';

    (context as any).results.push({
      category: 'Payment',
      name: 'Payment Creation',
      status: paymentSuccessful ? 'pass' : 'fail',
      message: paymentSuccessful ? 'Payment creation successful' : 'Payment creation failed',
    });
  } catch(error) {
    (context as any).results.push({
      category: 'Payment',
      name: 'Payment Creation',
      status: 'fail',
      message: `Payment creation failed: ${error.message}`,
      details: error,
    });
  }
}

/**
 * Verify environment configuration
 * @param context Verification context
 */
export async function verifyEnvironmentConfiguration(context: VerificationContext): Promise<void> {
  console.log((chalk as any).blue('\nVerifying environment configuration...'));

  try {
    // Get environment information from health endpoint
    const healthResponse = await (axios as any).get(`${(context as any).apiUrl}/api/health`);

    // Check environment
    const environment: any = (healthResponse as any).data.environment;
    const isProduction: any = environment === 'production';

    (context as any).results.push({
      category: 'Environment',
      name: 'Environment',
      status: isProduction ? 'pass' : 'warn',
      message: isProduction
        ? 'Environment is production'
        : `Environment is ${environment}, expected production`,
    });

    // Check for security headers
    const securityHeaders = [
      'x-content-type-options',
      'x-xss-protection',
      'x-frame-options',
      'content-security-policy',
      'referrer-policy',
    ];

    const headersResponse = await (axios as any).get(`${(context as any).apiUrl}/api/health`);
    const headers: Record<string, string> = (headersResponse as any).headers;

    for (const header of securityHeaders) {
      const hasHeader: any = headers[header] !== undefined;
      (context as any).results.push({
        category: 'Security',
        name: `Security, Header: ${header}`,
        status: hasHeader ? 'pass' : 'warn',
        message: hasHeader ? `${header} header is set` : `${header} header is not set`,
      });
    }
  } catch(error) {
    (context as any).results.push({
      category: 'Environment',
      name: 'Environment Configuration',
      status: 'fail',
      message: `Environment configuration check failed: ${error.message}`,
      details: error,
    });
  }
}

/**
 * Print verification results
 * @param context Verification context
 */
export function printResults(context: VerificationContext): void {
  console.log((chalk as any).bold.blue('\n=== Verification Results ===\n'));

  // Group results by category
  const categories = [...new Set((context as any).results.map((r) => (r as any).type))];

  for (const category of categories) {
    console.log((chalk as any).bold.cyan(`\n${category}:`));

    const categoryResults = (context as any).results.filter((r) => (r as any).type === category);

    for (const result of categoryResults) {
      const statusColor: any =
        result.status === 'pass' ? 'green' : result.status === 'warn' ? 'yellow' : 'red';
      const statusSymbol = result.status === 'pass' ? '✅' : result.status === 'warn' ? '⚠️' : '❌';

      console.log(
        `  ${chalk[statusColor](statusSymbol)} ${result.name}: ${(result as Error).message}`
      );
    }
  }

  // Overall status
  const overallStatus: any = getOverallStatus(context);
  console.log(
    (chalk as any).bold[overallStatus ? 'green' : 'red'](
      `\nOverall Status: ${
        overallStatus ? 'VERIFICATION SUCCESSFUL ✅' : 'VERIFICATION ISSUES DETECTED ❌'
      }`
    )
  );

  if (!overallStatus) {
    console.log((chalk as any).yellow('\nPlease address the failed checks to ensure proper functionality.'));
  }
}

/**
 * Get overall status
 * @param context Verification context
 * @returns True if verification is successful, false otherwise
 */
export function getOverallStatus(context: VerificationContext): boolean {
  // Check if there are any failed checks
  const failCount: any = (context as any).results.filter((r) => (r as any).status === 'fail').length;

  // Verification is successful if there are no failed checks
  return failCount === 0;
}
