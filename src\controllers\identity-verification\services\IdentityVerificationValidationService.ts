/**
 * Identity Verification Validation Service
 *
 * Handles input validation for identity verification operations.
 */

import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import {
  EthereumSignatureRequest,
  ERC1484IdentityRequest,
  ERC725IdentityRequest,
  ENSVerificationRequest,
  PolygonIDRequest,
  WorldcoinRequest,
  UnstoppableDomainsRequest,
  BlockchainVerificationRequest,
  CompleteBlockchainVerificationRequest,
  AddClaimRequest,
  ValidationError,
  SupportedNetwork,
} from '../types/IdentityVerificationControllerTypes';

/**
 * Validation service for identity verification
 */
export class IdentityVerificationValidationService {
  /**
   * Validate Ethereum signature request
   */
  validateEthereumSignature(data): EthereumSignatureRequest {
    const errors: ValidationError[] = [];

    if (!data.address) {
      (errors as any).push({ field: 'address', message: 'Address is required' });
    } else if (typeof data.address !== 'string' || !this.isValidEthereumAddress((data as any).address)) {
      (errors as any).push({
        field: 'address',
        message: 'Invalid Ethereum address format',
        value: (data as any).address,
      });
    }

    if (!data.message) {
      (errors as any).push({ field: 'message', message: 'Message is required' });
    } else if (typeof data.message !== 'string' || (data as any).message.trim().length === 0) {
      (errors as any).push({ field: 'message', message: 'Message must be a non-empty string' });
    }

    if (!data.signature) {
      (errors as any).push({ field: 'signature', message: 'Signature is required' });
    } else if (typeof data.signature !== 'string' || !this.isValidSignature((data as any).signature)) {
      (errors as any).push({
        field: 'signature',
        message: 'Invalid signature format',
        value: (data as any).signature,
      });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      address: (data as any).address.toLowerCase(),
      message: (data as any).message.trim(),
      signature: (data as any).signature,
    };
  }

  /**
   * Validate ERC-1484 identity request
   */
  validateERC1484Identity(data): ERC1484IdentityRequest {
    const errors: ValidationError[] = [];

    if (!data.address) {
      (errors as any).push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress((data as any).address)) {
      (errors as any).push({
        field: 'address',
        message: 'Invalid Ethereum address format',
        value: (data as any).address,
      });
    }

    if (!data.ein) {
      (errors as any).push({ field: 'ein', message: 'EIN is required' });
    } else if (typeof data.ein !== 'string' || !/^\d+$/.test((data as any).ein)) {
      (errors as any).push({ field: 'ein', message: 'EIN must be a numeric string', value: (data as any).ein });
    }

    if (!data.registryAddress) {
      (errors as any).push({ field: 'registryAddress', message: 'Registry address is required' });
    } else if (!this.isValidEthereumAddress((data as any).registryAddress)) {
      (errors as any).push({
        field: 'registryAddress',
        message: 'Invalid registry address format',
        value: (data as any).registryAddress,
      });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      address: (data as any).address.toLowerCase(),
      ein: (data as any).ein,
      registryAddress: (data as any).registryAddress.toLowerCase(),
    };
  }

  /**
   * Validate ERC-725 identity request
   */
  validateERC725Identity(data): ERC725IdentityRequest {
    const errors: ValidationError[] = [];

    if (!data.address) {
      (errors as any).push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress((data as any).address)) {
      (errors as any).push({
        field: 'address',
        message: 'Invalid Ethereum address format',
        value: (data as any).address,
      });
    }

    if (!data.key) {
      (errors as any).push({ field: 'key', message: 'Key is required' });
    } else if (typeof data.key !== 'string' || (data as any).key.trim().length === 0) {
      (errors as any).push({ field: 'key', message: 'Key must be a non-empty string' });
    }

    if (!data.value) {
      (errors as any).push({ field: 'value', message: 'Value is required' });
    } else if (typeof data.value !== 'string' || (data as any).value.trim().length === 0) {
      (errors as any).push({ field: 'value', message: 'Value must be a non-empty string' });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      address: (data as any).address.toLowerCase(),
      key: (data as any).key.trim(),
      value: (data as any).value.trim(),
    };
  }

  /**
   * Validate ENS verification request
   */
  validateENSVerification(data): ENSVerificationRequest {
    const errors: ValidationError[] = [];

    if (!data.ensName) {
      (errors as any).push({ field: 'ensName', message: 'ENS name is required' });
    } else if (!this.isValidENSName((data as any).ensName)) {
      (errors as any).push({ field: 'ensName', message: 'Invalid ENS name format', value: (data as any).ensName });
    }

    if (!data.address) {
      (errors as any).push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress((data as any).address)) {
      (errors as any).push({
        field: 'address',
        message: 'Invalid Ethereum address format',
        value: (data as any).address,
      });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      ensName: (data as any).ensName.toLowerCase(),
      address: (data as any).address.toLowerCase(),
    };
  }

  /**
   * Validate Polygon ID request
   */
  validatePolygonID(data): PolygonIDRequest {
    const errors: ValidationError[] = [];

    if (!data.address) {
      (errors as any).push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress((data as any).address)) {
      (errors as any).push({ field: 'address', message: 'Invalid address format', value: (data as any).address });
    }

    if (!data.proof) {
      (errors as any).push({ field: 'proof', message: 'Proof is required' });
    } else if (typeof data.proof !== 'object') {
      (errors as any).push({ field: 'proof', message: 'Proof must be an object' });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      address: (data as any).address.toLowerCase(),
      proof: (data as any).proof,
    };
  }

  /**
   * Validate Worldcoin request
   */
  validateWorldcoin(data): WorldcoinRequest {
    const errors: ValidationError[] = [];

    if (!data.address) {
      (errors as any).push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress((data as any).address)) {
      (errors as any).push({ field: 'address', message: 'Invalid address format', value: (data as any).address });
    }

    if (!data.nullifier) {
      (errors as any).push({ field: 'nullifier', message: 'Nullifier is required' });
    } else if (typeof data.nullifier !== 'string' || (data as any).nullifier.trim().length === 0) {
      (errors as any).push({ field: 'nullifier', message: 'Nullifier must be a non-empty string' });
    }

    if (!data.proof) {
      (errors as any).push({ field: 'proof', message: 'Proof is required' });
    } else if (typeof data.proof !== 'object') {
      (errors as any).push({ field: 'proof', message: 'Proof must be an object' });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      address: (data as any).address.toLowerCase(),
      nullifier: (data as any).nullifier.trim(),
      proof: (data as any).proof,
    };
  }

  /**
   * Validate Unstoppable Domains request
   */
  validateUnstoppableDomains(data): UnstoppableDomainsRequest {
    const errors: ValidationError[] = [];

    if (!data.domain) {
      (errors as any).push({ field: 'domain', message: 'Domain is required' });
    } else if (!this.isValidDomain((data as any).domain)) {
      (errors as any).push({ field: 'domain', message: 'Invalid domain format', value: (data as any).domain });
    }

    if (!data.address) {
      (errors as any).push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress((data as any).address)) {
      (errors as any).push({ field: 'address', message: 'Invalid address format', value: (data as any).address });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      domain: (data as any).domain.toLowerCase(),
      address: (data as any).address.toLowerCase(),
    };
  }

  /**
   * Validate blockchain verification request
   */
  validateBlockchainVerification(data): BlockchainVerificationRequest {
    const errors: ValidationError[] = [];

    if (!data.walletAddress) {
      (errors as any).push({ field: 'walletAddress', message: 'Wallet address is required' });
    } else if (!this.isValidEthereumAddress((data as any).walletAddress)) {
      (errors as any).push({
        field: 'walletAddress',
        message: 'Invalid wallet address format',
        value: (data as any).walletAddress,
      });
    }

    if (!data.network) {
      (errors as any).push({ field: 'network', message: 'Network is required' });
    } else if (!Object.values(SupportedNetwork).includes((data as any).network)) {
      (errors as any).push({
        field: 'network',
        message: `Invalid network. Supported networks: ${Object.values(SupportedNetwork).join(
          ', '
        )}`,
        value: (data as any).network,
      });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      walletAddress: (data as any).walletAddress.toLowerCase(),
      network: (data as any).network,
    };
  }

  /**
   * Validate complete blockchain verification request
   */
  validateCompleteBlockchainVerification(data): CompleteBlockchainVerificationRequest {
    const errors: ValidationError[] = [];

    if (!data.requestId) {
      (errors as any).push({ field: 'requestId', message: 'Request ID is required' });
    } else if (!this.isValidUUID((data as any).requestId)) {
      (errors as any).push({
        field: 'requestId',
        message: 'Invalid request ID format',
        value: (data as any).requestId,
      });
    }

    if (!data.signature) {
      (errors as any).push({ field: 'signature', message: 'Signature is required' });
    } else if (!this.isValidSignature((data as any).signature)) {
      (errors as any).push({
        field: 'signature',
        message: 'Invalid signature format',
        value: (data as any).signature,
      });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      requestId: (data as any).requestId,
      signature: (data as any).signature,
    };
  }

  /**
   * Validate add claim request
   */
  validateAddClaim(data): AddClaimRequest {
    const errors: ValidationError[] = [];

    if (!data.verificationId) {
      (errors as any).push({ field: 'verificationId', message: 'Verification ID is required' });
    } else if (!this.isValidUUID((data as any).verificationId)) {
      (errors as any).push({
        field: 'verificationId',
        message: 'Invalid verification ID format',
        value: (data as any).verificationId,
      });
    }

    if (!data.type) {
      (errors as any).push({ field: 'type', message: 'Type is required' });
    } else if (typeof data.type !== 'string' || (data as any).type.trim().length === 0) {
      (errors as any).push({ field: 'type', message: 'Type must be a non-empty string' });
    }

    if (!data.value) {
      (errors as any).push({ field: 'value', message: 'Value is required' });
    } else if (typeof data.value !== 'string' || (data as any).value.trim().length === 0) {
      (errors as any).push({ field: 'value', message: 'Value must be a non-empty string' });
    }

    if (!data.issuer) {
      (errors as any).push({ field: 'issuer', message: 'Issuer is required' });
    } else if (typeof data.issuer !== 'string' || (data as any).issuer.trim().length === 0) {
      (errors as any).push({ field: 'issuer', message: 'Issuer must be a non-empty string' });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      verificationId: (data as any).verificationId,
      type: (data as any).type.trim(),
      value: (data as any).value.trim(),
      issuer: (data as any).issuer.trim(),
    };
  }

  /**
   * Validate ID parameter
   */
  validateId(id: any, fieldName: string = 'id'): string {
    if (!id) {
      throw new AppError({
        message: `${fieldName} is required`,
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).MISSING_REQUIRED_FIELD,
      });
    }

    if (!this.isValidUUID(id)) {
      throw new AppError({
        message: `${fieldName} must be a valid UUID`,
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
      });
    }

    return id;
  }

  /**
   * Validate pagination parameters
   */
  validatePaginationParams(query: Record<string, string | string[]>): {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } {
    const page = (query as any).page ? parseInt((query as any).page, 10) : 1;
    const limit = (query as any).limit ? parseInt((query as any).limit, 10) : 10;

    if (isNaN(page) || page < 1) {
      throw new AppError({
        message: 'Page must be a positive integer',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
      });
    }

    if (isNaN(limit) || limit < 1 || limit > 100) {
      throw new AppError({
        message: 'Limit must be between 1 and 100',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
      });
    }

    const result = { page, limit };

    if ((query as any).sortBy) {
      const validSortFields = ['createdAt', 'updatedAt', 'status', 'type'];
      if (!(validSortFields as any).includes((query as any).sortBy)) {
        throw new AppError({
          message: `Invalid sort field. Must be one of: ${(validSortFields as any).join(', ')}`,
          type: ErrorType.VALIDATION,
          code: (ErrorCode as any).INVALID_INPUT,
        });
      }
      (result as any).sortBy = (query as any).sortBy;
    }

    if ((query as any).sortOrder) {
      if (!['asc', 'desc'].includes((query as any).sortOrder)) {
        throw new AppError({
          message: 'Sort order must be either "asc" or "desc"',
          type: ErrorType.VALIDATION,
          code: (ErrorCode as any).INVALID_INPUT,
        });
      }
      (result as any).sortOrder = (query as any).sortOrder;
    }

    return result;
  }

  /**
   * Check if string is a valid Ethereum address
   */
  private isValidEthereumAddress(address: string): boolean {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  }

  /**
   * Check if string is a valid signature
   */
  private isValidSignature(signature: string): boolean {
    return /^0x[a-fA-F0-9]{130}$/.test(signature);
  }

  /**
   * Check if string is a valid ENS name
   */
  private isValidENSName(ensName: string): boolean {
    return /^[a-z0-9-]+\.eth$/.test(ensName);
  }

  /**
   * Check if string is a valid domain
   */
  private isValidDomain(domain: string): boolean {
    return /^[a-z0-9-]+\.[a-z]{2,}$/.test(domain);
  }

  /**
   * Check if string is a valid UUID
   */
  private isValidUUID(uuid: string): boolean {
    return /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/(i as any).test(uuid);
  }
}
