import { PrismaClient } from '@prisma/client';
// jscpd:ignore-file
/**
 * blockchain-verification.service Tests
 *
 * This file contains tests for the blockchain-verification.service module using the test utility.
 */

import { blockchain-verification.serviceController } from '../controllers/blockchain-verification.service.controller';
import { blockchain-verification.serviceService } from '../services/blockchain-verification.service.service';
import { blockchain-verification.serviceRepository } from '../repositories/blockchain-verification.service.repository';
import { testControllerSuite, createMockRequest, createMockResponse, testService, testRepository, createMockPrismaClient } from './tests/utils/test-utilities';
import { Repository } from '../types/database';
import { blockchain-verification.serviceService } from '../services/blockchain-verification.service.service';
import { blockchain-verification.serviceRepository } from '../repositories/blockchain-verification.service.repository';
import { testControllerSuite, createMockRequest, createMockResponse, testService, testRepository, createMockPrismaClient } from './tests/utils/test-utilities';
import { Repository } from '../types/database';


// Mock the blockchain-verification.serviceService
jest.mock('../services/blockchain-verification.service.service');

describe('blockchain-verification.service Module Tests', () => {
  // Controller tests
  testControllerSuite('blockchain-verification.serviceController', blockchain-verification.serviceController, {
    getAll: { description: 'should get all blockchain-verification.services',
      expectedStatus: 200,
      expectedResponse: { success: true },
    },
    getById: { description: 'should get blockchain-verification.service by ID',
      req: createMockRequest({ params: { id: '1' } }),
      expectedStatus: 200,
      expectedResponse: { success: true },
    },
    create: { description: 'should create blockchain-verification.service',
      req: createMockRequest({ body: { name: 'Test' } }),
      expectedStatus: 201,
      expectedResponse: { success: true },
    },
    update: { description: 'should update blockchain-verification.service',
      req: createMockRequest({ params: { id: '1' }, body: { name: 'Updated' } }),
      expectedStatus: 200,
      expectedResponse: { success: true },
    },
    delete: { description: 'should delete blockchain-verification.service',
      req: createMockRequest({ params: { id: '1' } }),
      expectedStatus: 200,
      expectedResponse: { success: true, message: 'blockchain-verification.service deleted successfully' },
    },
  });

  // Service tests
  describe('blockchain-verification.serviceService', () => {
    let service;
    let mockRepository;

    beforeEach(() => {
      mockRepository = {
        findAll: jest.fn(),
        findById: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      };

      service = new blockchain-verification.serviceService();
      service.blockchain-verification.serviceRepository = mockRepository;
    });

    it('should find all blockchain-verification.services', async () => {
      mockRepository.findAll.mockResolvedValue([{ id: '1', name: 'Test' }]);

      const result = await testService(service, 'findAll');

      expect(result).toEqual([{ id: '1', name: 'Test' }]);
      expect(mockRepository.findAll).toHaveBeenCalled();
    });
  });

  // Repository tests
  describe('blockchain-verification.serviceRepository', () => {
    let repository;
    let mockPrisma;

    beforeEach(() => {
      mockPrisma = createMockPrismaClient();
      repository = new blockchain-verification.serviceRepository();
      repository.prisma = mockPrisma;
    });

    it('should find all blockchain-verification.services', async () => {
      mockPrisma.blockchain-verification.service.findMany.mockResolvedValue([{ id: '1', name: 'Test' }]);

      const result = await testRepository(repository, 'findAll');

      expect(result).toEqual([{ id: '1', name: 'Test' }]);
      expect(mockPrisma.blockchain-verification.service.findMany).toHaveBeenCalled();
    });
  });
});
