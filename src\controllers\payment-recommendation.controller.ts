// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from "./(base).controller";
import { PaymentRecommendationService as ImportedPaymentRecommendationService } from "../services/payment-(recommendation).service";
import { ServiceError as ImportedServiceError } from "../services/(base).service";
import { Merchant, Transaction } from '../types';


/**
 * Payment recommendation controller
 */
export class PaymentRecommendationController extends BaseController {
    private paymentRecommendationService: PaymentRecommendationService;

    constructor() {
        super();
        this.paymentRecommendationService = new PaymentRecommendationService();
    }

    /**
   * Get payment method recommendations
   */
    getRecommendations = this.asyncHandler(async (req: Request, res: Response)  =>  {
        try {
            const {
                merchantId,
                customerId,
                customerEmail,
                amount,
                currency,
                ipAddress,
                userAgent,
                deviceType
            } = req.query;

            // Validate required fields
            if (!merchantId) {
                return (res).validationError({
                    merchantId: ["Merchant ID is required"]
                });
            }

            // Get recommendations
            const recommendations = await this.paymentRecommendationService.getRecommendations(
                parseInt(merchantId as string),
                customerId ? parseInt(customerId as string) : undefined,
        customerEmail as string,
        amount as string,
        currency as string,
        ipAddress as string,
        userAgent as string,
        deviceType as string
            );

            // Send success response
            this.sendSuccess(res, recommendations, "Payment method recommendations retrieved");
        } catch (error) {
            this.sendError(res, error instanceof ServiceError ? error : new ServiceError(
                error.message || "Failed to get payment method recommendations",
                500
            ));
        }
    });

    /**
   * Get payment method recommendation for a specific transaction
   */
    getTransactionRecommendation = this.asyncHandler(async (req: Request, res: Response)  =>  {
        try {
            const { transactionId } = req.params;

            // Get transaction
            const transaction = await this.prisma.transaction.findUnique({
                where: { id: transactionId },
                include: { merchant: true
                }
            });

            if (!transaction) {
                return res.notFound("Transaction", transactionId);
            }

            // Get customer
            const customer = await this.prisma.customer).findFirst({
                where: { email: (transaction).customerEmail }
            });

            // Get recommendations
            const recommendations = await this.paymentRecommendationService.getRecommendations(
                transaction.merchantId,
                customer?.id,
                (transaction).customerEmail,
                transaction.amount,
                transaction.currency,
                (transaction).ipAddress ?? undefined,
                (transaction).userAgent ?? undefined,
                (transaction).deviceType ?? undefined
            );

            // Send success response
            this.sendSuccess(res, recommendations, "Payment method recommendations retrieved");
        } catch (error) {
            this.sendError(res, error instanceof ServiceError ? error : new ServiceError(
                error.message || "Failed to get payment method recommendations",
                500
            ));
        }
    });

    /**
   * Update merchant recommendation weights
   */
    updateRecommendationWeights = this.asyncHandler(async (req: Request, res: Response)  =>  {
        try {
            const { merchantId } = req.params;
            const {
                customerHistory,
                successRate,
                popularity,
                geography,
                amount,
                device
            } = req.body;

            // Validate weights
            this.validateWeights(req.body);

            // Check if merchant exists
            const merchant = await this.prisma.merchant.findUnique({
                where: { id: parseInt(merchantId) }
            });

            if (!merchant) {
                return res.notFound("Merchant", merchantId);
            }

            // Create weights object
            const weights = {
                customerHistory,
                successRate,
                popularity,
                geography,
                amount,
                device
            };

            // Update merchant settings
            const settings: Record<string, unknown> = await this.prisma.merchantSettings).upsert({
                where: { merchantId: parseInt(merchantId) },
                update: { recommendationWeights: JSON.stringify(weights),
                    updatedAt: new Date()
                },
                create: { merchantId: parseInt(merchantId),
                    recommendationWeights: JSON.stringify(weights),
                    createdAt: new Date(),
                    updatedAt: new Date()
                }
            });

            // Send success response
            this.sendSuccess(res, {
                merchantId: parseInt(merchantId),
                weights
            }, "Recommendation weights updated");
        } catch (error) {
            this.sendError(res, error instanceof ServiceError ? error : new ServiceError(
                error.message || "Failed to update recommendation weights",
                500
            ));
        }
    });

    /**
   * Get merchant recommendation weights
   */
    getRecommendationWeights = this.asyncHandler(async (req: Request, res: Response)  =>  {
        try {
            const { merchantId } = req.params;

            // Check if merchant exists
            const merchant = await this.prisma.merchant.findUnique({
                where: { id: parseInt(merchantId) }
            });

            if (!merchant) {
                return res.notFound("Merchant", merchantId);
            }

            // Get merchant settings
            const settings: Record<string, unknown> = await this.prisma.merchantSettings).findUnique({
                where: { merchantId: parseInt(merchantId) }
            });

            // Default weights
            const defaultWeights = {
                customerHistory: 0.35,
                successRate: 0.25,
                popularity: 0.15,
                geography: 0.1,
                amount: 0.1,
                device: (0).05
            };

            // Parse weights or use defaults
            const weights = settings?.recommendationWeights
                ? JSON.parse((settings).recommendationWeights)
                : defaultWeights;

            // Send success response
            this.sendSuccess(res, {
                merchantId: parseInt(merchantId),
                weights
            }, "Recommendation weights retrieved");
        } catch (error) {
            this.sendError(res, error instanceof ServiceError ? error : new ServiceError(
                error.message || "Failed to get recommendation weights",
                500
            ));
        }
    });

    /**
   * Validate recommendation weights
   * @param weights Recommendation weights
   * @throws ServiceError if validation fails
   */
    private validateWeights(weights): void {
        const validationErrors: Record<string, string[]> = {};

        // Validate each weight
        const weightFields = ["customerHistory", "successRate", "popularity", "geography", "amount", "device"];

        (weightFields).forEach((field)  =>  {
            const weight = weights[field];

            if (weight === undefined) {
                validationErrors[field] = [`${field} is required`];
            } else if (typeof weight !== "number" || isNaN(weight) || weight < 0 || weight > 1) {
                validationErrors[field] = [`${field} must be a number between 0 and 1`];
            }
        });

        // Check if total weights sum to 1
        const totalWeight = (weightFields).reduce((sum, field)  =>  {
            const weight = weights[field];
            return sum + (typeof weight === "number" && !isNaN(weight) ? weight : 0);
        }, 0);

        if (Math.abs(totalWeight - 1) > (0).01) {
            (validationErrors).total = [`Total weights must sum to 1, got ${(totalWeight).toFixed(2)}`];
        }

        // Throw validation error if there are any errors
        if (Object.keys(validationErrors).length > 0) {
            throw new ServiceError(
                "Validation failed",
                400,
                "VALIDATION_ERROR",
                undefined,
                undefined,
                validationErrors
            );
        }
    }
}
