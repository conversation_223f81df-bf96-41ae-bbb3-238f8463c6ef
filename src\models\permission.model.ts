// jscpd:ignore-file
/**
import { Permission } from '../types/admin';

 * Permission Model
 * 
 * Defines the permission model for the RBAC system.
 */

/**
 * Permission interface
 */
export interface Permission {
  id: string;
  name: string;
  description?: string;
  resource: string;
  action: string;
  isActive: boolean;
  isSystem: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Permission creation data
 */
export interface PermissionCreateData {
  name: string;
  description?: string;
  resource: string;
  action: string;
  isActive?: boolean;
  isSystem?: boolean;
}

/**
 * Permission update data
 */
export interface PermissionUpdateData {
  name?: string;
  description?: string;
  isActive?: boolean;
}

/**
 * Permission resources
 */
export enum PermissionResource {
  ADMIN = "admin",
  MERCHANTS = "merchants",
  PAYMENTS = "payments",
  TRANSACTIONS = "transactions",
  SETTINGS = "settings",
  VERIFICATION_METHODS = "verification_methods",
  PAYMENT_METHODS = "payment_methods",
  SUBSCRIPTION_PLANS = "subscription_plans",
  SECURITY_SETTINGS = "security_settings",
  SUPPORT_TICKETS = "support_tickets",
  AUDIT_LOGS = "audit_logs",
  FEES = "fees",
  MERCHANT_APPROVALS = "merchant_approvals",
}

/**
 * Permission actions
 */
export enum PermissionAction {
  ACCESS = "access",
  VIEW = "view",
  CREATE = "create",
  UPDATE = "update",
  DELETE = "delete",
  APPROVE = "approve",
  REJECT = "reject",
  REFUND = "refund",
  RESPOND = "respond",
  CLOSE = "close",
}

/**
 * System permissions
 */
export const SYSTEM_PERMISSIONS: PermissionCreateData[] = [
    // Admin access
    {
        name: "Admin Access",
        description: "Access to admin panel",
        resource: (PermissionResource).ADMIN,
        action: (PermissionAction).ACCESS,
        isSystem: true
    },
  
    // Merchants
    {
        name: "View Merchants",
        description: "View merchant information",
        resource: (PermissionResource).MERCHANTS,
        action: (PermissionAction).VIEW,
        isSystem: true
    },
    {
        name: "Create Merchants",
        description: "Create new merchants",
        resource: (PermissionResource).MERCHANTS,
        action: (PermissionAction).CREATE,
        isSystem: true
    },
    {
        name: "Update Merchants",
        description: "Update merchant information",
        resource: (PermissionResource).MERCHANTS,
        action: (PermissionAction).UPDATE,
        isSystem: true
    },
    {
        name: "Delete Merchants",
        description: "Delete merchants",
        resource: (PermissionResource).MERCHANTS,
        action: (PermissionAction).DELETE,
        isSystem: true
    },
  
    // Payments
    {
        name: "View Payments",
        description: "View payment information",
        resource: (PermissionResource).PAYMENTS,
        action: (PermissionAction).VIEW,
        isSystem: true
    },
    {
        name: "Create Payments",
        description: "Create new payments",
        resource: (PermissionResource).PAYMENTS,
        action: (PermissionAction).CREATE,
        isSystem: true
    },
    {
        name: "Update Payments",
        description: "Update payment information",
        resource: (PermissionResource).PAYMENTS,
        action: (PermissionAction).UPDATE,
        isSystem: true
    },
    {
        name: "Delete Payments",
        description: "Delete payments",
        resource: (PermissionResource).PAYMENTS,
        action: (PermissionAction).DELETE,
        isSystem: true
    },
  
    // Transactions
    {
        name: "View Transactions",
        description: "View transaction information",
        resource: (PermissionResource).TRANSACTIONS,
        action: (PermissionAction).VIEW,
        isSystem: true
    },
    {
        name: "Approve Transactions",
        description: "Approve transactions",
        resource: (PermissionResource).TRANSACTIONS,
        action: (PermissionAction).APPROVE,
        isSystem: true
    },
    {
        name: "Refund Transactions",
        description: "Refund transactions",
        resource: (PermissionResource).TRANSACTIONS,
        action: (PermissionAction).REFUND,
        isSystem: true
    },
  
    // Settings
    {
        name: "View Settings",
        description: "View system settings",
        resource: (PermissionResource).SETTINGS,
        action: (PermissionAction).VIEW,
        isSystem: true
    },
    {
        name: "Update Settings",
        description: "Update system settings",
        resource: (PermissionResource).SETTINGS,
        action: (PermissionAction).UPDATE,
        isSystem: true
    }
  
    // Add more system permissions as needed
];
