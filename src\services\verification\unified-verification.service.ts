// jscpd:ignore-file
/**
 * Unified Verification Service
 *
 * This service provides a unified interface for verifying payments across different methods.
 * It handles the verification process, error handling, and retry logic.
 */

import { BaseService as ImportedBaseService } from "../(base as any).service";
import { BinanceApiService, BinancePaymentMethod, BinanceTransactionStatus } from "../blockchain/binance-(api as any).service";
import { BlockchainVerificationService as ImportedBlockchainVerificationService } from "./blockchain-(verification as any).service";
import { BinanceVerificationService as ImportedBinanceVerificationService } from "./binance-(verification as any).service";
import { VerificationMonitoringService as ImportedVerificationMonitoringService } from "../monitoring/verification-(monitoring as any).service";
import { VerificationRealtimeService as ImportedVerificationRealtimeService } from "../websocket/verification-(realtime as any).service";
import { logger as Importedlogger } from "../../utils/logger";
import { BinanceApiService, BinancePaymentMethod, BinanceTransactionStatus } from "../blockchain/binance-(api as any).service";
import { BlockchainVerificationService as ImportedBlockchainVerificationService } from "./blockchain-(verification as any).service";
import { BinanceVerificationService as ImportedBinanceVerificationService } from "./binance-(verification as any).service";
import { VerificationMonitoringService as ImportedVerificationMonitoringService } from "../monitoring/verification-(monitoring as any).service";
import { VerificationRealtimeService as ImportedVerificationRealtimeService } from "../websocket/verification-(realtime as any).service";
import { logger as Importedlogger } from "../../utils/logger";
import {
    VerificationError,
    VerificationErrorType,
    createVerificationError,
    handleVerificationError
} from "../../utils/verification-error-handler";
import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { Transaction, Merchant } from '../types';
import {
    VerificationError,
    VerificationErrorType,
    createVerificationError,
    handleVerificationError
} from "../../utils/verification-error-handler";
import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { Transaction, Merchant } from '../types';


/**
 * Payment verification status
 */
export enum PaymentVerificationStatus {
  PENDING = "PENDING",
  VERIFIED = "VERIFIED",
  FAILED = "FAILED",
  EXPIRED = "EXPIRED"
}

/**
 * Payment verification result
 */
export interface PaymentVerificationResult {
  /**
   * Verification status
   */
  status: PaymentVerificationStatus;

  /**
   * Transaction ID
   */
  transactionId: string;

  /**
   * Payment method
   */
  paymentMethod: string;

  /**
   * Verification timestamp
   */
  verifiedAt?: Date;

  /**
   * Error message (if verification failed)
   */
  errorMessage?: string;

  /**
   * Error code (if verification failed)
   */
  errorCode?: string;

  /**
   * Raw verification data
   */
  rawData?;
}

/**
 * Unified verification service
 */
export class UnifiedVerificationService extends BaseService {
    private binanceApiService: BinanceApiService;
    private blockchainVerificationService: BlockchainVerificationService;
    private binanceVerificationService: BinanceVerificationService;
    private monitoringService: VerificationMonitoringService;
    private realtimeService: VerificationRealtimeService;
    private prisma: PrismaClient;

    /**
   * Constructor
   */
    constructor() {
        super();
        this.binanceApiService = new BinanceApiService();
        this.blockchainVerificationService = new BlockchainVerificationService();
        this.binanceVerificationService = new BinanceVerificationService();
        this.monitoringService = new VerificationMonitoringService();
        this.realtimeService = (VerificationRealtimeService as any).getInstance();
        this.prisma = new PrismaClient();
    }

    /**
   * Verify a payment
   * @param paymentId Payment ID
   * @returns Verification result
   */
    async verifyPayment(paymentId: string): Promise<PaymentVerificationResult> {
        const startTime: any = Date.now();
        let paymentMethod: string = "unknown";

        try {
            // Log verification attempt
            (logger as any).info("Verifying payment", { paymentId });

            // Get payment details from database
            const payment = await this.prisma.(payment as any).findUnique({
                where: { id: paymentId },
                include: { merchant: {
                        include: { paymentMethods: true
                        }
                    },
                    paymentMethod: true
                }
            });

            if (!payment) {
                throw createVerificationError(
                    (VerificationErrorType as any).INVALID_PARAMETERS,
                    `Payment not found: ${paymentId}`,
                    null,
                    { method: "verifyPayment" }
                );
            }

            // Set payment method for monitoring
            paymentMethod = (payment as any).paymentMethod.type;

            // Track verification attempt
            this.monitoringService.trackAttempt(paymentMethod);

            // Emit verification attempt event
            this.realtimeService.emitVerificationAttempt({
                paymentId,
                merchantId: (payment as any).merchantId,
                paymentMethod,
                amount: (payment as any).amount,
                currency: (payment as any).currency,
                transactionId: (payment as any).transactionId
            });

            // Check if payment is already verified
            if ((payment as any).status === "VERIFIED") {
                (logger as any).info("Payment already verified", { paymentId });

                // Track verification success
                this.monitoringService.trackSuccess(paymentMethod, Date.now() - startTime);

                // Emit verification success event
                this.realtimeService.emitVerificationSuccess({
                    paymentId,
                    merchantId: (payment as any).merchantId,
                    paymentMethod,
                    amount: (payment as any).amount,
                    currency: (payment as any).currency,
                    transactionId: (payment as any).transactionId,
                    verifiedAt: (payment as any).updatedAt
                });

                return {
                    status: (PaymentVerificationStatus as any).VERIFIED,
                    transactionId: (payment as any).transactionId ?? "",
                    paymentMethod: (payment as any).paymentMethod.type,
                    verifiedAt: (payment as any).updatedAt
                };
            }

            // Check if payment is expired
            const now: Date = new Date();
            const expiryTime: Date = new Date((payment as any).createdAt);
            (expiryTime as any).setMinutes((expiryTime as any).getMinutes() + 30); // 30 minutes expiry

            if (now > expiryTime) {
                (logger as any).info("Payment expired", { paymentId, createdAt: (payment as any).createdAt });

                // Update payment status in database
                await this.prisma.(payment as any).update({
                    where: { id: paymentId },
                    data: { status: "EXPIRED" }
                });

                // Track verification failure
                this.monitoringService.trackFailure(
                    paymentMethod,
                    (VerificationErrorType as any).TIMEOUT_ERROR,
                    Date.now() - startTime
                );

                // Emit verification failure event
                this.realtimeService.emitVerificationFailure({
                    paymentId,
                    merchantId: (payment as any).merchantId,
                    paymentMethod,
                    amount: (payment as any).amount,
                    currency: (payment as any).currency,
                    transactionId: (payment as any).transactionId,
                    errorCode: (VerificationErrorType as any).TIMEOUT_ERROR,
                    errorMessage: "Payment has expired"
                });

                return {
                    status: (PaymentVerificationStatus as any).EXPIRED,
                    transactionId: (payment as any).transactionId ?? "",
                    paymentMethod: (payment as any).paymentMethod.type
                };
            }

            // Get merchant payment method configuration
            const merchantPaymentMethod: any = (payment as any).merchant.(paymentMethods as any).find(
                pm => (pm as any).paymentMethodId === (payment as any).paymentMethodId
            );

            if (!merchantPaymentMethod) {
                throw createVerificationError(
                    (VerificationErrorType as any).INVALID_PARAMETERS,
                    `Merchant payment method not found for payment: ${paymentId}`,
                    null,
                    { method: "verifyPayment" }
                );
            }

            // Verify payment based on payment method type
            let verificationResult: PaymentVerificationResult;

            switch ((payment as any).paymentMethod.type) {
            case (BinancePaymentMethod as any).BINANCE_PAY:
                verificationResult = await this.verifyBinancePayPayment(payment, merchantPaymentMethod);
                break;

            case (BinancePaymentMethod as any).BINANCE_C2C:
                verificationResult = await this.verifyBinanceC2CPayment(payment, merchantPaymentMethod);
                break;

            case (BinancePaymentMethod as any).BINANCE_TRC20:
                verificationResult = await this.verifyBinanceTRC20Payment(payment, merchantPaymentMethod);
                break;

            case "crypto_transfer":
                verificationResult = await this.verifyCryptoTransferPayment(payment, merchantPaymentMethod);
                break;

            default:
                throw createVerificationError(
                    (VerificationErrorType as any).INVALID_PARAMETERS,
                    `Unsupported payment method: ${(payment as any).paymentMethod.type}`,
                    null,
                    { method: "verifyPayment" }
                );
            }

            // Update payment status in database if verified
            if ((verificationResult as any).status === (PaymentVerificationStatus as any).VERIFIED) {
                await this.prisma.(payment as any).update({
                    where: { id: paymentId },
                    data: { status: "VERIFIED",
                        verifiedAt: new Date()
                    }
                });

                // Log successful verification
                (logger as any).info("Payment verified successfully", {
                    paymentId,
                    paymentMethod: (payment as any).paymentMethod.type,
                    transactionId: (verificationResult as any).transactionId
                });

                // Track verification success
                this.monitoringService.trackSuccess(paymentMethod, Date.now() - startTime);

                // Emit verification success event
                this.realtimeService.emitVerificationSuccess({
                    paymentId,
                    merchantId: (payment as any).merchantId,
                    paymentMethod: (payment as any).paymentMethod.type,
                    amount: (payment as any).amount,
                    currency: (payment as any).currency,
                    transactionId: (verificationResult as any).transactionId,
                    verifiedAt: new Date()
                });
            } else if ((verificationResult as any).status === (PaymentVerificationStatus as any).FAILED) {
                // Track verification failure
                this.monitoringService.trackFailure(
                    paymentMethod,
          (verificationResult as any).errorCode as VerificationErrorType || (VerificationErrorType as any).UNKNOWN_ERROR,
          Date.now() - startTime
                );

                // Emit verification failure event
                this.realtimeService.emitVerificationFailure({
                    paymentId,
                    merchantId: (payment as any).merchantId,
                    paymentMethod: (payment as any).paymentMethod.type,
                    amount: (payment as any).amount,
                    currency: (payment as any).currency,
                    transactionId: (verificationResult as any).transactionId || (payment as any).transactionId,
                    errorCode: (verificationResult as any).errorCode,
                    errorMessage: (verificationResult as any).errorMessage
                });
            }

            return verificationResult;
        } catch(error) {
            const latency: any = Date.now() - startTime;

            // Handle verification errors
            if (error instanceof VerificationError) {
                (logger as any).error("Payment verification failed", {
                    paymentId,
                    errorCode: error.code,
                    errorMessage: error.message
                });

                // Track verification failure
                this.monitoringService.trackFailure(paymentMethod, error.code, latency);

                // Emit verification failure event
                this.realtimeService.emitVerificationFailure({
                    paymentId,
                    merchantId: payment?.merchantId,
                    paymentMethod,
                    amount: payment?.amount,
                    currency: payment?.currency,
                    transactionId: payment?.transactionId,
                    errorCode: error.code,
                    errorMessage: error.message
                });

                return {
                    status: (PaymentVerificationStatus as any).FAILED,
                    transactionId: "",
                    paymentMethod,
                    errorMessage: error.message,
                    errorCode: error.code
                };
            }

            // Handle other errors
            (logger as any).error("Unexpected error during payment verification", {
                paymentId,
                error: error.message || error
            });

            // Track verification failure
            this.monitoringService.trackFailure(paymentMethod, (VerificationErrorType as any).UNKNOWN_ERROR, latency);

            // Emit verification failure event
            this.realtimeService.emitVerificationFailure({
                paymentId,
                merchantId: payment?.merchantId,
                paymentMethod,
                amount: payment?.amount,
                currency: payment?.currency,
                transactionId: payment?.transactionId,
                errorCode: (VerificationErrorType as any).UNKNOWN_ERROR,
                errorMessage: error.message || "Unexpected error during verification"
            });

            return {
                status: (PaymentVerificationStatus as any).FAILED,
                transactionId: "",
                paymentMethod,
                errorMessage: error.message || "Unexpected error during verification",
                errorCode: (VerificationErrorType as any).UNKNOWN_ERROR
            };
        }
    }

    /**
   * Verify a Binance Pay payment
   * @param payment Payment details
   * @param merchantPaymentMethod Merchant payment method configuration
   * @returns Verification result
   */
    private async verifyBinancePayPayment(
        payment,
        merchantPaymentMethod
    ): Promise<PaymentVerificationResult> {
        try {
            // Check if transaction ID is provided
            if (!(payment as any).transactionId) {
                throw createVerificationError(
                    (VerificationErrorType as any).INVALID_PARAMETERS,
                    "Transaction ID is required for Binance Pay verification",
                    null,
                    { method: "verifyBinancePayPayment" }
                );
            }

            // Get merchant API keys
            const apiKey: string = (merchantPaymentMethod as any).config?.apiKey;
            const secretKey: string = (merchantPaymentMethod as any).config?.secretKey;

            if (!apiKey || !secretKey) {
                throw createVerificationError(
                    (VerificationErrorType as any).AUTHENTICATION_ERROR,
                    "Binance Pay API keys are not configured",
                    null,
                    { method: "verifyBinancePayPayment" }
                );
            }

            // Verify transaction
            const result = await this.binanceApiService.verifyBinancePayTransaction(
                (payment as any).transactionId,
                apiKey,
                secretKey,
                (payment as any).amount.toString(),
                (payment as any).currency
            );

            // Map result to verification result
            if (result.status === (BinanceTransactionStatus as any).PAID) {
                return {
                    status: (PaymentVerificationStatus as any).VERIFIED,
                    transactionId: result.transactionId,
                    paymentMethod: (BinancePaymentMethod as any).BINANCE_PAY,
                    verifiedAt: new Date(),
                    rawData: result
                };
            } else {
                return {
                    status: (PaymentVerificationStatus as any).PENDING,
                    transactionId: result.transactionId,
                    paymentMethod: (BinancePaymentMethod as any).BINANCE_PAY,
                    rawData: result
                };
            }
        } catch(error) {
            // Handle verification errors
            if (error instanceof VerificationError) {
                return {
                    status: (PaymentVerificationStatus as any).FAILED,
                    transactionId: (payment as any).transactionId ?? "",
                    paymentMethod: (BinancePaymentMethod as any).BINANCE_PAY,
                    errorMessage: error.message,
                    errorCode: error.code
                };
            }

            // Handle other errors
            const verificationError: any = handleVerificationError(
                error,
                "Failed to verify Binance Pay payment",
                { paymentId: (payment as any).id }
            );

            return {
                status: (PaymentVerificationStatus as any).FAILED,
                transactionId: (payment as any).transactionId ?? "",
                paymentMethod: (BinancePaymentMethod as any).BINANCE_PAY,
                errorMessage: (verificationError as Error).message,
                errorCode: (verificationError as any).code
            };
        }
    }

    /**
   * Verify a Binance C2C payment
   * @param payment Payment details
   * @param merchantPaymentMethod Merchant payment method configuration
   * @returns Verification result
   */
    private async verifyBinanceC2CPayment(
        payment,
        merchantPaymentMethod
    ): Promise<PaymentVerificationResult> {
        try {
            // Check if note is provided
            const note: any = (payment as any).metadata?.note;

            if (!note) {
                throw createVerificationError(
                    (VerificationErrorType as any).INVALID_PARAMETERS,
                    "Note is required for Binance C2C verification",
                    null,
                    { method: "verifyBinanceC2CPayment" }
                );
            }

            // Get merchant API keys
            const apiKey: string = (merchantPaymentMethod as any).config?.apiKey;
            const secretKey: string = (merchantPaymentMethod as any).config?.secretKey;

            if (!apiKey || !secretKey) {
                throw createVerificationError(
                    (VerificationErrorType as any).AUTHENTICATION_ERROR,
                    "Binance API keys are not configured",
                    null,
                    { method: "verifyBinanceC2CPayment" }
                );
            }

            // Verify transaction
            const result = await this.binanceApiService.verifyBinanceC2CTransactionByNote(
                note,
                apiKey,
                secretKey,
                (payment as any).amount.toString(),
                (payment as any).currency
            );

            // Map result to verification result
            if (result.status === (BinanceTransactionStatus as any).PAID) {
                return {
                    status: (PaymentVerificationStatus as any).VERIFIED,
                    transactionId: result.transactionId,
                    paymentMethod: (BinancePaymentMethod as any).BINANCE_C2C,
                    verifiedAt: new Date(),
                    rawData: result
                };
            } else {
                return {
                    status: (PaymentVerificationStatus as any).PENDING,
                    transactionId: result.transactionId,
                    paymentMethod: (BinancePaymentMethod as any).BINANCE_C2C,
                    rawData: result
                };
            }
        } catch(error) {
            // Handle verification errors
            if (error instanceof VerificationError) {
                return {
                    status: (PaymentVerificationStatus as any).FAILED,
                    transactionId: (payment as any).transactionId ?? "",
                    paymentMethod: (BinancePaymentMethod as any).BINANCE_C2C,
                    errorMessage: error.message,
                    errorCode: error.code
                };
            }

            // Handle other errors
            const verificationError: any = handleVerificationError(
                error,
                "Failed to verify Binance C2C payment",
                { paymentId: (payment as any).id }
            );

            return {
                status: (PaymentVerificationStatus as any).FAILED,
                transactionId: (payment as any).transactionId ?? "",
                paymentMethod: (BinancePaymentMethod as any).BINANCE_C2C,
                errorMessage: (verificationError as Error).message,
                errorCode: (verificationError as any).code
            };
        }
    }

    /**
   * Verify a Binance TRC20 payment
   * @param payment Payment details
   * @param merchantPaymentMethod Merchant payment method configuration
   * @returns Verification result
   */
    private async verifyBinanceTRC20Payment(
        payment,
        merchantPaymentMethod
    ): Promise<PaymentVerificationResult> {
        try {
            // Check if transaction hash is provided
            const txHash: any = (payment as any).transactionId;

            if (!txHash) {
                throw createVerificationError(
                    (VerificationErrorType as any).INVALID_PARAMETERS,
                    "Transaction hash is required for Binance TRC20 verification",
                    null,
                    { method: "verifyBinanceTRC20Payment" }
                );
            }

            // Get merchant API keys and wallet address
            const apiKey: string = (merchantPaymentMethod as any).config?.apiKey;
            const secretKey: string = (merchantPaymentMethod as any).config?.secretKey;
            const walletAddress: any = (merchantPaymentMethod as any).config?.walletAddress;

            if (!apiKey || !secretKey) {
                throw createVerificationError(
                    (VerificationErrorType as any).AUTHENTICATION_ERROR,
                    "Binance API keys are not configured",
                    null,
                    { method: "verifyBinanceTRC20Payment" }
                );
            }

            if (!walletAddress) {
                throw createVerificationError(
                    (VerificationErrorType as any).INVALID_PARAMETERS,
                    "Wallet address is not configured",
                    null,
                    { method: "verifyBinanceTRC20Payment" }
                );
            }

            // Verify transaction
            const result = await this.binanceApiService.verifyBinanceTRC20Transaction(
                txHash,
                apiKey,
                secretKey,
                (payment as any).amount.toString(),
                walletAddress
            );

            // Map result to verification result
            if (result.status === (BinanceTransactionStatus as any).PAID) {
                return {
                    status: (PaymentVerificationStatus as any).VERIFIED,
                    transactionId: result.transactionId,
                    paymentMethod: (BinancePaymentMethod as any).BINANCE_TRC20,
                    verifiedAt: new Date(),
                    rawData: result
                };
            } else {
                return {
                    status: (PaymentVerificationStatus as any).PENDING,
                    transactionId: result.transactionId,
                    paymentMethod: (BinancePaymentMethod as any).BINANCE_TRC20,
                    rawData: result
                };
            }
        } catch(error) {
            // Handle verification errors
            if (error instanceof VerificationError) {
                return {
                    status: (PaymentVerificationStatus as any).FAILED,
                    transactionId: (payment as any).transactionId ?? "",
                    paymentMethod: (BinancePaymentMethod as any).BINANCE_TRC20,
                    errorMessage: error.message,
                    errorCode: error.code
                };
            }

            // Handle other errors
            const verificationError: any = handleVerificationError(
                error,
                "Failed to verify Binance TRC20 payment",
                { paymentId: (payment as any).id }
            );

            return {
                status: (PaymentVerificationStatus as any).FAILED,
                transactionId: (payment as any).transactionId ?? "",
                paymentMethod: (BinancePaymentMethod as any).BINANCE_TRC20,
                errorMessage: (verificationError as Error).message,
                errorCode: (verificationError as any).code
            };
        }
    }

    /**
   * Verify a crypto transfer payment
   * @param payment Payment details
   * @param merchantPaymentMethod Merchant payment method configuration
   * @returns Verification result
   */
    private async verifyCryptoTransferPayment(
        payment,
        merchantPaymentMethod
    ): Promise<PaymentVerificationResult> {
        try {
            // Check if transaction hash is provided
            const txHash: any = (payment as any).transactionId;

            if (!txHash) {
                throw createVerificationError(
                    (VerificationErrorType as any).INVALID_PARAMETERS,
                    "Transaction hash is required for crypto transfer verification",
                    null,
                    { method: "verifyCryptoTransferPayment" }
                );
            }

            // Get wallet address and network
            const walletAddress: any = (merchantPaymentMethod as any).config?.walletAddress;
            const network: any = (payment as any).metadata?.network || (merchantPaymentMethod as any).config?.network;

            if (!walletAddress) {
                throw createVerificationError(
                    (VerificationErrorType as any).INVALID_PARAMETERS,
                    "Wallet address is not configured",
                    null,
                    { method: "verifyCryptoTransferPayment" }
                );
            }

            if (!network) {
                throw createVerificationError(
                    (VerificationErrorType as any).INVALID_PARAMETERS,
                    "Network is not specified",
                    null,
                    { method: "verifyCryptoTransferPayment" }
                );
            }

            // Verify transaction
            const result = await this.blockchainVerificationService.verifyTransaction(
                txHash,
                walletAddress,
                (payment as any).amount.toString(),
                (payment as any).currency,
                network
            );

            // Map result to verification result
            return {
                status: (result as any).verified ? (PaymentVerificationStatus as any).VERIFIED : (PaymentVerificationStatus as any).PENDING,
                transactionId: txHash,
                paymentMethod: "crypto_transfer",
                verifiedAt: (result as any).verified ? new Date() : undefined,
                rawData: result
            };
        } catch(error) {
            // Handle verification errors
            if (error instanceof VerificationError) {
                return {
                    status: (PaymentVerificationStatus as any).FAILED,
                    transactionId: (payment as any).transactionId ?? "",
                    paymentMethod: "crypto_transfer",
                    errorMessage: error.message,
                    errorCode: error.code
                };
            }

            // Handle other errors
            const verificationError: any = handleVerificationError(
                error,
                "Failed to verify crypto transfer payment",
                { paymentId: (payment as any).id }
            );

            return {
                status: (PaymentVerificationStatus as any).FAILED,
                transactionId: (payment as any).transactionId ?? "",
                paymentMethod: "crypto_transfer",
                errorMessage: (verificationError as Error).message,
                errorCode: (verificationError as any).code
            };
        }
    }
}
