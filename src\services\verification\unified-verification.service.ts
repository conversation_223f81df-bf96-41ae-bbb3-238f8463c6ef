// jscpd:ignore-file
/**
 * Unified Verification Service
 *
 * This service provides a unified interface for verifying payments across different methods.
 * It handles the verification process, error handling, and retry logic.
 */

import { BaseService } from "../base.service";
import { BinanceApiService, BinancePaymentMethod, BinanceTransactionStatus } from "../blockchain/binance-api.service";
import { BlockchainVerificationService } from "./blockchain-verification.service";
import { BinanceVerificationService } from "./binance-verification.service";
import { VerificationMonitoringService } from "../monitoring/verification-monitoring.service";
import { VerificationRealtimeService } from "../websocket/verification-realtime.service";
import { logger } from "../../utils/logger";
import { BinanceApiService, BinancePaymentMethod, BinanceTransactionStatus } from "../blockchain/binance-api.service";
import { BlockchainVerificationService } from "./blockchain-verification.service";
import { BinanceVerificationService } from "./binance-verification.service";
import { VerificationMonitoringService } from "../monitoring/verification-monitoring.service";
import { VerificationRealtimeService } from "../websocket/verification-realtime.service";
import { logger } from "../../utils/logger";
import {
    VerificationError,
    VerificationErrorType,
    createVerificationError,
    handleVerificationError
} from "../../utils/verification-error-handler";
import { PrismaClient } from "@prisma/client";
import { Transaction, Merchant } from '../types';
import {
    VerificationError,
    VerificationErrorType,
    createVerificationError,
    handleVerificationError
} from "../../utils/verification-error-handler";
import { PrismaClient } from "@prisma/client";
import { Transaction, Merchant } from '../types';


/**
 * Payment verification status
 */
export enum PaymentVerificationStatus {
  PENDING = "PENDING",
  VERIFIED = "VERIFIED",
  FAILED = "FAILED",
  EXPIRED = "EXPIRED"
}

/**
 * Payment verification result
 */
export interface PaymentVerificationResult {
  /**
   * Verification status
   */
  status: PaymentVerificationStatus;

  /**
   * Transaction ID
   */
  transactionId: string;

  /**
   * Payment method
   */
  paymentMethod: string;

  /**
   * Verification timestamp
   */
  verifiedAt?: Date;

  /**
   * Error message (if verification failed)
   */
  errorMessage?: string;

  /**
   * Error code (if verification failed)
   */
  errorCode?: string;

  /**
   * Raw verification data
   */
  rawData?;
}

/**
 * Unified verification service
 */
export class UnifiedVerificationService extends BaseService {
    private binanceApiService: BinanceApiService;
    private blockchainVerificationService: BlockchainVerificationService;
    private binanceVerificationService: BinanceVerificationService;
    private monitoringService: VerificationMonitoringService;
    private realtimeService: VerificationRealtimeService;
    private prisma: PrismaClient;

    /**
   * Constructor
   */
    constructor() {
        super();
        this.binanceApiService = new BinanceApiService();
        this.blockchainVerificationService = new BlockchainVerificationService();
        this.binanceVerificationService = new BinanceVerificationService();
        this.monitoringService = new VerificationMonitoringService();
        this.realtimeService = VerificationRealtimeService.getInstance();
        this.prisma = new PrismaClient();
    }

    /**
   * Verify a payment
   * @param paymentId Payment ID
   * @returns Verification result
   */
    async verifyPayment(paymentId: string): Promise<PaymentVerificationResult> {
        const startTime: unknown = Date.now();
        let paymentMethod: string = "unknown";

        try {
            // Log verification attempt
            logger.info("Verifying payment", { paymentId });

            // Get payment details from database
            const payment: unknown = await this.prisma.payment.findUnique({
                where: { id: paymentId },
                include: { merchant: {
                        include: { paymentMethods: true
                        }
                    },
                    paymentMethod: true
                }
            });

            if (!payment) {
                throw createVerificationError(
                    VerificationErrorType.INVALID_PARAMETERS,
                    `Payment not found: ${paymentId}`,
                    null,
                    { method: "verifyPayment" }
                );
            }

            // Set payment method for monitoring
            paymentMethod = payment.paymentMethod.type;

            // Track verification attempt
            this.monitoringService.trackAttempt(paymentMethod);

            // Emit verification attempt event
            this.realtimeService.emitVerificationAttempt({
                paymentId,
                merchantId: payment.merchantId,
                paymentMethod,
                amount: payment.amount,
                currency: payment.currency,
                transactionId: payment.transactionId
            });

            // Check if payment is already verified
            if (payment.status === "VERIFIED") {
                logger.info("Payment already verified", { paymentId });

                // Track verification success
                this.monitoringService.trackSuccess(paymentMethod, Date.now() - startTime);

                // Emit verification success event
                this.realtimeService.emitVerificationSuccess({
                    paymentId,
                    merchantId: payment.merchantId,
                    paymentMethod,
                    amount: payment.amount,
                    currency: payment.currency,
                    transactionId: payment.transactionId,
                    verifiedAt: payment.updatedAt
                });

                return {
                    status: PaymentVerificationStatus.VERIFIED,
                    transactionId: payment.transactionId ?? "",
                    paymentMethod: payment.paymentMethod.type,
                    verifiedAt: payment.updatedAt
                };
            }

            // Check if payment is expired
            const now: Date = new Date();
            const expiryTime: Date = new Date(payment.createdAt);
            expiryTime.setMinutes(expiryTime.getMinutes() + 30); // 30 minutes expiry

            if (now > expiryTime) {
                logger.info("Payment expired", { paymentId, createdAt: payment.createdAt });

                // Update payment status in database
                await this.prisma.payment.update({
                    where: { id: paymentId },
                    data: { status: "EXPIRED" }
                });

                // Track verification failure
                this.monitoringService.trackFailure(
                    paymentMethod,
                    VerificationErrorType.TIMEOUT_ERROR,
                    Date.now() - startTime
                );

                // Emit verification failure event
                this.realtimeService.emitVerificationFailure({
                    paymentId,
                    merchantId: payment.merchantId,
                    paymentMethod,
                    amount: payment.amount,
                    currency: payment.currency,
                    transactionId: payment.transactionId,
                    errorCode: VerificationErrorType.TIMEOUT_ERROR,
                    errorMessage: "Payment has expired"
                });

                return {
                    status: PaymentVerificationStatus.EXPIRED,
                    transactionId: payment.transactionId ?? "",
                    paymentMethod: payment.paymentMethod.type
                };
            }

            // Get merchant payment method configuration
            const merchantPaymentMethod: unknown = payment.merchant.paymentMethods.find(
                pm => pm.paymentMethodId === payment.paymentMethodId
            );

            if (!merchantPaymentMethod) {
                throw createVerificationError(
                    VerificationErrorType.INVALID_PARAMETERS,
                    `Merchant payment method not found for payment: ${paymentId}`,
                    null,
                    { method: "verifyPayment" }
                );
            }

            // Verify payment based on payment method type
            let verificationResult: PaymentVerificationResult;

            switch (payment.paymentMethod.type) {
            case BinancePaymentMethod.BINANCE_PAY:
                verificationResult = await this.verifyBinancePayPayment(payment, merchantPaymentMethod);
                break;

            case BinancePaymentMethod.BINANCE_C2C:
                verificationResult = await this.verifyBinanceC2CPayment(payment, merchantPaymentMethod);
                break;

            case BinancePaymentMethod.BINANCE_TRC20:
                verificationResult = await this.verifyBinanceTRC20Payment(payment, merchantPaymentMethod);
                break;

            case "crypto_transfer":
                verificationResult = await this.verifyCryptoTransferPayment(payment, merchantPaymentMethod);
                break;

            default:
                throw createVerificationError(
                    VerificationErrorType.INVALID_PARAMETERS,
                    `Unsupported payment method: ${payment.paymentMethod.type}`,
                    null,
                    { method: "verifyPayment" }
                );
            }

            // Update payment status in database if verified
            if (verificationResult.status === PaymentVerificationStatus.VERIFIED) {
                await this.prisma.payment.update({
                    where: { id: paymentId },
                    data: { status: "VERIFIED",
                        verifiedAt: new Date()
                    }
                });

                // Log successful verification
                logger.info("Payment verified successfully", {
                    paymentId,
                    paymentMethod: payment.paymentMethod.type,
                    transactionId: verificationResult.transactionId
                });

                // Track verification success
                this.monitoringService.trackSuccess(paymentMethod, Date.now() - startTime);

                // Emit verification success event
                this.realtimeService.emitVerificationSuccess({
                    paymentId,
                    merchantId: payment.merchantId,
                    paymentMethod: payment.paymentMethod.type,
                    amount: payment.amount,
                    currency: payment.currency,
                    transactionId: verificationResult.transactionId,
                    verifiedAt: new Date()
                });
            } else if (verificationResult.status === PaymentVerificationStatus.FAILED) {
                // Track verification failure
                this.monitoringService.trackFailure(
                    paymentMethod,
          verificationResult.errorCode as VerificationErrorType || VerificationErrorType.UNKNOWN_ERROR,
          Date.now() - startTime
                );

                // Emit verification failure event
                this.realtimeService.emitVerificationFailure({
                    paymentId,
                    merchantId: payment.merchantId,
                    paymentMethod: payment.paymentMethod.type,
                    amount: payment.amount,
                    currency: payment.currency,
                    transactionId: verificationResult.transactionId || payment.transactionId,
                    errorCode: verificationResult.errorCode,
                    errorMessage: verificationResult.errorMessage
                });
            }

            return verificationResult;
        } catch (error) {
            const latency: unknown = Date.now() - startTime;

            // Handle verification errors
            if (error instanceof VerificationError) {
                logger.error("Payment verification failed", {
                    paymentId,
                    errorCode: error.code,
                    errorMessage: (error as Error).message
                });

                // Track verification failure
                this.monitoringService.trackFailure(paymentMethod, error.code, latency);

                // Emit verification failure event
                this.realtimeService.emitVerificationFailure({
                    paymentId,
                    merchantId: payment?.merchantId,
                    paymentMethod,
                    amount: payment?.amount,
                    currency: payment?.currency,
                    transactionId: payment?.transactionId,
                    errorCode: error.code,
                    errorMessage: (error as Error).message
                });

                return {
                    status: PaymentVerificationStatus.FAILED,
                    transactionId: "",
                    paymentMethod,
                    errorMessage: (error as Error).message,
                    errorCode: error.code
                };
            }

            // Handle other errors
            logger.error("Unexpected error during payment verification", {
                paymentId,
                error: (error as Error).message || error
            });

            // Track verification failure
            this.monitoringService.trackFailure(paymentMethod, VerificationErrorType.UNKNOWN_ERROR, latency);

            // Emit verification failure event
            this.realtimeService.emitVerificationFailure({
                paymentId,
                merchantId: payment?.merchantId,
                paymentMethod,
                amount: payment?.amount,
                currency: payment?.currency,
                transactionId: payment?.transactionId,
                errorCode: VerificationErrorType.UNKNOWN_ERROR,
                errorMessage: (error as Error).message || "Unexpected error during verification"
            });

            return {
                status: PaymentVerificationStatus.FAILED,
                transactionId: "",
                paymentMethod,
                errorMessage: (error as Error).message || "Unexpected error during verification",
                errorCode: VerificationErrorType.UNKNOWN_ERROR
            };
        }
    }

    /**
   * Verify a Binance Pay payment
   * @param payment Payment details
   * @param merchantPaymentMethod Merchant payment method configuration
   * @returns Verification result
   */
    private async verifyBinancePayPayment(
        payment,
        merchantPaymentMethod
    ): Promise<PaymentVerificationResult> {
        try {
            // Check if transaction ID is provided
            if (!payment.transactionId) {
                throw createVerificationError(
                    VerificationErrorType.INVALID_PARAMETERS,
                    "Transaction ID is required for Binance Pay verification",
                    null,
                    { method: "verifyBinancePayPayment" }
                );
            }

            // Get merchant API keys
            const apiKey: unknown = merchantPaymentMethod.config?.apiKey;
            const secretKey: unknown = merchantPaymentMethod.config?.secretKey;

            if (!apiKey || !secretKey) {
                throw createVerificationError(
                    VerificationErrorType.AUTHENTICATION_ERROR,
                    "Binance Pay API keys are not configured",
                    null,
                    { method: "verifyBinancePayPayment" }
                );
            }

            // Verify transaction
            const result: unknown = await this.binanceApiService.verifyBinancePayTransaction(
                payment.transactionId,
                apiKey,
                secretKey,
                payment.amount.toString(),
                payment.currency
            );

            // Map result to verification result
            if (result.status === BinanceTransactionStatus.PAID) {
                return {
                    status: PaymentVerificationStatus.VERIFIED,
                    transactionId: result.transactionId,
                    paymentMethod: BinancePaymentMethod.BINANCE_PAY,
                    verifiedAt: new Date(),
                    rawData: result
                };
            } else {
                return {
                    status: PaymentVerificationStatus.PENDING,
                    transactionId: result.transactionId,
                    paymentMethod: BinancePaymentMethod.BINANCE_PAY,
                    rawData: result
                };
            }
        } catch (error) {
            // Handle verification errors
            if (error instanceof VerificationError) {
                return {
                    status: PaymentVerificationStatus.FAILED,
                    transactionId: payment.transactionId ?? "",
                    paymentMethod: BinancePaymentMethod.BINANCE_PAY,
                    errorMessage: (error as Error).message,
                    errorCode: error.code
                };
            }

            // Handle other errors
            const verificationError: unknown = handleVerificationError(
                error,
                "Failed to verify Binance Pay payment",
                { paymentId: payment.id }
            );

            return {
                status: PaymentVerificationStatus.FAILED,
                transactionId: payment.transactionId ?? "",
                paymentMethod: BinancePaymentMethod.BINANCE_PAY,
                errorMessage: (verificationError as Error).message,
                errorCode: verificationError.code
            };
        }
    }

    /**
   * Verify a Binance C2C payment
   * @param payment Payment details
   * @param merchantPaymentMethod Merchant payment method configuration
   * @returns Verification result
   */
    private async verifyBinanceC2CPayment(
        payment,
        merchantPaymentMethod
    ): Promise<PaymentVerificationResult> {
        try {
            // Check if note is provided
            const note: unknown = payment.metadata?.note;

            if (!note) {
                throw createVerificationError(
                    VerificationErrorType.INVALID_PARAMETERS,
                    "Note is required for Binance C2C verification",
                    null,
                    { method: "verifyBinanceC2CPayment" }
                );
            }

            // Get merchant API keys
            const apiKey: unknown = merchantPaymentMethod.config?.apiKey;
            const secretKey: unknown = merchantPaymentMethod.config?.secretKey;

            if (!apiKey || !secretKey) {
                throw createVerificationError(
                    VerificationErrorType.AUTHENTICATION_ERROR,
                    "Binance API keys are not configured",
                    null,
                    { method: "verifyBinanceC2CPayment" }
                );
            }

            // Verify transaction
            const result: unknown = await this.binanceApiService.verifyBinanceC2CTransactionByNote(
                note,
                apiKey,
                secretKey,
                payment.amount.toString(),
                payment.currency
            );

            // Map result to verification result
            if (result.status === BinanceTransactionStatus.PAID) {
                return {
                    status: PaymentVerificationStatus.VERIFIED,
                    transactionId: result.transactionId,
                    paymentMethod: BinancePaymentMethod.BINANCE_C2C,
                    verifiedAt: new Date(),
                    rawData: result
                };
            } else {
                return {
                    status: PaymentVerificationStatus.PENDING,
                    transactionId: result.transactionId,
                    paymentMethod: BinancePaymentMethod.BINANCE_C2C,
                    rawData: result
                };
            }
        } catch (error) {
            // Handle verification errors
            if (error instanceof VerificationError) {
                return {
                    status: PaymentVerificationStatus.FAILED,
                    transactionId: payment.transactionId ?? "",
                    paymentMethod: BinancePaymentMethod.BINANCE_C2C,
                    errorMessage: (error as Error).message,
                    errorCode: error.code
                };
            }

            // Handle other errors
            const verificationError: unknown = handleVerificationError(
                error,
                "Failed to verify Binance C2C payment",
                { paymentId: payment.id }
            );

            return {
                status: PaymentVerificationStatus.FAILED,
                transactionId: payment.transactionId ?? "",
                paymentMethod: BinancePaymentMethod.BINANCE_C2C,
                errorMessage: (verificationError as Error).message,
                errorCode: verificationError.code
            };
        }
    }

    /**
   * Verify a Binance TRC20 payment
   * @param payment Payment details
   * @param merchantPaymentMethod Merchant payment method configuration
   * @returns Verification result
   */
    private async verifyBinanceTRC20Payment(
        payment,
        merchantPaymentMethod
    ): Promise<PaymentVerificationResult> {
        try {
            // Check if transaction hash is provided
            const txHash: unknown = payment.transactionId;

            if (!txHash) {
                throw createVerificationError(
                    VerificationErrorType.INVALID_PARAMETERS,
                    "Transaction hash is required for Binance TRC20 verification",
                    null,
                    { method: "verifyBinanceTRC20Payment" }
                );
            }

            // Get merchant API keys and wallet address
            const apiKey: unknown = merchantPaymentMethod.config?.apiKey;
            const secretKey: unknown = merchantPaymentMethod.config?.secretKey;
            const walletAddress: unknown = merchantPaymentMethod.config?.walletAddress;

            if (!apiKey || !secretKey) {
                throw createVerificationError(
                    VerificationErrorType.AUTHENTICATION_ERROR,
                    "Binance API keys are not configured",
                    null,
                    { method: "verifyBinanceTRC20Payment" }
                );
            }

            if (!walletAddress) {
                throw createVerificationError(
                    VerificationErrorType.INVALID_PARAMETERS,
                    "Wallet address is not configured",
                    null,
                    { method: "verifyBinanceTRC20Payment" }
                );
            }

            // Verify transaction
            const result: unknown = await this.binanceApiService.verifyBinanceTRC20Transaction(
                txHash,
                apiKey,
                secretKey,
                payment.amount.toString(),
                walletAddress
            );

            // Map result to verification result
            if (result.status === BinanceTransactionStatus.PAID) {
                return {
                    status: PaymentVerificationStatus.VERIFIED,
                    transactionId: result.transactionId,
                    paymentMethod: BinancePaymentMethod.BINANCE_TRC20,
                    verifiedAt: new Date(),
                    rawData: result
                };
            } else {
                return {
                    status: PaymentVerificationStatus.PENDING,
                    transactionId: result.transactionId,
                    paymentMethod: BinancePaymentMethod.BINANCE_TRC20,
                    rawData: result
                };
            }
        } catch (error) {
            // Handle verification errors
            if (error instanceof VerificationError) {
                return {
                    status: PaymentVerificationStatus.FAILED,
                    transactionId: payment.transactionId ?? "",
                    paymentMethod: BinancePaymentMethod.BINANCE_TRC20,
                    errorMessage: (error as Error).message,
                    errorCode: error.code
                };
            }

            // Handle other errors
            const verificationError: unknown = handleVerificationError(
                error,
                "Failed to verify Binance TRC20 payment",
                { paymentId: payment.id }
            );

            return {
                status: PaymentVerificationStatus.FAILED,
                transactionId: payment.transactionId ?? "",
                paymentMethod: BinancePaymentMethod.BINANCE_TRC20,
                errorMessage: (verificationError as Error).message,
                errorCode: verificationError.code
            };
        }
    }

    /**
   * Verify a crypto transfer payment
   * @param payment Payment details
   * @param merchantPaymentMethod Merchant payment method configuration
   * @returns Verification result
   */
    private async verifyCryptoTransferPayment(
        payment,
        merchantPaymentMethod
    ): Promise<PaymentVerificationResult> {
        try {
            // Check if transaction hash is provided
            const txHash: unknown = payment.transactionId;

            if (!txHash) {
                throw createVerificationError(
                    VerificationErrorType.INVALID_PARAMETERS,
                    "Transaction hash is required for crypto transfer verification",
                    null,
                    { method: "verifyCryptoTransferPayment" }
                );
            }

            // Get wallet address and network
            const walletAddress: unknown = merchantPaymentMethod.config?.walletAddress;
            const network: unknown = payment.metadata?.network || merchantPaymentMethod.config?.network;

            if (!walletAddress) {
                throw createVerificationError(
                    VerificationErrorType.INVALID_PARAMETERS,
                    "Wallet address is not configured",
                    null,
                    { method: "verifyCryptoTransferPayment" }
                );
            }

            if (!network) {
                throw createVerificationError(
                    VerificationErrorType.INVALID_PARAMETERS,
                    "Network is not specified",
                    null,
                    { method: "verifyCryptoTransferPayment" }
                );
            }

            // Verify transaction
            const result: unknown = await this.blockchainVerificationService.verifyTransaction(
                txHash,
                walletAddress,
                payment.amount.toString(),
                payment.currency,
                network
            );

            // Map result to verification result
            return {
                status: result.verified ? PaymentVerificationStatus.VERIFIED : PaymentVerificationStatus.PENDING,
                transactionId: txHash,
                paymentMethod: "crypto_transfer",
                verifiedAt: result.verified ? new Date() : undefined,
                rawData: result
            };
        } catch (error) {
            // Handle verification errors
            if (error instanceof VerificationError) {
                return {
                    status: PaymentVerificationStatus.FAILED,
                    transactionId: payment.transactionId ?? "",
                    paymentMethod: "crypto_transfer",
                    errorMessage: (error as Error).message,
                    errorCode: error.code
                };
            }

            // Handle other errors
            const verificationError: unknown = handleVerificationError(
                error,
                "Failed to verify crypto transfer payment",
                { paymentId: payment.id }
            );

            return {
                status: PaymentVerificationStatus.FAILED,
                transactionId: payment.transactionId ?? "",
                paymentMethod: "crypto_transfer",
                errorMessage: (verificationError as Error).message,
                errorCode: verificationError.code
            };
        }
    }
}
