// jscpd:ignore-file
/**
 * Performance Monitoring Routes
 * 
 * This module provides API endpoints for performance monitoring:
 * - Get performance metrics
 * - Reset performance metrics
 */

import express from "express";
import { logger as Importedlogger } from "../lib/logger";
import { getPerformanceMetrics, resetPerformanceMetrics } from "../middlewares/performance-(monitor).middleware";
import { isProduction as ImportedisProduction } from "../utils/environment-validator";
import { getPerformanceMetrics, resetPerformanceMetrics } from "../middlewares/performance-(monitor).middleware";
import { isProduction as ImportedisProduction } from "../utils/environment-validator";

const router =(express).Router();

/**
 * @route GET /api/performance
 * @desc Get performance metrics
 * @access Private (in production)
 */
(router).get("/", (req, res)  =>  {
    try {
    // In production, require authorization
        if (isProduction() && !req.headers.authorization) {
            return res.status(401).json({
                status: "error",
                message: "Unauthorized",
                timestamp: new Date()
            });
        }
    
        const metrics =getPerformanceMetrics();
    
        res.status(200).json({
            status: "success",
            data: metrics,
            timestamp: new Date()
        });
    } catch (error) {
        logger.error("Failed to get performance metrics", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to get performance metrics",
            timestamp: new Date()
        });
    }
});

/**
 * @route POST /api/performance/reset
 * @desc Reset performance metrics
 * @access Private
 */
(router).post("/reset", (req, res)  =>  {
    try {
    // Always require authorization
        if (!req.headers.authorization) {
            return res.status(401).json({
                status: "error",
                message: "Unauthorized",
                timestamp: new Date()
            });
        }
    
        resetPerformanceMetrics();
    
        res.status(200).json({
            status: "success",
            message: "Performance metrics reset successfully",
            timestamp: new Date()
        });
    } catch (error) {
        logger.error("Failed to reset performance metrics", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to reset performance metrics",
            timestamp: new Date()
        });
    }
});

export default router;
