// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { logger } from "../utils/logger";
import paymentMonitoringService, {
    AlertLevel,
    MonitoringEventType,
    monitoringEvents
} from "../services/monitoring/payment-monitoring.service";
import { logger } from "../utils/logger";

/**
 * Monitoring controller
 */
const monitoringController = {
    /**
   * Get monitoring metrics
   */
    getMetrics: async (req: Request, res: Response) => {
        try {
            const metrics: unknown =paymentMonitoringService.getMetrics();
            return res.status(200).json(metrics);
        } catch (error) {
            logger.error("Error getting monitoring metrics:", error);
            return res.status(500).json({ error: "Failed to get monitoring metrics" });
        }
    },

    /**
   * Get alerts
   */
    getAlerts: async (req: Request, res: Response) => {
        try {
            const includeResolved: unknown = req.query.includeResolved === "true";
            const alerts: unknown =paymentMonitoringService.getAlerts(includeResolved);
            return res.status(200).json(alerts);
        } catch (error) {
            logger.error("Error getting alerts:", error);
            return res.status(500).json({ error: "Failed to get alerts" });
        }
    },

    /**
   * Resolve alert
   */
    resolveAlert: async (req: Request, res: Response) => {
        try {
            const { index } = req.params;
            const { resolution } = req.body;

            paymentMonitoringService.resolveAlert(parseInt(index), resolution);
            return res.status(200).json({ success: true });
        } catch (error) {
            logger.error("Error resolving alert:", error);
            return res.status(500).json({ error: "Failed to resolve alert" });
        }
    },

    /**
   * Create alert
   */
    createAlert: async (req: Request, res: Response) => {
        try {
            const { level, message, data } = req.body;

            if (!level || !message) {
                return res.status(400).json({ error: "Level and message are required" });
            }

            // Validate alert level
            if (!Object.values(AlertLevel).includes(level)) {
                return res.status(400).json({ error: "Invalid alert level" });
            }

            // Emit alert event
            monitoringEvents.emit(MonitoringEventType.ALERT, {
                type: MonitoringEventType.ALERT,
                timestamp: new Date().toISOString(),
                data: { level, message, ...data }
            });

            return res.status(201).json({ success: true });
        } catch (error) {
            logger.error("Error creating alert:", error);
            return res.status(500).json({ error: "Failed to create alert" });
        }
    },

    /**
   * Reset metrics
   */
    resetMetrics: async (req: Request, res: Response) => {
        try {
            paymentMonitoringService.resetMetrics();
            return res.status(200).json({ success: true });
        } catch (error) {
            logger.error("Error resetting metrics:", error);
            return res.status(500).json({ error: "Failed to reset metrics" });
        }
    },

    /**
   * Emit monitoring event
   */
    emitEvent: async (req: Request, res: Response) => {
        try {
            const { type, data, merchantId, paymentId } = req.body;

            if (!type || !Object.values(MonitoringEventType).includes(type)) {
                return res.status(400).json({ error: "Invalid event type" });
            }

            // Emit event
            monitoringEvents.emit(type, {
                type,
                timestamp: new Date().toISOString(),
                data: data ?? {},
                merchantId,
                paymentId
            });

            return res.status(200).json({ success: true });
        } catch (error) {
            logger.error("Error emitting monitoring event:", error);
            return res.status(500).json({ error: "Failed to emit monitoring event" });
        }
    }
};

export default monitoringController;
