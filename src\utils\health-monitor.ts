// jscpd:ignore-file
/**
 * Health Monitoring System
 *
 * This module provides comprehensive health monitoring for the application:
 * - System health checks
 * - Database connection monitoring
 * - API endpoint monitoring
 * - Resource usage tracking
 * - Alerting for critical issues
 */

import os from 'os';
import { logger } from '../lib/logger';
import prisma from '../lib/prisma-client';
import { isProduction, isDemo } from './environment-validator';

// Health check status types
export type HealthStatus = 'healthy' | 'degraded' | 'unhealthy';

// Health check result interface
export interface HealthCheckResult {
  status: HealthStatus;
  component: string;
  message: string;
  details?: unknown;
  timestamp: Date;
}

// System health interface
export interface SystemHealth {
  status: HealthStatus;
  checks: HealthCheckResult[];
  timestamp: Date;
  environment: string;
  version: string;
  uptime: number;
  resources: {
    cpu: number;
    memory: {
      total: number;
      free: number;
      used: number;
      usedPercent: number;
    };
  };
}

// Cache for health check results
let healthCache: SystemHealth | null = null;
let healthCacheTimestamp: number = 0;
const HEALTH_CACHE_TTL = 30 * 1000; // 30 seconds

/**
 * Check database health
 * @returns Health check result
 */
export async function checkDatabaseHealth(): Promise<HealthCheckResult> {
  try {
    // Try a simple query to check database connection
    await prisma.$queryRaw`SELECT 1`;

    return {
      status: 'healthy',
      component: 'database',
      message: 'Database connection is healthy',
      timestamp: new Date(),
    };
  } catch (error) {
    logger.error('Database health check failed', error);

    return {
      status: 'unhealthy',
      component: 'database',
      message: 'Database connection failed',
      details: isProduction() ? undefined : { error: (error as Error).message },
      timestamp: new Date(),
    };
  }
}

/**
 * Check memory usage
 * @returns Health check result
 */
export function checkMemoryHealth(): HealthCheckResult {
  const totalMemory: unknown = os.totalmem();
  const freeMemory: unknown = os.freemem();
  const usedMemory: unknown = totalMemory - freeMemory;
  const memoryUsagePercent = (usedMemory / totalMemory) * 100;

  let status: HealthStatus = 'healthy';
  let message: string = 'Memory usage is normal';

  if (memoryUsagePercent > 90) {
    status = 'unhealthy';
    message = 'Critical memory usage';
  } else if (memoryUsagePercent > 80) {
    status = 'degraded';
    message = 'High memory usage';
  }

  return {
    status,
    component: 'memory',
    message,
    details: {
      total: totalMemory,
      free: freeMemory,
      used: usedMemory,
      usedPercent: memoryUsagePercent,
    },
    timestamp: new Date(),
  };
}

/**
 * Check CPU usage
 * @returns Health check result
 */
export function checkCpuHealth(): HealthCheckResult {
  const cpus: unknown = os.cpus();
  const cpuUsage: unknown = process.cpuUsage();
  const totalCpuTime: unknown = cpuUsage.user + cpuUsage.system;

  // Calculate CPU usage percentage (rough estimate)
  const cpuUsagePercent = (totalCpuTime / (os.cpus().length * 1000000)) * 100;

  let status: HealthStatus = 'healthy';
  let message: string = 'CPU usage is normal';

  if (cpuUsagePercent > 90) {
    status = 'unhealthy';
    message = 'Critical CPU usage';
  } else if (cpuUsagePercent > 70) {
    status = 'degraded';
    message = 'High CPU usage';
  }

  return {
    status,
    component: 'cpu',
    message,
    details: {
      usage: cpuUsagePercent,
      cores: cpus.length,
    },
    timestamp: new Date(),
  };
}

/**
 * Check disk space
 * This is a placeholder since Node.js doesn't have built-in disk space checking
 * In a real implementation, you would use a library like 'diskusage'
 * @returns Health check result
 */
export function checkDiskHealth(): HealthCheckResult {
  // Placeholder for disk health check
  return {
    status: 'healthy',
    component: 'disk',
    message: 'Disk usage is normal',
    timestamp: new Date(),
  };
}

/**
 * Get overall system health
 * @returns System health information
 */
export async function getSystemHealth(): Promise<SystemHealth> {
  // Check if we have a recent health check result in cache
  const now: unknown = Date.now();
  if (healthCache && now - healthCacheTimestamp < HEALTH_CACHE_TTL) {
    return healthCache;
  }

  // Run all health checks
  const checks: HealthCheckResult[] = [
    await checkDatabaseHealth(),
    checkMemoryHealth(),
    checkCpuHealth(),
    checkDiskHealth(),
  ];

  // Determine overall status
  let overallStatus: HealthStatus = 'healthy';

  if (checks.some((check) => check.status === 'unhealthy')) {
    overallStatus = 'unhealthy';
  } else if (checks.some((check) => check.status === 'degraded')) {
    overallStatus = 'degraded';
  }

  // Get system resource information
  const memoryCheck: unknown = checks.find((check) => check.component === 'memory');
  const cpuCheck: unknown = checks.find((check) => check.component === 'cpu');

  const systemHealth: SystemHealth = {
    status: overallStatus,
    checks,
    timestamp: new Date(),
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '1.0.0',
    uptime: process.uptime(),
    resources: {
      cpu: cpuCheck?.details?.usage ?? 0,
      memory: {
        total: memoryCheck?.details?.total ?? 0,
        free: memoryCheck?.details?.free ?? 0,
        used: memoryCheck?.details?.used ?? 0,
        usedPercent: memoryCheck?.details?.usedPercent ?? 0,
      },
    },
  };

  // Update cache
  healthCache = systemHealth;
  healthCacheTimestamp = now;

  return systemHealth;
}

/**
 * Schedule regular health checks
 * @param interval Interval in milliseconds (default: 5 minutes)
 * @returns Timer ID
 */
export function scheduleHealthChecks(interval: number = 5 * 60 * 1000): NodeJS.Timeout {
  return setInterval(async () => {
    try {
      const health = await getSystemHealth();

      // Log health status
      if (health.status !== 'healthy') {
        logger.warn(`System health is ${health.status}`, {
          checks: health.checks.filter((check) => check.status !== 'healthy'),
        });
      } else {
        logger.debug('System health check passed');
      }
    } catch (error) {
      logger.error('Failed to run scheduled health check', error);
    }
  }, interval);
}

export default {
  getSystemHealth,
  checkDatabaseHealth,
  checkMemoryHealth,
  checkCpuHealth,
  checkDiskHealth,
  scheduleHealthChecks,
};
