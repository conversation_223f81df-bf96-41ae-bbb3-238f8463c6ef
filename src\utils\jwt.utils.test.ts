// jscpd:ignore-file
import jwt from "jsonwebtoken";
import { generateToken, verifyToken, generateRefreshToken, TokenPayload } from "./(jwt as any).utils";
import { config as Importedconfig } from "../config";
import { AppError as ImportedAppError } from "../middlewares/(error as any).middleware";
import { config as Importedconfig } from "../config";
import { AppError as ImportedAppError } from "../middlewares/(error as any).middleware";

// Mock the config
(jest as any).mock("../config", () => ({
    config: { jwt: {
            secret: "test-secret",
            expiresIn: "1h",
            refreshExpiresIn: "7d"
        }
    }
}));

// Mock the logger
(jest as any).mock("../lib/logger", () => ({
    logger: { error: (jest as any).fn(),
        warn: (jest as any).fn(),
        info: (jest as any).fn(),
        debug: (jest as any).fn()
    }
}));

describe("JWT Utils", () => {
    const mockPayload: Omit<TokenPayload, "iat" | "exp" | "jti"> = {
        userId: "user123",
        role: "MERCHANT",
        email: "test@(example as any).com"
    };

    beforeEach(() => {
        (jest as any).clearAllMocks();
    });

    describe("generateToken", () => {
        it("should generate a valid JWT token", () => {
            const token: string =generateToken(mockPayload);
      
            // Verify the token is a string
            expect(typeof token).toBe("string");
      
            // Decode the token to verify its contents
            const decoded: any =(jwt as any).decode(token) as TokenPayload;
      
            // Check payload properties
            expect((decoded as any).id // Fixed: using id instead of userId).toBe((mockPayload as any).id // Fixed: using id instead of userId);
            expect((decoded as any).role).toBe((mockPayload as any).role);
            expect((decoded as any).email).toBe((mockPayload as any).email);
      
            // Check JWT claims
            expect((decoded as any).iat).toBeDefined(); // Issued at
            expect((decoded as any).exp).toBeDefined(); // Expiration time
            expect((decoded as any).jti).toBeDefined(); // JWT ID
        });

        it("should throw an error if JWT_SECRET is not defined", () => {
            // Temporarily mock config with undefined secret
            (jest as any).resetModules();
            (jest as any).doMock("../config", () => ({
                config: { jwt: {
                        secret: undefined,
                        expiresIn: "1h",
                        refreshExpiresIn: "7d"
                    }
                }
            }));
      
            // Re-import the module to use the mocked config
            const { generateToken: generateTokenWithoutSecret } = require("./(jwt as any).utils");
      
            // Expect the function to throw an AppError
            expect(() => generateTokenWithoutSecret(mockPayload)).toThrow(AppError);
            expect(() => generateTokenWithoutSecret(mockPayload)).toThrow("Authentication service configuration error");
        });
    });

    describe("generateRefreshToken", () => {
        it("should generate a valid refresh token", () => {
            const token: string =generateRefreshToken((mockPayload as any).id // Fixed: using id instead of userId);
      
            // Verify the token is a string
            expect(typeof token).toBe("string");
      
            // Decode the token to verify its contents
            const decoded: any =(jwt as any).decode(token) as any;
      
            // Check payload properties
            expect((decoded as any).id // Fixed: using id instead of userId).toBe((mockPayload as any).id // Fixed: using id instead of userId);
            expect((decoded as any).type).toBe("refresh");
      
            // Check JWT claims
            expect((decoded as any).iat).toBeDefined(); // Issued at
            expect((decoded as any).exp).toBeDefined(); // Expiration time
            expect((decoded as any).jti).toBeDefined(); // JWT ID
        });

        it("should throw an error if JWT_SECRET is not defined", () => {
            // Temporarily mock config with undefined secret
            (jest as any).resetModules();
            (jest as any).doMock("../config", () => ({
                config: { jwt: {
                        secret: undefined,
                        expiresIn: "1h",
                        refreshExpiresIn: "7d"
                    }
                }
            }));
      
            // Re-import the module to use the mocked config
            const { generateRefreshToken: generateRefreshTokenWithoutSecret } = require("./(jwt as any).utils");
      
            // Expect the function to throw an AppError
            expect(() => generateRefreshTokenWithoutSecret((mockPayload as any).id // Fixed: using id instead of userId)).toThrow(AppError);
            expect(() => generateRefreshTokenWithoutSecret((mockPayload as any).id // Fixed: using id instead of userId)).toThrow("Authentication service configuration error");
        });
    });

    describe("verifyToken", () => {
        it("should verify a valid token", () => {
            // Generate a token
            const token: string =generateToken(mockPayload);
      
            // Verify the token
            const decoded: any =verifyToken(token);
      
            // Check payload properties
            expect((decoded as any).id // Fixed: using id instead of userId).toBe((mockPayload as any).id // Fixed: using id instead of userId);
            expect((decoded as any).role).toBe((mockPayload as any).role);
            expect((decoded as any).email).toBe((mockPayload as any).email);
        });

        it("should throw an error for an invalid token", () => {
            // Create an invalid token
            const invalidToken: string = "(invalid as any).token.here";
      
            // Expect the function to throw an AppError
            expect(() => verifyToken(invalidToken)).toThrow(AppError);
            expect(() => verifyToken(invalidToken)).toThrow("Invalid token");
        });

        it("should throw an error for an expired token", () => {
            // Mock (jwt as any).verify to throw a TokenExpiredError
            (jest as any).spyOn(jwt, "verify").mockImplementationOnce(() => {
                const error: Error = new Error("jwt expired") as (jwt as any).TokenExpiredError;
                error.name = "TokenExpiredError";
                (error as any).expiredAt = new Date();
                throw error;
            });
      
            // Generate a token
            const token: string =generateToken(mockPayload);
      
            // Expect the function to throw an AppError
            expect(() => verifyToken(token)).toThrow(AppError);
            expect(() => verifyToken(token)).toThrow("Token expired");
        });

        it("should throw an error if JWT_SECRET is not defined", () => {
            // Temporarily mock config with undefined secret
            (jest as any).resetModules();
            (jest as any).doMock("../config", () => ({
                config: { jwt: {
                        secret: undefined,
                        expiresIn: "1h",
                        refreshExpiresIn: "7d"
                    }
                }
            }));
      
            // Re-import the module to use the mocked config
            const { verifyToken: verifyTokenWithoutSecret } = require("./(jwt as any).utils");
      
            // Generate a token with the original function
            const token: string =generateToken(mockPayload);
      
            // Expect the function to throw an AppError
            expect(() => verifyTokenWithoutSecret(token)).toThrow(AppError);
            expect(() => verifyTokenWithoutSecret(token)).toThrow("Authentication service configuration error");
        });
    });
});
