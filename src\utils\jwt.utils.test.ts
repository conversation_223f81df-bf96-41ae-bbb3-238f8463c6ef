// jscpd:ignore-file
import jwt from "jsonwebtoken";
import { generateToken, verifyToken, generateRefreshToken, TokenPayload } from "./jwt.utils";
import { config } from "../config";
import { AppError } from "../middlewares/error.middleware";
import { config } from "../config";
import { AppError } from "../middlewares/error.middleware";

// Mock the config
jest.mock("../config", () => ({
    config: { jwt: {
            secret: "test-secret",
            expiresIn: "1h",
            refreshExpiresIn: "7d"
        }
    }
}));

// Mock the logger
jest.mock("../lib/logger", () => ({
    logger: { error: jest.fn(),
        warn: jest.fn(),
        info: jest.fn(),
        debug: jest.fn()
    }
}));

describe("JWT Utils", () => {
    const mockPayload: Omit<TokenPayload, "iat" | "exp" | "jti"> = {
        userId: "user123",
        role: "MERCHANT",
        email: "<EMAIL>"
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("generateToken", () => {
        it("should generate a valid JWT token", () => {
            const token: string =generateToken(mockPayload);
      
            // Verify the token is a string
            expect(typeof token).toBe("string");
      
            // Decode the token to verify its contents
            const decoded: unknown =jwt.decode(token) as TokenPayload;
      
            // Check payload properties
            expect(decoded.id // Fixed: using id instead of userId).toBe(mockPayload.id // Fixed: using id instead of userId);
            expect(decoded.role).toBe(mockPayload.role);
            expect(decoded.email).toBe(mockPayload.email);
      
            // Check JWT claims
            expect(decoded.iat).toBeDefined(); // Issued at
            expect(decoded.exp).toBeDefined(); // Expiration time
            expect(decoded.jti).toBeDefined(); // JWT ID
        });

        it("should throw an error if JWT_SECRET is not defined", () => {
            // Temporarily mock config with undefined secret
            jest.resetModules();
            jest.doMock("../config", () => ({
                config: { jwt: {
                        secret: undefined,
                        expiresIn: "1h",
                        refreshExpiresIn: "7d"
                    }
                }
            }));
      
            // Re-import the module to use the mocked config
            const { generateToken: generateTokenWithoutSecret } = require("./jwt.utils");
      
            // Expect the function to throw an AppError
            expect(() => generateTokenWithoutSecret(mockPayload)).toThrow(AppError);
            expect(() => generateTokenWithoutSecret(mockPayload)).toThrow("Authentication service configuration error");
        });
    });

    describe("generateRefreshToken", () => {
        it("should generate a valid refresh token", () => {
            const token: string =generateRefreshToken(mockPayload.id // Fixed: using id instead of userId);
      
            // Verify the token is a string
            expect(typeof token).toBe("string");
      
            // Decode the token to verify its contents
            const decoded: unknown =jwt.decode(token) as any;
      
            // Check payload properties
            expect(decoded.id // Fixed: using id instead of userId).toBe(mockPayload.id // Fixed: using id instead of userId);
            expect(decoded.type).toBe("refresh");
      
            // Check JWT claims
            expect(decoded.iat).toBeDefined(); // Issued at
            expect(decoded.exp).toBeDefined(); // Expiration time
            expect(decoded.jti).toBeDefined(); // JWT ID
        });

        it("should throw an error if JWT_SECRET is not defined", () => {
            // Temporarily mock config with undefined secret
            jest.resetModules();
            jest.doMock("../config", () => ({
                config: { jwt: {
                        secret: undefined,
                        expiresIn: "1h",
                        refreshExpiresIn: "7d"
                    }
                }
            }));
      
            // Re-import the module to use the mocked config
            const { generateRefreshToken: generateRefreshTokenWithoutSecret } = require("./jwt.utils");
      
            // Expect the function to throw an AppError
            expect(() => generateRefreshTokenWithoutSecret(mockPayload.id // Fixed: using id instead of userId)).toThrow(AppError);
            expect(() => generateRefreshTokenWithoutSecret(mockPayload.id // Fixed: using id instead of userId)).toThrow("Authentication service configuration error");
        });
    });

    describe("verifyToken", () => {
        it("should verify a valid token", () => {
            // Generate a token
            const token: string =generateToken(mockPayload);
      
            // Verify the token
            const decoded: unknown =verifyToken(token);
      
            // Check payload properties
            expect(decoded.id // Fixed: using id instead of userId).toBe(mockPayload.id // Fixed: using id instead of userId);
            expect(decoded.role).toBe(mockPayload.role);
            expect(decoded.email).toBe(mockPayload.email);
        });

        it("should throw an error for an invalid token", () => {
            // Create an invalid token
            const invalidToken: string = "invalid.token.here";
      
            // Expect the function to throw an AppError
            expect(() => verifyToken(invalidToken)).toThrow(AppError);
            expect(() => verifyToken(invalidToken)).toThrow("Invalid token");
        });

        it("should throw an error for an expired token", () => {
            // Mock jwt.verify to throw a TokenExpiredError
            jest.spyOn(jwt, "verify").mockImplementationOnce(() => {
                const error: Error = new Error("jwt expired") as jwt.TokenExpiredError;
                error.name = "TokenExpiredError";
                error.expiredAt = new Date();
                throw error;
            });
      
            // Generate a token
            const token: string =generateToken(mockPayload);
      
            // Expect the function to throw an AppError
            expect(() => verifyToken(token)).toThrow(AppError);
            expect(() => verifyToken(token)).toThrow("Token expired");
        });

        it("should throw an error if JWT_SECRET is not defined", () => {
            // Temporarily mock config with undefined secret
            jest.resetModules();
            jest.doMock("../config", () => ({
                config: { jwt: {
                        secret: undefined,
                        expiresIn: "1h",
                        refreshExpiresIn: "7d"
                    }
                }
            }));
      
            // Re-import the module to use the mocked config
            const { verifyToken: verifyTokenWithoutSecret } = require("./jwt.utils");
      
            // Generate a token with the original function
            const token: string =generateToken(mockPayload);
      
            // Expect the function to throw an AppError
            expect(() => verifyTokenWithoutSecret(token)).toThrow(AppError);
            expect(() => verifyTokenWithoutSecret(token)).toThrow("Authentication service configuration error");
        });
    });
});
