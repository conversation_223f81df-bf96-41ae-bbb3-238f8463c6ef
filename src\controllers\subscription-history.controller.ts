// jscpd:ignore-file

import { Request, Response, NextFunction } from 'express';
import subscriptionService from '../services/subscription.service';
import { format } from 'date-fns';
import { logger } from '../utils/logger';
import { format } from 'date-fns';
import { logger } from '../utils/logger';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

class SubscriptionHistoryController {
  /**
   * Get subscription history for a merchant
   * @param req Request
   * @param res Response
   */
  async getSubscriptionHistory(req: Request, res: Response) {
    try {
      const { merchantId } = req.params;
      const { startDate, endDate } = req.query;

      // Authorization check - ensure user can only view their own subscription history
      if (req.user?.role !== 'admin' && req.user?.merchantId !== merchantId) {
        return res.status(403).json({
          status: 'error',
          message: "You are not authorized to view this merchant's subscription history",
        });
      }

      // Parse dates if provided
      const parsedStartDate: unknown = startDate ? new Date(startDate as string) : undefined;
      const parsedEndDate: unknown = endDate ? new Date(endDate as string) : undefined;

      // Get subscription history
      const history: unknown = await subscriptionService.getSubscriptionHistory(
        merchantId,
        parsedStartDate,
        parsedEndDate
      );

      return res.status(200).json({
        status: 'success',
        data: history,
      });
    } catch (error) {
      logger.error('Subscription history error:', error);
      return res.status(500).json({
        status: 'error',
        message: (error as Error).message || 'Failed to retrieve subscription history',
      });
    }
  }

  /**
   * Download subscription history as CSV
   * @param req Request
   * @param res Response
   */
  async downloadSubscriptionHistory(req: Request, res: Response) {
    try {
      const { merchantId } = req.params;
      const { startDate, endDate } = req.query;

      // Authorization check - ensure user can only download their own subscription history
      if (req.user?.role !== 'admin' && req.user?.merchantId !== merchantId) {
        return res.status(403).json({
          status: 'error',
          message: "You are not authorized to download this merchant's subscription history",
        });
      }

      // Parse dates if provided
      const parsedStartDate: unknown = startDate ? new Date(startDate as string) : undefined;
      const parsedEndDate: unknown = endDate ? new Date(endDate as string) : undefined;

      // Get subscription history
      const history: unknown = await subscriptionService.getSubscriptionHistory(
        merchantId,
        parsedStartDate,
        parsedEndDate
      );

      // Convert to CSV
      const csvHeader: string = 'Date,Plan,Amount,Status\n';
      const csvRows: unknown = history.map((item) => {
        const date = format(new Date(item.date), 'yyyy-MM-dd');
        return `${date},${item.planName},${item.amount},${item.status}`;
      });

      const csvData: unknown = csvHeader + csvRows.join('\n');
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader(
        'Content-Disposition',
        `attachment; filename = subscription-history-${merchantId}.csv`
      );

      // Send the CSV data
      return res.send(csvData);
    } catch (error) {
      logger.error('Download error:', error);
      return res.status(500).json({
        status: 'error',
        message: (error as Error).message || 'Failed to download subscription history',
      });
    }
  }
}

export default new SubscriptionHistoryController();
