// Payment-related types and interfaces
export interface Payment {
  id: string;
  merchantId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethodId: string;
  transactionId?: string;
  description?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export type PaymentStatus = 
  | 'PENDING'
  | 'PROCESSING'
  | 'SUCCESS'
  | 'FAILED'
  | 'CANCELLED'
  | 'REFUNDED'
  | 'PARTIALLY_REFUNDED';

export interface PaymentMethod {
  id: string;
  merchantId: string;
  type: PaymentMethodType;
  name: string;
  isActive: boolean;
  configuration: PaymentMethodConfiguration;
  createdAt: Date;
  updatedAt: Date;
}

export type PaymentMethodType = 
  | 'CREDIT_CARD'
  | 'DEBIT_CARD'
  | 'BANK_TRANSFER'
  | 'DIGITAL_WALLET'
  | 'CRYPTOCURRENCY'
  | 'CASH'
  | 'CHECK';

export interface PaymentMethodConfiguration {
  [key: string]: any;
  // Specific configurations based on payment method type
  apiKey?: string;
  secretKey?: string;
  webhookUrl?: string;
  supportedCurrencies?: string[];
  fees?: PaymentFeeConfiguration;
}

export interface PaymentFeeConfiguration {
  fixedFee?: number;
  percentageFee?: number;
  minimumFee?: number;
  maximumFee?: number;
}

export interface Transaction {
  id: string;
  paymentId: string;
  merchantId: string;
  amount: number;
  currency: string;
  status: TransactionStatus;
  type: TransactionType;
  reference?: string;
  externalId?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export type TransactionStatus = 
  | 'PENDING'
  | 'SUCCESS'
  | 'FAILED'
  | 'CANCELLED'
  | 'EXPIRED';

export type TransactionType = 
  | 'PAYMENT'
  | 'REFUND'
  | 'CHARGEBACK'
  | 'ADJUSTMENT'
  | 'FEE';

export interface CreatePaymentRequest {
  merchantId: string;
  amount: number;
  currency: string;
  paymentMethodId: string;
  description?: string;
  metadata?: Record<string, any>;
  returnUrl?: string;
  cancelUrl?: string;
}

export interface UpdatePaymentRequest {
  status?: PaymentStatus;
  description?: string;
  metadata?: Record<string, any>;
}

export interface PaymentResponse {
  id: string;
  merchantId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: PaymentMethodResponse;
  transaction?: TransactionResponse;
  description?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaymentMethodResponse {
  id: string;
  type: PaymentMethodType;
  name: string;
  isActive: boolean;
}

export interface TransactionResponse {
  id: string;
  amount: number;
  currency: string;
  status: TransactionStatus;
  type: TransactionType;
  reference?: string;
  createdAt: Date;
}

export interface PaymentFilters {
  merchantId?: string;
  status?: PaymentStatus;
  paymentMethodId?: string;
  amountMin?: number;
  amountMax?: number;
  currency?: string;
  dateFrom?: Date;
  dateTo?: Date;
  search?: string;
}

export interface TransactionFilters {
  merchantId?: string;
  paymentId?: string;
  status?: TransactionStatus;
  type?: TransactionType;
  amountMin?: number;
  amountMax?: number;
  currency?: string;
  dateFrom?: Date;
  dateTo?: Date;
  search?: string;
}

export interface PaymentStatistics {
  totalPayments: number;
  successfulPayments: number;
  failedPayments: number;
  totalAmount: number;
  averageAmount: number;
  successRate: number;
  topPaymentMethods: PaymentMethodStatistic[];
  recentPayments: PaymentResponse[];
}

export interface PaymentMethodStatistic {
  paymentMethodId: string;
  paymentMethodName: string;
  count: number;
  totalAmount: number;
  percentage: number;
}

export interface RefundRequest {
  paymentId: string;
  amount?: number; // If not provided, full refund
  reason?: string;
  metadata?: Record<string, any>;
}

export interface RefundResponse {
  id: string;
  paymentId: string;
  amount: number;
  status: RefundStatus;
  reason?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
}

export type RefundStatus = 
  | 'PENDING'
  | 'SUCCESS'
  | 'FAILED'
  | 'CANCELLED';

export interface PaymentWebhook {
  id: string;
  merchantId: string;
  url: string;
  events: PaymentWebhookEvent[];
  secret: string;
  isActive: boolean;
  lastTriggeredAt?: Date;
  failureCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export type PaymentWebhookEvent = 
  | 'payment.created'
  | 'payment.updated'
  | 'payment.success'
  | 'payment.failed'
  | 'payment.cancelled'
  | 'transaction.created'
  | 'transaction.updated'
  | 'refund.created'
  | 'refund.success'
  | 'refund.failed';

export interface PaymentWebhookPayload {
  event: PaymentWebhookEvent;
  data: PaymentResponse | TransactionResponse | RefundResponse;
  timestamp: Date;
  merchantId: string;
}

export interface PaymentGatewayConfig {
  name: string;
  type: PaymentMethodType;
  isActive: boolean;
  configuration: PaymentMethodConfiguration;
  supportedCurrencies: string[];
  supportedCountries: string[];
  fees: PaymentFeeConfiguration;
}

export interface PaymentReport {
  period: {
    from: Date;
    to: Date;
  };
  summary: {
    totalPayments: number;
    successfulPayments: number;
    failedPayments: number;
    totalAmount: number;
    totalFees: number;
    netAmount: number;
  };
  breakdown: {
    byStatus: Record<PaymentStatus, number>;
    byPaymentMethod: Record<string, number>;
    byCurrency: Record<string, number>;
    byDay: Array<{
      date: Date;
      count: number;
      amount: number;
    }>;
  };
}

export interface PaymentValidationResult {
  isValid: boolean;
  errors: PaymentValidationError[];
}

export interface PaymentValidationError {
  field: string;
  message: string;
  code: string;
}

export interface PaymentProcessingOptions {
  async?: boolean;
  timeout?: number;
  retryAttempts?: number;
  webhookUrl?: string;
  metadata?: Record<string, any>;
}

export interface PaymentSecurityConfig {
  encryption: {
    algorithm: string;
    keySize: number;
  };
  tokenization: {
    enabled: boolean;
    provider?: string;
  };
  fraud: {
    enabled: boolean;
    rules: FraudRule[];
  };
}

export interface FraudRule {
  id: string;
  name: string;
  condition: string;
  action: 'BLOCK' | 'FLAG' | 'REVIEW';
  isActive: boolean;
}
