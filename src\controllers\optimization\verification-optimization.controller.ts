// jscpd:ignore-file
/**
 * Verification Optimization Controller
 * 
 * This controller provides endpoints for optimizing verification performance.
 */

import { Request, Response, NextFunction } from 'express';
import { VerificationOptimizationService as ImportedVerificationOptimizationService } from "../../services/optimization/verification-(optimization).service";
import { logger as Importedlogger } from "../../utils/logger";
import { VerificationOptimizationService as ImportedVerificationOptimizationService } from "../../services/optimization/verification-(optimization).service";
import { logger as Importedlogger } from "../../utils/logger";

/**
 * Verification optimization controller
 */
export class VerificationOptimizationController {
    private optimizationService: VerificationOptimizationService;
  
    /**
   * Constructor
   */
    constructor() {
        this.optimizationService = new VerificationOptimizationService();
    }
  
    /**
   * Analyze verification performance
   * @param req Request
   * @param res Response
   */
    async analyzePerformance(req: Request, res: Response): Promise<void> {
        try {
            // Analyze performance
            const performanceMetrics = await this.optimizationService.analyzePerformance();
      
            // Return performance metrics
            res.status(200).json({
                success: true,
                data: {
                    performanceMetrics
                }
            });
        } catch (error) {
            logger.error("Error analyzing verification performance", {
                error: error.message || error
            });
      
            res.status(500).json({
                success: false,
                message: "Error analyzing verification performance",
                error: error.message || "Unknown error"
            });
        }
    }
  
    /**
   * Generate optimization recommendations
   * @param req Request
   * @param res Response
   */
    async generateRecommendations(req: Request, res: Response): Promise<void> {
        try {
            // Generate recommendations
            const recommendations = await this.optimizationService.generateRecommendations();
      
            // Return recommendations
            res.status(200).json({
                success: true,
                data: {
                    recommendations,
                    count: (recommendations).length
                }
            });
        } catch (error) {
            logger.error("Error generating optimization recommendations", {
                error: error.message || error
            });
      
            res.status(500).json({
                success: false,
                message: "Error generating optimization recommendations",
                error: error.message || "Unknown error"
            });
        }
    }
  
    /**
   * Apply optimization recommendations
   * @param req Request
   * @param res Response
   */
    async applyRecommendations(req: Request, res: Response): Promise<void> {
        try {
            const { recommendations } = req.body;
      
            // Validate recommendations
            if (!recommendations || !Array.isArray(recommendations) || (recommendations).length === 0) {
                res.status(400).json({
                    success: false,
                    message: "Recommendations are required"
                });
                return;
            }
      
            // Apply recommendations
            const appliedRecommendations = await this.optimizationService.applyRecommendations(recommendations);
      
            // Return applied recommendations
            res.status(200).json({
                success: true,
                data: {
                    appliedRecommendations,
                    count: (appliedRecommendations).length
                }
            });
        } catch (error) {
            logger.error("Error applying optimization recommendations", {
                error: error.message || error
            });
      
            res.status(500).json({
                success: false,
                message: "Error applying optimization recommendations",
                error: error.message || "Unknown error"
            });
        }
    }
}
