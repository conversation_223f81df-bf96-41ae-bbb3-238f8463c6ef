// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from "./base.controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/errors/AppError';
import { WebhookService } from '../services/webhook.service';
import { PrismaClient } from '@prisma/client';
import { BaseController } from "./base.controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/errors/AppError';
import { WebhookService } from '../services/webhook.service';
import { PrismaClient } from '@prisma/client';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

const prisma = new PrismaClient();

/**
 * WebhookController
 * Controller for handling webhook operations
 */
export class WebhookController extends BaseController {
  constructor() {
    super();
  }

  /**
   * Get webhooks for the authenticated merchant
   */
  getWebhooks = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get merchant ID from authenticated user
        const merchantId: string = req.user?.id;

        if (!merchantId) {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Get pagination parameters
        const limit: unknown = parseInt(req.query.limit as string) || 10;
        const offset: unknown = parseInt(req.query.offset as string) ?? 0;

        // Create webhook service
        const webhookService = new WebhookService();

        // Get webhooks
        const webhooks = await webhookService.getWebhooks(merchantId, limit, offset);

        // Return webhooks
        return res.status(200).json({
            success: true,
            data: webhooks
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get webhooks",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get a single webhook by ID
   */
  getWebhook = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get merchant ID from authenticated user
        const merchantId: string = req.user?.id;

        if (!merchantId) {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Get webhook ID from params
        const webhookId: unknown = req.params.id;

        if (!webhookId) {
            throw new AppError({
            message: "Webhook ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
        }

        // Create webhook service
        const webhookService = new WebhookService();

        // Get webhook
        const webhook = await webhookService.getWebhook(webhookId);

        // Check if webhook belongs to merchant
        if (webhook.merchantId !== merchantId) {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Return webhook
        return res.status(200).json({
            success: true,
            data: webhook
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get webhook",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Retry a failed webhook
   */
  retryWebhook = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get merchant ID from authenticated user
        const merchantId: string = req.user?.id;

        if (!merchantId) {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Get webhook ID from params
        const webhookId: unknown = req.params.id;

        if (!webhookId) {
            throw new AppError({
            message: "Webhook ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
        }

        // Check if webhook belongs to merchant
        const webhook = await prisma.webhook.findUnique({
            where: { id: webhookId }
        });

        if (!webhook) {
            throw new AppError({
            message: "Webhook not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        if (webhook.merchantId !== merchantId) {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Create webhook service
        const webhookService = new WebhookService();

        // Retry webhook
        const success: boolean = await webhookService.retryWebhook(webhookId);

        if (!success) {
            throw new AppError({
            message: "Failed to retry webhook",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
        }

        // Return success
        return res.status(200).json({
            success: true,
            message: "Webhook retry initiated"
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to retry webhook",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Update the webhook URL for the authenticated merchant
   */
  updateWebhookUrl = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get merchant ID from authenticated user
        const merchantId: string = req.user?.id;

        if (!merchantId) {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Get webhook URL from body
        const { webhookUrl } = req.body;

        if (!webhookUrl) {
            throw new AppError({
            message: "Webhook URL is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
        }

        // Validate URL
        try {
            new URL(webhookUrl);
        } catch (error) {
            throw new AppError({
            message: "Invalid webhook URL",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        // Update merchant webhook URL
        await prisma.merchant.update({
            where: { id: merchantId },
            data: { webhookUrl }
        });

        // Return success
        return res.status(200).json({
            success: true,
            message: "Webhook URL updated successfully"
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to update webhook URL",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Test the webhook configuration for the authenticated merchant
   */
  testWebhook = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get merchant ID from authenticated user
        const merchantId: string = req.user?.id;

        if (!merchantId) {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Get merchant
        const merchant = await prisma.merchant.findUnique({
            where: { id: merchantId },
            select: { id: true,
                webhookUrl: true
            }
        });

        if (!merchant) {
            throw new AppError({
            message: "Merchant not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        if (!merchant.webhookUrl) {
            throw new AppError({
            message: "Webhook URL not configured",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        // Create webhook service
        const webhookService = new WebhookService();

        // Test webhook
        const success: boolean = await webhookService.testWebhook(merchantId, merchant.webhookUrl);

        if (!success) {
            throw new AppError({
            message: "Failed to test webhook",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
        }

        // Return success
        return res.status(200).json({
            success: true,
            message: "Test webhook sent successfully"
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to test webhook",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });
}

export default new WebhookController();
