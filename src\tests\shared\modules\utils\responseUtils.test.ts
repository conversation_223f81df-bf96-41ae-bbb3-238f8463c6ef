import { sendSuccess, sendError, createApiResponse } from '../../../../shared/modules/utils/responseUtils';
import { Response } from 'express';

describe('responseUtils', () => {
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };
  });

  describe('sendSuccess', () => {
    it('should send a success response with default values', () => {
      // Act
      sendSuccess(mockResponse as Response);
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Success',
        data: {}
      });
    });

    it('should send a success response with custom values', () => {
      // Arrange
      const data = { id: 1, name: 'Test' };
      const message = 'Custom message';
      const statusCode: number = 201;
      
      // Act
      sendSuccess(mockResponse as Response, data, message, statusCode);
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(statusCode);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message,
        data
      });
    });
  });

  describe('sendError', () => {
    it('should send an error response with default values', () => {
      // Act
      sendError(mockResponse as Response);
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Error',
        error: null
      });
    });

    it('should send an error response with custom values', () => {
      // Arrange
      const message: string = 'Custom error';
      const statusCode: number = 400;
      const error: Error = new Error('Test error');
      
      // Act
      sendError(mockResponse as Response, message, statusCode, error);
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(statusCode);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message,
        error: (error as Error).message
      });
    });

    it('should handle error objects with message property', () => {
      // Arrange
      const error: Error = { message: 'Error message' };
      
      // Act
      sendError(mockResponse as Response, 'Error', 500, error);
      
      // Assert
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Error',
        error: 'Error message'
      });
    });

    it('should handle non-object errors', () => {
      // Arrange
      const error: string = 'String error';
      
      // Act
      sendError(mockResponse as Response, 'Error', 500, error);
      
      // Assert
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: 'Error',
        error: 'String error'
      });
    });
  });

  describe('createApiResponse', () => {
    it('should create a success response', () => {
      // Arrange
      const success: boolean = true;
      const message: string = 'Success message';
      const data = { id: 1 };
      
      // Act
      const result =createApiResponse(success, message, data);
      
      // Assert
      expect(result).toEqual({
        success,
        message,
        data,
        error: null
      });
    });

    it('should create an error response', () => {
      // Arrange
      const success: boolean = false;
      const message: string = 'Error message';
      const error: string = 'Error details';
      
      // Act
      const result =createApiResponse(success, message, null, error);
      
      // Assert
      expect(result).toEqual({
        success,
        message,
        data: null,
        error
      });
    });

    it('should use default values when not provided', () => {
      // Arrange
      const success: boolean = true;
      const message: string = 'Message';
      
      // Act
      const result =createApiResponse(success, message);
      
      // Assert
      expect(result).toEqual({
        success,
        message,
        data: null,
        error: null
      });
    });
  });
});
