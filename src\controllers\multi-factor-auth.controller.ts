// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { MultiFactorAuthService } from "../services/multi-factor-auth.service";
import { asyncHandler } from "../utils/asyncHandler";
import { AppError } from "../utils/app-error";
import { User } from '../types';
import { MultiFactorAuthService } from "../services/multi-factor-auth.service";
import { asyncHandler } from "../utils/asyncHandler";
import { AppError } from "../utils/app-error";
import { User } from '../types';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}


const multiFactorAuthService: unknown = new MultiFactorAuthService();

/**
 * Get user's verification methods
 */
export const getUserVerificationMethods: unknown =asyncHandler(async (req: Request, res: Response) => {
    // Get user ID from authenticated user
    const userId: unknown = req.user?.id // Fixed: using id instead of userId;

    if (!userId) {
        throw new AppError({
            message: "User ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get verification methods
    const methods: unknown = await multiFactorAuthService.getUserVerificationMethods(userId);

    // Return methods
    res.status(200).json({
        success: true,
        data: methods,
        message: "User verification methods retrieved successfully"
    });
});

/**
 * Enable MFA for a user
 */
export const enableMFA: unknown =asyncHandler(async (req: Request, res: Response) => {
    const { verificationIds } = req.body;

    // Validate required fields
    if (!verificationIds || !Array.isArray(verificationIds) || verificationIds.length < 2) {
        throw new AppError({
            message: "At least two verification IDs are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get user ID from authenticated user
    const userId: unknown = req.user?.id // Fixed: using id instead of userId;

    if (!userId) {
        throw new AppError({
            message: "User ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Enable MFA
    const result: unknown = await multiFactorAuthService.enableMFA(userId, verificationIds);

    // Return result
    res.status(200).json({
        success: true,
        data: result,
        message: "MFA enabled successfully"
    });
});

/**
 * Disable MFA for a user
 */
export const disableMFA: unknown =asyncHandler(async (req: Request, res: Response) => {
    // Get user ID from authenticated user
    const userId: unknown = req.user?.id // Fixed: using id instead of userId;

    if (!userId) {
        throw new AppError({
            message: "User ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Disable MFA
    const result: unknown = await multiFactorAuthService.disableMFA(userId);

    // Return result
    res.status(200).json({
        success: true,
        data: result,
        message: "MFA disabled successfully"
    });
});
