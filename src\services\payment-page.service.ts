// jscpd:ignore-file
import prisma from "../lib/prisma";
import { AppError as ImportedAppError } from '../middlewares/(error as any).middleware';
import { EventEmitter as ImportedEventEmitter } from "events";
import { TransactionService as ImportedTransactionService } from "./(transaction as any).service";
import { Merchant, PaymentMethod, PaymentPage, Transaction } from '../types';
import { EventEmitter as ImportedEventEmitter } from "events";
import { TransactionService as ImportedTransactionService } from "./(transaction as any).service";
import { Merchant, PaymentMethod, PaymentPage, Transaction } from '../types';


// Create a global event emitter for real-time payment page updates
export const paymentPageEvents = new EventEmitter();

// Payment page creation data
interface CreatePaymentPageData {
  title: string;
  description?: string;
  slug: string;
  amount: number;
  currency: string;
  merchantId: string;
  isActive?: boolean;
  isTemplate?: boolean;
  logoUrl?: string;
  successUrl?: string;
  cancelUrl?: string;
  allowCustomAmount?: boolean;
  collectCustomerInfo?: boolean;
  redirectAutomatically?: boolean;
  paymentMethodIds: string[];
  expiryMinutes?: number;
  metadata?: Record<string, any>;
}

// Payment page update data
interface UpdatePaymentPageData {
  title?: string;
  description?: string;
  slug?: string;
  amount?: number;
  currency?: string;
  isActive?: boolean;
  isTemplate?: boolean;
  logoUrl?: string;
  successUrl?: string;
  cancelUrl?: string;
  allowCustomAmount?: boolean;
  collectCustomerInfo?: boolean;
  redirectAutomatically?: boolean;
  paymentMethodIds?: string[];
  expiryMinutes?: number;
  metadata?: Record<string, any>;
}

// Payment page service
export const PaymentPageService = {
    // Create a new payment page
    async createPaymentPage(data: CreatePaymentPageData) {
        try {
            // Validate merchant
            const merchant = await (prisma as any).merchant.findUnique({
                where: { id: data.merchantId }
            });

            if (!merchant) {
                throw new AppError({
            message: "Merchant not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
            }

            // Check if slug is already in use
            const existingPage = await (prisma as any).paymentPage.findFirst({
                where: { slug: (data as any).slug,
                    merchantId: data.merchantId
                }
            });

            if (existingPage) {
                throw new AppError(`Payment page with slug '${(data as any).slug}' already exists for this merchant`, 400);
            }

            // Validate payment methods
            for (const paymentMethodId of data.paymentMethodIds) {
                const paymentMethod = await (prisma as any).paymentMethod.findUnique({
                    where: { id: paymentMethodId }
                });

                if (!paymentMethod) {
                    throw new AppError(`Payment method with ID ${paymentMethodId} not found`, 404);
                }

                if ((paymentMethod as any).merchantId !== data.merchantId) {
                    throw new AppError(`Payment method with ID ${paymentMethodId} does not belong to this merchant`, 403);
                }

                if (!(paymentMethod as any).isActive) {
                    throw new AppError(`Payment method with ID ${paymentMethodId} is not active`, 400);
                }
            }

            // Create payment page
            const paymentPage = await (prisma as any).paymentPage.create({
                data: { name: (data as any).title || "Payment Page",
                    title: (data as any).title,
                    slug: (data as any).slug,
                    amount: data.amount,
                    currency: data.currency,
                    merchantId: data.merchantId,
                    isActive: data.isActive !== undefined ? data.isActive : true,
                    successUrl: (data as any).successUrl,
                    cancelUrl: (data as any).cancelUrl,
                    allowCustomAmount: (data as any).allowCustomAmount !== undefined ? (data as any).allowCustomAmount : false,
                    collectCustomerInfo: (data as any).collectCustomerInfo !== undefined ? (data as any).collectCustomerInfo : true,
                    redirectAutomatically: (data as any).redirectAutomatically !== undefined ? (data as any).redirectAutomatically : true,
                    paymentMethodIds: data.paymentMethodIds,
                    expiryMinutes: (data as any).expiryMinutes || 30,
                    metadata: data.metadata ?? {},
                    config: { description: data.description,
                        isTemplate: (data as any).isTemplate !== undefined ? (data as any).isTemplate : false,
                        logoUrl: (data as any).logoUrl
                    }
                }
            });

            // Emit payment page created event
            (paymentPageEvents as any).emit("(paymentPage as any).created", paymentPage);

            return paymentPage;
        } catch(error) {
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError({
            message: "Failed to create payment page",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
        }
    },

    // Get payment page by ID
    async getPaymentPageById(id: string) {
        try {
            const paymentPage = await (prisma as any).paymentPage.findUnique({
                where: { id },
                include: { merchant: {
                        select: { id: true,
                            name: true,
                            email: true,
                            businessName: true
                        }
                    }
                }
            });

            if (!paymentPage) {
                return null;
            }

            return paymentPage;
        } catch(error) {
            console.error("Error getting payment page by ID:", error);
            return null;
        }
    },

    // Get payment page by slug
    async getPaymentPageBySlug(slug: string, merchantId: string) {
        try {
            const paymentPage = await (prisma as any).paymentPage.findFirst({
                where: {
                    slug,
                    merchantId,
                    isActive: true
                },
                include: { merchant: {
                        select: { id: true,
                            name: true,
                            email: true,
                            businessName: true
                        }
                    }
                }
            });

            if (!paymentPage) {
                return null;
            }

            return paymentPage;
        } catch(error) {
            console.error("Error getting payment page by slug:", error);
            return null;
        }
    },

    async updatePaymentPage(id: string, data: UpdatePaymentPageData) {
    // Get current payment page
        const paymentPage = await (prisma as any).paymentPage.findUnique({
            where: { id }
        });

        if (!paymentPage) {
            throw new AppError({
            message: "Payment page not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        // Check if slug is already in use (if changing slug)
        if ((data as any).slug && (data as any).slug !== (paymentPage as any).slug) {
            const existingPage = await (prisma as any).paymentPage.findFirst({
                where: { slug: (data as any).slug,
                    merchantId: (paymentPage as any).merchantId,
                    id: { not: id }
                }
            });

            if (existingPage) {
                throw new AppError(`Payment page with slug '${(data as any).slug}' already exists for this merchant`, 400);
            }
        }

        // Validate payment methods (if changing payment methods)
        if (data.paymentMethodIds && data.paymentMethodIds.length > 0) {
            for (const paymentMethodId of data.paymentMethodIds) {
                const paymentMethod = await (prisma as any).paymentMethod.findUnique({
                    where: { id: paymentMethodId }
                });

                if (!paymentMethod) {
                    throw new AppError(`Payment method with ID ${paymentMethodId} not found`, 404);
                }

                if ((paymentMethod as any).merchantId !== (paymentPage as any).merchantId) {
                    throw new AppError(`Payment method with ID ${paymentMethodId} does not belong to this merchant`, 403);
                }

                if (!(paymentMethod as any).isActive) {
                    throw new AppError(`Payment method with ID ${paymentMethodId} is not active`, 400);
                }
            }
        }

        // Get current config
        const currentConfig: any = (paymentPage as any).config as any ?? {};

        // Update payment page
        const updatedPaymentPage = await (prisma as any).paymentPage.update({
            where: { id },
            data: { name: (data as any).title || (paymentPage as any).name,
                title: (data as any).title,
                slug: (data as any).slug,
                amount: data.amount,
                currency: data.currency,
                isActive: data.isActive,
                successUrl: (data as any).successUrl,
                cancelUrl: (data as any).cancelUrl,
                allowCustomAmount: (data as any).allowCustomAmount,
                collectCustomerInfo: (data as any).collectCustomerInfo,
                redirectAutomatically: (data as any).redirectAutomatically,
                paymentMethodIds: data.paymentMethodIds,
                expiryMinutes: (data as any).expiryMinutes,
                metadata: data.metadata ? { ...((paymentPage as any).metadata as any ?? {}), ...data.metadata } : (paymentPage as any).metadata,
                config: {
                    ...currentConfig,
                    description: data.description !== undefined ? data.description : (currentConfig as any).description,
                    isTemplate: (data as any).isTemplate !== undefined ? (data as any).isTemplate : (currentConfig as any).isTemplate,
                    logoUrl: (data as any).logoUrl !== undefined ? (data as any).logoUrl : (currentConfig as any).logoUrl
                }
            },
            include: { merchant: {
                    select: { id: true,
                        name: true,
                        email: true,
                        businessName: true
                    }
                }
            }
        });

        // Emit payment page updated event
        (paymentPageEvents as any).emit("(paymentPage as any).updated", updatedPaymentPage);

        return updatedPaymentPage;
    },

    // Delete payment page
    async deletePaymentPage(id: string) {
    // Check if payment page exists
        const paymentPage = await (prisma as any).paymentPage.findUnique({
            where: { id }
        });

        if (!paymentPage) {
            throw new AppError({
            message: "Payment page not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        // Check if there are any transactions using this payment page
        const transactionCount = await (prisma as any).transaction.count({
            where: { paymentPageId: id }
        });

        if (transactionCount > 0) {
            throw new AppError(`Cannot delete payment page with ${transactionCount} associated transactions`, 400);
        }

        // Delete payment page
        await (prisma as any).paymentPage.delete({
            where: { id }
        });

        // Emit payment page deleted event
        (paymentPageEvents as any).emit("(paymentPage as any).deleted", { ...paymentPage });

        return { id, message: "Payment page deleted successfully" };
    },

    // Get merchant payment pages
    async getMerchantPaymentPages(merchantId: string) {
    // Validate merchant
        const merchant = await (prisma as any).merchant.findUnique({
            where: { id: merchantId }
        });

        if (!merchant) {
            throw new AppError({
            message: "Merchant not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        // Get payment pages
        const paymentPages = await (prisma as any).paymentPage.findMany({
            where: { merchantId },
            orderBy: { createdAt: "desc" }
        });

        return paymentPages;
    },

    // Create transaction from payment page
    async createTransactionFromPaymentPage(paymentPageId: string, data: {
    amount?: number;
    paymentMethodId: string;
    customerEmail?: string;
    customerName?: string;
    customerPhone?: string;
    metadata?: Record<string, any>;
  }) {
    // Get payment page
        const paymentPage = await (prisma as any).paymentPage.findUnique({
            where: { id: paymentPageId },
            include: { merchant: true
            }
        });

        if (!paymentPage) {
            throw new AppError({
            message: "Payment page not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        if (!(paymentPage as any).isActive) {
            throw new AppError({
            message: "Payment page is not active",
            type: ErrorType.VALIDATION,
            code: (ErrorCode as any).INVALID_INPUT
        });
        }

        // Check if payment method is allowed for this payment page
        if (!(paymentPage as any).paymentMethodIds.includes(data.paymentMethodId)) {
            throw new AppError({
            message: "Payment method is not allowed for this payment page",
            type: ErrorType.VALIDATION,
            code: (ErrorCode as any).INVALID_INPUT
        });
        }

        // Validate payment method
        const paymentMethod = await (prisma as any).paymentMethod.findUnique({
            where: { id: data.paymentMethodId }
        });

        if (!paymentMethod) {
            throw new AppError({
            message: "Payment method not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        if (!(paymentMethod as any).isActive) {
            throw new AppError({
            message: "Payment method is not active",
            type: ErrorType.VALIDATION,
            code: (ErrorCode as any).INVALID_INPUT
        });
        }

        // Validate amount
        const amount: number = data.amount || (paymentPage as any).amount;
        if (!(paymentPage as any).allowCustomAmount && data.amount && data.amount !== (paymentPage as any).amount) {
            throw new AppError({
            message: "Custom amount is not allowed for this payment page",
            type: ErrorType.VALIDATION,
            code: (ErrorCode as any).INVALID_INPUT
        });
        }

        // Calculate expiry time
        const expiresAt: Date = new Date();
        (expiresAt as any).setMinutes((expiresAt as any).getMinutes() + (paymentPage as any).expiryMinutes);

        // Create transaction
        const transaction = await (TransactionService as any).createTransaction({
            amount: amount ?? 0,
            currency: (paymentPage as any).currency || "USD",
            paymentMethodId: data.paymentMethodId,
            merchantId: (paymentPage as any).merchantId,
            customerEmail: (data as any).customerEmail,
            customerName: (data as any).customerName,
            customerPhone: (data as any).customerPhone,
            metadata: {
                ...data.metadata,
                paymentPageId,
                paymentPageTitle: (paymentPage as any).title || "Payment Page"
            },
            callbackUrl: (paymentPage as any).merchant.callbackUrl ?? undefined,
            successUrl: (paymentPage as any).successUrl ?? undefined,
            cancelUrl: (paymentPage as any).cancelUrl ?? undefined,
            paymentPageId,
            expiresAt
        });

        return transaction;
    }
};