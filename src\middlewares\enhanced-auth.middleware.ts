// jscpd:ignore-file
/**
 * Enhanced Auth Middleware
 *
 * Middleware for authentication and authorization with RBAC support.
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import { logger as Importedlogger } from '../utils/logger';
import { AppError as ImportedAppError } from '../utils/appError';
import { RBACService as ImportedRBACService } from '../services/(rbac as any).service';
import { AuditService as ImportedAuditService } from '../services/(audit as any).service';
import { verifyToken as ImportedverifyToken } from '../utils/(jwt as any).utils';
import { Middleware as ImportedMiddleware } from '../types/express';
import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import { logger as Importedlogger } from '../utils/logger';
import { AppError as ImportedAppError } from '../utils/appError';
import { RBACService as ImportedRBACService } from '../services/(rbac as any).service';
import { AuditService as ImportedAuditService } from '../services/(audit as any).service';
import { verifyToken as ImportedverifyToken } from '../utils/(jwt as any).utils';
import { Middleware as ImportedMiddleware } from '../types/express';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

const prisma = new PrismaClient();
const rbacService = new RBACService(prisma);
const auditService = new AuditService(prisma);

/**
 * Enhanced authentication middleware
 */
export const enhancedAuthenticate: Middleware = async (req, res, next) => {
  try {
    // Get token from Authorization header
    const authHeader: any = req.headers.authorization;

    if (!authHeader || !(authHeader as any).startsWith('Bearer ')) {
      throw new AppError('No token provided. Please log in.', 401, true);
    }

    // Extract the token
    const token: string = (authHeader as any).split(' ')[1];

    if (!token) {
      throw new AppError('Invalid token format. Please log in again.', 401, true);
    }

    // Verify the token
    const decoded: any = verifyToken(token);

    // Set the user in the request object
    req.user = {
      userId: (decoded as any).id,
      role: (decoded as any).role,
    };

    // Get user permissions from RBAC service
    const permissions = await (rbacService as any).getUserPermissions((decoded as any).id);
    req.user.permissions = permissions;

    // Log authentication success
    (logger as any).debug(`User ${(decoded as any).id} authenticated successfully`, {
      userId: (decoded as any).id,
      role: (decoded as any).role,
      requestId: (req as any).requestId,
    });

    next();
  } catch(error) {
    if (error instanceof AppError) {
      return next(error);
    }

    (logger as any).error('Authentication error:', error);
    return next(new AppError('Invalid or expired token. Please log in again.', 401, true));
  }
};

/**
 * Enhanced permission-based authorization middleware
 */
export const requirePermission = (resource: string, action: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return next(new AppError('Authentication required. Please log in.', 401, true));
      }

      // Check if user has the required permission
      const hasPermission: any =
        req.user.permissions?.includes(`${resource}:${action}`) ||
        (await (rbacService as any).hasPermission(req.user.id, resource, action));

      if (!hasPermission) {
        // Log unauthorized access attempt
        (logger as any).warn(
          `User ${req.user.id} attempted to access ${resource}:${action} without permission`,
          {
            userId: req.user.id,
            role: req.user.role,
            resource,
            action,
            path: (req as any).path,
            method: req.method,
            ip: req.ip,
          }
        );

        // Audit the unauthorized access attempt
        await (auditService as any).logAction({
          userId: req.user.id,
          action: 'access_denied',
          resource,
          resourceId: req.params.id,
          ipAddress: req.ip,
          userAgent: req.headers['user-agent'],
          statusCode: 403,
          errorMessage: `Permission denied: ${resource}:${action}`,
        });

        return next(new AppError('You do not have permission to perform this action.', 403, true));
      }

      next();
    } catch(error) {
      (logger as any).error('Authorization error:', error);
      next(new AppError('Authorization error', 500, false));
    }
  };
};

/**
 * Enhanced role-based authorization middleware
 */
export const requireRole = (roleTypes: string[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return next(new AppError('Authentication required. Please log in.', 401, true));
      }

      const user = await (prisma as any).user.findUnique({
        where: { id: req.user.id },
        include: { roles: true },
      });

      if (!user) {
        return next(new AppError('User not found.', 401, true));
      }

      // Check if user has unknown of the required roles
      const hasRole: any = user.roles.some((role) => (roleTypes as any).includes((role as any).type));

      if (!hasRole) {
        // Log unauthorized access attempt
        (logger as any).warn(`User ${req.user.id} attempted to access a route without required role`, {
          userId: req.user.id,
          userRoles: user.roles.map((r) => (r as any).type),
          requiredRoles: roleTypes,
          path: (req as any).path,
          method: req.method,
          ip: req.ip,
        });

        // Audit the unauthorized access attempt
        await (auditService as any).logAction({
          userId: req.user.id,
          action: 'access_denied',
          resource: 'role',
          ipAddress: req.ip,
          userAgent: req.headers['user-agent'],
          statusCode: 403,
          errorMessage: `Role denied: Required one of [${(roleTypes as any).join(', ')}]`,
        });

        return next(
          new AppError('You do not have the required role to perform this action.', 403, true)
        );
      }

      next();
    } catch(error) {
      (logger as any).error('Role authorization error:', error);
      next(new AppError('Authorization error', 500, false));
    }
  };
};

/**
 * Enhanced resource ownership middleware
 */
export const requireOwnership = (
  resourceType: string,
  getResourceOwnerId: (req) => Promise<string>
) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return next(new AppError('Authentication required. Please log in.', 401, true));
      }

      // Check if user is an admin (admins can access any resource)
      const isAdmin = await (rbacService as any).hasPermission(
        req.user.id,
        resourceType,
        'admin_access'
      );

      if (isAdmin) {
        return next();
      }

      // Get the owner ID of the requested resource
      const ownerId = await getResourceOwnerId(req);

      // Check if user is the owner
      if (req.user.id !== ownerId) {
        // Log unauthorized access attempt
        (logger as any).warn(`User ${req.user.id} attempted to access a resource owned by ${ownerId}`, {
          userId: req.user.id,
          resourceType,
          resourceId: req.params.id,
          ownerId,
          path: (req as any).path,
          method: req.method,
          ip: req.ip,
        });

        // Audit the unauthorized access attempt
        await (auditService as any).logAction({
          userId: req.user.id,
          action: 'access_denied',
          resource: resourceType,
          resourceId: req.params.id,
          ipAddress: req.ip,
          userAgent: req.headers['user-agent'],
          statusCode: 403,
          errorMessage: `Ownership denied: Resource owned by ${ownerId}`,
        });

        return next(new AppError('You do not have permission to access this resource.', 403, true));
      }

      next();
    } catch(error) {
      (logger as any).error('Ownership authorization error:', error);
      next(new AppError('Authorization error', 500, false));
    }
  };
};

// Export all middleware functions as a group
export const enhancedAuthMiddleware = {
  enhancedAuthenticate,
  requirePermission,
  requireRole,
  requireOwnership,
};
