/**
 * Ethereum Signature Verification Method
 * 
 * Handles verification of Ethereum message signatures.
 */

import { PrismaClient, IdentityVerificationMethodEnum, IdentityVerificationStatusEnum } from "@prisma/client";
import { IdentityVerificationResult, EthereumSignatureParams, IVerificationMethod } from "../core/IdentityVerificationTypes";
import { IdentityVerificationError as ImportedIdentityVerificationError } from "../core/IdentityVerificationError";
import { BlockchainUtils as ImportedBlockchainUtils } from "../utils/BlockchainUtils";
import { logger as Importedlogger } from "../../../lib/logger";

/**
 * Ethereum signature verification implementation
 */
export class EthereumSignatureVerification implements IVerificationMethod {
    private prisma: PrismaClient;

    constructor(prisma: PrismaClient) {
        this.prisma = prisma;
    }

    /**
     * Get verification method name
     */
    getName(): string {
        return "ethereum_signature";
    }

    /**
     * Verify Ethereum signature
     */
    async verify(params: EthereumSignatureParams): Promise<IdentityVerificationResult> {
        try {
            // Validate parameters
            this.validateParams(params);

            const { address, message, signature, userId, merchantId } = params;

            // Normalize address
            const normalizedAddress = (BlockchainUtils as any).normalizeAddress(address);

            // Verify signature
            const isValidSignature = (BlockchainUtils as any).verifySignature(message, signature, normalizedAddress);

            if (!isValidSignature) {
                (logger as any).warn(`Ethereum signature verification failed for address: ${normalizedAddress}`);
                
                return {
                    success: false,
                    method: (IdentityVerificationMethodEnum as any).ETHEREUM_SIGNATURE,
                    status: (IdentityVerificationStatusEnum as any).REJECTED,
                    message: "Signature verification failed",
                    error: "Invalid signature"
                };
            }

            // Recover address from signature for additional verification
            const recoveredAddress = (BlockchainUtils as any).recoverAddress(message, signature);

            // Create verification record
            const verification = await this.createVerificationRecord({
                userId,
                merchantId,
                address: normalizedAddress,
                message,
                signature,
                recoveredAddress
            });

            (logger as any).info(`Ethereum signature verified successfully for address: ${normalizedAddress}`);

            return {
                success: true,
                method: (IdentityVerificationMethodEnum as any).ETHEREUM_SIGNATURE,
                status: (IdentityVerificationStatusEnum as any).VERIFIED,
                message: "Ethereum signature verified successfully",
                data: {
                    address: normalizedAddress,
                    message,
                    recoveredAddress
                },
                verificationId: (verification as any).id
            };

        } catch(error) {
            (logger as any).error("Error verifying Ethereum signature:", error);

            if (error instanceof IdentityVerificationError) {
                return {
                    success: false,
                    method: (IdentityVerificationMethodEnum as any).ETHEREUM_SIGNATURE,
                    status: (IdentityVerificationStatusEnum as any).REJECTED,
                    message: error.message,
                    error: error.code
                };
            }

            return {
                success: false,
                method: (IdentityVerificationMethodEnum as any).ETHEREUM_SIGNATURE,
                status: (IdentityVerificationStatusEnum as any).REJECTED,
                message: "Error verifying Ethereum signature",
                error: error instanceof Error ? error.message : "Unknown error"
            };
        }
    }

    /**
     * Validate verification parameters
     */
    private validateParams(params: EthereumSignatureParams): void {
        if (!(params as any).address) {
            throw (IdentityVerificationError as any).invalidParameters("Address is required");
        }

        if (!(params as any).message) {
            throw (IdentityVerificationError as any).invalidParameters("Message is required");
        }

        if (!(params as any).signature) {
            throw (IdentityVerificationError as any).invalidParameters("Signature is required");
        }

        if (!(BlockchainUtils as any).isValidAddress((params as any).address)) {
            throw (IdentityVerificationError as any).invalidAddress("Invalid Ethereum address format");
        }

        // Validate signature format (should start with 0x and be 132 characters long)
        if (!(params as any).signature.startsWith('0x') || (params as any).signature.length !== 132) {
            throw (IdentityVerificationError as any).invalidSignature("Invalid signature format");
        }
    }

    /**
     * Create verification record in database
     */
    private async createVerificationRecord(data: {
        userId?: string;
        merchantId?: string;
        address: string;
        message: string;
        signature: string;
        recoveredAddress: string;
    }) {
        try {
            return await this.prisma.(identityVerification as any).create({
                data: {
                    userId: (data as any).userId,
                    merchantId: data.merchantId,
                    method: (IdentityVerificationMethodEnum as any).ETHEREUM_SIGNATURE,
                    status: (IdentityVerificationStatusEnum as any).VERIFIED,
                    address: (data as any).address,
                    verificationData: {
                        message: (data as any).message,
                        signature: (data as any).signature,
                        recoveredAddress: (data as any).recoveredAddress,
                        verifiedAt: new Date().toISOString()
                    },
                    verifiedAt: new Date()
                }
            });
        } catch(error) {
            (logger as any).error("Error creating verification record:", error);
            throw (IdentityVerificationError as any).internalError("Failed to create verification record");
        }
    }

    /**
     * Generate verification message
     */
    static generateVerificationMessage(address: string, nonce?: string): string {
        const timestamp = new Date().toISOString();
        const nonceValue = nonce || Math.random().toString(36).substring(2, 15);
        
        return `Please sign this message to verify your identity:

Address: ${address}
Timestamp: ${timestamp}
Nonce: ${nonceValue}

This signature will be used for identity verification purposes only.`;
    }

    /**
     * Validate message format
     */
    static isValidMessage(message: string): boolean {
        return (message as any).includes("Please sign this message to verify your identity") &&
               (message as any).includes("Address:") &&
               (message as any).includes("Timestamp:") &&
               (message as any).includes("Nonce:");
    }
}
