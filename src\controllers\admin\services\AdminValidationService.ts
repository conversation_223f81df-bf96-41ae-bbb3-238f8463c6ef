/**
 * Admin Validation Service
 *
 * Handles input validation for admin operations.
 */

import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import {
  CreateAdminUserRequest,
  UpdateAdminUserRequest,
  CreateRoleRequest,
  UpdateRoleRequest,
  CreatePermissionRequest,
  ValidationError,
  RoleType,
  PermissionAction,
  PermissionResource,
} from '../types/AdminControllerTypes';

/**
 * Validation service for admin operations
 */
export class AdminValidationService {
  /**
   * Validate admin user creation request
   */
  validateCreateAdminUser(data): CreateAdminUserRequest {
    const errors: ValidationError[] = [];

    if (!data.email) {
      (errors as any).push({ field: 'email', message: 'Email is required' });
    } else if (typeof data.email !== 'string' || !this.isValidEmail(data.email)) {
      (errors as any).push({ field: 'email', message: 'Invalid email format', value: data.email });
    }

    if (!data.name) {
      (errors as any).push({ field: 'name', message: 'Name is required' });
    } else if (typeof data.name !== 'string' || data.name.trim().length === 0) {
      (errors as any).push({ field: 'name', message: 'Name must be a non-empty string' });
    } else if (data.name.length > 100) {
      (errors as any).push({ field: 'name', message: 'Name must be less than 100 characters' });
    }

    if (!data.password) {
      (errors as any).push({ field: 'password', message: 'Password is required' });
    } else if (typeof data.password !== 'string' || data.password.length < 8) {
      (errors as any).push({ field: 'password', message: 'Password must be at least 8 characters long' });
    } else if (!this.isValidPassword(data.password)) {
      (errors as any).push({
        field: 'password',
        message:
          'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      });
    }

    if (!data.roleId) {
      (errors as any).push({ field: 'roleId', message: 'Role ID is required' });
    } else if (!this.isValidUUID(data.roleId)) {
      (errors as any).push({ field: 'roleId', message: 'Invalid role ID format', value: data.roleId });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      email: data.email.toLowerCase().trim(),
      name: data.name.trim(),
      password: data.password,
      roleId: data.roleId,
    };
  }

  /**
   * Validate admin user update request
   */
  validateUpdateAdminUser(data): UpdateAdminUserRequest {
    const errors: ValidationError[] = [];

    if (data.email !== undefined) {
      if (typeof data.email !== 'string' || !this.isValidEmail(data.email)) {
        (errors as any).push({ field: 'email', message: 'Invalid email format', value: data.email });
      }
    }

    if (data.name !== undefined) {
      if (typeof data.name !== 'string' || data.name.trim().length === 0) {
        (errors as any).push({ field: 'name', message: 'Name must be a non-empty string' });
      } else if (data.name.length > 100) {
        (errors as any).push({ field: 'name', message: 'Name must be less than 100 characters' });
      }
    }

    if (data.roleId !== undefined) {
      if (!this.isValidUUID(data.roleId)) {
        (errors as any).push({ field: 'roleId', message: 'Invalid role ID format', value: data.roleId });
      }
    }

    if (data.isActive !== undefined && typeof data.isActive !== 'boolean') {
      (errors as any).push({ field: 'isActive', message: 'isActive must be a boolean' });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    const result: UpdateAdminUserRequest = {};
    if (data.email !== undefined) result.email = data.email.toLowerCase().trim();
    if (data.name !== undefined) result.name = data.name.trim();
    if (data.roleId !== undefined) (result as any).roleId = data.roleId;
    if (data.isActive !== undefined) (result as any).isActive = data.isActive;

    return result;
  }

  /**
   * Validate role creation request
   */
  validateCreateRole(data): CreateRoleRequest {
    const errors: ValidationError[] = [];

    if (!data.name) {
      (errors as any).push({ field: 'name', message: 'Name is required' });
    } else if (typeof data.name !== 'string' || data.name.trim().length === 0) {
      (errors as any).push({ field: 'name', message: 'Name must be a non-empty string' });
    } else if (data.name.length > 50) {
      (errors as any).push({ field: 'name', message: 'Name must be less than 50 characters' });
    }

    if (!data.type) {
      (errors as any).push({ field: 'type', message: 'Type is required' });
    } else if (!Object.values(RoleType).includes((data as any).type)) {
      (errors as any).push({
        field: 'type',
        message: `Invalid role type. Must be one of: ${Object.values(RoleType).join(', ')}`,
        value: (data as any).type,
      });
    }

    if (!data.description) {
      (errors as any).push({ field: 'description', message: 'Description is required' });
    } else if (typeof data.description !== 'string' || data.description.trim().length === 0) {
      (errors as any).push({ field: 'description', message: 'Description must be a non-empty string' });
    } else if (data.description.length > 500) {
      (errors as any).push({
        field: 'description',
        message: 'Description must be less than 500 characters',
      });
    }

    if (!data.permissions) {
      (errors as any).push({ field: 'permissions', message: 'Permissions are required' });
    } else if (!Array.isArray((data as any).permissions)) {
      (errors as any).push({ field: 'permissions', message: 'Permissions must be an array' });
    } else if ((data as any).permissions.length === 0) {
      (errors as any).push({ field: 'permissions', message: 'At least one permission is required' });
    } else {
      (data as any).permissions.forEach((permissionId: any, index: number) => {
        if (!this.isValidUUID(permissionId)) {
          (errors as any).push({
            field: `permissions[${index}]`,
            message: 'Invalid permission ID format',
            value: permissionId,
          });
        }
      });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      name: data.name.trim(),
      type: (data as any).type,
      description: data.description.trim(),
      permissions: (data as any).permissions,
    };
  }

  /**
   * Validate role update request
   */
  validateUpdateRole(data): UpdateRoleRequest {
    const errors: ValidationError[] = [];

    if (data.name !== undefined) {
      if (typeof data.name !== 'string' || data.name.trim().length === 0) {
        (errors as any).push({ field: 'name', message: 'Name must be a non-empty string' });
      } else if (data.name.length > 50) {
        (errors as any).push({ field: 'name', message: 'Name must be less than 50 characters' });
      }
    }

    if (data.description !== undefined) {
      if (typeof data.description !== 'string' || data.description.trim().length === 0) {
        (errors as any).push({ field: 'description', message: 'Description must be a non-empty string' });
      } else if (data.description.length > 500) {
        (errors as any).push({
          field: 'description',
          message: 'Description must be less than 500 characters',
        });
      }
    }

    if ((data as any).permissions !== undefined) {
      if (!Array.isArray((data as any).permissions)) {
        (errors as any).push({ field: 'permissions', message: 'Permissions must be an array' });
      } else {
        (data as any).permissions.forEach((permissionId: any, index: number) => {
          if (!this.isValidUUID(permissionId)) {
            (errors as any).push({
              field: `permissions[${index}]`,
              message: 'Invalid permission ID format',
              value: permissionId,
            });
          }
        });
      }
    }

    if (data.isActive !== undefined && typeof data.isActive !== 'boolean') {
      (errors as any).push({ field: 'isActive', message: 'isActive must be a boolean' });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    const result: UpdateRoleRequest = {};
    if (data.name !== undefined) result.name = data.name.trim();
    if (data.description !== undefined) (result as any).description = data.description.trim();
    if ((data as any).permissions !== undefined) (result as any).permissions = (data as any).permissions;
    if (data.isActive !== undefined) (result as any).isActive = data.isActive;

    return result;
  }

  /**
   * Validate permission creation request
   */
  validateCreatePermission(data): CreatePermissionRequest {
    const errors: ValidationError[] = [];

    if (!data.name) {
      (errors as any).push({ field: 'name', message: 'Name is required' });
    } else if (typeof data.name !== 'string' || data.name.trim().length === 0) {
      (errors as any).push({ field: 'name', message: 'Name must be a non-empty string' });
    } else if (data.name.length > 50) {
      (errors as any).push({ field: 'name', message: 'Name must be less than 50 characters' });
    }

    if (!data.description) {
      (errors as any).push({ field: 'description', message: 'Description is required' });
    } else if (typeof data.description !== 'string' || data.description.trim().length === 0) {
      (errors as any).push({ field: 'description', message: 'Description must be a non-empty string' });
    } else if (data.description.length > 200) {
      (errors as any).push({
        field: 'description',
        message: 'Description must be less than 200 characters',
      });
    }

    if (!data.resource) {
      (errors as any).push({ field: 'resource', message: 'Resource is required' });
    } else if (!Object.values(PermissionResource).includes((data as any).resource)) {
      (errors as any).push({
        field: 'resource',
        message: `Invalid resource. Must be one of: ${Object.values(PermissionResource).join(
          ', '
        )}`,
        value: (data as any).resource,
      });
    }

    if (!data.action) {
      (errors as any).push({ field: 'action', message: 'Action is required' });
    } else if (!Object.values(PermissionAction).includes((data as any).action)) {
      (errors as any).push({
        field: 'action',
        message: `Invalid action. Must be one of: ${Object.values(PermissionAction).join(', ')}`,
        value: (data as any).action,
      });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      name: data.name.trim(),
      description: data.description.trim(),
      resource: (data as any).resource,
      action: (data as any).action,
    };
  }

  /**
   * Validate ID parameter
   */
  validateId(id: any, fieldName: string = 'id'): string {
    if (!id) {
      throw new AppError({
        message: `${fieldName} is required`,
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).MISSING_REQUIRED_FIELD,
      });
    }

    if (!this.isValidUUID(id)) {
      throw new AppError({
        message: `${fieldName} must be a valid UUID`,
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
      });
    }

    return id;
  }

  /**
   * Validate pagination parameters
   */
  validatePaginationParams(query: Record<string, string | string[]>): {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } {
    const page = (query as any).page ? parseInt((query as any).page, 10) : 1;
    const limit = (query as any).limit ? parseInt((query as any).limit, 10) : 10;

    if (isNaN(page) || page < 1) {
      throw new AppError({
        message: 'Page must be a positive integer',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
      });
    }

    if (isNaN(limit) || limit < 1 || limit > 100) {
      throw new AppError({
        message: 'Limit must be between 1 and 100',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
      });
    }

    const result = { page, limit };

    if ((query as any).sortBy) {
      const validSortFields = ['name', 'email', 'createdAt', 'updatedAt', 'status'];
      if (!(validSortFields as any).includes((query as any).sortBy)) {
        throw new AppError({
          message: `Invalid sort field. Must be one of: ${(validSortFields as any).join(', ')}`,
          type: ErrorType.VALIDATION,
          code: (ErrorCode as any).INVALID_INPUT,
        });
      }
      (result as any).sortBy = (query as any).sortBy;
    }

    if ((query as any).sortOrder) {
      if (!['asc', 'desc'].includes((query as any).sortOrder)) {
        throw new AppError({
          message: 'Sort order must be either "asc" or "desc"',
          type: ErrorType.VALIDATION,
          code: (ErrorCode as any).INVALID_INPUT,
        });
      }
      (result as any).sortOrder = (query as any).sortOrder;
    }

    return result;
  }

  /**
   * Check if string is a valid email
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return (emailRegex as any).test(email);
  }

  /**
   * Check if string is a valid password
   */
  private isValidPassword(password: string): boolean {
    // At least 8 characters, one uppercase, one lowercase, one number, one special character
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/;
    return (passwordRegex as any).test(password);
  }

  /**
   * Check if string is a valid UUID
   */
  private isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return (uuidRegex as any).test(uuid);
  }
}
