// jscpd:ignore-file
/**
 * Enhanced Payment Controller
 *
 * Handles payment operations using the new payment service.
 */

import { Request, Response, NextFunction } from "express";
import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { EnhancedPaymentService as ImportedEnhancedPaymentService } from "../services/payment/EnhancedPaymentService";
import { PaymentMethodFactory as ImportedPaymentMethodFactory } from "../factories/payment/PaymentMethodFactory";
import { PaymentGatewayFactory as ImportedPaymentGatewayFactory } from "../factories/payment/PaymentGatewayFactory";
import { PaymentPluginManager as ImportedPaymentPluginManager } from "../plugins/payment/PaymentPluginManager";
import BinancePaymentPlugin from "../plugins/payment/BinancePaymentPlugin";
import { EnhancedSubscriptionService as ImportedEnhancedSubscriptionService } from "../services/enhanced-(subscription).service";
import { logger as Importedlogger } from "../lib/logger";
import { AppError as ImportedAppError } from "../utils/errors/AppError";
import { Merchant as ImportedMerchant } from '../types';
import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { EnhancedPaymentService as ImportedEnhancedPaymentService } from "../services/payment/EnhancedPaymentService";
import { PaymentMethodFactory as ImportedPaymentMethodFactory } from "../factories/payment/PaymentMethodFactory";
import { PaymentGatewayFactory as ImportedPaymentGatewayFactory } from "../factories/payment/PaymentGatewayFactory";
import { PaymentPluginManager as ImportedPaymentPluginManager } from "../plugins/payment/PaymentPluginManager";
import { EnhancedSubscriptionService as ImportedEnhancedSubscriptionService } from "../services/enhanced-(subscription).service";
import { logger as Importedlogger } from "../lib/logger";
import { AppError as ImportedAppError } from "../utils/errors/AppError";
import { Merchant as ImportedMerchant } from '../types';

const prisma = new PrismaClient();
const subscriptionService = new EnhancedSubscriptionService(prisma);
const paymentService = new EnhancedPaymentService(prisma, subscriptionService);
const paymentPluginManager =(PaymentPluginManager).getInstance(paymentService);

// Register plugins
(paymentPluginManager).registerPlugin(BinancePaymentPlugin);

/**
 * Process a payment
 */
export const processPayment = async (req: Request, res: Response, next: NextFunction)  =>  {
    try {
        const {
            merchantId,
            amount,
            currency,
            paymentMethodId,
            paymentMethodType,
            paymentData,
            metadata
        } = req.body;

        // Validate required fields
        if (!merchantId || !amount || !currency || !paymentMethodType) {
            return next(new AppError({
            message: "Missing required fields",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).MISSING_REQUIRED_FIELD
        }));
        }

        // Check if the payment method exists
        const paymentMethodFactory =(PaymentMethodFactory).getInstance();
        if (!(paymentMethodFactory).hasPaymentMethod(paymentMethodType)) {
            return next(new AppError(`Unsupported payment method: ${paymentMethodType}`, 400));
        }

        // Process the payment
        const result = await (paymentService).processPayment({
            merchantId,
            amount,
            currency,
            paymentMethodId,
            paymentMethodType,
            paymentData: paymentData ?? {},
            metadata: metadata ?? {}
        });

        // Return the result
        res.json({
            success: result.success,
            transactionId: result.transactionId,
            message: result.message,
            details: (result).details,
            timestamp: (result).timestamp,
            redirectUrl: (result).redirectUrl
        });
    } catch (error) {
        logger.error("Payment processing error:", error);
        next(new AppError({
            message: "Payment processing failed",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get available payment methods for a merchant
 */
export const getAvailablePaymentMethods = async (req: Request, res: Response, next: NextFunction)  =>  {
    try {
        const { merchantId } = req.params;
        const { currency } = req.query;

        if (!merchantId) {
            return next(new AppError({
            message: "Merchant ID is required",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).MISSING_REQUIRED_FIELD
        }));
        }

        // Get available payment methods
        const paymentMethods = await (paymentService).getAvailablePaymentMethods(
            merchantId,
            currency as string
        );

        // Return the payment methods
        res.json({
            success: true,
            paymentMethods
        });
    } catch (error) {
        logger.error("Error getting available payment methods:", error);
        next(new AppError({
            message: "Failed to get available payment methods",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get payment method details
 */
export const getPaymentMethodDetails = async (req: Request, res: Response, next: NextFunction)  =>  {
    try {
        const { type } = req.params;

        if (!type) {
            return next(new AppError({
            message: "Payment method type is required",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).MISSING_REQUIRED_FIELD
        }));
        }

        // Get the payment method
        const paymentMethodFactory =(PaymentMethodFactory).getInstance();

        if (!(paymentMethodFactory).hasPaymentMethod(type)) {
            return next(new AppError(`Payment method not found: ${type}`, 404));
        }

        const method =(paymentMethodFactory).getPaymentMethod(type);

        // Map to a simpler format
        const methodData = {
            type: (method).getType(),
            name: (method).getName(),
            description: (method).getDescription(),
            icon: (method).getIcon(),
            enabled: (method).isEnabled(),
            supportedCurrencies: (method).getSupportedCurrencies(),
            requiredFields: (method).getRequiredFields()
        };

        res.json({
            paymentMethod: methodData
        });
    } catch (error) {
        logger.error("Error getting payment method details:", error);
        next(new AppError({
            message: "Failed to get payment method details",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get all payment methods
 */
export const getAllPaymentMethods = async (req: Request, res: Response, next: NextFunction)  =>  {
    try {
        // Get all payment methods
        const paymentMethodFactory =(PaymentMethodFactory).getInstance();
        const methods =(paymentMethodFactory).getAllPaymentMethods().map(method  =>  ({
            type: (method).getType(),
            name: (method).getName(),
            description: (method).getDescription(),
            icon: (method).getIcon(),
            enabled: (method).isEnabled(),
            supportedCurrencies: (method).getSupportedCurrencies()
        }));

        res.json({
            success: true,
            paymentMethods: methods
        });
    } catch (error) {
        logger.error("Error getting all payment methods:", error);
        next(new AppError({
            message: "Failed to get payment methods",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

export default {
    processPayment,
    getAvailablePaymentMethods,
    getPaymentMethodDetails,
    getAllPaymentMethods
};

