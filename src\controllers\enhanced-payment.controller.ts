// jscpd:ignore-file
/**
 * Enhanced Payment Controller
 *
 * Handles payment operations using the new payment service.
 */

import { Request, Response, NextFunction } from "express";
import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { EnhancedPaymentService as ImportedEnhancedPaymentService } from "../services/payment/EnhancedPaymentService";
import { PaymentMethodFactory as ImportedPaymentMethodFactory } from "../factories/payment/PaymentMethodFactory";
import { PaymentGatewayFactory as ImportedPaymentGatewayFactory } from "../factories/payment/PaymentGatewayFactory";
import { PaymentPluginManager as ImportedPaymentPluginManager } from "../plugins/payment/PaymentPluginManager";
import BinancePaymentPlugin from "../plugins/payment/BinancePaymentPlugin";
import { EnhancedSubscriptionService as ImportedEnhancedSubscriptionService } from "../services/enhanced-(subscription as any).service";
import { logger as Importedlogger } from "../lib/logger";
import { AppError as ImportedAppError } from "../utils/errors/AppError";
import { Merchant as ImportedMerchant } from '../types';
import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { EnhancedPaymentService as ImportedEnhancedPaymentService } from "../services/payment/EnhancedPaymentService";
import { PaymentMethodFactory as ImportedPaymentMethodFactory } from "../factories/payment/PaymentMethodFactory";
import { PaymentGatewayFactory as ImportedPaymentGatewayFactory } from "../factories/payment/PaymentGatewayFactory";
import { PaymentPluginManager as ImportedPaymentPluginManager } from "../plugins/payment/PaymentPluginManager";
import { EnhancedSubscriptionService as ImportedEnhancedSubscriptionService } from "../services/enhanced-(subscription as any).service";
import { logger as Importedlogger } from "../lib/logger";
import { AppError as ImportedAppError } from "../utils/errors/AppError";
import { Merchant as ImportedMerchant } from '../types';

const prisma = new PrismaClient();
const subscriptionService = new EnhancedSubscriptionService(prisma);
const paymentService = new EnhancedPaymentService(prisma, subscriptionService);
const paymentPluginManager: any =(PaymentPluginManager as any).getInstance(paymentService);

// Register plugins
(paymentPluginManager as any).registerPlugin(BinancePaymentPlugin);

/**
 * Process a payment
 */
export const processPayment = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const {
            merchantId,
            amount,
            currency,
            paymentMethodId,
            paymentMethodType,
            paymentData,
            metadata
        } = req.body;

        // Validate required fields
        if (!merchantId || !amount || !currency || !paymentMethodType) {
            return next(new AppError({
            message: "Missing required fields",
            type: ErrorType.VALIDATION,
            code: (ErrorCode as any).MISSING_REQUIRED_FIELD
        }));
        }

        // Check if the payment method exists
        const paymentMethodFactory: any =(PaymentMethodFactory as any).getInstance();
        if (!(paymentMethodFactory as any).hasPaymentMethod(paymentMethodType)) {
            return next(new AppError(`Unsupported payment method: ${paymentMethodType}`, 400));
        }

        // Process the payment
        const result = await (paymentService as any).processPayment({
            merchantId,
            amount,
            currency,
            paymentMethodId,
            paymentMethodType,
            paymentData: paymentData ?? {},
            metadata: metadata ?? {}
        });

        // Return the result
        res.json({
            success: result.success,
            transactionId: result.transactionId,
            message: (result as any).message,
            details: (result as any).details,
            timestamp: (result as any).timestamp,
            redirectUrl: (result as any).redirectUrl
        });
    } catch(error) {
        (logger as any).error("Payment processing error:", error);
        next(new AppError({
            message: "Payment processing failed",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get available payment methods for a merchant
 */
export const getAvailablePaymentMethods = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { merchantId } = req.params;
        const { currency } = req.query;

        if (!merchantId) {
            return next(new AppError({
            message: "Merchant ID is required",
            type: ErrorType.VALIDATION,
            code: (ErrorCode as any).MISSING_REQUIRED_FIELD
        }));
        }

        // Get available payment methods
        const paymentMethods = await (paymentService as any).getAvailablePaymentMethods(
            merchantId,
            currency as string
        );

        // Return the payment methods
        res.json({
            success: true,
            paymentMethods
        });
    } catch(error) {
        (logger as any).error("Error getting available payment methods:", error);
        next(new AppError({
            message: "Failed to get available payment methods",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get payment method details
 */
export const getPaymentMethodDetails = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { type } = req.params;

        if (!type) {
            return next(new AppError({
            message: "Payment method type is required",
            type: ErrorType.VALIDATION,
            code: (ErrorCode as any).MISSING_REQUIRED_FIELD
        }));
        }

        // Get the payment method
        const paymentMethodFactory: any =(PaymentMethodFactory as any).getInstance();

        if (!(paymentMethodFactory as any).hasPaymentMethod(type)) {
            return next(new AppError(`Payment method not found: ${type}`, 404));
        }

        const method: any =(paymentMethodFactory as any).getPaymentMethod(type);

        // Map to a simpler format
        const methodData = {
            type: (method as any).getType(),
            name: (method as any).getName(),
            description: (method as any).getDescription(),
            icon: (method as any).getIcon(),
            enabled: (method as any).isEnabled(),
            supportedCurrencies: (method as any).getSupportedCurrencies(),
            requiredFields: (method as any).getRequiredFields()
        };

        res.json({
            paymentMethod: methodData
        });
    } catch(error) {
        (logger as any).error("Error getting payment method details:", error);
        next(new AppError({
            message: "Failed to get payment method details",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get all payment methods
 */
export const getAllPaymentMethods = async (req: Request, res: Response, next: NextFunction) => {
    try {
        // Get all payment methods
        const paymentMethodFactory: any =(PaymentMethodFactory as any).getInstance();
        const methods: any =(paymentMethodFactory as any).getAllPaymentMethods().map(method => ({
            type: (method as any).getType(),
            name: (method as any).getName(),
            description: (method as any).getDescription(),
            icon: (method as any).getIcon(),
            enabled: (method as any).isEnabled(),
            supportedCurrencies: (method as any).getSupportedCurrencies()
        }));

        res.json({
            success: true,
            paymentMethods: methods
        });
    } catch(error) {
        (logger as any).error("Error getting all payment methods:", error);
        next(new AppError({
            message: "Failed to get payment methods",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

export default {
    processPayment,
    getAvailablePaymentMethods,
    getPaymentMethodDetails,
    getAllPaymentMethods
};

