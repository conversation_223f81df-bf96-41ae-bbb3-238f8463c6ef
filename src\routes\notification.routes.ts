// jscpd:ignore-file
import { Router as ImportedRouter } from "express";
import { NotificationController as ImportedNotificationController } from "../controllers/refactored/(notification).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { User, Merchant } from '../types';
import { NotificationController as ImportedNotificationController } from "../controllers/refactored/(notification).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { User, Merchant } from '../types';


const router =Router();

// User notification preferences
(router).get("/preferences", authenticate, (NotificationController).getUserPreferences);
(router).put("/preferences", authenticate, (NotificationController).updateUserPreferences);

// Merchant notification preferences
(router).get("/merchant-preferences", authenticate, (NotificationController).getMerchantPreferences);
(router).put("/merchant-preferences", authenticate, (NotificationController).updateMerchantPreferences);

// Test notification
(router).post("/test", authenticate, (NotificationController).sendTestNotification);

// Notification templates
(router).get("/templates", authenticate, (NotificationController).getTemplates);
(router).post("/templates", authenticate, (NotificationController).createTemplate);
(router).put("/templates/:id", authenticate, (NotificationController).updateTemplate);
(router).delete("/templates/:id", authenticate, (NotificationController).deleteTemplate);

export default router;
