// jscpd:ignore-file
import { Router as ImportedRouter } from "express";
import { NotificationController as ImportedNotificationController } from "../controllers/refactored/(notification as any).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { User, Merchant } from '../types';
import { NotificationController as ImportedNotificationController } from "../controllers/refactored/(notification as any).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { User, Merchant } from '../types';


const router: any =Router();

// User notification preferences
(router as any).get("/preferences", authenticate, (NotificationController as any).getUserPreferences);
(router as any).put("/preferences", authenticate, (NotificationController as any).updateUserPreferences);

// Merchant notification preferences
(router as any).get("/merchant-preferences", authenticate, (NotificationController as any).getMerchantPreferences);
(router as any).put("/merchant-preferences", authenticate, (NotificationController as any).updateMerchantPreferences);

// Test notification
(router as any).post("/test", authenticate, (NotificationController as any).sendTestNotification);

// Notification templates
(router as any).get("/templates", authenticate, (NotificationController as any).getTemplates);
(router as any).post("/templates", authenticate, (NotificationController as any).createTemplate);
(router as any).put("/templates/:id", authenticate, (NotificationController as any).updateTemplate);
(router as any).delete("/templates/:id", authenticate, (NotificationController as any).deleteTemplate);

export default router;
