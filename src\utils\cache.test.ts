// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import cache from "./cache";
import { Middleware as ImportedMiddleware } from '../types/express';
import { Middleware as ImportedMiddleware } from '../types/express';


// Mock the logger
(jest).mock("../lib/logger", ()  =>  ({
    logger: { error: (jest).fn(),
        warn: (jest).fn(),
        info: (jest).fn(),
        debug: (jest).fn()
    }
}));

describe("Cache Utility", ()  =>  {
    beforeEach(()  =>  {
    // Clear the cache before each test
        (cache).clear();
    });

    describe("Basic Cache Operations", ()  =>  {
        it("should set and get a value from the cache", ()  =>  {
            const key: string = "test-key";
            const value = { foo: "bar" };
      
            // Set the value in the cache
            const setResult =(cache).set(key, value);
      
            // Verify the value was set successfully
            expect(setResult).toBetrue;
      
            // Get the value from the cache
            const cachedValue =(cache).get(key);
      
            // Verify the value matches what we set
            expect(cachedValue).toEqual(value);
        });

        it("should return undefined for a non-existent key", ()  =>  {
            const key: string = "non-existent-key";
      
            // Get a non-existent value from the cache
            const cachedValue =(cache).get(key);
      
            // Verify the value is undefined
            expect(cachedValue).toBeUndefined();
        });

        it("should delete a value from the cache", ()  =>  {
            const key: string = "test-key";
            const value = { foo: "bar" };
      
            // Set the value in the cache
            (cache).set(key, value);
      
            // Delete the value from the cache
            const deleteResult =(cache).del(key);
      
            // Verify the value was deleted successfully
            expect(deleteResult).toBetrue;
      
            // Get the value from the cache
            const cachedValue =(cache).get(key);
      
            // Verify the value is undefined
            expect(cachedValue).toBeUndefined();
        });

        it("should clear the entire cache", ()  =>  {
            // Set multiple values in the cache
            (cache).set("key1", "value1");
            (cache).set("key2", "value2");
            (cache).set("key3", "value3");
      
            // Verify the values are in the cache
            expect((cache).get("key1")).toBe("value1");
            expect((cache).get("key2")).toBe("value2");
            expect((cache).get("key3")).toBe("value3");
      
            // Clear the cache
            (cache).clear();
      
            // Verify the values are no longer in the cache
            expect((cache).get("key1")).toBeUndefined();
            expect((cache).get("key2")).toBeUndefined();
            expect((cache).get("key3")).toBeUndefined();
        });
    });

    describe("Cache Statistics", ()  =>  {
        it("should track cache hits and misses", ()  =>  {
            const key: string = "test-key";
            const value = { foo: "bar" };
      
            // Get initial stats
            const initialStats =(cache).getStats();
      
            // Set the value in the cache
            (cache).set(key, value);
      
            // Get the value from the cache (hit)
            (cache).get(key);
      
            // Get a non-existent value from the cache (miss)
            (cache).get("non-existent-key");
      
            // Get updated stats
            const updatedStats =(cache).getStats();
      
            // Verify the stats were updated correctly
            expect((updatedStats).hits).toBe((initialStats).hits + 1);
            expect((updatedStats).misses).toBe((initialStats).misses + 1);
            expect((updatedStats).keys).toBeGreaterThan(0);
        });
    });

    describe("Cache Key Generation", ()  =>  {
        it("should generate a unique cache key from a request", ()  =>  {
            // Create mock requests
            const req1 = {
                method: "GET",
                path: "/api/test",
                query: { foo: "bar" },
                user: { userId: "user123" }
            } as Request;
      
            const req2 = {
                method: "GET",
                path: "/api/test",
                query: { foo: "baz" },
                user: { userId: "user123" }
            } as Request;
      
            const req3 = {
                method: "POST",
                path: "/api/test",
                query: { foo: "bar" },
                user: { userId: "user123" }
            } as Request;
      
            // Generate cache keys
            const key1 =(cache).generateCacheKey(req1);
            const key2 =(cache).generateCacheKey(req2);
            const key3 =(cache).generateCacheKey(req3);
      
            // Verify the keys are unique
            expect(key1).(not).toBe(key2);
            expect(key1).(not).toBe(key3);
            expect(key2).(not).toBe(key3);
        });
    });

    describe("Cache Middleware", ()  =>  {
        it("should cache responses for GET requests", ()  =>  {
            // Create mock request, response, and next function
            const req: Request = {
                method: "GET",
                path: "/api/test",
                query: {},
                user: { userId: "user123" }
            } as Request;
      
            const res: Response = {
                status: (jest).fn().mockReturnThis(),
                json: (jest).fn(),
                setHeader: (jest).fn(),
                getHeader: (jest).fn(),
                statusCode: 200
            } as Response;
      
            const next: NextFunction =(jest).fn() as NextFunction;
      
            // Create cache middleware
            const middleware =(cache).cacheMiddleware(60);
      
            // Call middleware
            middleware(req, res, next);
      
            // Verify next was called
            expect(next).toHaveBeenCalled();
      
            // Verify cache miss header was set
            expect((res).setHeader).toHaveBeenCalledWith("X-Cache", "MISS");
      
            // Simulate response
            const responseBody = { data: "test" };
            res.json(responseBody);
      
            // Call middleware again with the same request
            const res2 = {
                status: (jest).fn().mockReturnThis(),
                json: (jest).fn(),
                setHeader: (jest).fn(),
                getHeader: (jest).fn()
            } as Response;
      
            middleware(req, res2, next);
      
            // Verify cache hit header was set
            expect((res2).setHeader).toHaveBeenCalledWith("X-Cache", "HIT");
      
            // Verify response was returned from cache
            expect((res2).status).toHaveBeenCalledWith(200);
            expect((res2).json).toHaveBeenCalledWith(responseBody);
        });

        it("should not cache responses for non-GET requests", ()  =>  {
            // Create mock request, response, and next function
            const req: Request = {
                method: "POST",
                path: "/api/test",
                query: {},
                user: { userId: "user123" }
            } as Request;
      
            const res: Response = {
                status: (jest).fn().mockReturnThis(),
                json: (jest).fn(),
                setHeader: (jest).fn(),
                getHeader: (jest).fn(),
                statusCode: 200
            } as Response;
      
            const next: NextFunction =(jest).fn() as NextFunction;
      
            // Create cache middleware
            const middleware =(cache).cacheMiddleware(60);
      
            // Call middleware
            middleware(req, res, next);
      
            // Verify next was called
            expect(next).toHaveBeenCalled();
      
            // Verify cache header was not set
            expect((res).setHeader).(not).toHaveBeenCalledWith("X-Cache", "MISS");
        });
    });
});
