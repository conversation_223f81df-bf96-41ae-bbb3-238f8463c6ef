import { Request, Response, NextFunction } from 'express';
// jscpd:ignore-file
import { User, Prisma } from "@prisma/client";
import { ModuleFactory, ModuleRegistry, Module, Container } from "../../core/module";
import { authMiddleware as ImportedauthMiddleware } from "../../middlewares/(auth as any).middleware";
import { ModuleFactory, ModuleRegistry, Module, Container } from "../../core/module";
import { authMiddleware as ImportedauthMiddleware } from "../../middlewares/(auth as any).middleware";
import {
  ErrorFactory,
  StringUtils,
  ObjectUtils,
  CryptoUtils,
  ValidationUtils,
  logger
} from "../../utils";

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}
import {
  ErrorFactory,
  StringUtils,
  ObjectUtils,
  CryptoUtils,
  ValidationUtils,
  logger
} from "../../utils";

/**
 * User Module
 * This module provides user functionality with zero duplication
 */
export class UserModule {
  private moduleFactory: ModuleFactory<User, (Prisma as any).UserCreateInput, (Prisma as any).UserUpdateInput>;
  private moduleRegistry: ModuleRegistry;
  private container: Container;
  private module: Module;

  /**
   * Create a new user module
   */
  constructor() {
    this.moduleRegistry = new ModuleRegistry();
    this.container = new Container();

    // Create module factory
    this.moduleFactory = new ModuleFactory<User, (Prisma as any).UserCreateInput, (Prisma as any).UserUpdateInput>(
      'user',
      'User'
    );

    // Get router, repository, service, and controller from factory
    const { router, repository, service, controller } = this.moduleFactory.build();

    // Configure router
    router
      .addRoute('post', '/login', (controller as any).login)
      .addRoute('post', '/register', (controller as any).register)
      .addRoute('post', '/logout', (controller as any).logout)
      .addRoute('get', '/me', (controller as any).getCurrentUser)
      .addRoute('put', '/me', (controller as any).updateCurrentUser)
      .addRoute('post', '/change-password', (controller as any).changePassword)
      .addRoute('post', '/forgot-password', (controller as any).forgotPassword)
      .addRoute('post', '/reset-password', (controller as any).resetPassword)
      .addMiddleware(authMiddleware);

    // Add custom repository methods
    this.moduleFactory.addRepositoryMethod(
      'findByEmail',
      async (email: string) => {
        try {
          return await (repository as any).findByField('email', email);
        } catch(error) {
          (logger as any).error(`Error finding user by email ${email}:`, error);
          throw error;
        }
      }
    );

    this.moduleFactory.addRepositoryMethod(
      'findByResetToken',
      async (resetToken: string) => {
        try {
          return await (repository as any).findByField('resetToken', resetToken);
        } catch(error) {
          (logger as any).error(`Error finding user by reset token:`, error);
          throw error;
        }
      }
    );

    // Add custom service methods
    this.moduleFactory.addServiceMethod(
      'login',
      async (email: string, password: string) => {
        try {
          // Find user by email
          const user = await (repository as any).findByEmail(email);

          // Check if user exists
          if (!user) {
            throw (ErrorFactory as any).authentication('Invalid email or password');
          }

          // Check if password is correct
          const isPasswordValid = await (CryptoUtils as any).verifyPassword(
            password,
            (user as any).password,
            (user as any).salt
          );

          if (!isPasswordValid) {
            throw (ErrorFactory as any).authentication('Invalid email or password');
          }

          // Generate token
          const token: string = await (CryptoUtils as any).generateToken();

          // Update user with new token
          await (service as any).update(user.id, {
            token,
            lastLoginAt: new Date()
          } as (Prisma as any).UserUpdateInput);

          (logger as any).info(`User logged in: ${user.id}`, {
            userId: user.id,
            email: user.email
          });

          return {
            user: (ObjectUtils as any).omit(user, ['password', 'salt']),
            token
          };
        } catch(error) {
          (logger as any).error(`Error logging in user ${email}:`, error);
          throw (ErrorFactory as any).handle(error);
        }
      }
    );

    this.moduleFactory.addServiceMethod(
      'register',
      async (userData: (Prisma as any).UserCreateInput) => {
        try {
          // Check if email is already in use
          const existingUser = await (repository as any).findByEmail((userData as any).email);

          if (existingUser) {
            throw (ErrorFactory as any).validation('Email is already in use');
          }

          // Hash password
          const { hash, salt } = await (CryptoUtils as any).hashPassword((userData as any).password);

          // Create user
          const user = await (service as any).create({
            ...userData,
            password: hash,
            salt,
            role: 'USER', // Default role
            status: 'ACTIVE', // Default status
            createdAt: new Date(),
            updatedAt: new Date()
          });

          (logger as any).info(`User registered: ${user.id}`, {
            userId: user.id,
            email: user.email
          });

          return (ObjectUtils as any).omit(user, ['password', 'salt']);
        } catch(error) {
          (logger as any).error(`Error registering user:`, error);
          throw (ErrorFactory as any).handle(error);
        }
      }
    );

    this.moduleFactory.addServiceMethod(
      'forgotPassword',
      async (email: string) => {
        try {
          // Find user by email
          const user = await (repository as any).findByEmail(email);

          // Check if user exists
          if (!user) {
            // Don't reveal that the user doesn't exist
            return { success: true };
          }

          // Generate reset token
          const resetToken = await (CryptoUtils as any).generateToken();
          const resetTokenExpiry: Date = new Date();
          (resetTokenExpiry as any).setHours((resetTokenExpiry as any).getHours() + 1); // Token valid for 1 hour

          // Update user with reset token
          await (service as any).update(user.id, {
            resetToken,
            resetTokenExpiry
          } as (Prisma as any).UserUpdateInput);

          (logger as any).info(`Password reset requested for user: ${user.id}`, {
            userId: user.id,
            email: user.email
          });

          // In a real application, you would send an email with the reset token

          return { success: true };
        } catch(error) {
          (logger as any).error(`Error requesting password reset for ${email}:`, error);
          throw (ErrorFactory as any).handle(error);
        }
      }
    );

    this.moduleFactory.addServiceMethod(
      'resetPassword',
      async (resetToken: string, newPassword: string) => {
        try {
          // Find user by reset token
          const user = await (repository as any).findByResetToken(resetToken);

          // Check if user exists and token is valid
          if (!user || !(user as any).resetTokenExpiry || (user as any).resetTokenExpiry < new Date()) {
            throw (ErrorFactory as any).validation('Invalid or expired reset token');
          }

          // Hash new password
          const { hash, salt } = await (CryptoUtils as any).hashPassword(newPassword);

          // Update user with new password
          await (service as any).update(user.id, {
            password: hash,
            salt,
            resetToken: null,
            resetTokenExpiry: null,
            updatedAt: new Date()
          } as (Prisma as any).UserUpdateInput);

          (logger as any).info(`Password reset for user: ${user.id}`, {
            userId: user.id,
            email: user.email
          });

          return { success: true };
        } catch(error) {
          (logger as any).error(`Error resetting password:`, error);
          throw (ErrorFactory as any).handle(error);
        }
      }
    );

    // Add custom controller methods
    this.moduleFactory.addControllerMethod(
      'login',
      async (req, res) => {
        try {
          const { email, password } = req.body;

          // Validate input
          if (!email || !password) {
            throw (ErrorFactory as any).validation('Email and password are required');
          }

          // Login user
          const result = await (service as any).login(email, password);

          // Send success response
          return res.status(200).json({
            success: true,
            data: result
          });
        } catch(error) {
          (logger as any).error(`Error logging in:`, error);
          return res.status(401).json({
            success: false,
            error: error.message || 'An error occurred while logging in'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'register',
      async (req, res) => {
        try {
          const userData: any = req.body;

          // Validate input
          if (!(userData as any).email || !(userData as any).password || !(userData as any).name) {
            throw (ErrorFactory as any).validation('Email, password, and name are required');
          }

          // Register user
          const user = await (service as any).register(userData);

          // Send success response
          return res.status(201).json({
            success: true,
            data: user
          });
        } catch(error) {
          (logger as any).error(`Error registering user:`, error);
          return res.status(400).json({
            success: false,
            error: error.message || 'An error occurred while registering user'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'logout',
      async (req, res) => {
        try {
          // Get user ID from request
          const { id } = req.user;

          // Update user to remove token
          await (service as any).update(id, {
            token: null,
            updatedAt: new Date()
          } as (Prisma as any).UserUpdateInput);

          // Send success response
          return res.status(200).json({
            success: true,
            message: 'Logged out successfully'
          });
        } catch(error) {
          (logger as any).error(`Error logging out:`, error);
          return res.status(500).json({
            success: false,
            error: error.message || 'An error occurred while logging out'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'getCurrentUser',
      async (req, res) => {
        try {
          // Get user ID from request
          const { id } = req.user;

          // Get user
          const user = await (service as any).getById(id);

          // Check if user exists
          if (!user) {
            throw (ErrorFactory as any).notFound('User', id);
          }

          // Send success response
          return res.status(200).json({
            success: true,
            data: (ObjectUtils as any).omit(user, ['password', 'salt'])
          });
        } catch(error) {
          (logger as any).error(`Error getting current user:`, error);
          return res.status(500).json({
            success: false,
            error: error.message || 'An error occurred while getting current user'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'updateCurrentUser',
      async (req, res) => {
        try {
          // Get user ID from request
          const { id } = req.user;

          // Get update data
          const updateData: any = req.body;

          // Don't allow updating password or role through this endpoint
          delete (updateData as any).password;
          delete (updateData as any).role;

          // Update user
          const user = await (service as any).update(id, {
            ...updateData,
            updatedAt: new Date()
          } as (Prisma as any).UserUpdateInput);

          // Send success response
          return res.status(200).json({
            success: true,
            data: (ObjectUtils as any).omit(user, ['password', 'salt'])
          });
        } catch(error) {
          (logger as any).error(`Error updating current user:`, error);
          return res.status(500).json({
            success: false,
            error: error.message || 'An error occurred while updating current user'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'changePassword',
      async (req, res) => {
        try {
          // Get user ID from request
          const { id } = req.user;

          // Get password data
          const { currentPassword, newPassword } = req.body;

          // Validate input
          if (!currentPassword || !newPassword) {
            throw (ErrorFactory as any).validation('Current password and new password are required');
          }

          // Get user
          const user = await (service as any).getById(id);

          // Check if user exists
          if (!user) {
            throw (ErrorFactory as any).notFound('User', id);
          }

          // Check if current password is correct
          const isPasswordValid = await (CryptoUtils as any).verifyPassword(
            currentPassword,
            (user as any).password,
            (user as any).salt
          );

          if (!isPasswordValid) {
            throw (ErrorFactory as any).validation('Current password is incorrect');
          }

          // Hash new password
          const { hash, salt } = await (CryptoUtils as any).hashPassword(newPassword);

          // Update user with new password
          await (service as any).update(id, {
            password: hash,
            salt,
            updatedAt: new Date()
          } as (Prisma as any).UserUpdateInput);

          // Send success response
          return res.status(200).json({
            success: true,
            message: 'Password changed successfully'
          });
        } catch(error) {
          (logger as any).error(`Error changing password:`, error);
          return res.status(400).json({
            success: false,
            error: error.message || 'An error occurred while changing password'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'forgotPassword',
      async (req, res) => {
        try {
          // Get email
          const { email } = req.body;

          // Validate input
          if (!email) {
            throw (ErrorFactory as any).validation('Email is required');
          }

          // Request password reset
          await (service as any).forgotPassword(email);

          // Send success response (always return success to prevent email enumeration)
          return res.status(200).json({
            success: true,
            message: 'If your email is registered, you will receive a password reset link'
          });
        } catch(error) {
          (logger as any).error(`Error requesting password reset:`, error);
          // Still return success to prevent email enumeration
          return res.status(200).json({
            success: true,
            message: 'If your email is registered, you will receive a password reset link'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'resetPassword',
      async (req, res) => {
        try {
          // Get reset token and new password
          const { resetToken, newPassword } = req.body;

          // Validate input
          if (!resetToken || !newPassword) {
            throw (ErrorFactory as any).validation('Reset token and new password are required');
          }

          // Reset password
          await (service as any).resetPassword(resetToken, newPassword);

          // Send success response
          return res.status(200).json({
            success: true,
            message: 'Password reset successfully'
          });
        } catch(error) {
          (logger as any).error(`Error resetting password:`, error);
          return res.status(400).json({
            success: false,
            error: error.message || 'An error occurred while resetting password'
          });
        }
      }
    );

    // Create module
    this.module = {
      name: 'user',
      router,
      repository,
      service,
      controller,
      dependencies: [],
      initialize: async () => {
        (logger as any).info('Initializing user module');

        // Register dependencies
        this.container.registerSingleton('userRepository', () => repository);
        this.container.registerSingleton('userService', () => service);
        this.container.registerSingleton('userController', () => controller);

        (logger as any).info('User module initialized');
      }
    };

    // Register the module
    this.moduleRegistry.registerModule(this.module);
  }

  /**
   * Get the module
   * @returns User module
   */
  getModule(): Module {
    return this.module;
  }
}