// jscpd:ignore-file

import { Router as ImportedRouter } from "express";
import { body, param } from "express-validator";
import verificationController from "../controllers/(verification as any).controller";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { authenticate as Importedauthenticate } from "../middlewares/(auth as any).middleware";
import { body, param } from "express-validator";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { authenticate as Importedauthenticate } from "../middlewares/(auth as any).middleware";

const router: any =Router();

// Public route to verify a payment
// This is used by payment pages to trigger verification
(router as any).post(
    "/",
    validate([
        body("merchantId").notEmpty(),
        body("amount").isNumeric(),
        body("currency").notEmpty(),
        body("paymentMethodId").notEmpty()
    ]),
    (verificationController as any).verifyPayment
);

// Route to verify a payment by ID using the unified verification service
// This is used by the admin dashboard and merchant dashboard
(router as any).get(
    "/payment/:paymentId",
    authenticate,
    validate([
        param("paymentId").notEmpty().withMessage("Payment ID is required")
    ]),
    (verificationController as any).verifyPaymentById
);

// Route to process webhooks from payment providers
(router as any).post(
    "/webhook",
    validate([
        body("provider").notEmpty().isString(),
        body("payload").notEmpty().isObject(),
        body("signature").optional().isString()
    ]),
    (verificationController as any).processWebhook
);

export default router;
