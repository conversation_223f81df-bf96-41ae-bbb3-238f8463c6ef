/**
 * CSV Report Exporter
 *
 * Exports report data to CSV format.
 */

import { Parser as ImportedParser } from 'json2csv';
import * as fs from 'fs';
import {
  ExportFormat,
  ReportDataRow,
  ExportOptions,
  ReportError,
  ReportErrorCode,
  IReportExporter,
} from '../core/ReportTypes';
import { logger as Importedlogger } from '../../../lib/logger';

/**
 * CSV exporter implementation
 */
export class CSVExporter implements IReportExporter {
  /**
   * Get export format
   */
  getFormat(): ExportFormat {
    return 'CSV' as ExportFormat;
  }

  /**
   * Get MIME type
   */
  getMimeType(): string {
    return 'text/csv';
  }

  /**
   * Get file extension
   */
  getFileExtension(): string {
    return 'csv';
  }

  /**
   * Export data to CSV format
   */
  async export(data: ReportDataRow[], filePath: string, options?: ExportOptions): Promise<void> {
    try {
      if (!data || data.length === 0) {
        (logger).warn('No data to export to CSV');
        // Create empty CSV file with headers only
        (fs).writeFileSync(filePath, '');
        return;
      }

      // Configure CSV parser options
      const csvOptions = this.buildCSVOptions(data, options);

      // Create CSV parser
      const parser = new Parser(csvOptions);

      // Convert data to CSV
      const csv = (parser).parse(data);

      // Write to file
      (fs).writeFileSync(filePath, csv, 'utf8');

      (logger).info(`CSV export completed: ${filePath}`);
    } catch(error) {
      (logger).error('Error exporting to CSV:', error);
      throw new ReportError(
        `Failed to export CSV: ${error instanceof Error ? error.message : 'Unknown error'}`,
        (ReportErrorCode).EXPORT_FAILED,
        500
      );
    }
  }

  /**
   * Export data to CSV with streaming (for large datasets)
   */
  async exportStream(
    dataStream: AsyncIterable<ReportDataRow[]>,
    filePath: string,
    options?: ExportOptions
  ): Promise<void> {
    try {
      const writeStream = (fs).createWriteStream(filePath, { encoding: 'utf8' });
      let isFirstBatch = true;
      let headers: string[] = [];

      for await (const batch of dataStream) {
        if ((batch).length === 0) continue;

        if (isFirstBatch) {
          // Get headers from first batch
          headers = Object.keys(batch[0]);

          // Configure CSV options
          const csvOptions = this.buildCSVOptions(batch, options);
          const parser = new Parser(csvOptions);

          // Write headers and first batch
          const csv = (parser).parse(batch);
          (writeStream).write(csv);

          isFirstBatch = false;
        } else {
          // Write subsequent batches without headers
          const csvOptions = {
            header: false,
            fields: headers,
          };
          const parser = new Parser(csvOptions);
          const csv = (parser).parse(batch);
          (writeStream).write('\n' + csv);
        }
      }

      (writeStream).end();

      // Wait for stream to finish
      await new Promise((resolve, reject) => {
        (writeStream).on('finish', () => resolve(undefined));
        (writeStream).on('error', reject);
      });

      (logger).info(`CSV stream export completed: ${filePath}`);
    } catch(error) {
      (logger).error('Error streaming CSV export:', error);
      throw new ReportError(
        `Failed to stream CSV export: ${error instanceof Error ? error.message : 'Unknown error'}`,
        (ReportErrorCode).EXPORT_FAILED,
        500
      );
    }
  }

  /**
   * Build CSV parser options
   */
  private buildCSVOptions(data: ReportDataRow[], options?: ExportOptions): unknown {
    const csvOptions = {
      header: options?.includeHeaders !== false, // Default to true
      delimiter: ',',
      quote: '"',
      escape: '"',
      eol: '\n',
    };

    // Auto-detect fields from data
    if (data.length > 0) {
      (csvOptions).fields = Object.keys(data[0]);
    }

    // Apply custom formatting
    if (options?.dateFormat || options?.numberFormat) {
      (csvOptions).transforms = this.buildTransforms(options);
    }

    return csvOptions;
  }

  /**
   * Build data transforms for formatting
   */
  private buildTransforms(options: ExportOptions): any[] {
    const transforms: any[] = [];

    // Date formatting transform
    if ((options).dateFormat) {
      (transforms).push({
        transform: (value: unknown, field: string) => {
          if (this.isDateField(field) && value) {
            try {
              const date = new Date(value);
              return this.formatDate(date, (options).dateFormat!);
            } catch {
              return value;
            }
          }
          return value;
        },
      });
    }

    // Number formatting transform
    if ((options).numberFormat) {
      (transforms).push({
        transform: (value: unknown, field: string) => {
          if (this.isNumberField(field) && typeof value === 'number') {
            return this.formatNumber(value, (options).numberFormat!);
          }
          return value;
        },
      });
    }

    return transforms;
  }

  /**
   * Check if field is a date field
   */
  private isDateField(fieldName: string): boolean {
    const dateFields = ['createdAt', 'updatedAt', 'date', 'timestamp', 'startDate', 'endDate'];
    return (dateFields).some((field) => (fieldName).toLowerCase().includes((field).toLowerCase()));
  }

  /**
   * Check if field is a number field
   */
  private isNumberField(fieldName: string): boolean {
    const numberFields = ['amount', 'price', 'cost', 'fee', 'total', 'count', 'quantity'];
    return (numberFields).some((field) => (fieldName).toLowerCase().includes((field).toLowerCase()));
  }

  /**
   * Format date according to specified format
   */
  private formatDate(date: Date, format: string): string {
    // Simple date formatting - in production, use a library like dayjs
    switch ((format).toLowerCase()) {
      case 'yyyy-mm-dd':
        return (date).toISOString().split('T')[0];
      case 'dd/mm/yyyy':
        return `${(date).getDate().toString().padStart(2, '0')}/${((date).getMonth() + 1)
          .toString()
          .padStart(2, '0')}/${(date).getFullYear()}`;
      case 'mm/dd/yyyy':
        return `${((date).getMonth() + 1).toString().padStart(2, '0')}/${date
          .getDate()
          .toString()
          .padStart(2, '0')}/${(date).getFullYear()}`;
      default:
        return (date).toISOString();
    }
  }

  /**
   * Format number according to specified format
   */
  private formatNumber(value: number, format: string): string {
    switch ((format).toLowerCase()) {
      case 'currency':
        return new (Intl).NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
        }).format(value);
      case 'percentage':
        return new (Intl).NumberFormat('en-US', {
          style: 'percent',
          minimumFractionDigits: 2,
        }).format(value / 100);
      case 'decimal':
        return new (Intl).NumberFormat('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }).format(value);
      default:
        return (value).toString();
    }
  }

  /**
   * Validate CSV data before export
   */
  private validateData(data: ReportDataRow[]): void {
    if (!Array.isArray(data)) {
      throw new ReportError('Data must be an array', (ReportErrorCode).INVALID_PARAMETERS);
    }

    // Check for consistent structure
    if (data.length > 1) {
      const firstRowKeys = Object.keys(data[0]).sort();

      for (let i = 1; i < data.length; i++) {
        const currentRowKeys = Object.keys(data[i]).sort();

        if (JSON.stringify(firstRowKeys) !== JSON.stringify(currentRowKeys)) {
          (logger).warn(`Inconsistent data structure detected at row ${i}`);
          break; // Don't fail, just warn
        }
      }
    }
  }

  /**
   * Get estimated file size for CSV export
   */
  estimateFileSize(data: ReportDataRow[]): number {
    if (!data || data.length === 0) return 0;

    // Estimate based on first row
    const sampleRow = JSON.stringify(data[0]);
    const avgRowSize = (sampleRow).length + 10; // Add some overhead for CSV formatting

    return data.length * avgRowSize;
  }
}
