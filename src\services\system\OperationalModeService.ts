// jscpd:ignore-file
/**
 * Operational Mode Service
 *
 * Manages the operational mode of the application.
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../../lib/logger';
import { moduleRegistry } from '../../lib/ModuleRegistry';
import { eventBus } from '../../lib/EventBus';

/**
 * Operational mode enum
 */
export enum OperationalMode {
  PRODUCTION = 'production',
}

/**
 * System status interface
 */
export interface SystemStatus {
  mode: OperationalMode;
  enabled: boolean;
  lastUpdated: Date;
  updatedBy: string;
}

/**
 * Operational mode service
 */
export class OperationalModeService {
  private prisma: PrismaClient;
  private currentMode: OperationalMode = OperationalMode.PRODUCTION; // Default to production
  private systemEnabled: boolean = true; // Default to enabled

  /**
   * Constructor
   *
   * @param prisma Prisma client
   */
  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.currentMode = OperationalMode.PRODUCTION; // Always production
    this.systemEnabled = true; // Default to enabled
  }

  /**
   * Initialize the operational mode service
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('Initializing operational mode service');

      // Load current mode from database
      await this.loadCurrentMode();

      // Register event listeners
      this.registerEventListeners();

      // Always in production mode
      this.currentMode = OperationalMode.PRODUCTION;

      logger.info(
        `Operational mode service initialized in production mode (${
          this.systemEnabled ? 'enabled' : 'disabled'
        })`
      );
    } catch (error) {
      logger.error('Error initializing operational mode service:', error);

      // Default to production mode if there's an error
      this.currentMode = OperationalMode.PRODUCTION;
      this.systemEnabled = true;

      logger.info(`Defaulting to production mode (${this.systemEnabled ? 'enabled' : 'disabled'})`);
    }
  }

  // Demo mode has been removed - only production is supported

  /**
   * Load current mode from database
   */
  private async loadCurrentMode(): Promise<void> {
    try {
      // Check if the systemSetting model exists (singular, not plural)
      if (!this.prisma.systemSetting) {
        logger.warn(
          'SystemSetting model not found in Prisma schema. Using default operational mode.'
        );
        logger.info(
          `Defaulting to production mode (${this.systemEnabled ? 'enabled' : 'disabled'})`
        );
        return;
      }

      // Get current mode from database
      const settings: Record<string, any> = await this.prisma.systemSetting.findFirst({
        where: { key: 'operational_mode' },
      });

      if (settings) {
        const value = JSON.parse(settings.value);
        // Always use production mode
        this.currentMode = OperationalMode.PRODUCTION;
        this.systemEnabled = value.enabled;

        logger.info(
          `Loaded system enabled status from database: ${
            this.systemEnabled ? 'enabled' : 'disabled'
          }`
        );
      } else {
        // Create default settings if not found
        try {
          await this.saveCurrentMode('system');
          logger.info(
            `Created default operational mode: production (${
              this.systemEnabled ? 'enabled' : 'disabled'
            })`
          );
        } catch (saveError) {
          logger.error('Error saving default operational mode:', saveError);
          logger.info(
            `Defaulting to production mode (${this.systemEnabled ? 'enabled' : 'disabled'})`
          );
        }
      }
    } catch (error) {
      logger.error('Error loading operational mode from database:', error);
      logger.info('Defaulting to production mode (enabled)');
      this.currentMode = OperationalMode.PRODUCTION;
      this.systemEnabled = true;
    }
  }

  /**
   * Save current mode to database
   *
   * @param updatedBy User who updated the mode
   */
  private async saveCurrentMode(updatedBy: string): Promise<void> {
    try {
      // Check if the systemSetting model exists (singular, not plural)
      if (!this.prisma.systemSetting) {
        logger.warn(
          'SystemSetting model not found in Prisma schema. Cannot save operational mode.'
        );
        return;
      }

      const value = JSON.stringify({
        mode: OperationalMode.PRODUCTION, // Always production
        enabled: this.systemEnabled,
        lastUpdated: new Date(),
        updatedBy,
      });

      // Find a system user for the updatedById field
      let updatedById: string = '00000000-0000-0000-0000-000000000000'; // Default ID if no user is found

      if (this.prisma.user) {
        try {
          const adminUser = await this.prisma.user.findFirst({
            where: { role: 'ADMIN' },
          });

          if (adminUser) {
            updatedById = adminUser.id;
          }
        } catch (userError) {
          logger.warn('Error finding admin user for system settings update:', userError);
        }
      }

      // Upsert system settings
      await this.prisma.systemSetting.upsert({
        where: { key: 'operational_mode' },
        update: {
          value,
          updatedAt: new Date(),
          updatedById,
        },
        create: { key: 'operational_mode', value, updatedAt: new Date(), updatedById },
      });

      logger.info(
        `Saved operational mode to database: ${this.currentMode} (${
          this.systemEnabled ? 'enabled' : 'disabled'
        })`
      );
    } catch (error) {
      logger.error('Error saving operational mode to database:', error);
      // Don't throw the error, just log it
    }
  }

  /**
   * Register event listeners
   */
  private registerEventListeners(): void {
    // Listen for mode change events
    eventBus.on('operational_mode.changed', async (data) => {
      logger.info(`Operational mode changed from ${this.currentMode} to ${data.mode}`);

      // Update module registry
      moduleRegistry.updateModuleConfig('core', {
        config: { operationalMode: data.mode, systemEnabled: data.enabled },
      });

      // Emit event for each module
      const modules: unknown = moduleRegistry.getAllModules();

      for (const [name, config] of Object.entries(modules)) {
        eventBus.emit(`module.${name}.mode_changed`, {
          mode: data.mode,
          enabled: data.enabled && config.enabled,
        });
      }
    });
  }

  /**
   * Get current operational mode
   */
  public getCurrentMode(): OperationalMode {
    return OperationalMode.PRODUCTION; // Always return production mode
  }

  /**
   * Check if system is enabled
   */
  public isSystemEnabled(): boolean {
    return this.systemEnabled;
  }

  /**
   * Get system status
   */
  public async getSystemStatus(): Promise<SystemStatus> {
    try {
      // Check if the systemSetting model exists (singular, not plural)
      if (!this.prisma.systemSetting) {
        logger.warn(
          'SystemSetting model not found in Prisma schema. Returning default system status.'
        );
        return {
          mode: this.currentMode,
          enabled: this.systemEnabled,
          lastUpdated: new Date(),
          updatedBy: 'system',
        };
      }

      const settings: Record<string, any> = await this.prisma.systemSetting.findFirst({
        where: { key: 'operational_mode' },
      });

      if (settings) {
        return JSON.parse(settings.value);
      }

      // Return default status if not found
      return {
        mode: this.currentMode,
        enabled: this.systemEnabled,
        lastUpdated: new Date(),
        updatedBy: 'system',
      };
    } catch (error) {
      logger.error('Error getting system status:', error);

      // Return current status if there's an error
      return {
        mode: this.currentMode,
        enabled: this.systemEnabled,
        lastUpdated: new Date(),
        updatedBy: 'system',
      };
    }
  }

  /**
   * Set operational mode
   *
   * @param mode Operational mode
   * @param updatedBy User who updated the mode
   */
  public async setOperationalMode(mode: OperationalMode, updatedBy: string): Promise<void> {
    try {
      // Always use production mode
      if (mode !== OperationalMode.PRODUCTION) {
        logger.warn(
          `Attempted to set operational mode to ${mode}, but only production mode is supported`
        );
        mode = OperationalMode.PRODUCTION;
      }

      logger.info(`Operational mode is set to production`);

      // Save to database
      await this.saveCurrentMode(updatedBy);

      // Emit event
      eventBus.emit('operational_mode.changed', {
        mode: OperationalMode.PRODUCTION,
        previousMode: OperationalMode.PRODUCTION,
        enabled: this.systemEnabled,
        updatedBy,
      });

      logger.info(`Operational mode confirmed as production`);
    } catch (error) {
      logger.error(`Error confirming operational mode:`, error);
      throw error;
    }
  }

  // Demo mode cleanup has been removed - only production is supported

  /**
   * Log system status change to audit log
   */
  private async logModeChangeAudit(
    previousMode: OperationalMode,
    newMode: OperationalMode,
    updatedBy: string
  ): Promise<void> {
    try {
      // Check if the auditLog model exists
      if (!this.prisma.auditLog) {
        logger.warn('AuditLog model not found in Prisma schema. Cannot log system status change.');
        return;
      }

      await this.prisma.auditLog.create({
        data: {
          level: 'INFO',
          message: `System status updated by ${updatedBy}`,
          source: 'OperationalModeService',
          details: JSON.stringify({
            mode: 'production',
            enabled: this.systemEnabled,
            updatedBy,
            timestamp: new Date(),
          }),
        },
      });

      logger.info(`Logged system status change to audit log`);
    } catch (error) {
      logger.error('Error logging system status change to audit log:', error);
      // Don't throw here to prevent disrupting the main flow
    }
  }

  /**
   * Enable or disable the system
   *
   * @param enabled Whether the system is enabled
   * @param updatedBy User who updated the status
   */
  public async setSystemEnabled(enabled: boolean, updatedBy: string): Promise<void> {
    try {
      // Check if status is changing
      if (enabled === this.systemEnabled) {
        logger.info(`System is already ${enabled ? 'enabled' : 'disabled'}`);
        return;
      }

      logger.info(`${enabled ? 'Enabling' : 'Disabling'} the system`);

      // Update system status
      this.systemEnabled = enabled;

      // Save to database
      await this.saveCurrentMode(updatedBy);

      // Emit event
      eventBus.emit('operational_mode.changed', {
        mode: this.currentMode,
        enabled: this.systemEnabled,
        updatedBy,
      });

      logger.info(`System ${enabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      logger.error(`Error ${enabled ? 'enabling' : 'disabling'} the system:`, error);
      throw error;
    }
  }

  /**
   * Check if the system is in production mode
   */
  public isProductionMode(): boolean {
    return true; // Always in production mode
  }

  /**
   * Check if the system is in demo mode
   */
  public isDemoMode(): boolean {
    return false; // Demo mode is not supported
  }

  /**
   * Check if the system is in development mode
   */
  public isDevelopmentMode(): boolean {
    return false; // Development mode is not supported
  }
}
