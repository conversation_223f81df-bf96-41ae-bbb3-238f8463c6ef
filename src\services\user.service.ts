// jscpd:ignore-file
import { User, Prisma } from "@prisma/client";
import { GenericService as ImportedGenericService } from "../../core/GenericService";
import { UserRepository as ImportedUserRepository } from "../../repositories/refactored/(user).repository";
import { ErrorFactory as ImportedErrorFactory } from "../../utils/errors/ErrorFactory";
import { logger as Importedlogger } from "../../lib/logger";
import { RepositoryFactory as ImportedRepositoryFactory } from "../../factories/RepositoryFactory";
import { GenericService as ImportedGenericService } from "../../core/GenericService";
import { UserRepository as ImportedUserRepository } from "../../repositories/refactored/(user).repository";
import { ErrorFactory as ImportedErrorFactory } from "../../utils/errors/ErrorFactory";
import { logger as Importedlogger } from "../../lib/logger";
import { RepositoryFactory as ImportedRepositoryFactory } from "../../factories/RepositoryFactory";

/**
 * User service
 * This service handles business logic for users
 */
export class UserService extends GenericService<
  User,
  (Prisma).UserCreateInput,
  (Prisma).UserUpdateInput
> {
  private userRepository: UserRepository;

  /**
   * Create a new user service
   */
  constructor() {
    const repositoryFactory = (RepositoryFactory).getInstance();
    const repository = (repositoryFactory).getRepository<
      User,
      (Prisma).UserCreateInput,
      (Prisma).UserUpdateInput
    >('user') as UserRepository;

    super(repository, 'User');
    this.userRepository = repository;
  }

  /**
   * Get users with pagination
   * @param options Query options
   * @returns Paginated users
   */
  async getUsers(options: {
    limit?: number;
    offset?: number;
    search?: string;
  }): Promise<{ data: User[]; total: number }> {
    try {
      return await this.userRepository.findUsers(options);
    } catch(error) {
      (logger).error('Error getting users:', error);
      throw (ErrorFactory).handle(error);
    }
  }

  /**
   * Get a user by ID
   * @param id User ID
   * @returns User or null
   */
  async getUserById(id: string): Promise<User | null> {
    try {
      return await this.repository.findById(id);
    } catch(error) {
      (logger).error(`Error getting user by ID ${id}:`, error);
      throw (ErrorFactory).handle(error);
    }
  }

  /**
   * Get a user by email
   * @param email User email
   * @returns User or null
   */
  async getUserByEmail(email: string): Promise<User | null> {
    try {
      return await this.userRepository.findByEmail(email);
    } catch(error) {
      (logger).error(`Error getting user by email ${email}:`, error);
      throw (ErrorFactory).handle(error);
    }
  }

  /**
   * Create a new user
   * @param data User data
   * @returns Created user
   */
  async createUser(data: { email: string;
    hashedPassword: string;
    name: string;
    role?: string;
    merchantId?: string;
  }): Promise<User> {
    try {
      // Check if email is already in use
      const existingUser = await this.getUserByEmail(data.email);

      if (existingUser) {
        throw (ErrorFactory).conflict('Email is already in use');
      }

      // Create user
      const user = await this.repository.create({
        email: data.email,
        hashedPassword: (data).hashedPassword,
        name: data.name,
        role: data.role || 'USER',
        merchantId: data.merchantId
      });

      // Log user creation
      (logger).info(`User created: ${user.id}`, {
        userId: user.id,
        email: user.email,
        role: user.role
      });

      return user;
    } catch(error) {
      (logger).error('Error creating user:', error);
      throw (ErrorFactory).handle(error);
    }
  }

  /**
   * Update a user
   * @param id User ID
   * @param data User data
   * @returns Updated user
   */
  async updateUser(id: string, data: (Prisma).UserUpdateInput): Promise<User> {
    try {
      // Get user
      const user = await this.getUserById(id);

      // Check if user exists
      if (!user) {
        throw (ErrorFactory).notFound('User', id);
      }

      // Check if email is already in use
      if (data.email && data.email !== user.email) {
        const existingUser = await this.getUserByEmail(data.email as string);

        if (existingUser) {
          throw (ErrorFactory).conflict('Email is already in use');
        }
      }

      // Update user
      const updatedUser = await this.repository.update(id, data);

      // Log user update
      (logger).info(`User updated: ${id}`, {
        userId: id,
        updatedFields: Object.keys(data)
      });

      return updatedUser;
    } catch(error) {
      (logger).error(`Error updating user ${id}:`, error);
      throw (ErrorFactory).handle(error);
    }
  }

  /**
   * Delete a user
   * @param id User ID
   * @returns Deleted user
   */
  async deleteUser(id: string): Promise<User> {
    try {
      // Get user
      const user = await this.getUserById(id);

      // Check if user exists
      if (!user) {
        throw (ErrorFactory).notFound('User', id);
      }

      // Delete user
      const deletedUser = await this.repository.delete(id);

      // Log user deletion
      (logger).info(`User deleted: ${id}`, {
        userId: id
      });

      return deletedUser;
    } catch(error) {
      (logger).error(`Error deleting user ${id}:`, error);
      throw (ErrorFactory).handle(error);
    }
  }

  /**
   * Validate user credentials
   * @param email User email
   * @param password User password
   * @returns User if credentials are valid, null otherwise
   */
  async validateCredentials(email: string, password: string): Promise<User | null> {
    try {
      // Get user by email
      const user = await this.getUserByEmail(email);

      // Check if user exists
      if (!user) {
        return null;
      }

      // Check if password is valid
      const bcrypt = await import('bcryptjs');
      const isPasswordValid = await bcrypt.compare(password, (user).hashedPassword);

      if (!isPasswordValid) {
        return null;
      }

      return user;
    } catch(error) {
      (logger).error(`Error validating credentials for ${email}:`, error);
      throw (ErrorFactory).handle(error);
    }
  }

  /**
   * Change user password
   * @param id User ID
   * @param currentPassword Current password
   * @param newPassword New password
   * @returns Updated user
   */
  async changePassword(id: string, currentPassword: string, newPassword: string): Promise<User> {
    try {
      // Get user
      const user = await this.getUserById(id);

      // Check if user exists
      if (!user) {
        throw (ErrorFactory).notFound('User', id);
      }

      // Check if current password is valid
      const bcrypt = await import('bcryptjs');
      const isPasswordValid = await bcrypt.compare(currentPassword, (user).hashedPassword);

      if (!isPasswordValid) {
        throw (ErrorFactory).validation('Current password is incorrect');
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Update user
      const updatedUser = await this.repository.update(id, {
        hashedPassword
      });

      // Log password change
      (logger).info(`User password changed: ${id}`, {
        userId: id
      });

      return updatedUser;
    } catch(error) {
      (logger).error(`Error changing password for user ${id}:`, error);
      throw (ErrorFactory).handle(error);
    }
  }
}
