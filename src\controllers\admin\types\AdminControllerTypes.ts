/**
 * Admin Controller Types
 * 
 * Type definitions for admin controller components.
 */

import { Request, Response, NextFunction } from 'express';

/**
 * Extended request interface with user information
 */
export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    role: string;
    merchantId?: string;
  };
}

/**
 * Dashboard data response
 */
export interface DashboardDataResponse {
  merchantCount: number;
  transactionCount: number;
  activePaymentMethodsCount: number;
  totalRevenue: number;
  recentTransactions: any[];
  recentMerchants: any[];
}

/**
 * Admin user creation request
 */
export interface CreateAdminUserRequest {
  email: string;
  name: string;
  password: string;
  roleId: string;
}

/**
 * Admin user update request
 */
export interface UpdateAdminUserRequest {
  name?: string;
  email?: string;
  roleId?: string;
  isActive?: boolean;
}

/**
 * Role creation request
 */
export interface CreateRoleRequest {
  name: string;
  type: string;
  description: string;
  permissions: string[];
}

/**
 * Role update request
 */
export interface UpdateRoleRequest {
  name?: string;
  description?: string;
  permissions?: string[];
  isActive?: boolean;
}

/**
 * Permission creation request
 */
export interface CreatePermissionRequest {
  name: string;
  description: string;
  resource: string;
  action: string;
}

/**
 * Permission update request
 */
export interface UpdatePermissionRequest {
  name?: string;
  description?: string;
  resource?: string;
  action?: string;
  isActive?: boolean;
}

/**
 * Admin user response
 */
export interface AdminUserResponse {
  id: string;
  name: string;
  email: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  user: {
    id: string;
    email: string;
    isActive: boolean;
    roles: RoleResponse[];
  };
}

/**
 * Role response
 */
export interface RoleResponse {
  id: string;
  name: string;
  type: string;
  description: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  permissions: PermissionResponse[];
}

/**
 * Permission response
 */
export interface PermissionResponse {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * API response wrapper
 */
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

/**
 * Pagination parameters
 */
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Admin user filters
 */
export interface AdminUserFilters {
  status?: 'active' | 'inactive';
  roleId?: string;
  search?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

/**
 * Role filters
 */
export interface RoleFilters {
  type?: string;
  isActive?: boolean;
  search?: string;
}

/**
 * Permission filters
 */
export interface PermissionFilters {
  resource?: string;
  action?: string;
  isActive?: boolean;
  search?: string;
}

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string;
  message: string;
  value?: unknown;
}

/**
 * Controller method options
 */
export interface ControllerMethodOptions {
  requireAuth?: boolean;
  requiredRole?: string;
  validateInput?: boolean;
  logRequest?: boolean;
}

/**
 * Request context interface
 */
export interface RequestContext {
  user?: {
    id: string;
    role: string;
    merchantId?: string;
  };
  requestId: string;
  timestamp: Date;
  ip: string;
  userAgent: string;
}

/**
 * Service dependencies interface
 */
export interface AdminServiceDependencies {
  adminService: any;
  authorizationService: any;
  validationService: any;
  auditService?: unknown;
}

/**
 * Controller configuration
 */
export interface AdminControllerConfig {
  enableAuditLogging?: boolean;
  enableRateLimiting?: boolean;
  defaultPageSize?: number;
  maxPageSize?: number;
  cacheEnabled?: boolean;
  cacheTtl?: number;
  passwordMinLength?: number;
  passwordRequireSpecialChars?: boolean;
}

/**
 * Audit log entry
 */
export interface AuditLogEntry {
  action: string;
  resource: string;
  resourceId?: string;
  userId: string;
  userRole: string;
  timestamp: Date;
  details?: unknown;
  ipAddress: string;
  userAgent: string;
  oldValues?: unknown;
  newValues?: unknown;
}

/**
 * Error response format
 */
export interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code: string;
    type: string;
    details?: unknown;
  };
  timestamp: Date;
  requestId: string;
}

/**
 * Success response format
 */
export interface SuccessResponse<T = unknown> {
  success: true;
  data: T;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  timestamp: Date;
  requestId: string;
}

/**
 * Controller method result
 */
export type ControllerResult<T = unknown> = Promise<SuccessResponse<T> | ErrorResponse>;

/**
 * Middleware function type
 */
export type MiddlewareFunction = (req: AuthenticatedRequest, res: Response, next: Function) => void | Promise<void>;

/**
 * Controller method type
 */
export type ControllerMethod<T = unknown> = (req: AuthenticatedRequest, res: Response) => ControllerResult<T>;

/**
 * Admin user status enum
 */
export enum AdminUserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

/**
 * Role type enum
 */
export enum RoleType {
  SYSTEM = 'system',
  CUSTOM = 'custom'
}

/**
 * Permission action enum
 */
export enum PermissionAction {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  MANAGE = 'manage'
}

/**
 * Permission resource enum
 */
export enum PermissionResource {
  USERS = 'users',
  MERCHANTS = 'merchants',
  TRANSACTIONS = 'transactions',
  ROLES = 'roles',
  PERMISSIONS = 'permissions',
  SETTINGS = 'settings',
  REPORTS = 'reports',
  AUDIT_LOGS = 'audit_logs'
}

/**
 * Authorization context
 */
export interface AuthorizationContext {
  user: {
    id: string;
    role: string;
    merchantId?: string;
  };
  resource: string;
  action: string;
  resourceId?: string;
}

/**
 * Permission check result
 */
export interface PermissionResult {
  allowed: boolean;
  reason?: string;
  requiredRole?: string;
  requiredPermissions?: string[];
}

/**
 * Dashboard statistics
 */
export interface DashboardStatistics {
  totalUsers: number;
  activeUsers: number;
  totalMerchants: number;
  activeMerchants: number;
  totalTransactions: number;
  successfulTransactions: number;
  totalRevenue: number;
  monthlyRevenue: number;
  recentActivity: any[];
}

/**
 * System health status
 */
export interface SystemHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  services: {
    database: 'connected' | 'disconnected';
    cache: 'connected' | 'disconnected';
    external: 'connected' | 'disconnected';
  };
  metrics: {
    uptime: number;
    memoryUsage: number;
    cpuUsage: number;
  };
}
