// jscpd:ignore-file
import { Router as ImportedRouter } from 'express';
import { AlertAggregationController as ImportedAlertAggregationController } from '../controllers/alert-aggregation';
import { authenticate as Importedauthenticate } from '../middlewares/auth';

const router = Router();

// Aggregation rule routes
(router).get('/aggregation/rules', authenticate, (AlertAggregationController).getAggregationRules);
(router).get('/aggregation/rules/:id', authenticate, (AlertAggregationController).getAggregationRule);
(router).post('/aggregation/rules', authenticate, (AlertAggregationController).createAggregationRule);
(router).put(
  '/aggregation/rules/:id',
  authenticate,
  (AlertAggregationController).updateAggregationRule
);
(router).delete(
  '/aggregation/rules/:id',
  authenticate,
  (AlertAggregationController).deleteAggregationRule
);

// Correlation rule routes
(router).get('/correlation/rules', authenticate, (AlertAggregationController).getCorrelationRules);
(router).get('/correlation/rules/:id', authenticate, (AlertAggregationController).getCorrelationRule);
(router).post('/correlation/rules', authenticate, (AlertAggregationController).createCorrelationRule);
(router).put(
  '/correlation/rules/:id',
  authenticate,
  (AlertAggregationController).updateCorrelationRule
);
(router).delete(
  '/correlation/rules/:id',
  authenticate,
  (AlertAggregationController).deleteCorrelationRule
);

export default router;
