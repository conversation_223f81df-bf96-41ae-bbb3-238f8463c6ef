// jscpd:ignore-file
import { Router as ImportedRouter } from 'express';
import { AlertAggregationController as ImportedAlertAggregationController } from '../controllers/alert-aggregation';
import { authenticate as Importedauthenticate } from '../middlewares/auth';

const router: any = Router();

// Aggregation rule routes
(router as any).get('/aggregation/rules', authenticate, (AlertAggregationController as any).getAggregationRules);
(router as any).get('/aggregation/rules/:id', authenticate, (AlertAggregationController as any).getAggregationRule);
(router as any).post('/aggregation/rules', authenticate, (AlertAggregationController as any).createAggregationRule);
(router as any).put(
  '/aggregation/rules/:id',
  authenticate,
  (AlertAggregationController as any).updateAggregationRule
);
(router as any).delete(
  '/aggregation/rules/:id',
  authenticate,
  (AlertAggregationController as any).deleteAggregationRule
);

// Correlation rule routes
(router as any).get('/correlation/rules', authenticate, (AlertAggregationController as any).getCorrelationRules);
(router as any).get('/correlation/rules/:id', authenticate, (AlertAggregationController as any).getCorrelationRule);
(router as any).post('/correlation/rules', authenticate, (AlertAggregationController as any).createCorrelationRule);
(router as any).put(
  '/correlation/rules/:id',
  authenticate,
  (AlertAggregationController as any).updateCorrelationRule
);
(router as any).delete(
  '/correlation/rules/:id',
  authenticate,
  (AlertAggregationController as any).deleteCorrelationRule
);

export default router;
