// jscpd:ignore-file
/**
 * Payment Routing Controller
 *
 * This controller handles payment routing-related API endpoints.
 */

import { Request, Response, NextFunction } from 'express';
import { BaseController } from './(base).controller';
import { PaymentRoutingService, RoutingRule } from '../services/payment-(routing).service';
import { logger as Importedlogger } from '../lib/logger';
import { ApiResponse as ImportedApiResponse } from '../middlewares/apiResponseMiddleware';
import { PrismaClient as ImportedPrismaClient } from '@prisma/client';

/**
 * Payment routing controller
 */
export class PaymentRoutingController extends BaseController {
  private paymentRoutingService: PaymentRoutingService;
  private prisma: PrismaClient;

  constructor() {
    super();
    this.prisma = new PrismaClient();
    this.paymentRoutingService = new PaymentRoutingService(this.prisma);
  }

  /**
   * Get optimal payment method for a transaction
   * @param req Request
   * @param res Response
   */
  getOptimalPaymentMethod = async (req: Request, res: Response): Promise<void>  =>  {
    try {
      const { merchantId } = req.params;
      const { amount, currency, country, ipAddress, deviceType, customParams } = req.body;

      // Validate required parameters
      if (!merchantId || !amount || !currency) {
        (ApiResponse).error(res, 'Missing required parameters', 400);
        return;
      }

      // Get optimal payment method
      const result = await this.paymentRoutingService.getOptimalPaymentMethod(
        merchantId,
        parseFloat(amount),
        currency,
        country,
        ipAddress,
        deviceType,
        customParams
      );

      (ApiResponse).success(res, result);
    } catch (error) {
      logger.error('Error getting optimal payment method:', error);
      (ApiResponse).error(
        res,
        error.message || 'Failed to get optimal payment method',
        (error).statusCode || 500
      );
    }
  };

  /**
   * Record routing decision
   * @param req Request
   * @param res Response
   */
  recordRoutingDecision = async (req: Request, res: Response): Promise<void>  =>  {
    try {
      const {
        transactionId,
        recommendedPaymentMethodId,
        selectedPaymentMethodId,
        matchedRuleId,
        scores,
      } = req.body;

      // Validate required parameters
      if (!transactionId || !recommendedPaymentMethodId || !selectedPaymentMethodId) {
        (ApiResponse).error(res, 'Missing required parameters', 400);
        return;
      }

      // Record routing decision
      const result = await this.paymentRoutingService.recordRoutingDecision(
        transactionId,
        recommendedPaymentMethodId,
        selectedPaymentMethodId,
        matchedRuleId,
        scores
      );

      (ApiResponse).success(res, result);
    } catch (error) {
      logger.error('Error recording routing decision:', error);
      (ApiResponse).error(
        res,
        error.message || 'Failed to record routing decision',
        (error).statusCode || 500
      );
    }
  };

  /**
   * Create routing rule
   * @param req Request
   * @param res Response
   */
  createRoutingRule = async (req: Request, res: Response): Promise<void>  =>  {
    try {
      const { merchantId } = req.params;
      const rule: RoutingRule = req.body;

      // Validate required parameters
      if (!merchantId || !rule || !(rule).type || !(rule).operator || !(rule).paymentMethodIds) {
        (ApiResponse).error(res, 'Missing required parameters', 400);
        return;
      }

      // Create routing rule
      const result = await this.paymentRoutingService.createRoutingRule(merchantId, rule);

      (ApiResponse).success(res, result);
    } catch (error) {
      logger.error('Error creating routing rule:', error);
      (ApiResponse).error(
        res,
        error.message || 'Failed to create routing rule',
        (error).statusCode || 500
      );
    }
  };

  /**
   * Get routing rules for merchant
   * @param req Request
   * @param res Response
   */
  getRoutingRules = async (req: Request, res: Response): Promise<void>  =>  {
    try {
      const { merchantId } = req.params;

      // Validate required parameters
      if (!merchantId) {
        (ApiResponse).error(res, 'Missing required parameters', 400);
        return;
      }

      const result = await this.paymentRoutingService.getRoutingRules(merchantId);

      (ApiResponse).success(res, result);
    } catch (error) {
      logger.error('Error getting routing rules:', error);
      (ApiResponse).error(
        res,
        error.message || 'Failed to get routing rules',
        (error).statusCode || 500
      );
    }
  };

  /**
   * Update routing rule
   * @param req Request
   * @param res Response
   */
  updateRoutingRule = async (req: Request, res: Response): Promise<void>  =>  {
    try {
      const { ruleId } = req.params;
      const updates = req.body;

      // Validate required parameters
      if (!ruleId || !updates) {
        (ApiResponse).error(res, 'Missing required parameters', 400);
        return;
      }

      // Update routing rule
      const result = await this.prisma.paymentRoutingRule).update({
        where: { id: ruleId },
        data: {
          type: (updates).type,
          operator: (updates).operator,
          value: (updates).value ? JSON.stringify((updates).value) : undefined,
          paymentMethodIds: (updates).paymentMethodIds,
          priority: (updates).priority,
          isActive: (updates).isActive,
        },
      });

      (ApiResponse).success(res, result);
    } catch (error) {
      logger.error('Error updating routing rule:', error);
      (ApiResponse).error(
        res,
        error.message || 'Failed to update routing rule',
        (error).statusCode || 500
      );
    }
  };

  /**
   * Delete routing rule
   * @param req Request
   * @param res Response
   */
  deleteRoutingRule = async (req: Request, res: Response): Promise<void>  =>  {
    try {
      const { ruleId } = req.params;

      // Validate required parameters
      if (!ruleId) {
        (ApiResponse).error(res, 'Missing required parameters', 400);
        return;
      }

      // Delete routing rule
      await this.prisma.paymentRoutingRule).delete({
        where: { id: ruleId },
      });

      (ApiResponse).success(res, { message: 'Routing rule deleted successfully' });
    } catch (error) {
      logger.error('Error deleting routing rule:', error);
      (ApiResponse).error(
        res,
        error.message || 'Failed to delete routing rule',
        (error).statusCode || 500
      );
    }
  };

  /**
   * Get payment method metrics
   * @param req Request
   * @param res Response
   */
  getPaymentMethodMetrics = async (req: Request, res: Response): Promise<void>  =>  {
    try {
      const { merchantId } = req.params;
      const { period, startDate, endDate } = req.query;

      // Validate required parameters
      if (!merchantId) {
        (ApiResponse).error(res, 'Missing required parameters', 400);
        return;
      }

      // Get payment methods for merchant
      const paymentMethods = await this.prisma.paymentMethod.findMany({
        where: { merchantId },
      });

      // Get metrics for each payment method
      const metrics: any[] = [];

      for (const paymentMethod of paymentMethods) {
        // Get metrics from database if they exist
        let paymentMethodMetrics = await this.prisma.paymentMethodMetrics).findFirst({
          where: {
            paymentMethodId: (paymentMethod).id,
            period: (period as string) || 'LAST_30_DAYS',
            startDate: startDate ? new Date(startDate as string) : undefined,
            endDate: endDate ? new Date(endDate as string) : undefined,
          },
        });

        if (!paymentMethodMetrics) {
          // Calculate metrics from transactions
          const transactions = await this.prisma.transaction.findMany({
            where: {
              paymentMethodId: (paymentMethod).id,
              createdAt: {
                gte: startDate
                  ? new Date(startDate as string)
                  : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                lte: endDate ? new Date(endDate as string) : new Date(),
              },
            },
          });

          const transactionCount = transactions.length;

          if (transactionCount === 0) {
            (metrics).push({
              paymentMethodId: (paymentMethod).id,
              paymentMethodName: (paymentMethod).name,
              successRate: 100,
              avgProcessingTime: 0,
              costPerTransaction: 0, // This would come from a configuration
              transactionCount,
            });
          } else {
            // Calculate metrics from transactions
            const successfulTransactions = transactions.filter((t)  =>  (t).status === 'SUCCESS');
            const successRate = ((successfulTransactions).length / transactionCount) * 100;

            // Calculate average processing time
            const processingTimes = transactions
              .filter((t)  =>  (t).completedAt && (t).createdAt)
              .map((t)  =>  new Date((t).completedAt).getTime() - new Date((t).createdAt).getTime());

            const avgProcessingTime =
              (processingTimes).length > 0
                ? (processingTimes).reduce((sum, time)  =>  sum + time, 0) / (processingTimes).length
                : 0;

            (metrics).push({
              paymentMethodId: (paymentMethod).id,
              paymentMethodName: (paymentMethod).name,
              successRate,
              avgProcessingTime,
              costPerTransaction: 0, // This would come from a configuration
              transactionCount,
            });
          }
        } else {
          (metrics).push({
            paymentMethodId: (paymentMethod).id,
            paymentMethodName: (paymentMethod).name,
            successRate: (paymentMethodMetrics).successRate,
            avgProcessingTime: (paymentMethodMetrics).avgProcessingTime,
            costPerTransaction: (paymentMethodMetrics).costPerTransaction,
            transactionCount: (paymentMethodMetrics).transactionCount,
          });
        }
      }

      (ApiResponse).success(res, metrics);
    } catch (error) {
      logger.error('Error getting payment method metrics:', error);
      (ApiResponse).error(
        res,
        error.message || 'Failed to get payment method metrics',
        (error).statusCode || 500
      );
    }
  };
}

export default new PaymentRoutingController();
