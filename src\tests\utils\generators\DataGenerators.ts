/**
 * Data Generators
 *
 * Functions to generate mock data for testing purposes.
 */

// Removed unused import

/**
 * Generate a random UUID
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * Generate a random email
 */
export function generateEmail(domain: string = 'example.com'): string {
  const username = generateRandomString(8, 'lowercase');
  return `${username}@${domain}`;
}

/**
 * Generate a random string
 */
export function generateRandomString(
  length: number = 10,
  type: 'alphanumeric' | 'alphabetic' | 'numeric' | 'lowercase' | 'uppercase' = 'alphanumeric'
): string {
  let chars = '';

  switch (type) {
    case 'alphabetic':
      chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
      break;
    case 'numeric':
      chars = '0123456789';
      break;
    case 'lowercase':
      chars = 'abcdefghijklmnopqrstuvwxyz';
      break;
    case 'uppercase':
      chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      break;
    case 'alphanumeric':
    default:
      chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      break;
  }

  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return result;
}

/**
 * Generate a random number
 */
export function generateRandomNumber(min: number = 0, max: number = 100): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Generate a random decimal
 */
export function generateRandomDecimal(
  min: number = 0,
  max: number = 100,
  decimals: number = 2
): number {
  const factor = Math.pow(10, decimals);
  return Math.round((Math.random() * (max - min) + min) * factor) / factor;
}

/**
 * Generate a random date
 */
export function generateRandomDate(
  start: Date = new Date(2020, 0, 1),
  end: Date = new Date()
): Date {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

/**
 * Generate a random boolean
 */
export function generateRandomBoolean(): boolean {
  return Math.random() < 0.5;
}

/**
 * Generate a random array element
 */
export function generateRandomArrayElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * Generate mock user data
 */
export function generateMockUser(overrides = {}): unknown {
  return {
    id: generateUUID(),
    email: generateEmail(),
    firstName: generateRandomArrayElement(['John', 'Jane', 'Bob', 'Alice', 'Charlie', 'Diana']),
    lastName: generateRandomArrayElement([
      'Smith',
      'Johnson',
      'Williams',
      'Brown',
      'Jones',
      'Garcia',
    ]),
    username: generateRandomString(8, 'lowercase'),
    password: generateRandomString(12),
    isActive: generateRandomBoolean(),
    role: generateRandomArrayElement(['USER', 'ADMIN', 'MERCHANT']),
    createdAt: generateRandomDate(),
    updatedAt: generateRandomDate(),
    lastLoginAt: generateRandomDate(),
    emailVerified: generateRandomBoolean(),
    phoneNumber: `+1${generateRandomString(10, 'numeric')}`,
    dateOfBirth: generateRandomDate(new Date(1950, 0, 1), new Date(2000, 0, 1)),
    ...overrides,
  };
}

/**
 * Generate mock merchant data
 */
export function generateMockMerchant(overrides = {}): unknown {
  return {
    id: generateUUID(),
    userId: generateUUID(),
    businessName: `${generateRandomArrayElement([
      'Tech',
      'Global',
      'Digital',
      'Smart',
      'Pro',
    ])} ${generateRandomArrayElement(['Solutions', 'Systems', 'Services', 'Corp', 'Inc'])}`,
    businessType: generateRandomArrayElement([
      'ECOMMERCE',
      'RETAIL',
      'SERVICES',
      'SAAS',
      'MARKETPLACE',
    ]),
    contactEmail: generateEmail(),
    contactPhone: `+1${generateRandomString(10, 'numeric')}`,
    website: `https://${generateRandomString(8, 'lowercase')}.com`,
    address: {
      street: `${generateRandomNumber(1, 9999)} ${generateRandomArrayElement([
        'Main',
        'Oak',
        'Pine',
        'Elm',
        'Cedar',
      ])} St`,
      city: generateRandomArrayElement([
        'New York',
        'Los Angeles',
        'Chicago',
        'Houston',
        'Phoenix',
      ]),
      state: generateRandomArrayElement(['NY', 'CA', 'IL', 'TX', 'AZ']),
      zipCode: generateRandomString(5, 'numeric'),
      country: 'US',
    },
    isActive: generateRandomBoolean(),
    isVerified: generateRandomBoolean(),
    createdAt: generateRandomDate(),
    updatedAt: generateRandomDate(),
    ...overrides,
  };
}

/**
 * Generate mock transaction data
 */
export function generateMockTransaction(overrides = {}): unknown {
  return {
    id: generateUUID(),
    merchantId: generateUUID(),
    customerId: generateUUID(),
    amount: generateRandomDecimal(1, 1000, 2),
    currency: generateRandomArrayElement(['USD', 'EUR', 'GBP', 'CAD', 'AUD']),
    status: generateRandomArrayElement(['PENDING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED']),
    type: generateRandomArrayElement(['PAYMENT', 'REFUND', 'CHARGEBACK', 'ADJUSTMENT']),
    paymentMethod: generateRandomArrayElement([
      'CREDIT_CARD',
      'DEBIT_CARD',
      'BANK_TRANSFER',
      'DIGITAL_WALLET',
      'CRYPTOCURRENCY',
    ]),
    paymentMethodId: generateUUID(),
    description: `Payment for ${generateRandomArrayElement([
      'Order',
      'Invoice',
      'Subscription',
      'Service',
    ])} #${generateRandomString(8, 'alphanumeric').toUpperCase()}`,
    reference: generateRandomString(12, 'alphanumeric').toUpperCase(),
    customerEmail: generateEmail(),
    customerName: `${generateRandomArrayElement([
      'John',
      'Jane',
      'Bob',
      'Alice',
    ])} ${generateRandomArrayElement(['Smith', 'Johnson', 'Williams', 'Brown'])}`,
    metadata: {
      orderId: generateUUID(),
      productId: generateUUID(),
      campaignId: generateUUID(),
    },
    createdAt: generateRandomDate(),
    updatedAt: generateRandomDate(),
    processedAt: generateRandomDate(),
    ...overrides,
  };
}

/**
 * Generate mock payment method data
 */
export function generateMockPaymentMethod(overrides = {}): unknown {
  return {
    id: generateUUID(),
    userId: generateUUID(),
    type: generateRandomArrayElement([
      'CREDIT_CARD',
      'DEBIT_CARD',
      'BANK_ACCOUNT',
      'DIGITAL_WALLET',
    ]),
    provider: generateRandomArrayElement(['STRIPE', 'PAYPAL', 'SQUARE', 'ADYEN', 'BRAINTREE']),
    isDefault: generateRandomBoolean(),
    isActive: generateRandomBoolean(),
    lastFour: generateRandomString(4, 'numeric'),
    expiryMonth: generateRandomNumber(1, 12),
    expiryYear: generateRandomNumber(2024, 2030),
    cardBrand: generateRandomArrayElement(['VISA', 'MASTERCARD', 'AMEX', 'DISCOVER']),
    holderName: `${generateRandomArrayElement([
      'John',
      'Jane',
      'Bob',
      'Alice',
    ])} ${generateRandomArrayElement(['Smith', 'Johnson', 'Williams', 'Brown'])}`,
    billingAddress: {
      street: `${generateRandomNumber(1, 9999)} ${generateRandomArrayElement([
        'Main',
        'Oak',
        'Pine',
      ])} St`,
      city: generateRandomArrayElement(['New York', 'Los Angeles', 'Chicago']),
      state: generateRandomArrayElement(['NY', 'CA', 'IL']),
      zipCode: generateRandomString(5, 'numeric'),
      country: 'US',
    },
    createdAt: generateRandomDate(),
    updatedAt: generateRandomDate(),
    ...overrides,
  };
}

/**
 * Generate mock subscription data
 */
export function generateMockSubscription(overrides = {}): unknown {
  return {
    id: generateUUID(),
    customerId: generateUUID(),
    merchantId: generateUUID(),
    planId: generateUUID(),
    status: generateRandomArrayElement(['ACTIVE', 'CANCELLED', 'EXPIRED', 'PAUSED', 'TRIAL']),
    interval: generateRandomArrayElement(['MONTHLY', 'YEARLY', 'WEEKLY', 'QUARTERLY']),
    amount: generateRandomDecimal(9.99, 999.99, 2),
    currency: 'USD',
    trialEndsAt: generateRandomDate(new Date(), new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)),
    currentPeriodStart: generateRandomDate(),
    currentPeriodEnd: generateRandomDate(
      new Date(),
      new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    ),
    cancelledAt: null,
    createdAt: generateRandomDate(),
    updatedAt: generateRandomDate(),
    ...overrides,
  };
}

/**
 * Generate mock notification data
 */
export function generateMockNotification(overrides = {}): unknown {
  return {
    id: generateUUID(),
    userId: generateUUID(),
    type: generateRandomArrayElement(['EMAIL', 'SMS', 'PUSH', 'WEBHOOK']),
    channel: generateRandomArrayElement(['TRANSACTION', 'SECURITY', 'MARKETING', 'SYSTEM']),
    title: generateRandomArrayElement([
      'Payment Received',
      'Security Alert',
      'Account Update',
      'System Maintenance',
    ]),
    message: 'This is a test notification message.',
    status: generateRandomArrayElement(['PENDING', 'SENT', 'DELIVERED', 'FAILED', 'READ']),
    priority: generateRandomArrayElement(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
    metadata: {
      transactionId: generateUUID(),
      amount: generateRandomDecimal(1, 1000, 2),
      currency: 'USD',
    },
    sentAt: generateRandomDate(),
    readAt: generateRandomBoolean() ? generateRandomDate() : null,
    createdAt: generateRandomDate(),
    updatedAt: generateRandomDate(),
    ...overrides,
  };
}

/**
 * Generate mock webhook data
 */
export function generateMockWebhook(overrides = {}): unknown {
  return {
    id: generateUUID(),
    merchantId: generateUUID(),
    url: `https://${generateRandomString(8, 'lowercase')}.com/webhook`,
    events: generateRandomArrayElement([
      ['transaction.created', 'transaction.updated'],
      ['payment.succeeded', 'payment.failed'],
      ['subscription.created', 'subscription.cancelled'],
    ]),
    isActive: generateRandomBoolean(),
    secret: generateRandomString(32),
    retryCount: generateRandomNumber(0, 5),
    lastTriggeredAt: generateRandomDate(),
    createdAt: generateRandomDate(),
    updatedAt: generateRandomDate(),
    ...overrides,
  };
}

/**
 * Generate an array of mock data
 */
export function generateMockArray<T>(
  generator: (overrides?: unknown) => T,
  count: number = 5,
  overrides = {}
): T[] {
  return Array.from({ length: count }, () => generator(overrides));
}

/**
 * Generate mock data with relationships
 */
export function generateMockDataWithRelationships(): {
  users: any[];
  merchants: any[];
  transactions: any[];
  paymentMethods: any[];
} {
  const users = generateMockArray(generateMockUser, 3);
  const merchants = users.map((user) => generateMockMerchant({ userId: user.id }));
  const paymentMethods = users.flatMap((user) =>
    generateMockArray(generateMockPaymentMethod, 2, { userId: user.id })
  );
  const transactions = merchants.flatMap((merchant) =>
    generateMockArray(generateMockTransaction, 5, {
      merchantId: merchant.id,
      paymentMethodId: generateRandomArrayElement(paymentMethods).id,
    })
  );

  return {
    users,
    merchants,
    transactions,
    paymentMethods,
  };
}

/**
 * Generate mock data based on schema
 */
export function generateMockDataFromSchema(schema: unknown, overrides = {}): unknown {
  const result = {};

  Object.entries(schema).forEach(([key, config]: [string, any]) => {
    if (overrides[key] !== undefined) {
      result[key] = overrides[key];
      return;
    }

    switch (config.type) {
      case 'string':
        result[key] =
          config.format === 'email' ? generateEmail() : generateRandomString(config.length ?? 10);
        break;
      case 'number':
        result[key] = generateRandomNumber(config.min ?? 0, config.max ?? 100);
        break;
      case 'boolean':
        result[key] = generateRandomBoolean();
        break;
      case 'date':
        result[key] = generateRandomDate();
        break;
      case 'uuid':
        result[key] = generateUUID();
        break;
      case 'array':
        result[key] = config.enum ? generateRandomArrayElement(config.enum) : [];
        break;
      default:
        result[key] = null;
    }
  });

  return result;
}
