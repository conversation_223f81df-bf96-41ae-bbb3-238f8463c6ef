// jscpd:ignore-file
import NodeCache from 'node-cache';
import { logger } from '../../utils/logger';
import { AnalyticsPeriod } from '../analytics/payment-analytics.service';
import { AnalyticsPeriod } from '../analytics/payment-analytics.service';

/**
 * Cache key generator for analytics
 */
export const generateAnalyticsCacheKey: unknown = (
  type: string,
  params: Record<string, unknown>
): string => {
  const sortedParams = Object.entries(params)
    .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  return `${type}:${sortedParams}`;
};

/**
 * Analytics cache service
 */
class AnalyticsCacheService {
  private static instance: AnalyticsCacheService;
  private cache: NodeCache;
  private enabled: boolean = true;

  /**
   * Default TTL values for different cache types (in seconds)
   */
  private defaultTTL: Record<string, number> = {
    dashboard: 5 * 60, // 5 minutes
    payments: 5 * 60, // 5 minutes
    merchants: 10 * 60, // 10 minutes
    paymentMethods: 10 * 60, // 10 minutes
    default: 5 * 60, // 5 minutes
  };

  /**
   * TTL values for different time periods (in seconds)
   */
  private periodTTL = {
    day: 2 * 60, // 2 minutes
    week: 5 * 60, // 5 minutes
    month: 15 * 60, // 15 minutes
    year: 30 * 60, // 30 minutes
    custom: 5 * 60, // 5 minutes
  };

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    this.cache = new NodeCache({
      stdTTL: this.defaultTTL.default,
      checkperiod: 60, // Check for expired keys every 60 seconds
      useClones: false, // Don't clone objects (for performance)
    });

    // Log cache statistics periodically
    setInterval(() => {
      const stats: unknown = this.cache.getStats();
      if (logger && typeof logger.debug === 'function') {
        logger.debug('Analytics cache statistics:', stats);
      }
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): AnalyticsCacheService {
    if (!AnalyticsCacheService.instance) {
      AnalyticsCacheService.instance = new AnalyticsCacheService();
    }
    return AnalyticsCacheService.instance;
  }

  /**
   * Enable or disable cache
   * @param enabled Whether cache is enabled
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
    if (logger && typeof logger.info === 'function') {
      logger.info(`Analytics cache ${enabled ? 'enabled' : 'disabled'}`);
    }
  }

  /**
   * Get cache enabled status
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * Get item from cache
   * @param key Cache key
   */
  public get<T>(key: string): T | undefined {
    if (!this.enabled) {
      return undefined;
    }

    const value: unknown = this.cache.get<T>(key);

    if (value !== undefined) {
      if (logger && typeof logger.debug === 'function') {
        logger.debug(`Cache hit for key: ${key}`);
      }
    } else {
      if (logger && typeof logger.debug === 'function') {
        logger.debug(`Cache miss for key: ${key}`);
      }
    }

    return value;
  }

  /**
   * Set item in cache
   * @param key Cache key
   * @param value Value to cache
   * @param ttl Time to live in seconds (optional)
   */
  public set<T>(key: string, value: T, ttl?: number): void {
    if (!this.enabled) {
      return;
    }

    this.cache.set(key, value, ttl);
    if (logger && typeof logger.debug === 'function') {
      logger.debug(`Cached data for key: ${key}, TTL: ${ttl || 'default'}`);
    }
  }

  /**
   * Delete item from cache
   * @param key Cache key
   */
  public delete(key: string): void {
    this.cache.del(key);
    if (logger && typeof logger.debug === 'function') {
      logger.debug(`Deleted cache for key: ${key}`);
    }
  }

  /**
   * Clear all cache
   */
  public clear(): void {
    this.cache.flushAll();
    if (logger && typeof logger.info === 'function') {
      logger.info('Cleared all analytics cache');
    }
  }

  /**
   * Get TTL for analytics type and period
   * @param type Analytics type
   * @param period Analytics period
   */
  public getTTL(type: string, period?: AnalyticsPeriod): number {
    // Get base TTL for type
    const baseTTL: unknown = this.defaultTTL[type] || this.defaultTTL.default;

    // If period is specified, adjust TTL based on period
    if (period) {
      const periodMultiplier: unknown = this.getPeriodTTLMultiplier(period);
      return baseTTL * periodMultiplier;
    }

    return baseTTL;
  }

  /**
   * Get TTL multiplier for period
   * @param period Analytics period
   */
  private getPeriodTTLMultiplier(period: AnalyticsPeriod): number {
    switch (period) {
      case AnalyticsPeriod.DAY:
        return 0.5; // Shorter TTL for day period (more frequent updates)
      case AnalyticsPeriod.WEEK:
        return 1; // Standard TTL for week period
      case AnalyticsPeriod.MONTH:
        return 2; // Longer TTL for month period
      case AnalyticsPeriod.YEAR:
        return 3; // Even longer TTL for year period
      case AnalyticsPeriod.CUSTOM:
        return 1; // Standard TTL for custom period
      default:
        return 1;
    }
  }

  /**
   * Wrap function with cache
   * @param cacheKey Cache key
   * @param fn Function to wrap
   * @param ttl Time to live in seconds (optional)
   */
  public async wrap<T>(cacheKey: string, fn: () => Promise<T>, ttl?: number): Promise<T> {
    // If cache is disabled, just execute the function
    if (!this.enabled) {
      return fn();
    }

    // Check if data is in cache
    const cachedData: unknown = this.get<T>(cacheKey);
    if (cachedData !== undefined) {
      return cachedData;
    }

    // Execute function to get data
    const data: unknown = await fn();

    // Cache the result
    this.set(cacheKey, data, ttl);

    return data;
  }
}

// Export singleton instance
export const analyticsCacheService: unknown = AnalyticsCacheService.getInstance();

export default analyticsCacheService;
