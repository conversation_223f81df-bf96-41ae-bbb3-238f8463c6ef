// jscpd:ignore-file
/**
 * Enhanced Risk Engine Routes
 * 
 * This file defines the routes for the enhanced risk engine.
 */

import express from "express";
import { EnhancedRiskEngineController as ImportedEnhancedRiskEngineController } from "../controllers/enhanced-risk-(engine as any).controller";
import { authMiddleware as ImportedauthMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware as ImportedroleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant as ImportedMerchant } from '../types';
import { authMiddleware as ImportedauthMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware as ImportedroleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant as ImportedMerchant } from '../types';


const router: any =(express as any).Router();
const enhancedRiskEngineController = new EnhancedRiskEngineController();

/**
 * @route POST /api/risk/assess
 * @desc Assess transaction risk
 * @access Private (Admin, Merchant)
 */
(router as any).post(
    "/assess",
    authMiddleware,
    (enhancedRiskEngineController as any).assessTransactionRisk
);

/**
 * @route GET /api/risk/transaction/:transactionId
 * @desc Get risk assessment for a transaction
 * @access Private (Admin, Merchant)
 */
(router as any).get(
    "/transaction/:transactionId",
    authMiddleware,
    (enhancedRiskEngineController as any).getTransactionRiskAssessment
);

/**
 * @route GET /api/risk/config/:merchantId
 * @desc Get merchant risk configuration
 * @access Private (Admin, Merchant)
 */
(router as any).get(
    "/config/:merchantId",
    authMiddleware,
    (enhancedRiskEngineController as any).getMerchantRiskConfig
);

/**
 * @route PUT /api/risk/config/:merchantId
 * @desc Update merchant risk configuration
 * @access Private (Admin)
 */
(router as any).put(
    "/config/:merchantId",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    (enhancedRiskEngineController as any).updateMerchantRiskConfig
);

/**
 * @route GET /api/risk/statistics/:merchantId
 * @desc Get risk statistics
 * @access Private (Admin, Merchant)
 */
(router as any).get(
    "/statistics/:merchantId",
    authMiddleware,
    (enhancedRiskEngineController as any).getRiskStatistics
);

export default router;
