// jscpd:ignore-file
/**
 * Enhanced Risk Engine Routes
 * 
 * This file defines the routes for the enhanced risk engine.
 */

import express from "express";
import { EnhancedRiskEngineController } from "../controllers/enhanced-risk-engine.controller";
import { authMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant } from '../types';
import { authMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant } from '../types';


const router: unknown =express.Router();
const enhancedRiskEngineController: unknown = new EnhancedRiskEngineController();

/**
 * @route POST /api/risk/assess
 * @desc Assess transaction risk
 * @access Private (Admin, Merchant)
 */
router.post(
    "/assess",
    authMiddleware,
    enhancedRiskEngineController.assessTransactionRisk
);

/**
 * @route GET /api/risk/transaction/:transactionId
 * @desc Get risk assessment for a transaction
 * @access Private (Admin, Merchant)
 */
router.get(
    "/transaction/:transactionId",
    authMiddleware,
    enhancedRiskEngineController.getTransactionRiskAssessment
);

/**
 * @route GET /api/risk/config/:merchantId
 * @desc Get merchant risk configuration
 * @access Private (Admin, Merchant)
 */
router.get(
    "/config/:merchantId",
    authMiddleware,
    enhancedRiskEngineController.getMerchantRiskConfig
);

/**
 * @route PUT /api/risk/config/:merchantId
 * @desc Update merchant risk configuration
 * @access Private (Admin)
 */
router.put(
    "/config/:merchantId",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    enhancedRiskEngineController.updateMerchantRiskConfig
);

/**
 * @route GET /api/risk/statistics/:merchantId
 * @desc Get risk statistics
 * @access Private (Admin, Merchant)
 */
router.get(
    "/statistics/:merchantId",
    authMiddleware,
    enhancedRiskEngineController.getRiskStatistics
);

export default router;
