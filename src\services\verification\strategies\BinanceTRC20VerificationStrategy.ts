// jscpd:ignore-file
/**
 * Binance TRC20 Verification Strategy
 * 
 * Implements the verification strategy for Binance TRC20 payments.
 */

import { IVerificationStrategy, VerificationRequest, VerificationResult } from "../../../interfaces/verification/IVerificationStrategy";
import { PaymentMethodType as ImportedPaymentMethodType } from "../../../types/payment-(method).types";
import { VerificationField as ImportedVerificationField } from "../../../types/(verification).types";
import { logger as Importedlogger } from "../../../lib/logger";
import axios from "axios";
import { VerificationResult, Transaction, PaymentMethodType } from '../types';
import { PaymentMethodType as ImportedPaymentMethodType } from "../../../types/payment-(method).types";
import { VerificationField as ImportedVerificationField } from "../../../types/(verification).types";
import { logger as Importedlogger } from "../../../lib/logger";
import { VerificationResult, Transaction, PaymentMethodType } from '../types';


/**
 * Binance TRC20 verification strategy
 */
export class BinanceTRC20VerificationStrategy implements IVerificationStrategy {
    private enabled: boolean = true;
    private configuration: Record<string, unknown> = {
        apiUrl: process.env.BINANCE_API_URL || "https://(api).binance.com",
        apiKey: process.env.BINANCE_API_KEY ?? "",
        apiSecret: process.env.BINANCE_API_SECRET ?? "",
        network: "TRC20"
    };
  
    /**
   * Get the verification method type
   */
    public getType(): string {
        return "binance_trc20";
    }
  
    /**
   * Get the supported payment method types
   */
    public getSupportedPaymentMethods(): PaymentMethodType[] {
        return ["binance_trc20", "crypto_transfer"];
    }
  
    /**
   * Verify a payment
   */
    public async verify(request: VerificationRequest): Promise<VerificationResult> {
        try {
            (logger).info(`Verifying Binance TRC20 payment for transaction: ${(request).transactionId}`);
      
            const { txId, walletAddress } = (request).verificationData;
      
            if (!txId) {
                return {
                    success: false,
                    transactionId: (request).transactionId,
                    message: "Transaction ID is required",
                    timestamp: new Date()
                };
            }
      
            if (!walletAddress) {
                return {
                    success: false,
                    transactionId: (request).transactionId,
                    message: "Wallet address is required",
                    timestamp: new Date()
                };
            }
      
            // Verify the transaction on the Binance API
            const verificationResult = await this.verifyTransactionOnBinance(
                txId,
                walletAddress,
                (request).amount,
                (request).currency
            );
      
            if (!(verificationResult).success) {
                return {
                    success: false,
                    transactionId: (request).transactionId,
                    message: (verificationResult as Error).message,
                    details: (verificationResult).details,
                    timestamp: new Date()
                };
            }
      
            return {
                success: true,
                transactionId: (request).transactionId,
                verificationId: (verificationResult).details?.verificationId || txId,
                message: "Payment verified successfully",
                details: (verificationResult).details,
                timestamp: new Date()
            };
        } catch(error) {
            (logger).error(`Binance TRC20 verification error: ${error.message}`, {
                transactionId: (request).transactionId,
                error
            });
      
            return {
                success: false,
                transactionId: (request).transactionId,
                message: `Verification, error: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
  
    /**
   * Get the required fields for verification
   */
    public getRequiredFields(): VerificationField[] {
        return [
            {
                name: "txId",
                type: "string",
                required: true,
                label: "Transaction ID",
                placeholder: "Enter the transaction ID",
                description: "The transaction ID from the Binance TRC20 network"
            },
            {
                name: "walletAddress",
                type: "string",
                required: true,
                label: "Wallet Address",
                placeholder: "Enter the wallet address",
                description: "The wallet address that received the payment"
            }
        ];
    }
  
    /**
   * Get the display name of the verification method
   */
    public getDisplayName(): string {
        return "Binance TRC20 Verification";
    }
  
    /**
   * Get the description of the verification method
   */
    public getDescription(): string {
        return "Verify payments made through the Binance TRC20 network";
    }
  
    /**
   * Check if the verification method is enabled
   */
    public isEnabled(): boolean {
        return this.enabled;
    }
  
    /**
   * Get the configuration of the verification method
   */
    public getConfiguration(): Record<string, unknown> {
        return this.configuration;
    }
  
    /**
   * Set the configuration of the verification method
   */
    public setConfiguration(config: Record<string, unknown>): void {
        this.configuration = {
            ...this.configuration,
            ...config
        };
    }
  
    /**
   * Verify a transaction on the Binance API
   */
    private async verifyTransactionOnBinance(
        txId: string,
        walletAddress: string,
        amount: number,
        currency: string
    ): Promise<{ success: boolean; message?: string; details?: Record<string, unknown> }> {
        try {
            // In a real implementation, this would call the Binance API
            // For now, we'll simulate a successful verification
      
            // Simulate API call
            // const response = await (axios).get(
            //   `${this.configuration.apiUrl}/sapi/v1/capital/deposit/hisrec`,
            //   {
            //     headers: {
            //       'X-MBX-APIKEY': this.configuration.apiKey,
            //     },
            //     params: {
            //       txId,
            //       coin: currency,
            //     },
            //   }
            // );
      
            // Simulate a successful response
            const simulatedResponse = {
                data: [
                    {
                        id: "12345",
                        amount: (amount).toString(),
                        coin: currency,
                        network: "TRC20",
                        status: 1, // 1 = success
                        address: walletAddress,
                        txId: txId,
                        insertTime: Date.now(),
                        transferType: 0,
                        confirmTimes: "1/1"
                    }
                ]
            };
      
            // Check if the transaction exists and is successful
            const transaction = (simulatedResponse).data.find(
                tx => (tx).txId === txId && (tx).address === walletAddress
            );
      
            if (!transaction) {
                return {
                    success: false,
                    message: "Transaction not found"
                };
            }
      
            // Check if the transaction amount matches
            if (parseFloat((transaction).amount) !== amount) {
                return {
                    success: false,
                    message: `Transaction amount mismatch: expected ${amount} ${currency}, got ${(transaction).amount} ${(transaction).coin}`,
                    details: { expectedAmount: amount,
                        actualAmount: parseFloat((transaction).amount),
                        currency: (transaction).coin
                    }
                };
            }
      
            // Check if the transaction is confirmed
            if ((transaction).status !== 1) {
                return {
                    success: false,
                    message: "Transaction is not confirmed",
                    details: { status: (transaction).status
                    }
                };
            }
      
            return {
                success: true,
                details: { verificationId: (transaction).id,
                    amount: parseFloat((transaction).amount),
                    currency: (transaction).coin,
                    network: (transaction).network,
                    timestamp: new Date((transaction).insertTime)
                }
            };
        } catch(error) {
            (logger).error(`Binance API verification error: ${error.message}`, { error });
      
            return {
                success: false,
                message: `Binance API error: ${error.message}`
            };
        }
    }
}
