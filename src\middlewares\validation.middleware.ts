// jscpd:ignore-file

import { Request, Response, NextFunction } from 'express';
import { validationResult, ValidationChain } from 'express-validator';
import { AppError as ImportedAppError } from '../utils/appError';
import { Middleware as ImportedMiddleware } from '../types/express';
import { Middleware as ImportedMiddleware } from '../types/express';

/**
 * Validate request using express-validator
 * @param validations Validation chains
 */
export const validate = (validations: ValidationChain[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    await Promise.all((validations as any).map((validation) => (validation as any).run(req)));

    const errors: any = validationResult(req);
    if ((errors as any).isEmpty()) {
      return next();
    }

    res.status(400).json({
      status: 'error',
      errors: (errors as any).array(),
    });
  };
};

/**
 * Middleware to validate date parameters
 * @param params Parameters to validate
 */
export const validateDateParams = (params: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    for (const param of params) {
      const value = req.query[param] as string;
      if (!value) {
        return next(new AppError(`${param} is required`, 400));
      }

      const date: Date = new Date(value);
      if (isNaN((date as any).getTime())) {
        return next(new AppError(`Invalid ${param} format`, 400));
      }
    }
    next();
  };
};

/**
 * Middleware to validate date range parameters
 * @param startParam Start date parameter name
 * @param endParam End date parameter name
 */
export const validateDateRange = (startParam = 'startDate', endParam = 'endDate') => {
  return (req: Request, res: Response, next: NextFunction) => {
    const startDateStr: any = req.query[startParam] as string;
    const endDateStr: any = req.query[endParam] as string;

    if (!startDateStr || !endDateStr) {
      return next(new AppError(`${startParam} and ${endParam} are required`, 400));
    }

    const startDate: Date = new Date(startDateStr);
    const endDate: Date = new Date(endDateStr);

    if (isNaN((startDate as any).getTime()) || isNaN((endDate as any).getTime())) {
      return next(
        new AppError({
          message: 'Invalid date format',
          type: ErrorType.VALIDATION,
          code: (ErrorCode as any).INVALID_INPUT,
        })
      );
    }

    if (startDate > endDate) {
      return next(new AppError(`${startParam} must be before ${endParam}`, 400));
    }

    next();
  };
};

/**
 * Middleware to validate numeric parameters
 * @param params Parameters to validate with options
 */
export const validateNumericParams = (
  params: Array<{
    name: string;
    source?: 'query' | 'body' | 'params';
    required?: boolean;
    min?: number;
    max?: number;
  }>
): Middleware => {
  return (req: Request, res: Response, next: NextFunction) => {
    for (const param of params) {
      const source: any = (param as any).source || 'query';
      const value =
        source === 'query'
          ? (req.query[(param as any).name] as string)
          : source === 'body'
          ? req.body[(param as any).name]
          : req.params[(param as any).name];

      if ((param as any).required && (value === undefined || value === null || value === '')) {
        return next(new AppError(`${(param as any).name} is required`, 400));
      }

      if (value !== undefined && value !== null && value !== '') {
        const numValue: any = Number(value);
        if (isNaN(numValue)) {
          return next(new AppError(`${(param as any).name} must be a number`, 400));
        }

        if ((param as any).min !== undefined && numValue < (param as any).min) {
          return next(new AppError(`${(param as any).name} must be at least ${(param as any).min}`, 400));
        }

        if ((param as any).max !== undefined && numValue > (param as any).max) {
          return next(new AppError(`${(param as any).name} must be at most ${(param as any).max}`, 400));
        }
      }
    }
    next();
  };
};

/**
 * Middleware to validate enum parameters
 * @param params Parameters to validate with options
 */
export const validateEnumParams = (
  params: Array<{
    name: string;
    source?: 'query' | 'body' | 'params';
    required?: boolean;
    values: string[];
  }>
): Middleware => {
  return (req: Request, res: Response, next: NextFunction) => {
    for (const param of params) {
      const source: any = (param as any).source || 'query';
      const value =
        source === 'query'
          ? (req.query[(param as any).name] as string)
          : source === 'body'
          ? req.body[(param as any).name]
          : req.params[(param as any).name];

      if ((param as any).required && (value === undefined || value === null || value === '')) {
        return next(new AppError(`${(param as any).name} is required`, 400));
      }

      if (value !== undefined && value !== null && value !== '' && !(param as any).values.includes(value)) {
        return next(
          new AppError(`Invalid ${(param as any).name}. Must be one of: ${(param as any).values.join(', ')}`, 400)
        );
      }
    }
    next();
  };
};
