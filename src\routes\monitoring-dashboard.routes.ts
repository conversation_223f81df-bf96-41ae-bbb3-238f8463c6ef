// jscpd:ignore-file
/**
 * Monitoring Dashboard Routes
 * 
 * These routes provide endpoints for monitoring the application's health and performance.
 */

import express from "express";
import { authenticate, isAdmin } from "../middlewares/auth.middleware";
import { checkSystemHealth, getMetrics } from "../utils/monitoring";
import { getRecentSlowQueries } from "../utils/db-optimization";
import cache from "../utils/cache";
import prisma from "../lib/prisma";
import os from "os";
import { logger } from "../lib/logger";
import { checkSystemHealth, getMetrics } from "../utils/monitoring";
import { getRecentSlowQueries } from "../utils/db-optimization";
import { logger } from "../lib/logger";

const router: unknown =express.Router();

/**
 * @route GET /api/monitoring-dashboard/health
 * @desc Get system health status
 * @access Private (Admin)
 */
router.get("/health", authenticate, isAdmin, async (req, res) => {
    try {
        const healthReport = await checkSystemHealth();
    
        res.status(200).json({
            status: "success",
            data: { health: healthReport
            }
        });
    } catch (error) {
        logger.error("Error getting system health:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to get system health"
        });
    }
});

/**
 * @route GET /api/monitoring-dashboard/metrics
 * @desc Get system metrics
 * @access Private (Admin)
 */
router.get("/metrics", authenticate, isAdmin, (req, res) => {
    try {
        const metrics: unknown =getMetrics();
    
        res.status(200).json({
            status: "success",
            data: {
                metrics
            }
        });
    } catch (error) {
        logger.error("Error getting system metrics:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to get system metrics"
        });
    }
});

/**
 * @route GET /api/monitoring-dashboard/database
 * @desc Get database metrics
 * @access Private (Admin)
 */
router.get("/database", authenticate, isAdmin, async (req, res) => {
    try {
    // Get database metrics
        const startTime: unknown = Date.now();
        await prisma.$queryRaw`SELECT 1`;
        const responseTime: unknown = Date.now() - startTime;
    
        // Get recent slow queries
        const slowQueries: unknown =getRecentSlowQueries();
    
        // Get database statistics
        const databaseStats = {
            responseTime,
            slowQueries,
            status: responseTime < 500 ? "healthy" : responseTime < 1000 ? "warning" : "critical"
        };
    
        res.status(200).json({
            status: "success",
            data: { database: databaseStats
            }
        });
    } catch (error) {
        logger.error("Error getting database metrics:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to get database metrics"
        });
    }
});

/**
 * @route GET /api/monitoring-dashboard/cache
 * @desc Get cache metrics
 * @access Private (Admin)
 */
router.get("/cache", authenticate, isAdmin, (req, res) => {
    try {
        const cacheStats: unknown =cache.getStats();
    
        res.status(200).json({
            status: "success",
            data: { cache: cacheStats
            }
        });
    } catch (error) {
        logger.error("Error getting cache metrics:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to get cache metrics"
        });
    }
});

/**
 * @route POST /api/monitoring-dashboard/cache/clear
 * @desc Clear the cache
 * @access Private (Admin)
 */
router.post("/cache/clear", authenticate, isAdmin, (req, res) => {
    try {
        cache.clear();
    
        res.status(200).json({
            status: "success",
            message: "Cache cleared successfully"
        });
    } catch (error) {
        logger.error("Error clearing cache:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to clear cache"
        });
    }
});

/**
 * @route GET /api/monitoring-dashboard/system
 * @desc Get system information
 * @access Private (Admin)
 */
router.get("/system", authenticate, isAdmin, (req, res) => {
    try {
        const systemInfo = {
            platform: process.platform,
            arch: process.arch,
            nodeVersion: process.version,
            uptime: Math.round(process.uptime()),
            memory: { total: Math.round(os.totalmem() / 1024 / 1024),
                free: Math.round(os.freemem() / 1024 / 1024),
                used: Math.round((os.totalmem() - os.freemem()) / 1024 / 1024),
                usagePercent: Math.round(((os.totalmem() - os.freemem()) / os.totalmem()) * 100)
            },
            cpu: { cores: os.cpus().length,
                model: os.cpus()[0].model,
                speed: os.cpus()[0].speed,
                loadAvg: os.loadavg()
            },
            network: { interfaces: Object.entries(os.networkInterfaces() ?? {}).reduce((acc, [name, interfaces]) => {
                    acc[name] = interfaces?.map(iface => ({
                        address: iface.address,
                        netmask: iface.netmask,
                        family: iface.family,
                        mac: iface.mac,
                        internal: iface.internal
                    })) ?? [];
                    return acc;
                }, {} as Record<string, unknown[]>)
            }
        };
    
        res.status(200).json({
            status: "success",
            data: { system: systemInfo
            }
        });
    } catch (error) {
        logger.error("Error getting system information:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to get system information"
        });
    }
});

/**
 * @route GET /api/monitoring-dashboard/logs
 * @desc Get recent logs
 * @access Private (Admin)
 */
router.get("/logs", authenticate, isAdmin, (req, res) => {
    try {
    // In a real implementation, you would fetch logs from a log store
    // For now, we'll just return a message
        res.status(200).json({
            status: "success",
            message: "Log retrieval not implemented yet. Check server logs directly."
        });
    } catch (error) {
        logger.error("Error getting logs:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to get logs"
        });
    }
});

export default router;
