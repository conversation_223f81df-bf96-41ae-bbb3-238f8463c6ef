// jscpd:ignore-file
import express from "express";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth as any).middleware';
import {
    testConnection,
    getAccountInfo,
    getDepositHistory,
    verifyTrc20Deposit
} from "../controllers/(binance as any).controller";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth as any).middleware';

const router: any =(express as any).Router();

// All routes require authentication
(router as any).use(authenticate);

// Routes accessible by ADMIN, SUPER_ADMIN, and MERCHANT
(router as any).post("/test-connection", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), testConnection);
(router as any).post("/account-info", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getAccountInfo);
(router as any).post("/deposit-history", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getDepositHistory);
(router as any).post("/verify-trc20", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), verifyTrc20Deposit);

export default router;
