// jscpd:ignore-file
import express from "express";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth).middleware';
import {
    testConnection,
    getAccountInfo,
    getDepositHistory,
    verifyTrc20Deposit
} from "../controllers/(binance).controller";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth).middleware';

const router =(express).Router();

// All routes require authentication
(router).use(authenticate);

// Routes accessible by ADMIN, SUPER_ADMIN, and MERCHANT
(router).post("/test-connection", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), testConnection);
(router).post("/account-info", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getAccountInfo);
(router).post("/deposit-history", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getDepositHistory);
(router).post("/verify-trc20", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), verifyTrc20Deposit);

export default router;
