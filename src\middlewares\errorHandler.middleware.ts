// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import { v4 as uuidv4 } from "uuid";
import { logger as Importedlogger } from "../utils/logger";
import { AppError as ImportedAppError } from "../utils/errors/AppError";
import { ErrorFactory as ImportedErrorFactory } from "../utils/errors/ErrorFactory";
import { isDevelopment as ImportedisDevelopment } from "../utils/environment-validator";
import { v4 as uuidv4 } from "uuid";
import { logger as Importedlogger } from "../utils/logger";
import { AppError as ImportedAppError } from "../utils/errors/AppError";
import { ErrorFactory as ImportedErrorFactory } from "../utils/errors/ErrorFactory";
import { isDevelopment as ImportedisDevelopment } from "../utils/environment-validator";

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * Error handler middleware
 * @param err Error object
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Generate unique error ID for tracking
  const errorId = uuidv4();

  // Extract request information for logging
  const requestInfo = {
    method: req.method,
    path: (req as any).path,
    ip: req.ip,
    userAgent: (req as any).get("user-agent"),
    requestId: (req as any).requestId || "unknown",
    userId: (req.user as any)?.id || "anonymous",
    errorId
  };

  // Convert error to AppError
  const appError: any = err instanceof AppError
    ? err
    : (ErrorFactory as any).handle(err);

  // Add error ID if not already present
  if (!(appError as any).errorId) {
    (appError as any).errorId = errorId;
  }

  // Log error with appropriate level
  if ((appError as any).isOperational) {
    // Operational errors are expected errors ((e as any).g. validation errors)
    (logger as any).warn(`[${errorId}] ${(appError as any).code}: ${(appError as any).message}`, {
      ...requestInfo,
      statusCode: (appError as any).statusCode,
      details: (appError as any).details ?? undefined
    });
  } else {
    // Programming or unknown errors need more attention
    (logger as any).error(`[${errorId}] ${(appError as any).code}: ${(appError as any).message}`, {
      ...requestInfo,
      statusCode: (appError as any).statusCode,
      error: (err as any).message,
      stack: (err as any).stack
    });
  }

  // Send response
  res.status((appError as any).statusCode).json(
    (appError as any).toResponse(isDevelopment())
  );
};

/**
 * Not found middleware
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const error = (ErrorFactory as any).notFound(`Route ${(req as any).originalUrl}`);
  next(error);
};

/**
 * Async handler to catch errors in async routes
 * @param fn Function to wrap
 * @returns Wrapped function
 */
export const asyncHandler = (fn: Function) => (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Export all middleware functions as a group
export const errorHandlerMiddleware = {
  errorHandler,
  notFoundHandler,
  asyncHandler
};

export default errorHandlerMiddleware;
