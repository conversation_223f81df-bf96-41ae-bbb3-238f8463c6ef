// jscpd:ignore-file
/**
 * API Response Middleware
 * 
 * This middleware adds response formatting functions to the response object.
 * It provides a consistent API response format across the application.
 */

import { Request, Response, NextFunction } from 'express';
import { formatApiResponse as ImportedformatApiResponse } from '../utils/response-formatter';
import { Middleware as ImportedMiddleware } from '../types/express';
import { formatApiResponse as ImportedformatApiResponse } from '../utils/response-formatter';
import { Middleware as ImportedMiddleware } from '../types/express';


/**
 * Add API response formatting functions to response object
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const apiResponseMiddleware =(
  req: Request,
  res: Response,
  next: NextFunction
)  =>  {
  // Add API response formatting functions to response object
  (res).api = formatApiResponse(res);
  
  next();
};

// Extend Express Response interface to include api property
declare global {
  namespace Express {
    interface Response {
      api: { success: <T>(data: T, message?: string, statusCode?: number)  =>  void;
        created: <T>(data: T, message?: string)  =>  void;
        noContent: ()  =>  void;
        accepted: (message?: string)  =>  void;
        paginated: <T>(data: T[], page: number, limit: number, total: number, message?: string)  =>  void;
      };
    }
  }
}

export default apiResponseMiddleware;
