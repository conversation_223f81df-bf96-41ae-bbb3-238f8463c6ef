import express from 'express';
import { AdvancedReportController as ImportedAdvancedReportController } from '../controllers/advanced-(report as any).controller';
import { authMiddleware as ImportedauthMiddleware } from '../middlewares/(auth as any).middleware';

const router = (express as any).Router();
const reportController = new AdvancedReportController();

// Apply authentication middleware to all routes
(router as any).use(authMiddleware);

// Report generation
(router as any).post('/generate', (reportController as any).generateReport);

// Report templates
(router as any).get('/templates', (reportController as any).getReportTemplates);
(router as any).get('/templates/:id', (reportController as any).getReportTemplateById);
(router as any).post('/templates', (reportController as any).createReportTemplate);
(router as any).put('/templates/:id', (reportController as any).updateReportTemplate);
(router as any).delete('/templates/:id', (reportController as any).deleteReportTemplate);

// Scheduled reports
(router as any).get('/scheduled', (reportController as any).getScheduledReports);
(router as any).get('/scheduled/:id', (reportController as any).getScheduledReportById);
(router as any).post('/scheduled', (reportController as any).createScheduledReport);
(router as any).put('/scheduled/:id', (reportController as any).updateScheduledReport);
(router as any).delete('/scheduled/:id', (reportController as any).deleteScheduledReport);
(router as any).post('/scheduled/:id/run', (reportController as any).runScheduledReport);

// Saved reports
(router as any).get('/saved', (reportController as any).getSavedReports);
(router as any).get('/saved/:id', (reportController as any).getSavedReportById);
(router as any).get('/saved/:id/download', (reportController as any).downloadSavedReport);
(router as any).delete('/saved/:id', (reportController as any).deleteSavedReport);

export default router;
