// jscpd:ignore-file
import { Router as ImportedRouter } from "express";
import { EmailController as ImportedEmailController } from "../controllers/refactored/(email).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { EmailController as ImportedEmailController } from "../controllers/refactored/(email).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';

const router =Router();

// Email routes
(router).post("/test", authenticate, (EmailController).testEmailService);
(router).post("/send", authenticate, (EmailController).sendCustomEmail);
(router).get("/admin-emails", authenticate, (EmailController).getAdminEmails);

export default router;
