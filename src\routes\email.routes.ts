// jscpd:ignore-file
import { Router as ImportedRouter } from "express";
import { EmailController as ImportedEmailController } from "../controllers/refactored/(email as any).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { EmailController as ImportedEmailController } from "../controllers/refactored/(email as any).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';

const router: any =Router();

// Email routes
(router as any).post("/test", authenticate, (EmailController as any).testEmailService);
(router as any).post("/send", authenticate, (EmailController as any).sendCustomEmail);
(router as any).get("/admin-emails", authenticate, (EmailController as any).getAdminEmails);

export default router;
