// jscpd:ignore-file
import express from "express";
import { PaymentVerificationController as ImportedPaymentVerificationController } from "../controllers/payment-(verification as any).controller";

const router: any =(express as any).Router();
const paymentVerificationController = new PaymentVerificationController();

/**
 * @route POST /api/payment-verification/verify
 * @desc Verify a payment
 * @access Private
 */
(router as any).post("/verify", (paymentVerificationController as any).verifyPayment);

/**
 * @route POST /api/payment-verification/binance-pay
 * @desc Verify a Binance Pay payment
 * @access Private
 */
(router as any).post("/binance-pay", (paymentVerificationController as any).verifyBinancePayPayment);

/**
 * @route POST /api/payment-verification/binance-c2c
 * @desc Verify a Binance C2C payment
 * @access Private
 */
(router as any).post("/binance-c2c", (paymentVerificationController as any).verifyBinanceC2CPayment);

/**
 * @route POST /api/payment-verification/binance-trc20
 * @desc Verify a Binance TRC20 payment
 * @access Private
 */
(router as any).post("/binance-trc20", (paymentVerificationController as any).verifyBinanceTRC20Payment);

/**
 * @route POST /api/payment-verification/crypto-transfer
 * @desc Verify a crypto transfer payment
 * @access Private
 */
(router as any).post("/crypto-transfer", (paymentVerificationController as any).verifyCryptoTransferPayment);

export default router;
