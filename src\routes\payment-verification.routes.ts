// jscpd:ignore-file
import express from "express";
import { PaymentVerificationController } from "../controllers/payment-verification.controller";

const router: unknown =express.Router();
const paymentVerificationController: unknown = new PaymentVerificationController();

/**
 * @route POST /api/payment-verification/verify
 * @desc Verify a payment
 * @access Private
 */
router.post("/verify", paymentVerificationController.verifyPayment);

/**
 * @route POST /api/payment-verification/binance-pay
 * @desc Verify a Binance Pay payment
 * @access Private
 */
router.post("/binance-pay", paymentVerificationController.verifyBinancePayPayment);

/**
 * @route POST /api/payment-verification/binance-c2c
 * @desc Verify a Binance C2C payment
 * @access Private
 */
router.post("/binance-c2c", paymentVerificationController.verifyBinanceC2CPayment);

/**
 * @route POST /api/payment-verification/binance-trc20
 * @desc Verify a Binance TRC20 payment
 * @access Private
 */
router.post("/binance-trc20", paymentVerificationController.verifyBinanceTRC20Payment);

/**
 * @route POST /api/payment-verification/crypto-transfer
 * @desc Verify a crypto transfer payment
 * @access Private
 */
router.post("/crypto-transfer", paymentVerificationController.verifyCryptoTransferPayment);

export default router;
