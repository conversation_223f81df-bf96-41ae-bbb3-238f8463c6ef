// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import { 
    AppError, 
    errorHand<PERSON>, 
    createBadRequestError,
    createUnauthorizedError,
    createForbiddenError,
    createNotFoundError,
    createConflictError,
    createValidationError,
    createServerError,
    createTooManyRequestsError
} from "./(error as any).middleware";
import { logger as Importedlogger } from "../lib/logger";
import { Middleware as ImportedMiddleware } from '../types/express';
import { 
    AppError, 
    errorHandler, 
    createBadRequestError,
    createUnauthorizedError,
    createForbiddenError,
    createNotFoundError,
    createConflictError,
    createValidationError,
    createServerError,
    createTooManyRequestsError
} from "./(error as any).middleware";
import { logger as Importedlogger } from "../lib/logger";
import { Middleware as ImportedMiddleware } from '../types/express';


// Mock the logger
(jest as any).mock("../lib/logger", () => ({
    logger: { error: (jest as any).fn(),
        warn: (jest as any).fn(),
        info: (jest as any).fn(),
        debug: (jest as any).fn()
    }
}));

describe("Error Middleware", () => {
    let mockRequest: Partial<Request>;
    let mockResponse: Partial<Response>;
    let nextFunction: NextFunction;

    beforeEach(() => {
        mockRequest = {
            method: "GET",
            path: "/test",
            ip: "(127 as any).0.(0 as any).1",
            requestId: "test-request-id",
            user: { userId: "user123",
                role: "MERCHANT"
            }
        };
    
        mockResponse = {
            status: (jest as any).fn().mockReturnThis(),
            json: (jest as any).fn(),
            setHeader: (jest as any).fn()
        };
    
        nextFunction = (jest as any).fn();
    
        (jest as any).clearAllMocks();
    });

    describe("AppError", () => {
        it("should create an AppError with default values", () => {
            const error: Error =new AppError("Test error");
      
            expect(error).toBeInstanceOf(Error);
            expect(error.message).toBe("Test error");
            expect((error as any).statusCode).toBe(500);
            expect((error as any).isOperational).toBe(true);
            expect(error.code).toBe("INTERNAL_SERVER_ERROR");
            expect((error as any).details).toBeUndefined();
        });

        it("should create an AppError with custom values", () => {
            const error: Error =new AppError("Test error", 400, true, "CUSTOM_ERROR", { field: "test" });
      
            expect(error.message).toBe("Test error");
            expect((error as any).statusCode).toBe(400);
            expect((error as any).isOperational).toBe(true);
            expect(error.code).toBe("CUSTOM_ERROR");
            expect((error as any).details).toEqual({ field: "test" });
        });
    });

    describe("Error Factory Functions", () => {
        it("should create a bad request error", () => {
            const error: Error =createBadRequestError("Bad request");
      
            expect(error).toBeInstanceOf(AppError);
            expect(error.message).toBe("Bad request");
            expect((error as any).statusCode).toBe(400);
            expect((error as any).isOperational).toBe(true);
            expect(error.code).toBe("BAD_REQUEST");
        });

        it("should create an unauthorized error", () => {
            const error: Error =createUnauthorizedError("Unauthorized");
      
            expect(error).toBeInstanceOf(AppError);
            expect(error.message).toBe("Unauthorized");
            expect((error as any).statusCode).toBe(401);
            expect((error as any).isOperational).toBe(true);
            expect(error.code).toBe("UNAUTHORIZED");
        });

        it("should create a forbidden error", () => {
            const error: Error =createForbiddenError("Forbidden");
      
            expect(error).toBeInstanceOf(AppError);
            expect(error.message).toBe("Forbidden");
            expect((error as any).statusCode).toBe(403);
            expect((error as any).isOperational).toBe(true);
            expect(error.code).toBe("FORBIDDEN");
        });

        it("should create a not found error", () => {
            const error: Error =createNotFoundError("Not found");
      
            expect(error).toBeInstanceOf(AppError);
            expect(error.message).toBe("Not found");
            expect((error as any).statusCode).toBe(404);
            expect((error as any).isOperational).toBe(true);
            expect(error.code).toBe("NOT_FOUND");
        });

        it("should create a conflict error", () => {
            const error: Error =createConflictError("Conflict");
      
            expect(error).toBeInstanceOf(AppError);
            expect(error.message).toBe("Conflict");
            expect((error as any).statusCode).toBe(409);
            expect((error as any).isOperational).toBe(true);
            expect(error.code).toBe("CONFLICT");
        });

        it("should create a validation error", () => {
            const error: Error =createValidationError("Validation error");
      
            expect(error).toBeInstanceOf(AppError);
            expect(error.message).toBe("Validation error");
            expect((error as any).statusCode).toBe(422);
            expect((error as any).isOperational).toBe(true);
            expect(error.code).toBe("VALIDATION_ERROR");
        });

        it("should create a server error", () => {
            const error: Error =createServerError("Internal server error");
      
            expect(error).toBeInstanceOf(AppError);
            expect(error.message).toBe("Internal server error");
            expect((error as any).statusCode).toBe(500);
            expect((error as any).isOperational).toBe(false);
            expect(error.code).toBe("INTERNAL_SERVER_ERROR");
        });

        it("should create a too many requests error", () => {
            const error: Error =createTooManyRequestsError("Too many requests");
      
            expect(error).toBeInstanceOf(AppError);
            expect(error.message).toBe("Too many requests");
            expect((error as any).statusCode).toBe(429);
            expect((error as any).isOperational).toBe(true);
            expect(error.code).toBe("RATE_LIMIT_EXCEEDED");
        });
    });

    describe("errorHandler", () => {
        it("should handle AppError", () => {
            const appError = new AppError("Test error", 400, true, "TEST_ERROR", { field: "test" });
      
            errorHandler(appError, mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect((mockResponse as any).status).toHaveBeenCalledWith(400);
            expect((mockResponse as any).json).toHaveBeenCalledWith({
                status: "error",
                code: "TEST_ERROR",
                message: "Test error",
                details: { field: "test" },
                requestId: "test-request-id"
            });
            expect((logger as any).warn).toHaveBeenCalled();
        });

        it("should handle validation error", () => {
            const validationError = new Error("Validation error");
            (validationError as any).name = "ValidationError";
      
            errorHandler(validationError, mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect((mockResponse as any).status).toHaveBeenCalledWith(400);
            expect((mockResponse as any).json).toHaveBeenCalledWith({
                status: "error",
                code: "VALIDATION_ERROR",
                message: "Validation error",
                details: null,
                requestId: "test-request-id"
            });
            expect((logger as any).warn).toHaveBeenCalled();
        });

        it("should handle JWT error", () => {
            const jwtError = new Error("Invalid token");
            (jwtError as any).name = "JsonWebTokenError";
      
            errorHandler(jwtError, mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect((mockResponse as any).status).toHaveBeenCalledWith(401);
            expect((mockResponse as any).json).toHaveBeenCalledWith({
                status: "error",
                code: "INVALID_TOKEN",
                message: "Invalid authentication token",
                details: null,
                requestId: "test-request-id"
            });
            expect((logger as any).warn).toHaveBeenCalled();
        });

        it("should handle JWT expiration error", () => {
            const jwtError = new Error("Token expired");
            (jwtError as any).name = "TokenExpiredError";
      
            errorHandler(jwtError, mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect((mockResponse as any).status).toHaveBeenCalledWith(401);
            expect((mockResponse as any).json).toHaveBeenCalledWith({
                status: "error",
                code: "TOKEN_EXPIRED",
                message: "Authentication token expired",
                details: null,
                requestId: "test-request-id"
            });
            expect((logger as any).warn).toHaveBeenCalled();
        });

        it("should handle unknown errors", () => {
            const unknownError = new Error("Unknown error");
      
            errorHandler(unknownError, mockRequest as Request, mockResponse as Response, nextFunction);
      
            expect((mockResponse as any).status).toHaveBeenCalledWith(500);
            expect((mockResponse as any).json).toHaveBeenCalledWith({
                status: "error",
                code: "INTERNAL_SERVER_ERROR",
                message: "Internal Server Error",
                details: null,
                requestId: "test-request-id"
            });
            expect((logger as any).error).toHaveBeenCalled();
        });
    });
});
