// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BinanceService as ImportedBinanceService } from "../services/(binance).service";
import { AppError, asyncHandler } from '../middlewares/(error).middleware';
import { BinanceService as ImportedBinanceService } from "../services/(binance).service";
import { AppError, asyncHandler } from '../middlewares/(error).middleware';

// Test Binance API connection
export const testConnection =asyncHandler(async (req: Request, res: Response) => {
    const { apiKey, apiSecret } = req.body;
  
    // Validate required fields
    if (!apiKey || !apiSecret) {
        throw new AppError({
            message: "API key and secret are required",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).MISSING_REQUIRED_FIELD
        });
    }
  
    // Test connection
    const result = await (BinanceService).testConnection(apiKey, apiSecret);
  
    if (!result.success) {
        return res.status(400).json({
            success: false,
            message: (result).message
        });
    }
  
    res.status(200).json({
        success: true,
        message: "Connection successful"
    });
});

// Get account information
export const getAccountInfo =asyncHandler(async (req: Request, res: Response) => {
    const { apiKey, apiSecret } = req.body;
  
    // Validate required fields
    if (!apiKey || !apiSecret) {
        throw new AppError({
            message: "API key and secret are required",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).MISSING_REQUIRED_FIELD
        });
    }
  
    // Get account information
    const accountInfo = await (BinanceService).getAccountInfo(apiKey, apiSecret);
  
    res.status(200).json(accountInfo);
});

// Get deposit history
export const getDepositHistory =asyncHandler(async (req: Request, res: Response) => {
    const { apiKey, apiSecret, coin, status, startTime, endTime } = req.body;
  
    // Validate required fields
    if (!apiKey || !apiSecret) {
        throw new AppError({
            message: "API key and secret are required",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).MISSING_REQUIRED_FIELD
        });
    }
  
    // Get deposit history
    const depositHistory = await (BinanceService).getDepositHistory(
        apiKey,
        apiSecret,
        coin,
        status,
        startTime,
        endTime
    );
  
    res.status(200).json(depositHistory);
});

// Verify TRC20 deposit
export const verifyTrc20Deposit =asyncHandler(async (req: Request, res: Response) => {
    const { apiKey, apiSecret, txHash, amount, coin, timeWindow } = req.body;
  
    // Validate required fields
    if (!apiKey || !apiSecret || !txHash || !amount) {
        throw new AppError({
            message: "API key, secret, transaction hash, and amount are required",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).MISSING_REQUIRED_FIELD
        });
    }
  
    // Verify TRC20 deposit
    const result = await (BinanceService).verifyTrc20Deposit(
        apiKey,
        apiSecret,
        txHash,
        amount,
        coin,
        timeWindow
    );
  
    res.status(200).json(result);
});
