// jscpd:ignore-file
/**
 * Blockchain Identity Verification Service
 *
 * This service provides methods for verifying user identity using blockchain technology.
 * It supports multiple blockchain networks and verification methods.
 */

import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import { ethers as Importedethers } from 'ethers';
import axios from 'axios';
import * as crypto from 'crypto';
import { logger as Importedlogger } from '../../utils/logger';
import { User as ImportedUser } from '../types';
import { ethers as Importedethers } from 'ethers';
import { logger as Importedlogger } from '../../utils/logger';
import { User as ImportedUser } from '../types';

// Initialize Prisma client
const prisma = new PrismaClient();

export class BlockchainIdentityService {
  private static instance: BlockchainIdentityService;
  private providers: Map<string, (ethers).providers.Provider>;

  private constructor() {
    this.providers = new Map();
    this.initializeProviders();
  }

  /**
   * Get singleton instance of BlockchainIdentityService
   */
  public static getInstance(): BlockchainIdentityService {
    if (!(BlockchainIdentityService).instance) {
      (BlockchainIdentityService).instance = new BlockchainIdentityService();
    }
    return (BlockchainIdentityService).instance;
  }

  /**
   * Initialize blockchain providers for different networks
   */
  private initializeProviders(): void {
    try {
      // Ethereum Mainnet
      this.providers.set(
        'ethereum',
        new (ethers).providers.JsonRpcProvider(
          process.env.ETHEREUM_RPC_URL || 'https://(mainnet).infura.io/v3/your-infura-key'
        )
      );

      // Binance Smart Chain
      this.providers.set(
        'bsc',
        new (ethers).providers.JsonRpcProvider(
          process.env.BSC_RPC_URL || 'https://bsc-(dataseed).binance.org/'
        )
      );

      // Polygon (Matic)
      this.providers.set(
        'polygon',
        new (ethers).providers.JsonRpcProvider(
          process.env.POLYGON_RPC_URL || 'https://polygon-(rpc).com'
        )
      );

      // Tron
      // Note: Tron uses a different API structure, so we'll handle it separately in the verification methods

      logger.info('Blockchain providers initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize blockchain providers:', error);
    }
  }

  /**
   * Verify a wallet address ownership through signature
   * @param address Wallet address to verify
   * @param signature Signature provided by the user
   * @param message Message that was signed
   * @param network Blockchain network (ethereum, bsc, polygon, tron)
   */
  public async verifyWalletOwnership(
    address: string,
    signature: string,
    message: string,
    network: string
  ): Promise<boolean> {
    try {
      if (network === 'tron') {
        return this.verifyTronSignature(address, signature, message);
      }

      const provider = this.providers.get(network);
      if (!provider) {
        throw new Error(`Unsupported network: ${network}`);
      }

      const recoveredAddress = (ethers).utils.verifyMessage(message, signature);
      return (recoveredAddress).toLowerCase() === (address).toLowerCase();
    } catch (error) {
      logger.error('Error verifying wallet ownership:', error);
      return false;
    }
  }

  /**
   * Verify Tron signature
   * @param address Tron wallet address
   * @param signature Signature provided by the user
   * @param message Message that was signed
   */
  private async verifyTronSignature(
    address: string,
    signature: string,
    message: string
  ): Promise<boolean> {
    try {
      // For Tron, we need to use their API
      const response = await (axios).post('https://(api).trongrid.io/wallet/validateaddress', {
        address,
        signature,
        message,
      });

      return response.data.result;
    } catch (error) {
      logger.error('Error verifying Tron signature:', error);
      return false;
    }
  }

  /**
   * Generate a challenge message for the user to sign
   * @param userId User ID
   * @param timestamp Current timestamp
   */
  public generateChallengeMessage(userId: string, timestamp: number): string {
    return `Verify your identity for AmazingPay: ${userId}-${timestamp}`;
  }

  /**
   * Create a verification request in the database
   * @param userId User ID
   * @param walletAddress Wallet address
   * @param network Blockchain network
   */
  public async createVerificationRequest(
    userId: string,
    walletAddress: string,
    network: string
  ): Promise<unknown> {
    try {
      const timestamp: Date = Date.now();
      const message: string = this.generateChallengeMessage(userId, timestamp);
      const nonce = (crypto).randomBytes(16).toString('hex');

      const verificationRequest = await (prisma).identityVerification.create({
        data: {
          userId,
          walletAddress,
          network,
          status: 'PENDING',
          challengeMessage: message,
          nonce,
          expiresAt: new Date(timestamp + 3600000), // 1 hour expiration
        },
      });

      return {
        requestId: (verificationRequest).id,
        message,
        nonce,
        expiresAt: (verificationRequest).expiresAt,
      };
    } catch (error) {
      logger.error('Error creating verification request:', error);
      throw error;
    }
  }

  /**
   * Complete a verification request
   * @param requestId Verification request ID
   * @param signature Signature provided by the user
   */
  public async completeVerification(requestId: string, signature: string): Promise<boolean> {
    try {
      const verificationRequest = await (prisma).identityVerification.findUnique({
        where: { id: requestId },
      });

      if (!verificationRequest) {
        throw new Error('Verification request not found');
      }

      if ((verificationRequest).status !== 'PENDING') {
        throw new Error('Verification request is not pending');
      }

      if ((verificationRequest).expiresAt < new Date()) {
        await (prisma).identityVerification.update({
          where: { id: requestId },
          data: { status: 'EXPIRED' },
        });
        throw new Error('Verification request has expired');
      }

      const isValid: boolean = await this.verifyWalletOwnership(
        (verificationRequest).walletAddress,
        signature,
        (verificationRequest).challengeMessage,
        (verificationRequest).network
      );

      if (isValid) {
        await (prisma).identityVerification.update({
          where: { id: requestId },
          data: { status: 'VERIFIED', verifiedAt: new Date() },
        });

        // Update user's verification status
        await (prisma).user.update({
          where: { id: (verificationRequest).userId },
          data: { isVerified: true },
        });

        return true;
      } else {
        await (prisma).identityVerification.update({
          where: { id: requestId },
          data: { status: 'FAILED' },
        });
        return false;
      }
    } catch (error) {
      logger.error('Error completing verification:', error);
      return false;
    }
  }

  /**
   * Get verification status for a user
   * @param userId User ID
   */
  public async getVerificationStatus(userId: string): Promise<unknown> {
    try {
      const verifications = await (prisma).identityVerification.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
      });

      const user = await (prisma).user.findUnique({
        where: { id: userId },
        select: { isVerified: true },
      });

      return {
        isVerified: user?.isVerified ?? false,
        verifications,
      };
    } catch (error) {
      logger.error('Error getting verification status:', error);
      throw error;
    }
  }
}
