// jscpd:ignore-file
/**
 * Event Bus
 *
 * A centralized event system for cross-module communication.
 */

import { logger } from "./logger";

type EventHandler = (data) => void | Promise<void>;

/**
 * Event bus for cross-module communication
 */
export class EventBus {
    private static instance: EventBus;
    private handlers: Map<string, EventHandler[]> = new Map();
    private wildcardHandlers: EventHandler[] = [];

    /**
   * Get the singleton instance
   */
    public static getInstance(): EventBus {
        if (!EventBus.instance) {
            EventBus.instance = new EventBus();
        }

        return EventBus.instance;
    }

    /**
   * Register an event handler
   *
   * @param event Event name or '*' for all events
   * @param handler Event handler function
   */
    public on(event: string, handler: EventHandler): void {
        if (event === "*") {
            this.wildcardHandlers.push(handler);
            return;
        }

        const handlers: unknown = this.handlers.get(event) ?? [];
        handlers.push(handler);
        this.handlers.set(event, handlers);

        logger.debug(`Registered handler for event: ${event}`);
    }

    /**
   * Remove an event handler
   *
   * @param event Event name or '*' for all events
   * @param handler Event handler function to remove
   */
    public off(event: string, handler: EventHandler): void {
        if (event === "*") {
            this.wildcardHandlers = this.wildcardHandlers.filter(h => h !== handler);
            return;
        }

        const handlers: unknown = this.handlers.get(event);

        if (!handlers) {
            return;
        }

        this.handlers.set(event, handlers.filter(h => h !== handler));

        logger.debug(`Removed handler for event: ${event}`);
    }

    /**
   * Emit an event
   *
   * @param event Event name
   * @param data Event data
   */
    public async emit(event: string, data = {}): Promise<void> {
        logger.debug(`Emitting event: ${event}`, { data });

        // Add event name to data
        const eventData: unknown = {
            ...data,
            _event: event,
            _timestamp: new Date()
        };

        // Call specific handlers
        const handlers: unknown = this.handlers.get(event) ?? [];

        // Call wildcard handlers
        const allHandlers: unknown = [...handlers, ...this.wildcardHandlers];

        // Execute handlers
        const promises: unknown =allHandlers.map(async (handler) => {
            try {
                await handler(eventData);
            } catch (error) {
                logger.error(`Error in event handler for ${event}:`, error);
            }
        });

        await Promise.all(promises);
    }

    /**
   * Register multiple event handlers
   *
   * @param handlers Object with event names as keys and handlers as values
   */
    public registerHandlers(handlers: Record<string, EventHandler>): void {
        Object.entries(handlers).forEach(([event, handler]) => {
            this.on(event, handler);
        });
    }

    /**
   * Clear all event handlers
   */
    public clear(): void {
        this.handlers.clear();
        this.wildcardHandlers = [];

        logger.debug("Cleared all event handlers");
    }
}

// Export singleton instance
export const eventBus: unknown =EventBus.getInstance();
