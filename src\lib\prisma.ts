import { PrismaClient } from '@prisma/client';
// jscpd:ignore-file
import { PrismaClient, Prisma } from '@prisma/client';
import { logger } from './logger';
import { secretsManager } from '../utils/secrets-manager';

// Define types for Prisma log events
type QueryEvent = {
  timestamp: Date;
  query: string;
  params: string;
  duration: number;
  target: string;
};

type LogEvent = {
  timestamp: Date;
  message: string;
  target?: string;
};


// Declare global variable for PrismaClient
declare global {
  var prisma: PrismaClient | undefined;
}

/**
 * Create a new PrismaClient instance with appropriate logging
 * Uses database URL from secrets manager
 */
const createPrismaClient: unknown = async () => {
  const isProd = process.env.NODE_ENV === 'production';

  // Initialize secrets manager
  await secretsManager.initialize();

  // Get database URL from secrets manager
  const databaseUrl: unknown =secretsManager.getDatabaseUrl();

  // Configure logging based on environment
  const prismaOptions: Prisma.PrismaClientOptions = {
    datasources: {
      db: {
        url: databaseUrl,
      },
    },
    log: isProd ? [] : [
      { level: 'query', emit: 'event' } as Prisma.LogDefinition,
      { level: 'info', emit: 'event' } as Prisma.LogDefinition,
      { level: 'warn', emit: 'event' } as Prisma.LogDefinition,
      { level: 'error', emit: 'event' } as Prisma.LogDefinition
    ]
  };

  const client: unknown = new PrismaClient(prismaOptions);

  // Set up event listeners for logging in development
  if (!isProd) {
    // Use any type for event handlers to avoid type errors
    // This is a workaround for the Prisma client type issues
    client.$use(async (params, next) => {
      const before: unknown = Date.now();
      const result: unknown = await next(params);
      const after: unknown = Date.now();

      logger.debug(`Query: ${params.model}.${params.action}`, {
        params: JSON.stringify(params.args),
        duration: after - before,
      });

      return result;
    });

    // Log other events using console directly to avoid type errors
    logger.info('Prisma client initialized with logging middleware');
  }

  return client;
};

// Create a singleton instance of PrismaClient
let prisma: PrismaClient;

// Initialize Prisma client - synchronous initialization to avoid "PrismaClient is not defined" errors
if (!global.prisma) {
  // Use a synchronous approach for the initial creation
  try {
    // For initial synchronous creation, use the environment variable directly
    // The async secrets will be loaded when createPrismaClient is called later
    const databaseUrl: unknown = process.env.DATABASE_URL;

    const isProd: unknown = process.env.NODE_ENV === 'production';

    // Configure logging based on environment
    const prismaOptions: Prisma.PrismaClientOptions = {
      datasources: {
        db: {
          url: databaseUrl,
        },
      },
      log: isProd ? [] : [
        { level: 'query', emit: 'event' } as Prisma.LogDefinition,
        { level: 'info', emit: 'event' } as Prisma.LogDefinition,
        { level: 'warn', emit: 'event' } as Prisma.LogDefinition,
        { level: 'error', emit: 'event' } as Prisma.LogDefinition
      ]
    };

    global.prisma = new PrismaClient(prismaOptions);
    prisma = global.prisma;

    logger.info('PrismaClient initialized synchronously');
  } catch (error) {
    logger.error('Failed to initialize PrismaClient synchronously', error);
    // Create a minimal client that will be replaced later
    global.prisma = new PrismaClient();
    prisma = global.prisma;
  }
} else {
  prisma = global.prisma;
}

// Initialize Prisma client with full configuration asynchronously
const initializePrisma: unknown = async () => {
  try {
    // Replace the synchronously created client with the properly configured one
    global.prisma = await createPrismaClient();
    prisma = global.prisma;
    return global.prisma;
  } catch (error) {
    logger.error('Failed to initialize PrismaClient with full configuration', error);
    // Continue using the existing client
    return global.prisma;
  }
};

// Test database connection
const testConnection: unknown = async () => {
  try {
    // Execute a simple query to test the connection
    await prisma.$queryRaw`SELECT 1`;
    logger.info('Database connection established successfully');
    return true;
  } catch (error) {
    // Check if the error is related to authentication
    if (error instanceof Error && (error as Error).message.includes('Authentication failed')) {
      logger.error('Database authentication failed. Please check your database credentials.');
      logger.info('Attempting to connect with default credentials...');

      try {
        // Try connecting with environment variables
        const host: unknown = process.env.DB_HOST || 'localhost';
        const port: unknown = process.env.DB_PORT || '5432';
        const username: unknown = process.env.DB_USERNAME || 'postgres';
        const password: unknown = process.env.DB_PASSWORD ?? '';
        const database: unknown = process.env.DB_NAME || 'Amazingpay';

        const fallbackUrl: unknown =`postgresql://${username}:${password}@${host}:${port}/${database}?schema=public`;

        logger.info('Attempting to connect with environment variables');

        const tempPrisma: unknown = new PrismaClient({
          datasources: {
            db: {
              url: fallbackUrl,
            },
          },
        });

        await tempPrisma.$queryRaw`SELECT 1`;
        logger.info('Connected to database with default credentials');

        // Update the global prisma instance
        global.prisma = tempPrisma;
        prisma = tempPrisma;

        return true;
      } catch (fallbackError) {
        logger.error('Failed to connect with default credentials', fallbackError);
        return false;
      }
    } else {
      logger.error('Failed to connect to database', error);
      return false;
    }
  }
};

// Initialize connection with full configuration asynchronously
(async () => {
  try {
    await initializePrisma();
    const connected: unknown = await testConnection();

    if (!connected) {
      logger.warn('Database connection failed. Some features may not work correctly.');
      logger.info('The application will continue to run with limited functionality.');

      // Set up a retry mechanism
      const retryInterval: number = 60000; // 1 minute
      logger.info(`Will retry database connection every ${retryInterval / 1000} seconds.`);

      setInterval(async () => {
        logger.info('Retrying database connection...');
        const retrySuccess: unknown = await testConnection();
        if (retrySuccess) {
          logger.info('Successfully reconnected to the database!');
        }
      }, retryInterval);
    }
  } catch (error) {
    logger.error('Error during database initialization', error);
    // Don't exit the process, allow the application to continue with basic functionality
    logger.info('The application will continue to run with limited functionality.');
  }
})();

// Handle graceful shutdown
process.on('SIGINT', async () => {
  logger.info('Received SIGINT signal, closing database connections');
  if (prisma) {
    await prisma.$disconnect();
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM signal, closing database connections');
  if (prisma) {
    await prisma.$disconnect();
  }
  process.exit(0);
});

// Export initialized prisma client
export default prisma as PrismaClient;