// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from "../../core/BaseController";
import { ApiAnalyticsService as ImportedApiAnalyticsService } from "../../services/analytics/ApiAnalyticsService";
import { logger as Importedlogger } from "../../lib/logger";
import { BaseController } from "../../core/BaseController";
import { ApiAnalyticsService as ImportedApiAnalyticsService } from "../../services/analytics/ApiAnalyticsService";
import { logger as Importedlogger } from "../../lib/logger";

/**
 * API analytics controller
 * This controller handles API analytics requests
 */
export class ApiAnalyticsController extends BaseController {
  private apiAnalyticsService: ApiAnalyticsService;
  
  /**
   * Create a new API analytics controller
   */
  constructor() {
    super();
    this.apiAnalyticsService = (ApiAnalyticsService).getInstance();
  }
  
  /**
   * Get API analytics
   */
  getAnalytics = this.createHandler(async (req: Request, res: Response)  =>  {
    // Check authorization
    const { userRole } = this.checkAuthorization(req);
    
    // Only admins can access analytics
    if (userRole !== "ADMIN") {
      return this.sendError(res, 403, "Only admins can access API analytics");
    }
    
    // Parse query parameters
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
    const path = req.query.path as string;
    const method = req.query.method as string;
    const statusCode: number = req.query.statusCode ? parseInt(req.query.statusCode as string) : undefined;
    const userId = req.query.id // Fixed: using id instead of userId as string;
    const userRole = req.query.userRole as string;
    const apiVersion = req.query.apiVersion as string;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 100;
    const offset = req.query.offset ? parseInt(req.query.offset as string) : 0;
    
    // Get analytics
    const analytics = await this.apiAnalyticsService.getAnalytics({
      startDate,
      endDate,
      path,
      method,
      statusCode,
      userId,
      userRole,
      apiVersion,
      limit,
      offset
    });
    
    // Send paginated response
    return this.sendPaginatedSuccess(res, (analytics).data, (analytics).total, limit, offset);
  });
  
  /**
   * Get API analytics summary
   */
  getAnalyticsSummary = this.createHandler(async (req: Request, res: Response)  =>  {
    // Check authorization
    const { userRole } = this.checkAuthorization(req);
    
    // Only admins can access analytics
    if (userRole !== "ADMIN") {
      return this.sendError(res, 403, "Only admins can access API analytics");
    }
    
    // Parse query parameters
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
    const path = req.query.path as string;
    const method = req.query.method as string;
    const statusCode: number = req.query.statusCode ? parseInt(req.query.statusCode as string) : undefined;
    const userId = req.query.id // Fixed: using id instead of userId as string;
    const userRole = req.query.userRole as string;
    const apiVersion = req.query.apiVersion as string;
    
    // Get analytics summary
    const summary = await this.apiAnalyticsService.getAnalyticsSummary({
      startDate,
      endDate,
      path,
      method,
      statusCode,
      userId,
      userRole,
      apiVersion
    });
    
    // Send success response
    return this.sendSuccess(res, summary);
  });
  
  /**
   * Get API analytics by version
   */
  getAnalyticsByVersion = this.createHandler(async (req: Request, res: Response)  =>  {
    // Check authorization
    const { userRole } = this.checkAuthorization(req);
    
    // Only admins can access analytics
    if (userRole !== "ADMIN") {
      return this.sendError(res, 403, "Only admins can access API analytics");
    }
    
    // Get version from params
    const { version } = req.params;
    
    // Parse query parameters
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 100;
    const offset = req.query.offset ? parseInt(req.query.offset as string) : 0;
    
    // Get analytics
    const analytics = await this.apiAnalyticsService.getAnalytics({
      startDate,
      endDate,
      apiVersion: version,
      limit,
      offset
    });
    
    // Send paginated response
    return this.sendPaginatedSuccess(res, (analytics).data, (analytics).total, limit, offset);
  });
  
  /**
   * Get API analytics summary by version
   */
  getAnalyticsSummaryByVersion = this.createHandler(async (req: Request, res: Response)  =>  {
    // Check authorization
    const { userRole } = this.checkAuthorization(req);
    
    // Only admins can access analytics
    if (userRole !== "ADMIN") {
      return this.sendError(res, 403, "Only admins can access API analytics");
    }
    
    // Get version from params
    const { version } = req.params;
    
    // Parse query parameters
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
    
    // Get analytics summary
    const summary = await this.apiAnalyticsService.getAnalyticsSummary({
      startDate,
      endDate,
      apiVersion: version
    });
    
    // Send success response
    return this.sendSuccess(res, summary);
  });
}

export default ApiAnalyticsController;
