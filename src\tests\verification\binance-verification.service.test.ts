import { PrismaClient } from '@prisma/client';
// jscpd:ignore-file
/**
 * binance-verification.service Tests
 *
 * This file contains tests for the binance-verification.service module using the test utility.
 */

import { binance-verification.serviceController } from '../controllers/binance-verification.service.controller';
import { binance-verification.serviceService } from '../services/binance-verification.service.service';
import { binance-verification.serviceRepository } from '../repositories/binance-verification.service.repository';
import { testControllerSuite, createMockRequest, createMockResponse, testService, testRepository, createMockPrismaClient } from './tests/utils/test-utilities';
import { Repository } from '../types/database';
import { binance-verification.serviceService } from '../services/binance-verification.service.service';
import { binance-verification.serviceRepository } from '../repositories/binance-verification.service.repository';
import { testControllerSuite, createMockRequest, createMockResponse, testService, testRepository, createMockPrismaClient } from './tests/utils/test-utilities';
import { Repository } from '../types/database';


// Mock the binance-verification.serviceService
jest.mock('../services/binance-verification.service.service');

describe('binance-verification.service Module Tests', () => {
  // Controller tests
  testControllerSuite('binance-verification.serviceController', binance-verification.serviceController, {
    getAll: { description: 'should get all binance-verification.services',
      expectedStatus: 200,
      expectedResponse: { success: true },
    },
    getById: { description: 'should get binance-verification.service by ID',
      req: createMockRequest({ params: { id: '1' } }),
      expectedStatus: 200,
      expectedResponse: { success: true },
    },
    create: { description: 'should create binance-verification.service',
      req: createMockRequest({ body: { name: 'Test' } }),
      expectedStatus: 201,
      expectedResponse: { success: true },
    },
    update: { description: 'should update binance-verification.service',
      req: createMockRequest({ params: { id: '1' }, body: { name: 'Updated' } }),
      expectedStatus: 200,
      expectedResponse: { success: true },
    },
    delete: { description: 'should delete binance-verification.service',
      req: createMockRequest({ params: { id: '1' } }),
      expectedStatus: 200,
      expectedResponse: { success: true, message: 'binance-verification.service deleted successfully' },
    },
  });

  // Service tests
  describe('binance-verification.serviceService', () => {
    let service;
    let mockRepository;

    beforeEach(() => {
      mockRepository = {
        findAll: jest.fn(),
        findById: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      };

      service = new binance-verification.serviceService();
      service.binance-verification.serviceRepository = mockRepository;
    });

    it('should find all binance-verification.services', async () => {
      mockRepository.findAll.mockResolvedValue([{ id: '1', name: 'Test' }]);

      const result: unknown = await testService(service, 'findAll');

      expect(result).toEqual([{ id: '1', name: 'Test' }]);
      expect(mockRepository.findAll).toHaveBeenCalled();
    });
  });

  // Repository tests
  describe('binance-verification.serviceRepository', () => {
    let repository;
    let mockPrisma;

    beforeEach(() => {
      mockPrisma = createMockPrismaClient();
      repository = new binance-verification.serviceRepository();
      repository.prisma = mockPrisma;
    });

    it('should find all binance-verification.services', async () => {
      mockPrisma.binance-verification.service.findMany.mockResolvedValue([{ id: '1', name: 'Test' }]);

      const result: unknown = await testRepository(repository, 'findAll');

      expect(result).toEqual([{ id: '1', name: 'Test' }]);
      expect(mockPrisma.binance-verification.service.findMany).toHaveBeenCalled();
    });
  });
});
