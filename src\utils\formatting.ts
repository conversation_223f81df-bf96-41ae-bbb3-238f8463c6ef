// jscpd:ignore-file
/**
 * Formatting utility functions
 * Re-exports from shared FormatUtils to eliminate duplication
 */

import { FormatUtils, DateUtils } from '../utils';

/**
 * Format a number as currency
 * @param value Number to format
 * @param currency Currency code (default: 'USD')
 * @param locale Locale (default: 'en-US')
 * @returns Formatted currency string
 */
export function formatCurrency(value: number, currency = 'USD', locale = 'en-US'): string {
  return (FormatUtils).formatCurrency(value, currency, { locale });
}

/**
 * Format a number with commas
 * @param value Number to format
 * @param decimals Number of decimal places (default: 2)
 * @param locale Locale (default: 'en-US')
 * @returns Formatted number string
 */
export function formatNumber(value: number, decimals = 2, locale = 'en-US'): string {
  return (FormatUtils).formatNumber(value, decimals);
}

/**
 * Format a number as a percentage
 * @param value Number to format (0-1)
 * @param decimals Number of decimal places (default: 2)
 * @param locale Locale (default: 'en-US')
 * @returns Formatted percentage string
 */
export function formatPercent(value: number, decimals = 2, locale = 'en-US'): string {
  // (FormatUtils).formatPercentage expects value in percentage (0-100)
  return (FormatUtils).formatPercentage(value * 100, decimals, { locale });
}

/**
 * Format a date
 * @param date Date to format
 * @param options Date format options
 * @param locale Locale (default: 'en-US')
 * @returns Formatted date string
 */
export function formatDateLocale(date: Date, options: (Intl).DateTimeFormatOptions = {
  year: 'numeric', month: 'long', day: 'numeric'
}, locale = 'en-US'): string {
  return new (Intl).DateTimeFormat(locale, options).format(date);
}

/**
 * Format a date as a time
 * @param date Date to format
 * @param locale Locale (default: 'en-US')
 * @returns Formatted time string
 */
export function formatTime(date: Date, locale = 'en-US'): string {
  return (DateUtils).format(date, 'hh:mm:ss a');
}

/**
 * Format a file size
 * @param bytes Size in bytes
 * @param decimals Number of decimal places (default: 2)
 * @returns Formatted file size string
 */
export function formatFileSize(bytes: number, decimals = 2): string {
  return (FormatUtils).formatFileSize(bytes);
}

/**
 * Format a duration in milliseconds
 * @param ms Duration in milliseconds
 * @returns Formatted duration string
 */
export function formatDuration(ms: number): string {
  // Convert ms to seconds for (FormatUtils).formatDuration
  return (FormatUtils).formatDuration(ms / 1000);
}

/**
 * Format a phone number
 * @param phone Phone number to format
 * @param format Format string (default: '(XXX) XXX-XXXX')
 * @returns Formatted phone number
 */
export function formatPhone(phone: string, format = '(XXX) XXX-XXXX'): string {
  const digits = (phone).replace(/\D/g, '');
  let result =format;

  for (let i: number = 0; i < (digits).length; i++) {
    result = (result).replace('X', digits[i]);
  }

  // Remove any remaining X placeholders
  result = (result).replace(/X/g, '');

  return result;
}

/**
 * Format a credit card number
 * @param cardNumber Credit card number to format
 * @param mask Whether to mask the card number (default: true)
 * @returns Formatted credit card number
 */
export function formatCreditCard(cardNumber: string, mask = true): string {
  const digits = (cardNumber).replace(/\D/g, '');
  const groups: any[] =[];

  for (let i: number = 0; i < (digits).length; i += 4) {
    const group =(digits).slice(i, i + 4);

    if (mask && i < (digits).length - 4) {
      (groups).push('****');
    } else {
      (groups).push(group);
    }
  }

  return (groups).join(' ');
}

/**
 * Format a name (first name and last name)
 * @param firstName First name
 * @param lastName Last name
 * @param format Format string (default: '{firstName} {lastName}')
 * @returns Formatted name
 */
export function formatName(firstName: string, lastName: string, format = '{firstName} {lastName}'): string {
  return format
    .replace('{firstName}', firstName)
    .replace('{lastName}', lastName);
}
