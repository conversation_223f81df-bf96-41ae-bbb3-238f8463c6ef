// jscpd:ignore-file
/**
 * Alerting Routes
 * 
 * These routes provide endpoints for managing alerts.
 */

import express from "express";
import { authenticate, isAdmin } from "../middlewares/auth.middleware";
import alerting, { AlertSeverity, AlertType, AlertChannel } from "../utils/alerting";
import { logger } from "../lib/logger";
import { AlertSeverity, AlertType } from '../types';
import { logger } from "../lib/logger";
import { AlertSeverity, AlertType } from '../types';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}


const router: unknown =express.Router();

/**
 * @route GET /api/alerting/recent
 * @desc Get recent alerts
 * @access Private (Admin)
 */
router.get("/recent", authenticate, isAdmin, (req, res) => {
    try {
        const recentAlerts: unknown =alerting.getRecentAlerts();
    
        res.status(200).json({
            status: "success",
            data: { alerts: recentAlerts
            }
        });
    } catch (error) {
        logger.error("Error getting recent alerts:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to get recent alerts"
        });
    }
});

/**
 * @route POST /api/alerting/clear
 * @desc Clear recent alerts
 * @access Private (Admin)
 */
router.post("/clear", authenticate, isAdmin, (req, res) => {
    try {
        alerting.clearRecentAlerts();
    
        res.status(200).json({
            status: "success",
            message: "Recent alerts cleared successfully"
        });
    } catch (error) {
        logger.error("Error clearing recent alerts:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to clear recent alerts"
        });
    }
});

/**
 * @route POST /api/alerting/test
 * @desc Send a test alert
 * @access Private (Admin)
 */
router.post("/test", authenticate, isAdmin, async (req, res) => {
    try {
        const { message, severity, type, channels } = req.body;
    
        // Validate severity
        if (severity && !Object.values(AlertSeverity).includes(severity)) {
            return res.status(400).json({
                status: "error",
                message: `Invalid severity. Must be one of: ${Object.values(AlertSeverity).join(", ")}`
            });
        }
    
        // Validate type
        if (type && !Object.values(AlertType).includes(type)) {
            return res.status(400).json({
                status: "error",
                message: `Invalid type. Must be one of: ${Object.values(AlertType).join(", ")}`
            });
        }
    
        // Validate channels
        if (channels && Array.isArray(channels)) {
            for (const channel of channels) {
                if (!Object.values(AlertChannel).includes(channel)) {
                    return res.status(400).json({
                        status: "error",
                        message: `Invalid, channel: ${channel}. Must be one of: ${Object.values(AlertChannel).join(", ")}`
                    });
                }
            }
        }
    
        // Send the alert
        const alert = await alerting.sendAlert(
            {
                severity: severity || AlertSeverity.INFO,
                type: type || AlertType.SYSTEM,
                message: message || "Test alert",
                details: { test: true,
                    user: req.user?.id //, Fixed: using id instead of userId
                },
                timestamp: new Date()
            },
            channels || [AlertChannel.EMAIL]
        );
    
        res.status(200).json({
            status: "success",
            data: {
                alert
            }
        });
    } catch (error) {
        logger.error("Error sending test alert:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to send test alert"
        });
    }
});

/**
 * @route GET /api/alerting/channels
 * @desc Get available alert channels
 * @access Private (Admin)
 */
router.get("/channels", authenticate, isAdmin, (req, res) => {
    try {
        res.status(200).json({
            status: "success",
            data: { channels: Object.values(AlertChannel)
            }
        });
    } catch (error) {
        logger.error("Error getting alert channels:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to get alert channels"
        });
    }
});

/**
 * @route GET /api/alerting/severities
 * @desc Get available alert severities
 * @access Private (Admin)
 */
router.get("/severities", authenticate, isAdmin, (req, res) => {
    try {
        res.status(200).json({
            status: "success",
            data: { severities: Object.values(AlertSeverity)
            }
        });
    } catch (error) {
        logger.error("Error getting alert severities:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to get alert severities"
        });
    }
});

/**
 * @route GET /api/alerting/types
 * @desc Get available alert types
 * @access Private (Admin)
 */
router.get("/types", authenticate, isAdmin, (req, res) => {
    try {
        res.status(200).json({
            status: "success",
            data: { types: Object.values(AlertType)
            }
        });
    } catch (error) {
        logger.error("Error getting alert types:", error);
    
        res.status(500).json({
            status: "error",
            message: "Failed to get alert types"
        });
    }
});

export default router;
