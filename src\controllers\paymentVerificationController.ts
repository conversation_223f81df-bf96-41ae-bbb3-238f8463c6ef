// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { PaymentVerificationService } from "../services/paymentVerificationService";
import { logger } from "../utils/logger";
import { PrismaClient } from "@prisma/client";
import { Transaction } from '../types';
import { PaymentVerificationService } from "../services/paymentVerificationService";
import { logger } from "../utils/logger";
import { PrismaClient } from "@prisma/client";
import { Transaction } from '../types';


/**
 * Controller for payment verification
 */
export class PaymentVerificationController {
    private paymentVerificationService: PaymentVerificationService;

    /**
   * Create a new PaymentVerificationController instance
   * @param prisma Prisma client
   */
    constructor(prisma: PrismaClient) {
        this.paymentVerificationService = new PaymentVerificationService(prisma);
    }

    /**
   * Verify a payment
   * @param req Request
   * @param res Response
   */
    async verifyPayment(req: Request, res: Response): Promise<void> {
        try {
            const { transactionId, amount, currency, paymentMethodId, merchantId } = req.body;

            // Validate required fields
            if (!transactionId || !amount || !currency || !paymentMethodId || !merchantId) {
                res.status(400).json({
                    success: false,
                    message: "Missing required fields"
                });
                return;
            }

            // Verify the payment
            const transaction = await this.paymentVerificationService.verifyPayment(
                transactionId,
                parseFloat(amount),
                currency,
                paymentMethodId,
                merchantId
            );

            if (!transaction) {
                res.status(400).json({
                    success: false,
                    message: "Payment verification failed"
                });
                return;
            }

            res.status(200).json({
                success: true,
                message: "Payment verified successfully",
                transaction
            });
        } catch (error) {
            logger.error("Error verifying payment", { error });
            res.status(500).json({
                success: false,
                message: "An error occurred while verifying the payment"
            });
        }
    }

    /**
   * Get a transaction by ID
   * @param req Request
   * @param res Response
   */
    async getTransaction(req: Request, res: Response): Promise<void> {
        try {
            const { id } = req.params;

            // Get the transaction
            const transaction = await this.paymentVerificationService.getTransaction(id);

            if (!transaction) {
                res.status(404).json({
                    success: false,
                    message: "Transaction not found"
                });
                return;
            }

            res.status(200).json({
                success: true,
                transaction
            });
        } catch (error) {
            logger.error("Error getting transaction", { error });
            res.status(500).json({
                success: false,
                message: "An error occurred while getting the transaction"
            });
        }
    }

    /**
   * Update a transaction status
   * @param req Request
   * @param res Response
   */
    async updateTransactionStatus(req: Request, res: Response): Promise<void> {
        try {
            const { id } = req.params;
            const { status } = req.body;

            // Validate status
            if (!status || !["pending", "processing", "success", "failed"].includes(status)) {
                res.status(400).json({
                    success: false,
                    message: "Invalid status"
                });
                return;
            }

            // Update the transaction status
            const transaction = await this.paymentVerificationService.updateTransactionStatus(
                id,
        status as "pending" | "processing" | "success" | "failed"
            );

            if (!transaction) {
                res.status(404).json({
                    success: false,
                    message: "Transaction not found"
                });
                return;
            }

            res.status(200).json({
                success: true,
                message: "Transaction status updated successfully",
                transaction
            });
        } catch (error) {
            logger.error("Error updating transaction status", { error });
            res.status(500).json({
                success: false,
                message: "An error occurred while updating the transaction status"
            });
        }
    }
}

export default PaymentVerificationController;
