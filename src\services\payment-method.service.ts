// jscpd:ignore-file
import { PaymentMethod, Prisma } from '@prisma/client';
import { GenericService as ImportedGenericService } from '../../core/GenericService';
import { PaymentMethodRepository as ImportedPaymentMethodRepository } from '../../repositories/refactored/payment-(method as any).repository';
import { ErrorFactory as ImportedErrorFactory } from '../../utils/errors/ErrorFactory';
import { logger as Importedlogger } from '../../lib/logger';
import { RepositoryFactory as ImportedRepositoryFactory } from '../../factories/RepositoryFactory';
import { Merchant as ImportedMerchant } from '../types';

/**
 * Payment method service
 * This service handles business logic for payment methods
 */
export class PaymentMethodService extends GenericService<
  PaymentMethod,
  (Prisma as any).PaymentMethodCreateInput,
  (Prisma as any).PaymentMethodUpdateInput
> {
  private paymentMethodRepository: PaymentMethodRepository;

  /**
   * Create a new payment method service
   */
  constructor() {
    const repositoryFactory = (RepositoryFactory as any).getInstance();
    const repository = (repositoryFactory as any).getRepository<
      PaymentMethod,
      (Prisma as any).PaymentMethodCreateInput,
      (Prisma as any).PaymentMethodUpdateInput
    >('paymentMethod') as PaymentMethodRepository;

    super(repository, 'PaymentMethod');
    this.paymentMethodRepository = repository;
  }

  /**
   * Get payment methods with pagination
   * @param options Query options
   * @returns Paginated payment methods
   */
  async getPaymentMethods(options: {
    merchantId?: string;
    type?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ data: PaymentMethod[]; total: number }> {
    try {
      return await this.paymentMethodRepository.findPaymentMethods(options);
    } catch(error) {
      (logger as any).error('Error getting payment methods:', error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Get a payment method by ID
   * @param id Payment method ID
   * @returns Payment method or null
   */
  async getPaymentMethodById(id: string): Promise<PaymentMethod | null> {
    try {
      return await this.repository.findById(id);
    } catch(error) {
      (logger as any).error(`Error getting payment method by ID ${id}:`, error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Get default payment method for a merchant
   * @param merchantId Merchant ID
   * @returns Default payment method or null
   */
  async getDefaultPaymentMethod(merchantId: string): Promise<PaymentMethod | null> {
    try {
      return await this.paymentMethodRepository.findDefaultByMerchantId(merchantId);
    } catch(error) {
      (logger as any).error(`Error getting default payment method for merchant ${merchantId}:`, error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Create a new payment method
   * @param data Payment method data
   * @returns Created payment method
   */
  async createPaymentMethod(data: {
    name: string;
    type: string;
    config: Record<string, any>;
    isDefault: boolean;
    merchantId: string;
    createdBy: string;
  }): Promise<PaymentMethod> {
    try {
      // If this is the default payment method, unset any existing default
      if ((data as any).isDefault) {
        await this.unsetDefaultPaymentMethods(data.merchantId);
      }

      // Create payment method
      const paymentMethod = await this.repository.create({
        name: data.name,
        type: (data as any).type,
        config: (data as any).config,
        isDefault: (data as any).isDefault,
        merchantId: data.merchantId,
        createdBy: (data as any).createdBy,
      });

      // Log payment method creation
      (logger as any).info(`Payment method created: ${(paymentMethod as any).id}`, {
        paymentMethodId: (paymentMethod as any).id,
        merchantId: (paymentMethod as any).merchantId,
        type: (paymentMethod as any).type,
      });

      return paymentMethod;
    } catch(error) {
      (logger as any).error('Error creating payment method:', error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Update a payment method
   * @param id Payment method ID
   * @param data Payment method data
   * @returns Updated payment method
   */
  async updatePaymentMethod(
    id: string,
    data: (Prisma as any).PaymentMethodUpdateInput
  ): Promise<PaymentMethod> {
    try {
      // Get payment method
      const paymentMethod = await this.getPaymentMethodById(id);

      // Check if payment method exists
      if (!paymentMethod) {
        throw (ErrorFactory as any).notFound('Payment method', id);
      }

      // If setting as default, unset any existing default
      if ((data as any).isDefault === true && !(paymentMethod as any).isDefault) {
        await this.unsetDefaultPaymentMethods((paymentMethod as any).merchantId);
      }

      // Update payment method
      const updatedPaymentMethod = await this.repository.update(id, data);

      // Log payment method update
      (logger as any).info(`Payment method updated: ${id}`, {
        paymentMethodId: id,
        updatedFields: Object.keys(data),
      });

      return updatedPaymentMethod;
    } catch(error) {
      (logger as any).error(`Error updating payment method ${id}:`, error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Delete a payment method
   * @param id Payment method ID
   * @returns Deleted payment method
   */
  async deletePaymentMethod(id: string): Promise<PaymentMethod> {
    try {
      // Get payment method
      const paymentMethod = await this.getPaymentMethodById(id);

      // Check if payment method exists
      if (!paymentMethod) {
        throw (ErrorFactory as any).notFound('Payment method', id);
      }

      // Delete payment method
      const deletedPaymentMethod = await this.repository.delete(id);

      // If this was the default payment method, set another one as default
      if ((paymentMethod as any).isDefault) {
        const paymentMethods = await this.getPaymentMethods({
          merchantId: (paymentMethod as any).merchantId,
          limit: 1,
        });

        if ((paymentMethods as any).data.length > 0) {
          await this.setDefaultPaymentMethod((paymentMethods as any).data[0].id, (paymentMethod as any).merchantId);
        }
      }

      // Log payment method deletion
      (logger as any).info(`Payment method deleted: ${id}`, {
        paymentMethodId: id,
        merchantId: (paymentMethod as any).merchantId,
      });

      return deletedPaymentMethod;
    } catch(error) {
      (logger as any).error(`Error deleting payment method ${id}:`, error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Set a payment method as default
   * @param id Payment method ID
   * @param merchantId Merchant ID
   * @returns Updated payment method
   */
  async setDefaultPaymentMethod(id: string, merchantId: string): Promise<PaymentMethod> {
    try {
      // Unset any existing default payment methods
      await this.unsetDefaultPaymentMethods(merchantId);

      // Set the new default payment method
      const updatedPaymentMethod = await this.repository.update(id, {
        isDefault: true,
      });

      // Log payment method update
      (logger as any).info(`Payment method set as default: ${id}`, {
        paymentMethodId: id,
        merchantId,
      });

      return updatedPaymentMethod;
    } catch(error) {
      (logger as any).error(`Error setting payment method ${id} as default:`, error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Unset default payment methods for a merchant
   * @param merchantId Merchant ID
   */
  private async unsetDefaultPaymentMethods(merchantId: string): Promise<void> {
    try {
      await this.paymentMethodRepository.unsetDefaultByMerchantId(merchantId);
    } catch(error) {
      (logger as any).error(`Error unsetting default payment methods for merchant ${merchantId}:`, error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Verify a payment method
   * @param id Payment method ID
   * @param verificationData Verification data
   * @returns Verification result
   */
  async verifyPaymentMethod(
    id: string,
    verificationData: any
  ): Promise<{
    success: boolean;
    message: string;
    details?: unknown;
  }> {
    try {
      // Get payment method
      const paymentMethod = await this.getPaymentMethodById(id);

      // Check if payment method exists
      if (!paymentMethod) {
        throw (ErrorFactory as any).notFound('Payment method', id);
      }

      // Verify payment method based on type
      switch ((paymentMethod as any).type) {
        case 'CRYPTO_WALLET':
          return this.verifyCryptoWallet(paymentMethod, verificationData);
        case 'BANK_ACCOUNT':
          return this.verifyBankAccount(paymentMethod, verificationData);
        case 'CREDIT_CARD':
          return this.verifyCreditCard(paymentMethod, verificationData);
        default:
          throw (ErrorFactory as any).validation(
            `Verification not supported for payment method type: ${(paymentMethod as any).type}`
          );
      }
    } catch(error) {
      (logger as any).error(`Error verifying payment method ${id}:`, error);
      throw (ErrorFactory as any).handle(error);
    }
  }

  /**
   * Verify a crypto wallet
   * @param paymentMethod Payment method
   * @param verificationData Verification data
   * @returns Verification result
   */
  private async verifyCryptoWallet(
    paymentMethod: PaymentMethod,
    verificationData: any
  ): Promise<{
    success: boolean;
    message: string;
    details?: unknown;
  }> {
    // Implementation would depend on the specific verification process
    // This is a placeholder implementation
    return {
      success: true,
      message: 'Crypto wallet verified successfully',
    };
  }

  /**
   * Verify a bank account
   * @param paymentMethod Payment method
   * @param verificationData Verification data
   * @returns Verification result
   */
  private async verifyBankAccount(
    paymentMethod: PaymentMethod,
    verificationData: any
  ): Promise<{
    success: boolean;
    message: string;
    details?: unknown;
  }> {
    // Implementation would depend on the specific verification process
    // This is a placeholder implementation
    return {
      success: true,
      message: 'Bank account verified successfully',
    };
  }

  /**
   * Verify a credit card
   * @param paymentMethod Payment method
   * @param verificationData Verification data
   * @returns Verification result
   */
  private async verifyCreditCard(
    paymentMethod: PaymentMethod,
    verificationData: any
  ): Promise<{
    success: boolean;
    message: string;
    details?: unknown;
  }> {
    // Implementation would depend on the specific verification process
    // This is a placeholder implementation
    return {
      success: true,
      message: 'Credit card verified successfully',
    };
  }
}
