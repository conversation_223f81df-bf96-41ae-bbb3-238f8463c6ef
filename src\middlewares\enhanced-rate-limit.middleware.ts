// jscpd:ignore-file
/**
 * Enhanced Rate Limiting Middleware
 *
 * This middleware provides improved rate limiting with:
 * - Tiered rate limiting based on endpoint sensitivity
 * - IP-based and user-based rate limiting
 * - Proper rate limit headers
 * - Environment-specific rate limits
 */

import { Request, Response, NextFunction } from "express";
import rateLimit from "express-rate-limit";
import RedisStore from "rate-limit-redis";
import { logger } from "../lib/logger";
import { isProduction, isDevelopment } from "../utils/environment-validator";
import { AppError } from "../utils/app-error";
import redisManager from "../lib/redis-manager";
import { Middleware } from '../types/express';
import { logger } from "../lib/logger";
import { isProduction, isDevelopment } from "../utils/environment-validator";
import { AppError } from "../utils/app-error";
import { Middleware } from '../types/express';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}


// Create rate limit store based on environment
const createLimitStore = () => {
    const redisClient = redisManager.getClient();

    if (redisClient && redisManager.isRedisEnabled() && (isProduction() || process.env.USE_REDIS_RATE_LIMIT === "true")) {
        logger.info("Using Redis store for rate limiting");
        return new RedisStore({
            sendCommand: (...args: string[]) => redisClient.sendCommand(args),
            prefix: "rl:"
        });
    }

    logger.info("Using memory store for rate limiting");
    return undefined; // Use default memory store
};

// Base rate limiter configuration
const baseLimiter = (options) => {
    const defaultOptions = {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: isProduction() ? 100 : 1000, // Limit each IP to 100 requests per windowMs in production, 1000 in development
        standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
        legacyHeaders: false, // Disable the `X-RateLimit-*` headers
        store: createLimitStore(),
        message: { status: "error",
            code: "RATE_LIMIT_EXCEEDED",
            message: "Too many requests, please try again later."
        },
        handler: (req: Request, res: Response, next: NextFunction, options) => {
            const error: Error =new AppError(
                options.message.message,
                429,
                true,
                "RATE_LIMIT_EXCEEDED",
                { retryAfter: Math.ceil(options.windowMs / 1000) }
            );

            logger.warn(`Rate limit exceeded: ${req.ip} - ${req.method} ${req.originalUrl}`);

            res.status(429).json({
                status: "error",
                code: "RATE_LIMIT_EXCEEDED",
                message: options.message.message,
                retryAfter: Math.ceil(options.windowMs / 1000)
            });
        },
        skip: (req: Request) => {
            // Skip rate limiting for health check endpoints
            return req.path === "/health" || req.path === "/api/health";
        },
        keyGenerator: (req: Request) => {
            // Use user ID if available, otherwise use IP
            return (req.user as any)?.id || req.ip;
        }
    };

    return rateLimit({
        ...defaultOptions,
        ...options
    });
};

// General API rate limiter
export const apiLimiter: unknown =baseLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: isProduction() ? 300 : 3000 // 300 requests per 15 minutes in production
});

// Authentication rate limiter (more strict)
export const authLimiter: unknown =baseLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: isProduction() ? 20 : 100, // 20 requests per 15 minutes in production
    message: { status: "error",
        code: "AUTH_RATE_LIMIT_EXCEEDED",
        message: "Too many authentication attempts, please try again later."
    }
});

// Payment rate limiter (more strict)
export const paymentLimiter: unknown =baseLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: isProduction() ? 60 : 300, // 60 requests per hour in production
    message: { status: "error",
        code: "PAYMENT_RATE_LIMIT_EXCEEDED",
        message: "Too many payment requests, please try again later."
    }
});

// Verification rate limiter
export const verificationLimiter: unknown =baseLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: isProduction() ? 100 : 500, // 100 requests per hour in production
    message: { status: "error",
        code: "VERIFICATION_RATE_LIMIT_EXCEEDED",
        message: "Too many verification requests, please try again later."
    }
});

// Admin rate limiter (less strict)
export const adminLimiter: unknown =baseLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: isProduction() ? 500 : 2000 // 500 requests per 15 minutes in production
});

export default {
    apiLimiter,
    authLimiter,
    paymentLimiter,
    verificationLimiter,
    adminLimiter
};
