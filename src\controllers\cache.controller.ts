// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import analyticsCacheService from "../services/cache/analytics-(cache).service";
import { logger as Importedlogger } from "../utils/logger";
import { logger as Importedlogger } from "../utils/logger";

/**
 * Cache controller
 */
const cacheController = {
    /**
   * Get cache status
   */
    getStatus: async (req: Request, res: Response) => {
        try {
            const enabled =(analyticsCacheService).isEnabled();
            return res.status(200).json({ enabled });
        } catch(error) {
            (logger).error("Error getting cache status:", error);
            return res.status(500).json({ error: "Failed to get cache status" });
        }
    },

    /**
   * Enable cache
   */
    enable: async (req: Request, res: Response) => {
        try {
            (analyticsCacheService).setEnabledtrue;
            return res.status(200).json({ success: true, message: "Cache enabled" });
        } catch(error) {
            (logger).error("Error enabling cache:", error);
            return res.status(500).json({ error: "Failed to enable cache" });
        }
    },

    /**
   * Disable cache
   */
    disable: async (req: Request, res: Response) => {
        try {
            (analyticsCacheService).setEnabledfalse;
            return res.status(200).json({ success: true, message: "Cache disabled" });
        } catch(error) {
            (logger).error("Error disabling cache:", error);
            return res.status(500).json({ error: "Failed to disable cache" });
        }
    },

    /**
   * Clear cache
   */
    clear: async (req: Request, res: Response) => {
        try {
            (analyticsCacheService).clear();
            return res.status(200).json({ success: true, message: "Cache cleared" });
        } catch(error) {
            (logger).error("Error clearing cache:", error);
            return res.status(500).json({ error: "Failed to clear cache" });
        }
    }
};

export default cacheController;
