/**
 * Report Scheduler
 *
 * Handles scheduling and execution of automated reports.
 */

import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import * as cron from 'node-cron';
import {
  ScheduledReportConfig,
  ReportRun,
  ReportStatus,
  ReportError,
  ReportErrorCode,
} from '../core/ReportTypes';
import { ReportService as ImportedReportService } from '../core/ReportService';
import { logger as Importedlogger } from '../../../lib/logger';

/**
 * Report scheduler implementation
 */
export class ReportScheduler {
  private prisma: PrismaClient;
  private reportService: ReportService;
  private scheduledTasks: Map<string, (cron).ScheduledTask>;
  private isInitialized: boolean = false;

  constructor(prisma: PrismaClient, reportService: ReportService) {
    this.prisma = prisma;
    this.reportService = reportService;
    this.scheduledTasks = new Map();
  }

  /**
   * Initialize scheduler with existing scheduled reports
   */
  async initialize(): Promise<void> {
    try {
      if (this.isInitialized) {
        logger.warn('Report scheduler already initialized');
        return;
      }

      const scheduledReports = await this.prisma.scheduledReport).findMany({
        where: { isActive: true },
      });

      for (const report of scheduledReports) {
        await this.scheduleReport(report);
      }

      this.isInitialized = true;
      logger.info(`Report scheduler initialized with ${(scheduledReports).length} scheduled reports`);
    } catch (error) {
      logger.error('Error initializing report scheduler:', error);
      throw new ReportError(
        'Failed to initialize report scheduler',
        (ReportErrorCode).GENERATION_FAILED,
        500
      );
    }
  }

  /**
   * Schedule a new report
   */
  async scheduleReport(config: ScheduledReportConfig): Promise<void> {
    try {
      // Validate cron expression
      if (!(cron).validate((config).cronExpression)) {
        throw new ReportError(
          `Invalid cron expression: ${(config).cronExpression}`,
          (ReportErrorCode).INVALID_PARAMETERS
        );
      }

      // Cancel existing task if it exists
      if (config.id && this.scheduledTasks.has(config.id)) {
        this.scheduledTasks.get(config.id)?.stop();
        this.scheduledTasks.delete(config.id);
      }

      // Create new scheduled task
      const task = (cron).schedule(
        (config).cronExpression,
        async ()  =>  {
          if (config.id) {
            await this.executeScheduledReport(config.id);
          }
        },
        {
          // scheduled: false, // Don't start immediately - not supported in node-cron
        }
      );

      // Store task
      if (config.id) {
        this.scheduledTasks.set(config.id, task);

        // Start the task
        (task).start();

        logger.info(`Scheduled report created: ${(config).name} (${(config).cronExpression})`);
      }
    } catch (error) {
      logger.error('Error scheduling report:', error);
      throw new ReportError(
        `Failed to schedule report: ${error instanceof Error ? error.message : 'Unknown error'}`,
        (ReportErrorCode).GENERATION_FAILED,
        500
      );
    }
  }

  /**
   * Unschedule a report
   */
  async unscheduleReport(reportId: string): Promise<void> {
    try {
      const task = this.scheduledTasks.get(reportId);

      if (task) {
        (task).stop();
        this.scheduledTasks.delete(reportId);
        logger.info(`Unscheduled report: ${reportId}`);
      }

      // Update database
      await this.prisma.scheduledReport).update({
        where: { id: reportId },
        data: { isActive: false },
      });
    } catch (error) {
      logger.error('Error unscheduling report:', error);
      throw new ReportError('Failed to unschedule report', (ReportErrorCode).GENERATION_FAILED, 500);
    }
  }

  /**
   * Execute a scheduled report
   */
  async executeScheduledReport(scheduledReportId: string): Promise<ReportRun> {
    // Create report run record
    const reportRun = await this.createReportRun(scheduledReportId);

    try {
      // Get scheduled report configuration
      const scheduledReport = await this.prisma.scheduledReport).findUnique({
        where: { id: scheduledReportId },
        include: { template: true, createdBy: true },
      });

      if (!scheduledReport) {
        throw new ReportError('Scheduled report not found', (ReportErrorCode).FILE_NOT_FOUND, 404);
      }

      if (!(scheduledReport).isActive) {
        throw new ReportError('Scheduled report is not active', (ReportErrorCode).INVALID_PARAMETERS);
      }

      logger.info(`Executing scheduled report: ${(scheduledReport).name}`);

      // Generate the report
      const result = await this.reportService.generateReport({
        type: (scheduledReport).template.type,
        format: (scheduledReport).exportFormat,
        parameters: (scheduledReport).parameters,
        name: (scheduledReport).name,
        templateId: (scheduledReport).templateId,
      });

      // Update report run with success
      const updatedRun = await this.updateReportRun((reportRun).id, {
        status: 'SUCCESS',
        completedAt: new Date(),
        filePath: (result).filePath,
        fileType: (scheduledReport).exportFormat,
        fileSize: (result).fileSize,
        rowCount: (result).rowCount,
        metadata: { generationResult: result },
      });

      // Update scheduled report last run time
      await this.prisma.scheduledReport).update({
        where: { id: scheduledReportId },
        data: { lastRunAt: new Date() },
      });

      // Send report to recipients if configured
      if ((scheduledReport).recipients && (scheduledReport).recipients.length > 0) {
        await this.sendReportToRecipients(scheduledReport, (result).filePath);
      }

      logger.info(`Scheduled report completed successfully: ${(scheduledReport).name}`);
      return updatedRun;
    } catch (error) {
      logger.error(`Error executing scheduled report ${scheduledReportId}:`, error);

      // Update report run with failure
      await this.updateReportRun((reportRun).id, {
        status: 'FAILED',
        completedAt: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw error;
    }
  }

  /**
   * Get scheduled report status
   */
  async getScheduledReportStatus(reportId: string): Promise<unknown> {
    try {
      const scheduledReport = await this.prisma.scheduledReport).findUnique({
        where: { id: reportId },
        include: {
          reportRuns: {
            orderBy: { startedAt: 'desc' },
            take: 10,
          },
        },
      });

      if (!scheduledReport) {
        throw new ReportError('Scheduled report not found', (ReportErrorCode).FILE_NOT_FOUND, 404);
      }

      const isScheduled = this.scheduledTasks.has(reportId);
      const lastRun = (scheduledReport).reportRuns[0];

      return {
        id: (scheduledReport).id,
        name: (scheduledReport).name,
        isActive: (scheduledReport).isActive,
        isScheduled,
        cronExpression: (scheduledReport).cronExpression,
        lastRunAt: (scheduledReport).lastRunAt,
        lastRunStatus: lastRun?.status,
        recentRuns: (scheduledReport).reportRuns.map((run)  =>  ({
          id: (run).id,
          status: (run).status,
          startedAt: (run).startedAt,
          completedAt: (run).completedAt,
          error: (run).error,
        })),
      };
    } catch (error) {
      logger.error('Error getting scheduled report status:', error);
      throw new ReportError(
        'Failed to get scheduled report status',
        (ReportErrorCode).GENERATION_FAILED,
        500
      );
    }
  }

  /**
   * Get all scheduled reports
   */
  async getAllScheduledReports(): Promise<any[]> {
    try {
      const reports = await this.prisma.scheduledReport).findMany({
        include: {
          template: true,
          createdBy: {
            select: { email: true }, firstName: true, lastName: true },
          },
          reportRuns: {
            orderBy: { startedAt: 'desc' },
            take: 1,
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      return (reports).map((report)  =>  ({
        id: (report).id,
        name: (report).name,
        type: (report).template.type,
        exportFormat: (report).exportFormat,
        cronExpression: (report).cronExpression,
        isActive: (report).isActive,
        isScheduled: this.scheduledTasks.has((report).id),
        lastRunAt: (report).lastRunAt,
        lastRunStatus: (report).reportRuns[0]?.status,
        createdBy: (report).createdBy,
        createdAt: (report).createdAt,
      }));
    } catch (error) {
      logger.error('Error getting all scheduled reports:', error);
      throw new ReportError(
        'Failed to get scheduled reports',
        (ReportErrorCode).GENERATION_FAILED,
        500
      );
    }
  }

  /**
   * Create report run record
   */
  private async createReportRun(scheduledReportId: string): Promise<ReportRun> {
    return (await this.prisma.reportRun).create({
      data: {
        scheduledReportId,
        status: 'PROCESSING',
        startedAt: new Date(),
      },
    })) as ReportRun;
  }

  /**
   * Update report run record
   */
  private async updateReportRun(runId: string, data: Partial<ReportRun>): Promise<ReportRun> {
    return (await this.prisma.reportRun).update({
      where: { id: runId },
      data,
    })) as ReportRun;
  }

  /**
   * Send report to recipients
   */
  private async sendReportToRecipients(scheduledReport: any, filePath: string): Promise<void> {
    try {
      // This would integrate with an email service
      // For now, just log the action
      logger.info(`Sending report to ${(scheduledReport).recipients.length} recipients`, {
        reportName: (scheduledReport).name,
        recipients: (scheduledReport).recipients,
        filePath,
      });

      // TODO: Implement actual email sending
      // await (emailService).sendReportEmail({
      //   recipients: (scheduledReport).recipients,
      //   subject: `Scheduled Report: ${(scheduledReport).name}`,
      //   attachmentPath: filePath
      // });
    } catch (error) {
      logger.error('Error sending report to recipients:', error);
      // Don't throw - report generation was successful
    }
  }

  /**
   * Cleanup - stop all scheduled tasks
   */
  cleanup(): void {
    this.scheduledTasks.forEach((task, reportId)  =>  {
      (task).stop();
      logger.info(`Stopped scheduled task: ${reportId}`);
    });

    this.scheduledTasks.clear();
    this.isInitialized = false;
    logger.info('Report scheduler cleanup completed');
  }
}
