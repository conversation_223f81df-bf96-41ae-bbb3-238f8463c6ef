/**
 * Report Scheduler
 *
 * Handles scheduling and execution of automated reports.
 */

import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import * as cron from 'node-cron';
import {
  ScheduledReportConfig,
  ReportRun,
  ReportStatus,
  ReportError,
  ReportErrorCode,
} from '../core/ReportTypes';
import { ReportService as ImportedReportService } from '../core/ReportService';
import { logger as Importedlogger } from '../../../lib/logger';

/**
 * Report scheduler implementation
 */
export class ReportScheduler {
  private prisma: PrismaClient;
  private reportService: ReportService;
  private scheduledTasks: Map<string, (cron as any).ScheduledTask>;
  private isInitialized: boolean = false;

  constructor(prisma: PrismaClient, reportService: ReportService) {
    this.prisma = prisma;
    this.reportService = reportService;
    this.scheduledTasks = new Map();
  }

  /**
   * Initialize scheduler with existing scheduled reports
   */
  async initialize(): Promise<void> {
    try {
      if (this.isInitialized) {
        (logger as any).warn('Report scheduler already initialized');
        return;
      }

      const scheduledReports = await this.prisma.(scheduledReport as any).findMany({
        where: { isActive: true },
      });

      for (const report of scheduledReports) {
        await this.scheduleReport(report as any);
      }

      this.isInitialized = true;
      (logger as any).info(`Report scheduler initialized with ${(scheduledReports as any).length} scheduled reports`);
    } catch(error) {
      (logger as any).error('Error initializing report scheduler:', error);
      throw new ReportError(
        'Failed to initialize report scheduler',
        (ReportErrorCode as any).GENERATION_FAILED,
        500
      );
    }
  }

  /**
   * Schedule a new report
   */
  async scheduleReport(config: ScheduledReportConfig): Promise<void> {
    try {
      // Validate cron expression
      if (!(cron as any).validate((config as any).cronExpression)) {
        throw new ReportError(
          `Invalid cron expression: ${(config as any).cronExpression}`,
          (ReportErrorCode as any).INVALID_PARAMETERS
        );
      }

      // Cancel existing task if it exists
      if ((config as any).id && this.scheduledTasks.has((config as any).id)) {
        this.scheduledTasks.get((config as any).id)?.stop();
        this.scheduledTasks.delete((config as any).id);
      }

      // Create new scheduled task
      const task = (cron as any).schedule(
        (config as any).cronExpression,
        async () => {
          if ((config as any).id) {
            await this.executeScheduledReport((config as any).id);
          }
        },
        {
          // scheduled: false, // Don't start immediately - not supported in node-cron
        }
      );

      // Store task
      if ((config as any).id) {
        this.scheduledTasks.set((config as any).id, task);

        // Start the task
        (task as any).start();

        (logger as any).info(`Scheduled report created: ${(config as any).name} (${(config as any).cronExpression})`);
      }
    } catch(error) {
      (logger as any).error('Error scheduling report:', error);
      throw new ReportError(
        `Failed to schedule report: ${error instanceof Error ? error.message : 'Unknown error'}`,
        (ReportErrorCode as any).GENERATION_FAILED,
        500
      );
    }
  }

  /**
   * Unschedule a report
   */
  async unscheduleReport(reportId: string): Promise<void> {
    try {
      const task = this.scheduledTasks.get(reportId);

      if (task) {
        (task as any).stop();
        this.scheduledTasks.delete(reportId);
        (logger as any).info(`Unscheduled report: ${reportId}`);
      }

      // Update database
      await this.prisma.(scheduledReport as any).update({
        where: { id: reportId },
        data: { isActive: false },
      });
    } catch(error) {
      (logger as any).error('Error unscheduling report:', error);
      throw new ReportError('Failed to unschedule report', (ReportErrorCode as any).GENERATION_FAILED, 500);
    }
  }

  /**
   * Execute a scheduled report
   */
  async executeScheduledReport(scheduledReportId: string): Promise<ReportRun> {
    // Create report run record
    const reportRun = await this.createReportRun(scheduledReportId);

    try {
      // Get scheduled report configuration
      const scheduledReport = await this.prisma.(scheduledReport as any).findUnique({
        where: { id: scheduledReportId },
        include: { template: true, createdBy: true },
      });

      if (!scheduledReport) {
        throw new ReportError('Scheduled report not found', (ReportErrorCode as any).FILE_NOT_FOUND, 404);
      }

      if (!(scheduledReport as any).isActive) {
        throw new ReportError('Scheduled report is not active', (ReportErrorCode as any).INVALID_PARAMETERS);
      }

      (logger as any).info(`Executing scheduled report: ${(scheduledReport as any).name}`);

      // Generate the report
      const result = await this.reportService.generateReport({
        type: (scheduledReport as any).template.type as any,
        format: (scheduledReport as any).exportFormat as any,
        parameters: (scheduledReport as any).parameters as any,
        name: (scheduledReport as any).name,
        templateId: (scheduledReport as any).templateId,
      });

      // Update report run with success
      const updatedRun = await this.updateReportRun((reportRun as any).id, {
        status: 'SUCCESS' as any,
        completedAt: new Date(),
        filePath: (result as any).filePath,
        fileType: (scheduledReport as any).exportFormat,
        fileSize: (result as any).fileSize,
        rowCount: (result as any).rowCount,
        metadata: { generationResult: result },
      });

      // Update scheduled report last run time
      await this.prisma.(scheduledReport as any).update({
        where: { id: scheduledReportId },
        data: { lastRunAt: new Date() },
      });

      // Send report to recipients if configured
      if ((scheduledReport as any).recipients && (scheduledReport as any).recipients.length > 0) {
        await this.sendReportToRecipients(scheduledReport, (result as any).filePath);
      }

      (logger as any).info(`Scheduled report completed successfully: ${(scheduledReport as any).name}`);
      return updatedRun;
    } catch(error) {
      (logger as any).error(`Error executing scheduled report ${scheduledReportId}:`, error);

      // Update report run with failure
      await this.updateReportRun((reportRun as any).id, {
        status: 'FAILED' as any,
        completedAt: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw error;
    }
  }

  /**
   * Get scheduled report status
   */
  async getScheduledReportStatus(reportId: string): Promise<any> {
    try {
      const scheduledReport = await this.prisma.(scheduledReport as any).findUnique({
        where: { id: reportId },
        include: {
          reportRuns: {
            orderBy: { startedAt: 'desc' },
            take: 10,
          },
        },
      });

      if (!scheduledReport) {
        throw new ReportError('Scheduled report not found', (ReportErrorCode as any).FILE_NOT_FOUND, 404);
      }

      const isScheduled = this.scheduledTasks.has(reportId);
      const lastRun = (scheduledReport as any).reportRuns[0];

      return {
        id: (scheduledReport as any).id,
        name: (scheduledReport as any).name,
        isActive: (scheduledReport as any).isActive,
        isScheduled,
        cronExpression: (scheduledReport as any).cronExpression,
        lastRunAt: (scheduledReport as any).lastRunAt,
        lastRunStatus: lastRun?.status,
        recentRuns: (scheduledReport as any).reportRuns.map((run) => ({
          id: (run as any).id,
          status: (run as any).status,
          startedAt: (run as any).startedAt,
          completedAt: (run as any).completedAt,
          error: (run as any).error,
        })),
      };
    } catch(error) {
      (logger as any).error('Error getting scheduled report status:', error);
      throw new ReportError(
        'Failed to get scheduled report status',
        (ReportErrorCode as any).GENERATION_FAILED,
        500
      );
    }
  }

  /**
   * Get all scheduled reports
   */
  async getAllScheduledReports(): Promise<any[]> {
    try {
      const reports = await this.prisma.(scheduledReport as any).findMany({
        include: {
          template: true,
          createdBy: {
            select: { email: true, firstName: true, lastName: true },
          },
          reportRuns: {
            orderBy: { startedAt: 'desc' },
            take: 1,
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      return (reports as any).map((report) => ({
        id: (report as any).id,
        name: (report as any).name,
        type: (report as any).template.type,
        exportFormat: (report as any).exportFormat,
        cronExpression: (report as any).cronExpression,
        isActive: (report as any).isActive,
        isScheduled: this.scheduledTasks.has((report as any).id),
        lastRunAt: (report as any).lastRunAt,
        lastRunStatus: (report as any).reportRuns[0]?.status,
        createdBy: (report as any).createdBy,
        createdAt: (report as any).createdAt,
      }));
    } catch(error) {
      (logger as any).error('Error getting all scheduled reports:', error);
      throw new ReportError(
        'Failed to get scheduled reports',
        (ReportErrorCode as any).GENERATION_FAILED,
        500
      );
    }
  }

  /**
   * Create report run record
   */
  private async createReportRun(scheduledReportId: string): Promise<ReportRun> {
    return (await this.prisma.(reportRun as any).create({
      data: {
        scheduledReportId,
        status: 'PROCESSING' as any,
        startedAt: new Date(),
      },
    })) as ReportRun;
  }

  /**
   * Update report run record
   */
  private async updateReportRun(runId: string, data: Partial<ReportRun>): Promise<ReportRun> {
    return (await this.prisma.(reportRun as any).update({
      where: { id: runId },
      data,
    })) as ReportRun;
  }

  /**
   * Send report to recipients
   */
  private async sendReportToRecipients(scheduledReport: any, filePath: string): Promise<void> {
    try {
      // This would integrate with an email service
      // For now, just log the action
      (logger as any).info(`Sending report to ${(scheduledReport as any).recipients.length} recipients`, {
        reportName: (scheduledReport as any).name,
        recipients: (scheduledReport as any).recipients,
        filePath,
      });

      // TODO: Implement actual email sending
      // await (emailService as any).sendReportEmail({
      //   recipients: (scheduledReport as any).recipients,
      //   subject: `Scheduled Report: ${(scheduledReport as any).name}`,
      //   attachmentPath: filePath
      // });
    } catch(error) {
      (logger as any).error('Error sending report to recipients:', error);
      // Don't throw - report generation was successful
    }
  }

  /**
   * Cleanup - stop all scheduled tasks
   */
  cleanup(): void {
    this.scheduledTasks.forEach((task, reportId) => {
      (task as any).stop();
      (logger as any).info(`Stopped scheduled task: ${reportId}`);
    });

    this.scheduledTasks.clear();
    this.isInitialized = false;
    (logger as any).info('Report scheduler cleanup completed');
  }
}
