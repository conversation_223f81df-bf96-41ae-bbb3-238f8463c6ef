// jscpd:ignore-file
/**
 * Enhanced Verification Controller
 *
 * Handles verification operations using the new verification service.
 */

import { Request, Response, NextFunction } from "express";
import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { VerificationService as ImportedVerificationService } from "../services/verification/VerificationService";
import { VerificationStrategyFactory as ImportedVerificationStrategyFactory } from "../factories/verification/VerificationStrategyFactory";
import { VerificationPluginManager as ImportedVerificationPluginManager } from "../plugins/verification/VerificationPluginManager";
import BinanceVerificationPlugin from "../plugins/verification/BinanceVerificationPlugin";
import { LoggingPreProcessor as ImportedLoggingPreProcessor } from "../services/verification/processors/LoggingPreProcessor";
import { NotificationPostProcessor as ImportedNotificationPostProcessor } from "../services/verification/processors/NotificationPostProcessor";
import { logger as Importedlogger } from "../lib/logger";
import { AppError as ImportedAppError } from "../middlewares/(error as any).middleware";
import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { VerificationService as ImportedVerificationService } from "../services/verification/VerificationService";
import { VerificationStrategyFactory as ImportedVerificationStrategyFactory } from "../factories/verification/VerificationStrategyFactory";
import { VerificationPluginManager as ImportedVerificationPluginManager } from "../plugins/verification/VerificationPluginManager";
import { LoggingPreProcessor as ImportedLoggingPreProcessor } from "../services/verification/processors/LoggingPreProcessor";
import { NotificationPostProcessor as ImportedNotificationPostProcessor } from "../services/verification/processors/NotificationPostProcessor";
import { logger as Importedlogger } from "../lib/logger";
import { AppError as ImportedAppError } from "../middlewares/(error as any).middleware";

const prisma = new PrismaClient();
const verificationService = new VerificationService(prisma);
const verificationPluginManager: any =(VerificationPluginManager as any).getInstance(verificationService);

// Register pre-processors
(verificationService as any).registerPreProcessor(new LoggingPreProcessor());

// Register post-processors
(verificationService as any).registerPostProcessor(new NotificationPostProcessor(prisma));

// Register plugins
(verificationPluginManager as any).registerPlugin(BinanceVerificationPlugin);

/**
 * Verify a payment
 */
export const verifyPayment = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const {
            transactionId,
            merchantId,
            paymentMethodId,
            paymentMethodType,
            amount,
            currency,
            verificationData
        } = req.body;

        // Validate required fields
        if (!transactionId || !merchantId || !paymentMethodId || !paymentMethodType || !amount || !currency || !verificationData) {
            return next(new AppError({
            message: "Missing required fields",
            type: ErrorType.VALIDATION,
            code: (ErrorCode as any).MISSING_REQUIRED_FIELD
        }));
        }

        // Validate verification data
        if (!(verificationData as any).verificationMethod) {
            return next(new AppError({
            message: "Verification method is required",
            type: ErrorType.VALIDATION,
            code: (ErrorCode as any).MISSING_REQUIRED_FIELD
        }));
        }

        // Check if the verification method exists
        const strategyFactory: any =(VerificationStrategyFactory as any).getInstance();
        if (!(strategyFactory as any).hasStrategy((verificationData as any).verificationMethod)) {
            return next(new AppError(`Unsupported verification method: ${(verificationData as any).verificationMethod}`, 400));
        }

        // Verify the payment
        const result = await (verificationService as any).verify({
            verificationMethod: (verificationData as any).verificationMethod,
            transactionId,
            merchantId,
            paymentMethodId,
            paymentMethodType,
            amount,
            currency,
            verificationData
        });

        // Return the result
        res.json({
            verified: result.success,
            status: result.success ? "success" : "failed",
            message: (result as any).message,
            details: (result as any).details,
            timestamp: (result as any).timestamp
        });
    } catch(error) {
        (logger as any).error("Verification error:", error);
        next(new AppError({
            message: "Verification failed",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get verification methods for a payment method
 */
export const getVerificationMethodsForPaymentMethod = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { paymentMethodType } = req.params;

        if (!paymentMethodType) {
            return next(new AppError({
            message: "Payment method type is required",
            type: ErrorType.VALIDATION,
            code: (ErrorCode as any).MISSING_REQUIRED_FIELD
        }));
        }

        // Get verification methods for the payment method
        const verificationMethods: any =(verificationService as any).getVerificationMethodsForPaymentMethod(paymentMethodType);

        // Map to a simpler format
        const methods: any =(verificationMethods as any).map(method => ({
            type: (method as any).getType(),
            displayName: (method as any).getDisplayName(),
            description: (method as any).getDescription(),
            requiredFields: (method as any).getRequiredFields(),
            enabled: (method as any).isEnabled()
        }));

        res.json({
            paymentMethodType,
            verificationMethods: methods
        });
    } catch(error) {
        (logger as any).error("Error getting verification methods:", error);
        next(new AppError({
            message: "Failed to get verification methods",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get all verification methods
 */
export const getAllVerificationMethods = async (req: Request, res: Response, next: NextFunction) => {
    try {
    // Get all verification methods
        const verificationMethods: any =(verificationService as any).getAllVerificationMethods();

        // Map to a simpler format
        const methods: any =(verificationMethods as any).map(method => ({
            type: (method as any).getType(),
            displayName: (method as any).getDisplayName(),
            description: (method as any).getDescription(),
            supportedPaymentMethods: (method as any).getSupportedPaymentMethods(),
            requiredFields: (method as any).getRequiredFields(),
            enabled: (method as any).isEnabled()
        }));

        res.json({
            verificationMethods: methods
        });
    } catch(error) {
        (logger as any).error("Error getting all verification methods:", error);
        next(new AppError({
            message: "Failed to get verification methods",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get verification method by type
 */
export const getVerificationMethodByType = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { type } = req.params;

        if (!type) {
            return next(new AppError({
            message: "Verification method type is required",
            type: ErrorType.VALIDATION,
            code: (ErrorCode as any).MISSING_REQUIRED_FIELD
        }));
        }

        // Get the verification method
        const strategyFactory: any =(VerificationStrategyFactory as any).getInstance();

        if (!(strategyFactory as any).hasStrategy(type)) {
            return next(new AppError(`Verification method not found: ${type}`, 404));
        }

        const method: any =(strategyFactory as any).getStrategy(type);

        // Map to a simpler format
        const methodData = {
            type: (method as any).getType(),
            displayName: (method as any).getDisplayName(),
            description: (method as any).getDescription(),
            supportedPaymentMethods: (method as any).getSupportedPaymentMethods(),
            requiredFields: (method as any).getRequiredFields(),
            enabled: (method as any).isEnabled(),
            configuration: (method as any).getConfiguration()
        };

        res.json({
            verificationMethod: methodData
        });
    } catch(error) {
        (logger as any).error("Error getting verification method:", error);
        next(new AppError({
            message: "Failed to get verification method",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

export default {
    verifyPayment,
    getVerificationMethodsForPaymentMethod,
    getAllVerificationMethods,
    getVerificationMethodByType
};
