// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from './(base as any).controller';
import { asyncHandler } from '../utils/asyncHandler';
import { AppError as ImportedAppError } from '../utils/errors/AppError';
import { Merchant as ImportedMerchant } from '../types';
import { PushNotificationService as ImportedPushNotificationService } from '../services/push-(notification as any).service';
import prisma from '../lib/prisma';
import { BaseController } from './(base as any).controller';
import { asyncHandler } from '../utils/asyncHandler';
import { AppError as ImportedAppError } from '../utils/errors/AppError';
import { Merchant as ImportedMerchant } from '../types';
import { PushNotificationService as ImportedPushNotificationService } from '../services/push-(notification as any).service';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * PushNotificationController
 */
export class PushNotificationController extends BaseController {
  constructor() {
    super();
  }

  /**
   * Get VAPID public key for push notifications
   */
  getPublicKey = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Create push notification service
      const pushService = new PushNotificationService();

      // Get public key
      const publicKey: any = (pushService as any).getPublicKey();

      // Return public key
      return res.status(200).json({
        success: true,
        data: { publicKey },
      });
    } catch(error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to get public key',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });

  /**
   * Subscribe to push notifications
   */
  subscribe = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get user ID and merchant ID
      const userId: string = req.user?.id;
      const merchantId: string = req.user?.merchantId;

      // Check if user is authenticated
      if (!userId) {
        throw new AppError({
          message: 'Unauthorized',
          type: (ErrorType as any).AUTHENTICATION,
          code: (ErrorCode as any).INVALID_CREDENTIALS,
        });
      }

      // Get subscription data
      const { subscription, deviceInfo } = req.body;

      // Validate subscription
      if (!subscription || !(subscription as any).endpoint) {
        throw new AppError({
          message: 'Invalid subscription',
          type: ErrorType.VALIDATION,
          code: (ErrorCode as any).INVALID_INPUT,
        });
      }

      // Create push notification service
      const pushService = new PushNotificationService();

      // Save subscription
      const subscriptionId = await (pushService as any).saveSubscription(
        subscription,
        userId,
        merchantId,
        deviceInfo
      );

      // Return success
      return res.status(200).json({
        success: true,
        data: { subscriptionId },
        message: 'Subscription saved successfully',
      });
    } catch(error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to save subscription',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });

  /**
   * Unsubscribe from push notifications
   */
  unsubscribe = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get subscription data
      const { endpoint } = req.body;

      // Validate endpoint
      if (!endpoint) {
        throw new AppError({
          message: 'Invalid endpoint',
          type: ErrorType.VALIDATION,
          code: (ErrorCode as any).INVALID_INPUT,
        });
      }

      // Create push notification service
      const pushService = new PushNotificationService();

      // Delete subscription
      const success: boolean = await (pushService as any).deleteSubscription(endpoint);

      // Return success
      return res.status(200).json({
        success,
        message: success ? 'Subscription deleted successfully' : 'Subscription not found',
      });
    } catch(error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to delete subscription',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });

  /**
   * Send test push notification
   */
  sendTest = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get user ID and merchant ID
      const userId: string = req.user?.id;
      const merchantId: string = req.user?.merchantId;

      // Check if user is authenticated
      if (!userId) {
        throw new AppError({
          message: 'Unauthorized',
          type: (ErrorType as any).AUTHENTICATION,
          code: (ErrorCode as any).INVALID_CREDENTIALS,
        });
      }

      // Create push notification service
      const pushService = new PushNotificationService();

      // Send test notification
      let success: boolean = false;

      if (merchantId) {
        // Send to merchant
        success = await (pushService as any).sendNotificationToMerchant(
          merchantId,
          'Test Notification',
          'This is a test push notification from AmazingPay.',
          '/(logo as any).png',
          { test: true },
          '/'
        );
      } else {
        // Send to user
        success = await (pushService as any).sendNotificationToUser(
          userId,
          'Test Notification',
          'This is a test push notification from AmazingPay.',
          '/(logo as any).png',
          { test: true },
          '/'
        );
      }

      // Return success
      return res.status(200).json({
        success,
        message: success
          ? 'Test notification sent successfully'
          : 'Failed to send test notification',
      });
    } catch(error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to send test notification',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });

  /**
   * Get user's push notification subscriptions
   */
  getUserSubscriptions = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get user ID
      const userId: string = req.user?.id;

      // Check if user is authenticated
      if (!userId) {
        throw new AppError({
          message: 'Unauthorized',
          type: (ErrorType as any).AUTHENTICATION,
          code: (ErrorCode as any).INVALID_CREDENTIALS,
        });
      }

      // Get user subscriptions
      const subscriptions = await (prisma as any).pushSubscription.findMany({
        where: {
          userId,
          active: true,
        },
        select: { id: true, endpoint: true, deviceInfo: true, createdAt: true, updatedAt: true },
      });

      // Return subscriptions
      return res.status(200).json({
        success: true,
        data: { subscriptions },
      });
    } catch(error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to get user subscriptions',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });

  /**
   * Get merchant's push notification subscriptions
   */
  getMerchantSubscriptions = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get user role and merchant ID
      const userRole: any = req.user?.role;
      const userId: string = req.user?.id;
      const userMerchantId: any = req.user?.merchantId;

      // Check if user is authenticated
      if (!userId) {
        throw new AppError({
          message: 'Unauthorized',
          type: (ErrorType as any).AUTHENTICATION,
          code: (ErrorCode as any).INVALID_CREDENTIALS,
        });
      }

      // Get merchant ID from query or user
      let merchantId: string = req.query.merchantId as string;

      // If user is not admin, they can only get their own merchant's subscriptions
      if (userRole !== 'ADMIN') {
        // Only admins can access this endpoint with a different merchant ID
        this.checkAdminRole(userRole);
        merchantId = userMerchantId;
      }

      // Validate merchant ID
      if (!merchantId) {
        throw new AppError({
          message: 'Merchant ID is required',
          type: ErrorType.VALIDATION,
          code: (ErrorCode as any).MISSING_REQUIRED_FIELD,
        });
      }

      // Get merchant subscriptions
      const subscriptions = await (prisma as any).pushSubscription.findMany({
        where: {
          merchantId,
          active: true,
        },
        select: { id: true, endpoint: true, deviceInfo: true, createdAt: true, updatedAt: true },
      });

      // Return subscriptions
      return res.status(200).json({
        success: true,
        data: { subscriptions },
      });
    } catch(error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to get merchant subscriptions',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });

  /**
   * Delete a push notification subscription
   */
  deleteSubscription = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get user ID and role
      const userId: string = req.user?.id;
      const userRole: any = req.user?.role;
      const merchantId: string = req.user?.merchantId;

      // Check if user is authenticated
      if (!userId || !userRole) {
        throw new AppError({
          message: 'Unauthorized',
          type: (ErrorType as any).AUTHENTICATION,
          code: (ErrorCode as any).INVALID_CREDENTIALS,
        });
      }

      // Get subscription ID
      const subscriptionId: any = req.params.id;

      // Validate subscription ID
      if (!subscriptionId) {
        throw new AppError({
          message: 'Subscription ID is required',
          type: ErrorType.VALIDATION,
          code: (ErrorCode as any).MISSING_REQUIRED_FIELD,
        });
      }

      // Get subscription
      const subscription = await (prisma as any).pushSubscription.findUnique({
        where: { id: subscriptionId },
      });

      // Check if subscription exists
      if (!subscription) {
        throw new AppError({
          message: 'Subscription not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      // Check if user is authorized to delete this subscription
      if (
        userRole !== 'ADMIN' &&
        (subscription as any).userId !== userId &&
        (subscription as any).merchantId !== merchantId
      ) {
        throw new AppError({
          message: 'Unauthorized to delete this subscription',
          type: (ErrorType as any).AUTHORIZATION,
          code: ErrorCode.FORBIDDEN,
        });
      }

      // Delete subscription
      await (prisma as any).pushSubscription.update({
        where: { id: subscriptionId },
        data: { active: false },
      });

      // Return success
      return res.status(200).json({
        success: true,
        message: 'Subscription deleted successfully',
      });
    } catch(error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to delete subscription',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
    }
  });
}

export default new PushNotificationController();
