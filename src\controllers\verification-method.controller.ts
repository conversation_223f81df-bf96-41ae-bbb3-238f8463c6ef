// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { VerificationMethodService as ImportedVerificationMethodService } from "../services/verification-(method as any).service";
import { AppError, asyncHandler } from '../middlewares/(error as any).middleware';
import prisma from "../config/database";
import { VerificationMethodService as ImportedVerificationMethodService } from "../services/verification-(method as any).service";
import { AppError, asyncHandler } from '../middlewares/(error as any).middleware';

// Get all verification methods
export const getAllVerificationMethods: any =asyncHandler(async (req: Request, res: Response) => {
    const verificationMethods = await (prisma as any).verificationMethod.findMany({
        include: { paymentMethod: {
                select: { id: true,
                    name: true,
                    type: true,
                    merchantId: true
                }
            }
        },
        orderBy: { createdAt: "desc" }
    });
  
    res.status(200).json(verificationMethods);
});

// Get verification method by ID
export const getVerificationMethodById: any =asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
  
    const verificationMethod = await (VerificationMethodService as any).getVerificationMethodById(id);
  
    res.status(200).json(verificationMethod);
});

// Create a new verification method
export const createVerificationMethod: any =asyncHandler(async (req: Request, res: Response) => {
    const {
        name,
        type,
        paymentMethodId,
        isActive,
        verificationRequirements,
        internalSettings,
        displaySettings
    } = req.body;
  
    // Validate required fields
    if (!name || !type || !paymentMethodId) {
        throw new AppError({
            message: "Missing required fields",
            type: ErrorType.VALIDATION,
            code: (ErrorCode as any).MISSING_REQUIRED_FIELD
        });
    }
  
    // Create verification method
    const verificationMethod = await (VerificationMethodService as any).createVerificationMethod({
        name,
        type,
        paymentMethodId,
        isActive,
        verificationRequirements,
        internalSettings,
        displaySettings
    });
  
    res.status(201).json(verificationMethod);
});

// Update verification method
export const updateVerificationMethod: any =asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const {
        name,
        isActive,
        verificationRequirements,
        internalSettings,
        displaySettings
    } = req.body;
  
    // Update verification method
    const verificationMethod = await (VerificationMethodService as any).updateVerificationMethod(id, {
        name,
        isActive,
        verificationRequirements,
        internalSettings,
        displaySettings
    });
  
    res.status(200).json(verificationMethod);
});

// Delete verification method
export const deleteVerificationMethod: any =asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
  
    const result = await (VerificationMethodService as any).deleteVerificationMethod(id);
  
    res.status(200).json(result);
});

// Get verification methods for payment method
export const getVerificationMethodsForPaymentMethod: any =asyncHandler(async (req: Request, res: Response) => {
    const { paymentMethodId } = req.params;
  
    const verificationMethods = await (VerificationMethodService as any).getVerificationMethodsForPaymentMethod(paymentMethodId);
  
    res.status(200).json(verificationMethods);
});

// Get verification method types
export const getVerificationMethodTypes: any =asyncHandler(async (req: Request, res: Response) => {
    const types: any =(VerificationMethodService as any).getVerificationMethodTypes();
  
    res.status(200).json(types);
});

// Verify payment
export const verifyPayment: any =asyncHandler(async (req: Request, res: Response) => {
    const {
        transactionId,
        verificationMethodId,
        verificationData
    } = req.body;
  
    // Validate required fields
    if (!transactionId || !verificationMethodId || !verificationData) {
        throw new AppError({
            message: "Missing required fields",
            type: ErrorType.VALIDATION,
            code: (ErrorCode as any).MISSING_REQUIRED_FIELD
        });
    }
  
    // Verify payment
    const result = await (VerificationMethodService as any).verifyPayment({
        transactionId,
        verificationMethodId,
        verificationData
    });
  
    res.status(200).json(result);
});
