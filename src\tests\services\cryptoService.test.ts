import { PrismaClient } from '@prisma/client';
// jscpd:ignore-file
/**
 * cryptoService Tests
 *
 * This file contains tests for the cryptoService module using the test utility.
 */

import { cryptoServiceController } from '../controllers/cryptoservice.controller';
import { cryptoServiceService } from '../services/cryptoservice.service';
import { cryptoServiceRepository } from '../repositories/cryptoservice.repository';
import { testControllerSuite, createMockRequest, createMockResponse, testService, testRepository, createMockPrismaClient } from './tests/utils/test-utilities';
import { Repository } from '../types/database';
import { cryptoServiceService } from '../services/cryptoservice.service';
import { cryptoServiceRepository } from '../repositories/cryptoservice.repository';
import { testControllerSuite, createMockRequest, createMockResponse, testService, testRepository, createMockPrismaClient } from './tests/utils/test-utilities';
import { Repository } from '../types/database';


// Mock the cryptoServiceService
jest.mock('../services/cryptoservice.service');

describe('cryptoService Module Tests', () => {
  // Controller tests
  testControllerSuite('cryptoServiceController', cryptoServiceController, {
    getAll: { description: 'should get all cryptoservices',
      expectedStatus: 200,
      expectedResponse: { success: true },
    },
    getById: { description: 'should get cryptoservice by ID',
      req: createMockRequest({ params: { id: '1' } }),
      expectedStatus: 200,
      expectedResponse: { success: true },
    },
    create: { description: 'should create cryptoservice',
      req: createMockRequest({ body: { name: 'Test' } }),
      expectedStatus: 201,
      expectedResponse: { success: true },
    },
    update: { description: 'should update cryptoservice',
      req: createMockRequest({ params: { id: '1' }, body: { name: 'Updated' } }),
      expectedStatus: 200,
      expectedResponse: { success: true },
    },
    delete: { description: 'should delete cryptoservice',
      req: createMockRequest({ params: { id: '1' } }),
      expectedStatus: 200,
      expectedResponse: { success: true, message: 'cryptoService deleted successfully' },
    },
  });

  // Service tests
  describe('cryptoServiceService', () => {
    let service;
    let mockRepository;

    beforeEach(() => {
      mockRepository = {
        findAll: jest.fn(),
        findById: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      };

      service = new cryptoServiceService();
      service.cryptoserviceRepository = mockRepository;
    });

    it('should find all cryptoservices', async () => {
      mockRepository.findAll.mockResolvedValue([{ id: '1', name: 'Test' }]);

      const result: unknown =await testService(service, 'findAll');

      expect(result).toEqual([{ id: '1', name: 'Test' }]);
      expect(mockRepository.findAll).toHaveBeenCalled();
    });
  });

  // Repository tests
  describe('cryptoServiceRepository', () => {
    let repository;
    let mockPrisma;

    beforeEach(() => {
      mockPrisma = createMockPrismaClient();
      repository = new cryptoServiceRepository();
      repository.prisma = mockPrisma;
    });

    it('should find all cryptoservices', async () => {
      mockPrisma.cryptoservice.findMany.mockResolvedValue([{ id: '1', name: 'Test' }]);

      const result: unknown =await testRepository(repository, 'findAll');

      expect(result).toEqual([{ id: '1', name: 'Test' }]);
      expect(mockPrisma.cryptoservice.findMany).toHaveBeenCalled();
    });
  });
});
