import { User, Merchant, PaymentMethod, Transaction } from '../types';
// jscpd:ignore-file

// Common types shared across the application

export interface User {
  id: string;
  email: string;
  password: string;
  role: "admin" | "merchant";
  isActive: boolean;
}

export interface Merchant {
  id: string;
  userId: string;
  name: string;
  email: string;
  status: "active" | "pending" | "suspended";
  currentPlan: string;
  planExpiryDate: Date;
  totalRevenue: number;
}

export interface PaymentMethod {
  id: string;
  merchantId: string;
  name: string;
  type: "binance_c2c" | "binance_pay" | "binance_trc20_direct" | "usdt" | "usdc";
  network?: "trc20" | "erc20" | "bep20";
  address?: string;
  qrCodeUrl?: string;
  note?: string;
  apiKey?: string;
  secretKey?: string;
  isActive: boolean;
}

export interface Transaction {
  id: string;
  merchantId: string;
  merchantName: string;
  amount: number;
  currency: string;
  status: "pending" | "success" | "failed";
  method: string;
  customerEmail?: string;
  date: Date;
  verificationMethod: string;
  verificationDetails?: Record<string, any>;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  duration: 1 | 3 | 6 | 12; // months
  maxPayments: number;
  maxMethods: number;
  price: number;
  features: string[];
}

export interface SystemSetting {
  id: string;
  key: string;
  value: string;
  description: string;
  updatedBy: string;
  updatedAt: Date;
}

export interface Log {
  id: string;
  level: "info" | "warning" | "error";
  message: string;
  source: string;
  timestamp: Date;
  details?: Record<string, any>;
}
