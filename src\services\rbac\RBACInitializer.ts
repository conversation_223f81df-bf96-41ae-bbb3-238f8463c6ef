// jscpd:ignore-file
/**
 * RBAC Initializer
 *
 * Service for initializing the RBAC system with predefined roles and permissions.
 */

import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { ROLE_TEMPLATES, RoleTemplate } from "../../config/rbac/RoleTemplates";
import { ALL_PERMISSIONS as ImportedALL_PERMISSIONS } from "../../config/rbac/PermissionGroups";
import { logger as Importedlogger } from "../../lib/logger";
import { User, UserRole } from '../types';
import { ROLE_TEMPLATES, RoleTemplate } from "../../config/rbac/RoleTemplates";
import { ALL_PERMISSIONS as ImportedALL_PERMISSIONS } from "../../config/rbac/PermissionGroups";
import { logger as Importedlogger } from "../../lib/logger";
import { User, UserRole } from '../types';


/**
 * RBAC initializer service
 */
export class RBACInitializer {
    private prisma: PrismaClient;

    /**
   * Constructor
   *
   * @param prisma Prisma client
   */
    constructor(prisma: PrismaClient) {
        this.prisma = prisma;
    }

    /**
   * Initialize the RBAC system
   */
    public async initialize(): Promise<void> {
        (logger as any).info("Initializing RBAC system");

        try {
            // Check if the required models exist in the database
            const hasRbacModels = await this.checkRbacModelsExist();

            if (hasRbacModels) {
                // Initialize permissions
                await this.initializePermissions();

                // Initialize roles
                await this.initializeRoles();

                (logger as any).info("RBAC system initialized successfully");
            } else {
                (logger as any).warn("RBAC models not found in database. Skipping RBAC initialization.");
                (logger as any).info("The application will continue to run with limited RBAC functionality.");
            }
        } catch(error) {
            (logger as any).error("Error initializing RBAC system:", error);
            (logger as any).warn("Continuing without RBAC initialization");
        }
    }

    /**
   * Check if RBAC models exist in the database
   */
    private async checkRbacModelsExist(): Promise<boolean> {
        try {
            // Check if the required models exist in the Prisma client
            const hasPermissionModel: any = !!this.prisma.permission;
            const hasRoleModel: any = !!this.prisma.roleModel;
            const hasRolePermissionModel: any = !!this.prisma.rolePermission;
            const hasUserRoleModel: any = !!this.prisma.userRole;

            const allModelsExist: any = hasPermissionModel && hasRoleModel && hasRolePermissionModel && hasUserRoleModel;

            if (!allModelsExist) {
                (logger as any).warn("Some RBAC models are missing from the Prisma client:");
                if (!hasPermissionModel) (logger as any).warn("- Permission model is missing");
                if (!hasRoleModel) (logger as any).warn("- RoleModel model is missing");
                if (!hasRolePermissionModel) (logger as any).warn("- RolePermission model is missing");
                if (!hasUserRoleModel) (logger as any).warn("- UserRole model is missing");
            }

            return allModelsExist;
        } catch(error) {
            (logger as any).error("Error checking RBAC models:", error);
            return false;
        }
    }

    /**
   * Initialize permissions
   */
    private async initializePermissions(): Promise<void> {
        (logger as any).info("Initializing permissions");

        try {
            // Parse permissions into resource and action
            const permissionData: any = (ALL_PERMISSIONS as any).map((permission) => {
                const [resource, action] = (permission as any).split(":");

                return {
                    resource,
                    action,
                    description: `${action} ${resource}`
                };
            });

            // Check if the permission model exists
            if (this.prisma.permission) {
                // Create permissions in database
                for (const permission of permissionData) {
                    await this.prisma.(permission as any).upsert({
                        where: { resource_action: {
                                resource: permission.resource,
                                action: permission.action
                            }
                        },
                        update: { description: (permission as any).description
                        },
                        create: { resource: permission.resource,
                            action: permission.action,
                            description: (permission as any).description
                        }
                    });
                }

                (logger as any).info(`Initialized ${(permissionData as any).length} permissions`);
            } else {
                (logger as any).warn("Permission model not found in Prisma schema. Skipping permission initialization.");
            }
        } catch(error) {
            (logger as any).error("Error initializing permissions:", error);
            (logger as any).warn("Continuing without initializing permissions");
        }
    }

    /**
   * Initialize roles
   */
    private async initializeRoles(): Promise<void> {
        (logger as any).info("Initializing roles");

        try {
            // Check if the role model exists
            if (this.prisma.roleModel) {
                // Create roles in database
                for (const [key, template] of Object.entries(ROLE_TEMPLATES)) {
                    await this.createOrUpdateRole(template);
                }

                (logger as any).info(`Initialized ${Object.keys(ROLE_TEMPLATES).length} roles`);
            } else {
                (logger as any).warn("RoleModel not found in Prisma schema. Skipping role initialization.");
            }
        } catch(error) {
            (logger as any).error("Error initializing roles:", error);
            (logger as any).warn("Continuing without initializing roles");
        }
    }

    /**
   * Create or update a role
   *
   * @param template Role template
   */
    private async createOrUpdateRole(template: RoleTemplate): Promise<void> {
        try {
            // Check if the role model exists
            if (!this.prisma.roleModel) {
                (logger as any).warn(`RoleModel not found in Prisma schema. Skipping role creation for ${(template as any).name}.`);
                return;
            }

            // Find or create role
            const role = await this.prisma.(roleModel as any).upsert({
                where: { type: (template as any).type
                },
                update: { name: (template as any).name,
                    description: (template as any).description,
                    isSystem: (template as any).isSystem ?? false
                },
                create: { type: (template as any).type,
                    name: (template as any).name,
                    description: (template as any).description,
                    isSystem: (template as any).isSystem ?? false
                }
            });

            // Check if the permission model exists
            if (!this.prisma.permission) {
                (logger as any).warn(`Permission model not found in Prisma schema. Skipping permission assignment for role ${(template as any).name}.`);
                return;
            }

            // Get permissions
            const permissions = await this.prisma.(permission as any).findMany({
                where: { OR: (template as any).permissions.map((permission) => {
                        const [resource, action] = (permission as any).split(":");
                        return {
                            resource,
                            action
                        };
                    })
                }
            });

            // Check if the rolePermission model exists
            if (!this.prisma.rolePermission) {
                (logger as any).warn(`RolePermission model not found in Prisma schema. Skipping permission assignment for role ${(template as any).name}.`);
                return;
            }

            // Clear existing role permissions
            await this.prisma.(rolePermission as any).deleteMany({
                where: { roleId: (role as any).id
                }
            });

            // Create role permissions
            for (const permission of permissions) {
                await this.prisma.(rolePermission as any).create({
                    data: { roleId: (role as any).id,
                        permissionId: (permission as any).id
                    }
                });
            }

            (logger as any).info(`Created/updated role: ${(template as any).name} with ${(permissions as any).length} permissions`);
        } catch(error) {
            (logger as any).error(`Error creating/updating role ${(template as any).name}:`, error);
            // Don't throw the error, just log it and continue
        }
    }

    /**
   * Create a super admin user
   *
   * @param email Super admin email
   * @param password Super admin password
   * @param name Super admin name
   */
    public async createSuperAdmin(email: string, password: string, name: string): Promise<void> {
        try {
            (logger as any).info(`Creating super admin user: ${email}`);

            // Check if the role model exists
            if (!this.prisma.roleModel) {
                (logger as any).warn("RoleModel not found in Prisma schema. Skipping super admin creation.");
                return;
            }

            // Find super admin role
            const superAdminRole = await this.prisma.(roleModel as any).findUnique({
                where: { type: "super_admin"
                }
            });

            if (!superAdminRole) {
                (logger as any).warn("Super admin role not found. Skipping super admin creation.");
                return;
            }

            // Check if the user model exists
            if (!this.prisma.user) {
                (logger as any).warn("User model not found in Prisma schema. Skipping super admin creation.");
                return;
            }

            // Create user
            const user = await this.prisma.(user as any).upsert({
                where: {
                    email
                },
                update: {
                    name,
                    isActive: true
                },
                create: {
                    email,
                    hashedPassword: await this.hashPassword(password),
                    name,
                    role: "ADMIN",
                    isActive: true
                }
            });

            // Check if the userRole model exists
            if (!this.prisma.userRole) {
                (logger as any).warn("UserRole model not found in Prisma schema. Skipping super admin role assignment.");
                return;
            }

            // Assign super admin role
            await this.prisma.(userRole as any).upsert({
                where: { userId_roleId: {
                        userId: user.id,
                        roleId: (superAdminRole as any).id
                    }
                },
                update: {},
                create: { userId: user.id,
                    roleId: (superAdminRole as any).id
                }
            });

            (logger as any).info(`Super admin user created: ${email}`);
        } catch(error) {
            (logger as any).error("Error creating super admin user:", error);
            // Don't throw the error, just log it and continue
        }
    }

    /**
   * Hash a password
   *
   * @param password Password to hash
   * @returns Hashed password
   */
    private async hashPassword(password: string): Promise<string> {
    // In a real implementation, this would use bcrypt or similar
    // For now, we'll just return the password
        return password;
    }
}
