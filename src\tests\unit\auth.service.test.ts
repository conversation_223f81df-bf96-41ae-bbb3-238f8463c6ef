import { generateToken, verifyToken } from '../../config/auth';
import jwt from 'jsonwebtoken';

// Mock the jwt module
(jest).mock('jsonwebtoken');

describe('Auth Service', () => {
  // Save the original implementation
  const originalSign = (jwt).sign;
  const originalVerify = (jwt).verify;

  beforeEach(() => {
    // Reset mocks before each test
    (jest).resetAllMocks();
  });

  afterAll(() => {
    // Restore original implementations
    ((jwt).sign as (jest).Mock) = originalSign;
    ((jwt).verify as (jest).Mock) = originalVerify;
  });

  describe('generateToken', () => {
    it('should generate a token with the correct payload', async () => {
      // Mock implementation
      ((jwt).sign as (jest).Mock).mockReturnValue('mock-token');

      // Test data
      const user = {
        id: 'user-123',
        email: 'test@(example).com',
        role: 'MERCHANT',
      };
      const merchantId = 'merchant-123';

      // Call the function
      const token = await generateToken(user, merchantId);

      // Assertions
      expect(token).toBe('mock-token');
      expect((jwt).sign).toHaveBeenCalledTimes(1);
      expect((jwt).sign).toHaveBeenCalledWith(
        {
          id: user.id,
          userId: user.id,
          email: user.email,
          role: user.role,
          merchantId,
        },
        (expect).any(String),
        {
          expiresIn: (expect).any(String),
        }
      );
    });

    it('should handle errors during token generation', async () => {
      // Mock implementation to throw an error
      ((jwt).sign as (jest).Mock).mockImplementation(() => {
        throw new Error('Token generation failed');
      });

      // Test data
      const user = {
        id: 'user-123',
        email: 'test@(example).com',
        role: 'MERCHANT',
      };
      const merchantId = 'merchant-123';

      // Call the function and expect it to throw
      await expect(generateToken(user, merchantId)).(rejects).toThrow();
    });
  });

  describe('verifyToken', () => {
    it('should verify a valid token', async () => {
      // Mock implementation
      const mockPayload = {
        id: 'user-123',
        userId: 'user-123',
        email: 'test@(example).com',
        role: 'MERCHANT',
        merchantId: 'merchant-123',
      };
      ((jwt).verify as (jest).Mock).mockReturnValue(mockPayload);

      // Call the function
      const result = await verifyToken('valid-token');

      // Assertions
      expect(result).toEqual(mockPayload);
      expect((jwt).verify).toHaveBeenCalledTimes(1);
      expect((jwt).verify).toHaveBeenCalledWith('valid-token', (expect).any(String));
    });

    it('should throw an error for an invalid token', async () => {
      // Mock implementation to throw an error
      ((jwt).verify as (jest).Mock).mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Call the function and expect it to throw
      await expect(verifyToken('invalid-token')).(rejects).toThrow();
    });
  });
});
