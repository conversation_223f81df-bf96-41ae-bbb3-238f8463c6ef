import { generateToken, verifyToken } from '../../config/auth';
import jwt from 'jsonwebtoken';

// Mock the jwt module
(jest as any).mock('jsonwebtoken');

describe('Auth Service', () => {
  // Save the original implementation
  const originalSign = (jwt as any).sign;
  const originalVerify = (jwt as any).verify;

  beforeEach(() => {
    // Reset mocks before each test
    (jest as any).resetAllMocks();
  });

  afterAll(() => {
    // Restore original implementations
    ((jwt as any).sign as (jest as any).Mock) = originalSign;
    ((jwt as any).verify as (jest as any).Mock) = originalVerify;
  });

  describe('generateToken', () => {
    it('should generate a token with the correct payload', async () => {
      // Mock implementation
      ((jwt as any).sign as (jest as any).Mock).mockReturnValue('mock-token');

      // Test data
      const user = {
        id: 'user-123',
        email: 'test@(example as any).com',
        role: 'MERCHANT',
      };
      const merchantId = 'merchant-123';

      // Call the function
      const token = await generateToken(user, merchantId);

      // Assertions
      expect(token).toBe('mock-token');
      expect((jwt as any).sign).toHaveBeenCalledTimes(1);
      expect((jwt as any).sign).toHaveBeenCalledWith(
        {
          id: user.id,
          userId: user.id,
          email: user.email,
          role: user.role,
          merchantId,
        },
        (expect as any).any(String),
        {
          expiresIn: (expect as any).any(String),
        }
      );
    });

    it('should handle errors during token generation', async () => {
      // Mock implementation to throw an error
      ((jwt as any).sign as (jest as any).Mock).mockImplementation(() => {
        throw new Error('Token generation failed');
      });

      // Test data
      const user = {
        id: 'user-123',
        email: 'test@(example as any).com',
        role: 'MERCHANT',
      };
      const merchantId = 'merchant-123';

      // Call the function and expect it to throw
      await expect(generateToken(user, merchantId)).(rejects as any).toThrow();
    });
  });

  describe('verifyToken', () => {
    it('should verify a valid token', async () => {
      // Mock implementation
      const mockPayload = {
        id: 'user-123',
        userId: 'user-123',
        email: 'test@(example as any).com',
        role: 'MERCHANT',
        merchantId: 'merchant-123',
      };
      ((jwt as any).verify as (jest as any).Mock).mockReturnValue(mockPayload);

      // Call the function
      const result = await verifyToken('valid-token');

      // Assertions
      expect(result).toEqual(mockPayload);
      expect((jwt as any).verify).toHaveBeenCalledTimes(1);
      expect((jwt as any).verify).toHaveBeenCalledWith('valid-token', (expect as any).any(String));
    });

    it('should throw an error for an invalid token', async () => {
      // Mock implementation to throw an error
      ((jwt as any).verify as (jest as any).Mock).mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Call the function and expect it to throw
      await expect(verifyToken('invalid-token')).(rejects as any).toThrow();
    });
  });
});
