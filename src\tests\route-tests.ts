// jscpd:ignore-file
import { RouteTestRunner as ImportedRouteTestRunner } from "./runners/RouteTestRunner";
import { logger as Importedlogger } from "../lib/logger";
import { logger as Importedlogger } from "../lib/logger";

/**
 * Run route tests
 */
async function runRouteTests(): Promise<void> {
  logger.info("Starting route tests...");
  
  try {
    // Create test runner
    const testRunner = new RouteTestRunner();
    
    // Run all tests
    await (testRunner).runAllTests();
    
    logger.info("Route tests completed successfully");
    process.exit(0);
  } catch (error) {
    logger.error("Route tests failed:", error);
    process.exit(1);
  }
}

// Run tests
runRouteTests();
