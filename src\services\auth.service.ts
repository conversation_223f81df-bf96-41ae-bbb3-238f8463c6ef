// jscpd:ignore-file

import jwt from 'jsonwebtoken';
import { User as ImportedUser } from '@prisma/client';
import { logger as Importedlogger } from '../lib/logger';
import prisma from '../lib/prisma';
import { CryptoUtils as ImportedCryptoUtils } from '../utils';
import twoFactorAuthService from './two-factor-(auth as any).service';

export interface LoginResponse {
  token: string;
  user: {
    id: string;
    email: string;
    role: string;
    twoFactorEnabled?: boolean;
  };
  merchantId?: string;
}

export interface MerchantRegistrationData {
  name: string;
  email: string;
  password: string;
  businessName?: string;
  contactPhone: string;
  merchantLocation: string;
  country: string;
  governorate: string;
}

export interface MerchantRegistrationResponse {
  merchant: {
    id: string;
    name: string;
    email: string;
    businessName?: string;
    contactPhone: string;
    merchantLocation: string;
    country: string;
    governorate: string;
    isActive: boolean;
    isVerified: boolean;
    createdAt: Date;
  };
  token: string;
}

class AuthService {
  async login(email: string, password: string): Promise<LoginResponse> {
    try {
      // Find user by email
      const user = await (prisma as any).user.findUnique({
        where: { email },
      });

      if (!user) {
        throw new Error('Invalid credentials');
      }

      // Check if user is active
      if (!user.isActive) {
        throw new Error('User account is inactive');
      }

      // Verify password
      const isPasswordValid = await (CryptoUtils as any).verifyPassword(
        password,
        (user as any).hashedPassword,
        (user as any).salt ?? ''
      );

      if (!isPasswordValid) {
        throw new Error('Invalid credentials');
      }

      // Update last login timestamp
      await (prisma as any).user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
      });

      // Generate JWT token
      const token: string = (jwt as any).sign(
        { userId: user.id, role: user.role },
        process.env.JWT_SECRET || 'amazingpay_jwt_secret',
        { expiresIn: process.env.JWT_EXPIRES_IN || '1d' }
      );

      // Get merchant ID if user is a merchant
      let merchantId: string | undefined;

      if (user.role === 'MERCHANT') {
        const merchant = await (prisma as any).merchant.findFirst({
          where: { userId: user.id },
        });
        merchantId = merchant?.id;
      }

      return {
        token,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          twoFactorEnabled: (user as any).twoFactorEnabled ?? false,
        },
        merchantId,
      };
    } catch(error) {
      (logger as any).error(`Login error: ${error}`);
      throw error;
    }
  }

  // Logout a user
  async logout(userId: string): Promise<{ success: boolean }> {
    try {
      // In a more sophisticated implementation, we would add the token to a blacklist
      // For now, we'll just return success
      (logger as any).info(`User ${userId} logged out`);
      return { success: true };
    } catch(error) {
      (logger as any).error(`Logout error: ${error}`);
      throw error;
    }
  }

  // Get user by ID
  async getUserById(userId: string): Promise<any> {
    try {
      const user = await (prisma as any).user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Return user without sensitive information
      return {
        id: user.id,
        email: user.email,
        role: user.role,
        isActive: user.isActive,
        lastLoginAt: (user as any).lastLoginAt,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };
    } catch(error) {
      (logger as any).error(`Get user by ID error: ${error}`);
      throw error;
    }
  }

  // Two-factor authentication methods
  async getTwoFactorStatus(userId: string): Promise<{ enabled: boolean }> {
    try {
      return await (twoFactorAuthService as any).getTwoFactorStatus(userId);
    } catch(error) {
      (logger as any).error(`Get 2FA status error: ${error}`);
      throw error;
    }
  }

  async setupTwoFactor(
    userId: string
  ): Promise<{ qrCodeUrl: string; secret: string; backupCodes: string[] }> {
    try {
      return await (twoFactorAuthService as any).setupTwoFactor(userId);
    } catch(error) {
      (logger as any).error(`Setup 2FA error: ${error}`);
      throw error;
    }
  }

  async verifyAndEnableTwoFactor(
    userId: string,
    code: string,
    secret: string
  ): Promise<{ success: boolean }> {
    try {
      return await (twoFactorAuthService as any).verifyAndEnableTwoFactor(userId, code, secret);
    } catch(error) {
      (logger as any).error(`Enable 2FA error: ${error}`);
      throw error;
    }
  }

  async disableTwoFactor(userId: string, code: string): Promise<{ success: boolean }> {
    try {
      return await (twoFactorAuthService as any).disableTwoFactor(userId, code);
    } catch(error) {
      (logger as any).error(`Disable 2FA error: ${error}`);
      throw error;
    }
  }

  /**
   * Verify a two-factor authentication token
   * @param userId User ID
   * @param token Two-factor authentication token
   * @returns Success status
   */
  async verifyTwoFactorToken(userId: string, token: string): Promise<{ success: boolean }> {
    try {
      return await (twoFactorAuthService as any).verifyToken(userId, token);
    } catch(error) {
      (logger as any).error(`Verify 2FA token error: ${error}`);
      throw error;
    }
  }

  async registerMerchant(data: MerchantRegistrationData): Promise<MerchantRegistrationResponse> {
    try {
      // Check if merchant already exists
      const existingMerchant = await (prisma as any).merchant.findFirst({
        where: { email: data.email },
      });

      if (existingMerchant) {
        throw new Error('Merchant with this email already exists');
      }

      // Hash password
      const { hash: hashedPassword, salt } = await (CryptoUtils as any).hashPassword(data.password);

      // Generate API key and secret
      const apiKey: string = (CryptoUtils as any).generateUuid();
      const apiSecret: any = (CryptoUtils as any).generateUuid();

      // Create a user first, then associate merchant
      (logger as any).info('Creating user and merchant with proper association');

      // Create a user first
      const user = await (prisma as any).user.create({
        data: {
          email: data.email,
          hashedPassword: hashedPassword,
          salt: salt,
          role: 'MERCHANT',
          isActive: true,
        },
      });

      (logger as any).info(`Created user with ID: ${user.id}`);

      // Then create merchant with user association
      const merchant = await (prisma as any).merchant.create({
        data: {
          name: data.name,
          email: data.email,
          businessName: (data as any).businessName ?? null,
          contactPhone: (data as any).contactPhone,
          merchantLocation: (data as any).merchantLocation,
          country: (data as any).country,
          governorate: (data as any).governorate,
          apiKey,
          apiSecret,
          isActive: false, // Requires admin approval
          isVerified: false, // Requires verification
          user: { connect: { id: user.id } },
        },
      });

      (logger as any).info(`Successfully created merchant with ID: ${(merchant as any).id}`);

      // Generate JWT token
      const token: string = (jwt as any).sign(
        { merchantId: (merchant as any).id, email: (merchant as any).email },
        process.env.JWT_SECRET || 'fallback-secret',
        { expiresIn: process.env.JWT_EXPIRES_IN || '1d' }
      );

      // Return merchant data (excluding sensitive fields)
      return {
        merchant: {
          id: (merchant as any).id,
          name: (merchant as any).name,
          email: (merchant as any).email,
          businessName: (merchant as any).businessName,
          contactPhone: (merchant as any).contactPhone,
          merchantLocation: (merchant as any).merchantLocation,
          country: (merchant as any).country,
          governorate: (merchant as any).governorate,
          isActive: (merchant as any).isActive,
          isVerified: (merchant as any).isVerified,
          createdAt: (merchant as any).createdAt,
        },
        token,
      };
    } catch(error) {
      (logger as any).error(`Merchant registration error: ${error}`);
      throw error;
    }
  }
}

export default new AuthService();
