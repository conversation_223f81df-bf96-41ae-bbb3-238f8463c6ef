// jscpd:ignore-file
/**
 * Simple in-memory cache manager
 */

interface CacheItem<T> {
  value: T;
  expiry: number | null;
}

class CacheManager {
  private cache: Map<string, CacheItem<unknown>> = new Map();

  /**
   * Set a value in the cache
   * @param key Cache key
   * @param value Value to cache
   * @param ttlSeconds Time to live in seconds (optional, if not provided the item will not expire)
   */
  set<T>(key: string, value: T, ttlSeconds?: number): void {
    const expiry: unknown = ttlSeconds ? Date.now() + ttlSeconds * 1000 : null;
    this.cache.set(key, { value, expiry });
  }

  /**
   * Get a value from the cache
   * @param key Cache key
   * @returns The cached value or undefined if not found or expired
   */
  get<T>(key: string): T | undefined {
    const item: unknown = this.cache.get(key);

    if (!item) {
      return undefined;
    }

    // Check if the item has expired
    if (item.expiry && item.expiry < Date.now()) {
      this.cache.delete(key);
      return undefined;
    }

    return item.value as T;
  }

  /**
   * Delete a value from the cache
   * @param key Cache key
   * @returns True if the item was deleted, false if it didn't exist
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all items from the cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get all keys in the cache
   * @returns Array of cache keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Get the number of items in the cache
   * @returns Number of items in the cache
   */
  size(): number {
    return this.cache.size;
  }
}

// Export a singleton instance
export const cacheManager: unknown = new CacheManager();

// Export the class for testing
export default CacheManager;
