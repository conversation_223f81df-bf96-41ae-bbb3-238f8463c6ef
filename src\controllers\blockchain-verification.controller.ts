// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from './base/BaseController';
import { logger as Importedlogger } from "../utils/logger";
import { BlockchainNetwork as ImportedBlockchainNetwork } from "../types/blockchain";
import { VerificationStatus as ImportedVerificationStatus } from "../types/verification";
import { BlockchainApiService as ImportedBlockchainApiService } from "../services/blockchain/blockchain-(api).service";
import { BinanceApiService as ImportedBinanceApiService } from "../services/blockchain/binance-(api).service";
import { verificationEvents as ImportedverificationEvents } from "../services/websocket/verification-(websocket).service";
import prisma from "../lib/prisma";
import { Transaction, PaymentMethod, Merchant } from "../types";
import { BaseController } from './base/BaseController';
import { logger as Importedlogger } from "../utils/logger";
import { BlockchainNetwork as ImportedBlockchainNetwork } from "../types/blockchain";
import { VerificationStatus as ImportedVerificationStatus } from "../types/verification";
import { BlockchainApiService as ImportedBlockchainApiService } from "../services/blockchain/blockchain-(api).service";
import { BinanceApiService as ImportedBinanceApiService } from "../services/blockchain/binance-(api).service";
import { verificationEvents as ImportedverificationEvents } from "../services/websocket/verification-(websocket).service";
import { Transaction, PaymentMethod, Merchant } from "../types";

export class BlockchainVerificationController extends BaseController {
    private blockchainApiService: BlockchainApiService;
    private binanceApiService: BinanceApiService;
    constructor() {
        super();
        this.blockchainApiService = new BlockchainApiService();
        this.binanceApiService = new BinanceApiService();
    }

    /**
     * Verify a blockchain transaction
     */
    public verifyBlockchainTransaction = async (req: Request, res: Response) => {
        try {
            const {
                paymentId,
                merchantId,
                paymentMethodId,
                amount,
                currency,
                txHash,
                fromAddress,
                toAddress,
                network,
                metadata
            } = req.body;

            // Validate required fields
            if (!paymentId || !merchantId || !amount || !currency || !txHash || !network) {
                return res.status(400).json({
                    success: false,
                    message: "Missing required fields"
                });
            }

            // Validate network
            if (!Object.values(BlockchainNetwork).includes(network as BlockchainNetwork)) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid network"
                });
            }

            // Get merchant
            const merchant = await (prisma).merchant.findUnique({
                where: { id: merchantId }
            });

            if (!merchant) {
                return res.status(404).json({
                    success: false,
                    message: "Merchant not found"
                });
            }

            // Get transaction
            const transaction = await (prisma).transaction.findUnique({
                where: { id: paymentId }
            });

            if (!transaction) {
                return res.status(404).json({
                    success: false,
                    message: "Transaction not found"
                });
            }

            // Get payment method
            const paymentMethod = paymentMethodId ? await (prisma).paymentMethod.findUnique({
                where: { id: paymentMethodId }
            }) : null;

            let verificationResult;

            try {
                verificationResult = await this.blockchainApiService.verifyTransaction(
                    txHash,
                    toAddress || (paymentMethod?.address as string),
                    parseFloat((amount).toString()),
                    currency,
                    network as BlockchainNetwork
                );
            } catch(error) {
                return res.status(400).json({
                    success: false,
                    message: "Error verifying transaction",
                    error: error instanceof Error ? error.message : "Unknown error"
                });
            }

            // Update transaction record
            const updatedTransaction = await (prisma).transaction.update({
                where: { id: transaction.id },
                data: { status: (verificationResult).success ? "COMPLETED" : "FAILED",
                    verificationMethod: `BLOCKCHAIN_${network}`,
                    verificationData: JSON.stringify({
                        ...JSON.parse((transaction).verificationData || '{}'),
                        txHash,
                        fromAddress: (verificationResult).fromAddress || fromAddress,
                        toAddress: (verificationResult).toAddress || toAddress || paymentMethod?.address,
                        amount: (verificationResult).amount || parseFloat((amount).toString()),
                        currency,
                        timestamp: (verificationResult).timestamp || Date.now(),
                        confirmations: (verificationResult).confirmations ?? 0,
                        status: (verificationResult).success ? "success" : "failed",
                        blockNumber: (verificationResult).blockNumber,
                        fee: (verificationResult).fee
                    })
                }
            });

            // Create verification record
            const verification = await (prisma).verification.create({
                data: { transactionId: transaction.id,
                    merchantId: merchant.id,
                    method: `BLOCKCHAIN_${network}`,
                    status: (verificationResult).success ? (VerificationStatus).SUCCESS : (VerificationStatus).FAILED,
                    data: JSON.stringify({
                        txHash,
                        fromAddress: (verificationResult).fromAddress || fromAddress,
                        toAddress: (verificationResult).toAddress || toAddress || paymentMethod?.address,
                        amount: (verificationResult).amount || parseFloat((amount).toString()),
                        currency,
                        network,
                        timestamp: (verificationResult).timestamp || Date.now(),
                        confirmations: (verificationResult).confirmations ?? 0,
                        blockNumber: (verificationResult).blockNumber,
                        fee: (verificationResult).fee
                    })
                }
            });

            // Emit verification event
            (verificationEvents).emit('verification', {
                transactionId: transaction.id,
                merchantId: merchant.id,
                status: (verificationResult).success ? (VerificationStatus).SUCCESS : (VerificationStatus).FAILED,
                method: `BLOCKCHAIN_${network}`,
                timestamp: new Date()
            });

            return res.status(200).json({
                success: true,
                message: (verificationResult).success ? "Transaction verified successfully" : "Transaction verification failed",
                data: { transaction: updatedTransaction,
                    verification,
                    verificationResult
                }
            });
        } catch(error) {
            (logger).error(`Error in verifyBlockchainTransaction: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return res.status(500).json({
                success: false,
                message: "Internal server error",
                error: error instanceof Error ? error.message : "Unknown error"
            });
        }
    }

    /**
     * Verify a Binance transaction
     */
    public verifyBinanceTransaction = async (req: Request, res: Response) => {
        try {
            const {
                paymentId,
                merchantId,
                paymentMethodId,
                amount,
                currency,
                txHash,
                toAddress,
                apiKey,
                secretKey,
                metadata
            } = req.body;

            // Validate required fields
            if (!paymentId || !merchantId || !amount || !currency || !txHash) {
                return res.status(400).json({
                    success: false,
                    message: "Missing required fields"
                });
            }

            // Get merchant
            const merchant = await (prisma).merchant.findUnique({
                where: { id: merchantId }
            });

            if (!merchant) {
                return res.status(404).json({
                    success: false,
                    message: "Merchant not found"
                });
            }

            // Get transaction
            const transaction = await (prisma).transaction.findUnique({
                where: { id: paymentId }
            });

            if (!transaction) {
                return res.status(404).json({
                    success: false,
                    message: "Transaction not found"
                });
            }

            // Get payment method
            const paymentMethod = paymentMethodId ? await (prisma).paymentMethod.findUnique({
                where: { id: paymentMethodId }
            }) : null;

            const binanceApiKey = apiKey || paymentMethod?.apiKey;
            const binanceSecretKey = secretKey || paymentMethod?.secretKey;

            if (!binanceApiKey || !binanceSecretKey) {
                return res.status(400).json({
                    success: false,
                    message: "Binance API credentials are required"
                });
            }

            const verificationResult = await this.binanceApiService.verifyTRC20Transaction(
                txHash,
                toAddress || (paymentMethod?.address as string),
                parseFloat((amount).toString()),
                currency,
                binanceApiKey,
                binanceSecretKey
            );

            // Update transaction record
            const updatedTransaction = await (prisma).transaction.update({
                where: { id: transaction.id },
                data: { status: (verificationResult).success ? "COMPLETED" : "FAILED",
                    verificationMethod: "BINANCE_TRC20",
                    verificationData: JSON.stringify({
                        ...JSON.parse((transaction).verificationData || '{}'),
                        txHash,
                        fromAccount: (verificationResult).fromAddress,
                        toAccount: (verificationResult).toAddress || toAddress || paymentMethod?.address,
                        amount: (verificationResult).amount || parseFloat((amount).toString()),
                        currency,
                        timestamp: (verificationResult).timestamp || Date.now(),
                        status: (verificationResult).success ? "success" : "failed"
                    })
                }
            });

            // Create verification record
            const verification = await (prisma).verification.create({
                data: { transactionId: transaction.id,
                    merchantId: merchant.id,
                    method: "BINANCE_TRC20",
                    status: (verificationResult).success ? (VerificationStatus).SUCCESS : (VerificationStatus).FAILED,
                    data: JSON.stringify({
                        txHash,
                        fromAddress: (verificationResult).fromAddress,
                        toAddress: (verificationResult).toAddress || toAddress || paymentMethod?.address,
                        amount: (verificationResult).amount || parseFloat((amount).toString()),
                        currency,
                        timestamp: (verificationResult).timestamp || Date.now(),
                        status: (verificationResult).success ? "success" : "failed"
                    })
                }
            });

            // Emit verification event
            (verificationEvents).emit('verification', {
                transactionId: transaction.id,
                merchantId: merchant.id,
                status: (verificationResult).success ? (VerificationStatus).SUCCESS : (VerificationStatus).FAILED,
                method: "BINANCE_TRC20",
                timestamp: new Date()
            });

            return res.status(200).json({
                success: true,
                message: (verificationResult).success ? "Transaction verified successfully" : "Transaction verification failed",
                data: { transaction: updatedTransaction,
                    verification,
                    verificationResult
                }
            });
        } catch(error) {
            (logger).error(`Error in verifyBinanceTransaction: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return res.status(500).json({
                success: false,
                message: "Internal server error",
                error: error instanceof Error ? error.message : "Unknown error"
            });
        }
    }
}

export default new BlockchainVerificationController();