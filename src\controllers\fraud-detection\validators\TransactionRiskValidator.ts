/**
 * Transaction Risk Validator
 * 
 * Focused validator for transaction risk assessment operations.
 */

import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import { AssessTransactionRiskRequest, ValidationError } from '../types/FraudDetectionControllerTypes';
import { BaseValidator as ImportedBaseValidator } from './BaseValidator';

/**
 * Validator for transaction risk assessment
 */
export class TransactionRiskValidator extends BaseValidator {
  
  /**
   * Validate transaction risk assessment request
   */
  validateAssessTransactionRisk(data): AssessTransactionRiskRequest {
    const errors: ValidationError[] = [];

    if (!data.transactionId) {
      (errors as any).push({ field: 'transactionId', message: 'Transaction ID is required' });
    } else if (typeof data.transactionId !== 'string' || !this.isValidUUID((data as any).transactionId)) {
      (errors as any).push({ field: 'transactionId', message: 'Invalid transaction ID format', value: (data as any).transactionId });
    }

    if (!data.ipAddress) {
      (errors as any).push({ field: 'ipAddress', message: 'IP address is required' });
    } else if (typeof data.ipAddress !== 'string' || !this.isValidIPAddress((data as any).ipAddress)) {
      (errors as any).push({ field: 'ipAddress', message: 'Invalid IP address format', value: (data as any).ipAddress });
    }

    if ((data as any).userAgent !== undefined && typeof data.userAgent !== 'string') {
      (errors as any).push({ field: 'userAgent', message: 'User agent must be a string', value: (data as any).userAgent });
    }

    if ((data as any).deviceId !== undefined && typeof data.deviceId !== 'string') {
      (errors as any).push({ field: 'deviceId', message: 'Device ID must be a string', value: (data as any).deviceId });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors }
      });
    }

    return {
      transactionId: (data as any).transactionId,
      ipAddress: (data as any).ipAddress,
      userAgent: (data as any).userAgent ?? 'Unknown',
      deviceId: (data as any).deviceId ?? 'Unknown'
    };
  }

  /**
   * Validate transaction ID parameter
   */
  validateTransactionId(transactionId: string): string {
    if (!transactionId) {
      throw new AppError({
        message: 'Transaction ID is required',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).MISSING_REQUIRED_FIELD
      });
    }

    if (!this.isValidUUID(transactionId)) {
      throw new AppError({
        message: 'Transaction ID must be a valid UUID',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT
      });
    }

    return transactionId;
  }
}
