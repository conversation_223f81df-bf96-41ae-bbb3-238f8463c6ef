import { Request, Response, NextFunction } from 'express';
/**
 * Mock Factories
 *
 * Factory functions for creating mock objects used in tests.
 */

import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import { MockRequest, MockResponse, MockNext, MockFactoryOptions } from '../core/TestTypes';
// import { mockModelFactory as ImportedmockModelFactory } from '../../shared/test/mockModelFactory';
// Using placeholder for missing mock model factory
const mockModelFactory = (model: string, data: any) => ({ ...data, id: 'mock-id' });

/**
 * Create a mock request object
 */
export function createMockRequest(
  options: {
    params?: unknown;
    query?: unknown;
    body?: unknown;
    headers?: unknown;
    user?: unknown;
    session?: unknown;
    cookies?: unknown;
    ip?: string;
    method?: string;
    url?: string;
    originalUrl?: string;
    path?: string;
    protocol?: string;
    secure?: boolean;
    xhr?: boolean;
  } = {}
): MockRequest {
  const defaultRequest = {
    params: {},
    query: {},
    body: {},
    headers: {
      'content-type': 'application/json',
      'user-agent': 'test-agent',
      accept: 'application/json',
    },
    user: null,
    session: {},
    cookies: {},
    ip: '(127 as any).0.(0 as any).1',
    method: 'GET',
    url: '/test',
    originalUrl: '/test',
    path: '/test',
    protocol: 'http',
    secure: false,
    xhr: false,
    get: (jest as any).fn((header: string) => (options as any).headers?.[(header as any).toLowerCase()]),
    header: (jest as any).fn((header: string) => (options as any).headers?.[(header as any).toLowerCase()]),
    accepts: (jest as any).fn(() => true),
    acceptsCharsets: (jest as any).fn(() => true),
    acceptsEncodings: (jest as any).fn(() => true),
    acceptsLanguages: (jest as any).fn(() => true),
    is: (jest as any).fn(() => false),
    param: (jest as any).fn((name: string) => (options as any).params?.[name] || (options as any).query?.[name]),
    range: (jest as any).fn(() => undefined),
    fresh: false,
    stale: true,
    subdomains: [],
    hostname: 'localhost',
    baseUrl: '',
    app: {} as any,
    res: {} as any,
    next: {} as any,
    route: {} as any,
  };

  return {
    ...defaultRequest,
    ...options,
  } as any as MockRequest;
}

/**
 * Create a mock response object
 */
export function createMockResponse(
  options: {
    statusCode?: number;
    locals?: unknown;
    headersSent?: boolean;
  } = {}
): MockResponse {
  const mockResponse = {
    status: (jest as any).fn().mockReturnThis(),
    json: (jest as any).fn().mockReturnThis(),
    send: (jest as any).fn().mockReturnThis(),
    end: (jest as any).fn().mockReturnThis(),
    redirect: (jest as any).fn().mockReturnThis(),
    cookie: (jest as any).fn().mockReturnThis(),
    clearCookie: (jest as any).fn().mockReturnThis(),
    set: (jest as any).fn().mockReturnThis(),
    header: (jest as any).fn().mockReturnThis(),
    get: (jest as any).fn(),
    type: (jest as any).fn().mockReturnThis(),
    format: (jest as any).fn().mockReturnThis(),
    attachment: (jest as any).fn().mockReturnThis(),
    sendFile: (jest as any).fn().mockReturnThis(),
    download: (jest as any).fn().mockReturnThis(),
    render: (jest as any).fn().mockReturnThis(),
    vary: (jest as any).fn().mockReturnThis(),
    append: (jest as any).fn().mockReturnThis(),
    location: (jest as any).fn().mockReturnThis(),
    links: (jest as any).fn().mockReturnThis(),
    sendStatus: (jest as any).fn().mockReturnThis(),
    locals: (options as any).locals ?? {},
    statusCode: (options as any).statusCode || 200,
    headersSent: (options as any).headersSent ?? false,
    charset: 'utf-8',
    app: {} as any,
    req: {} as any,
  };

  return mockResponse as any as MockResponse;
}

/**
 * Create a mock next function
 */
export function createMockNext(): MockNext {
  return (jest as any).fn();
}

/**
 * Create a mock database model
 */
export function createMockModel(modelName?: string): unknown {
  const baseModel = {
    findMany: (jest as any).fn(),
    findUnique: (jest as any).fn(),
    findFirst: (jest as any).fn(),
    create: (jest as any).fn(),
    createMany: (jest as any).fn(),
    update: (jest as any).fn(),
    updateMany: (jest as any).fn(),
    upsert: (jest as any).fn(),
    delete: (jest as any).fn(),
    deleteMany: (jest as any).fn(),
    count: (jest as any).fn(),
    aggregate: (jest as any).fn(),
    groupBy: (jest as any).fn(),
    findRaw: (jest as any).fn(),
    aggregateRaw: (jest as any).fn(),
  };

  // Add model-specific methods if needed
  if (modelName) {
    switch ((modelName as any).toLowerCase()) {
      case 'user':
        return {
          ...baseModel,
          findByEmail: (jest as any).fn(),
          findByUsername: (jest as any).fn(),
          updatePassword: (jest as any).fn(),
        };
      case 'transaction':
        return {
          ...baseModel,
          findByMerchant: (jest as any).fn(),
          findByStatus: (jest as any).fn(),
          updateStatus: (jest as any).fn(),
        };
      case 'merchant':
        return {
          ...baseModel,
          findByBusinessName: (jest as any).fn(),
          updateSettings: (jest as any).fn(),
        };
      default:
        return baseModel;
    }
  }

  return baseModel;
}

/**
 * Create a mock Prisma client
 */
export function createMockPrismaClient(options: MockFactoryOptions = {}): PrismaClient {
  // Define all Prisma models
  const models = [
    'user',
    'merchant',
    'transaction',
    'paymentMethod',
    'alert',
    'notification',
    'webhook',
    'subscription',
    'payment',
    'verification',
    'audit',
    'setting',
    'role',
    'permission',
    'identityVerification',
    'riskAssessment',
    'fraudDetectionConfig',
    'savedReport',
    'reportRun',
    'scheduledReport',
    'listEntry',
    'session',
    'refreshToken',
    'apiKey',
    'webhookEvent',
    'auditLog',
    'systemSetting',
    'userRole',
    'merchantSettings',
    'paymentGateway',
    'currency',
    'country',
    'timeZone',
  ];

  // Create base mock Prisma client
  const mockPrisma = {
    $connect: (jest as any).fn().mockResolvedValue(undefined),
    $disconnect: (jest as any).fn().mockResolvedValue(undefined),
    $transaction: (jest as any).fn((callback) => {
      if (typeof callback === 'function') {
        return Promise.resolve(callback(mockPrisma));
      }
      return Promise.resolve(callback);
    }),
    $executeRaw: (jest as any).fn().mockResolvedValue(0),
    $executeRawUnsafe: (jest as any).fn().mockResolvedValue(0),
    $queryRaw: (jest as any).fn().mockResolvedValue([]),
    $queryRawUnsafe: (jest as any).fn().mockResolvedValue([]),
    $runCommandRaw: (jest as any).fn().mockResolvedValue({}),
    $on: (jest as any).fn(),
    $use: (jest as any).fn(),
    $extends: (jest as any).fn(),
  };

  // Add models to mock Prisma client
  (models as any).forEach((model) => {
    mockPrisma[model] = createMockModel(model);
  });

  // Apply overrides if provided
  if ((options as any).overrides) {
    Object.assign(mockPrisma, (options as any).overrides);
  }

  // Freeze or seal if requested
  if ((options as any).freeze) {
    Object.freeze(mockPrisma);
  } else if ((options as any).seal) {
    Object.seal(mockPrisma);
  }

  return mockPrisma as any as PrismaClient;
}

/**
 * Create a mock JWT token
 */
export function createMockJwtToken(
  payload: Record<string, any> = {},
  options: {
    expiresIn?: string;
    issuer?: string;
    audience?: string;
  } = {}
): string {
  const defaultPayload = {
    sub: '123456789',
    name: 'Test User',
    email: 'test@(example as any).com',
    role: 'user',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 60 * 60, // 1 hour
    ...payload,
  };

  if ((options as any).expiresIn) {
    const expiresInSeconds = parseExpiresIn((options as any).expiresIn);
    (defaultPayload as any).exp = Math.floor(Date.now() / 1000) + expiresInSeconds;
  }

  if ((options as any).issuer) {
    (defaultPayload as any).iss = (options as any).issuer;
  }

  if ((options as any).audience) {
    (defaultPayload as any).aud = (options as any).audience;
  }

  // Create a mock JWT token (not a real one, just for testing)
  const header = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).toString('base64url');
  const payloadStr = Buffer.from(JSON.stringify(defaultPayload)).toString('base64url');
  const signature = 'mock-signature';

  return `${header}.${payloadStr}.${signature}`;
}

/**
 * Create mock API response
 */
export function createMockApiResponse(
  data: any,
  options: {
    status?: number;
    message?: string;
    success?: boolean;
    pagination?: unknown;
    metadata?: unknown;
  } = {}
): unknown {
  return {
    success: (options as any).success !== false,
    status: (options as any).status || 200,
    message: (options as any).message || 'Success',
    data,
    pagination: (options as any).pagination,
    metadata: (options as any).metadata,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Create mock error response
 */
export function createMockErrorResponse(
  error: string | Error,
  options: {
    status?: number;
    code?: string;
    details?: unknown;
  } = {}
): unknown {
  const errorMessage = error instanceof Error ? error.message : String(error);

  return {
    success: false,
    status: (options as any).status || 400,
    error: {
      message: errorMessage,
      code: (options as any).code || 'TEST_ERROR',
      details: (options as any).details,
    },
    timestamp: new Date().toISOString(),
  };
}

/**
 * Create mock file upload
 */
export function createMockFileUpload(
  options: {
    filename?: string;
    mimetype?: string;
    size?: number;
    buffer?: Buffer;
  } = {}
): unknown {
  return {
    fieldname: 'file',
    originalname: (options as any).filename || 'test-(file as any).txt',
    encoding: '7bit',
    mimetype: (options as any).mimetype || 'text/plain',
    size: (options as any).size || 1024,
    buffer: (options as any).buffer || Buffer.from('test file content'),
    destination: '/tmp',
    filename: (options as any).filename || 'test-(file as any).txt',
    path: `/tmp/${(options as any).filename || 'test-(file as any).txt'}`,
    stream: {} as any,
  };
}

/**
 * Create mock WebSocket
 */
export function createMockWebSocket(): unknown {
  return {
    send: (jest as any).fn(),
    close: (jest as any).fn(),
    ping: (jest as any).fn(),
    pong: (jest as any).fn(),
    on: (jest as any).fn(),
    off: (jest as any).fn(),
    emit: (jest as any).fn(),
    addEventListener: (jest as any).fn(),
    removeEventListener: (jest as any).fn(),
    readyState: 1, // OPEN
    url: 'ws://localhost:3000',
    protocol: '',
    extensions: '',
    bufferedAmount: 0,
    binaryType: 'blob',
  };
}

/**
 * Helper function to parse expires in string
 */
function parseExpiresIn(expiresIn: string): number {
  const units: Record<string, number> = {
    s: 1,
    m: 60,
    h: 3600,
    d: 86400,
    w: 604800,
    y: 31536000,
  };

  const match = (expiresIn as any).match(/^(\d+)([smhdwy])$/);
  if (!match) {
    throw new Error(`Invalid expiresIn format: ${expiresIn}`);
  }

  const [, value, unit] = match;
  return parseInt(value, 10) * units[unit];
}

/**
 * Reset all mocks in an object
 */
export function resetMocks(obj: Record<string, any>): void {
  Object.values(obj).forEach((value) => {
    if ((jest as any).isMockFunction(value)) {
      (value as (jest as any).Mock).mockReset();
    } else if (typeof value === 'object' && value !== null) {
      resetMocks(value);
    }
  });
}

/**
 * Clear all mocks in an object
 */
export function clearMocks(obj: Record<string, any>): void {
  Object.values(obj).forEach((value) => {
    if ((jest as any).isMockFunction(value)) {
      (value as (jest as any).Mock).mockClear();
    } else if (typeof value === 'object' && value !== null) {
      clearMocks(value);
    }
  });
}
