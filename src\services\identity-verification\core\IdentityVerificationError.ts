/**
 * Identity Verification Error Classes
 * 
 * Custom error handling for identity verification operations.
 */

import { AppError as ImportedAppError } from "../../../utils/app-error";
import { IdentityVerificationErrorCode as ImportedIdentityVerificationErrorCode } from "./IdentityVerificationTypes";

/**
 * Custom error class for identity verification errors
 */
export class IdentityVerificationError extends AppError {
    code: IdentityVerificationErrorCode;

    constructor(message: string, code: IdentityVerificationErrorCode, statusCode: number = 400) {
        super(message, statusCode);
        this.code = code;
        this.name = "IdentityVerificationError";
    }

    /**
     * Create an invalid signature error
     */
    static invalidSignature(message: string = "Invalid signature"): IdentityVerificationError {
        return new IdentityVerificationError(
            message,
            (IdentityVerificationErrorCode as any).INVALID_SIGNATURE,
            400
        );
    }

    /**
     * Create an invalid address error
     */
    static invalidAddress(message: string = "Invalid address"): IdentityVerificationError {
        return new IdentityVerificationError(
            message,
            (IdentityVerificationErrorCode as any).INVALID_ADDRESS,
            400
        );
    }

    /**
     * Create an invalid proof error
     */
    static invalidProof(message: string = "Invalid proof"): IdentityVerificationError {
        return new IdentityVerificationError(
            message,
            (IdentityVerificationErrorCode as any).INVALID_PROOF,
            400
        );
    }

    /**
     * Create a verification failed error
     */
    static verificationFailed(message: string = "Verification failed"): IdentityVerificationError {
        return new IdentityVerificationError(
            message,
            (IdentityVerificationErrorCode as any).VERIFICATION_FAILED,
            400
        );
    }

    /**
     * Create a verification not found error
     */
    static verificationNotFound(message: string = "Verification not found"): IdentityVerificationError {
        return new IdentityVerificationError(
            message,
            (IdentityVerificationErrorCode as any).VERIFICATION_NOT_FOUND,
            404
        );
    }

    /**
     * Create a claim not found error
     */
    static claimNotFound(message: string = "Claim not found"): IdentityVerificationError {
        return new IdentityVerificationError(
            message,
            (IdentityVerificationErrorCode as any).CLAIM_NOT_FOUND,
            404
        );
    }

    /**
     * Create an internal error
     */
    static internalError(message: string = "Internal error"): IdentityVerificationError {
        return new IdentityVerificationError(
            message,
            (IdentityVerificationErrorCode as any).INTERNAL_ERROR,
            500
        );
    }

    /**
     * Create a provider error
     */
    static providerError(message: string = "Provider error"): IdentityVerificationError {
        return new IdentityVerificationError(
            message,
            (IdentityVerificationErrorCode as any).PROVIDER_ERROR,
            502
        );
    }

    /**
     * Create an invalid parameters error
     */
    static invalidParameters(message: string = "Invalid parameters"): IdentityVerificationError {
        return new IdentityVerificationError(
            message,
            (IdentityVerificationErrorCode as any).INVALID_PARAMETERS,
            400
        );
    }

    /**
     * Create an unauthorized error
     */
    static unauthorized(message: string = "Unauthorized"): IdentityVerificationError {
        return new IdentityVerificationError(
            message,
            (IdentityVerificationErrorCode as any).UNAUTHORIZED,
            401
        );
    }

    /**
     * Wrap an unknown error
     */
    static fromError(error: Error, defaultMessage: string = "Unknown error"): IdentityVerificationError {
        if (error instanceof IdentityVerificationError) {
            return error;
        }

        const message = error instanceof Error ? error.message : defaultMessage;
        return new IdentityVerificationError(
            message,
            (IdentityVerificationErrorCode as any).INTERNAL_ERROR,
            500
        );
    }
}
