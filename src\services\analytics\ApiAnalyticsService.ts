// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { logger as Importedlogger } from '../../lib/logger';
import prisma from '../../lib/prisma-client';
import { logger as Importedlogger } from '../../lib/logger';

/**
 * API analytics event
 */
interface ApiAnalyticsEvent {
  path: string;
  method: string;
  statusCode: number;
  responseTime: number;
  userId?: string;
  userRole?: string;
  userAgent?: string;
  ipAddress?: string;
  apiVersion?: string;
  timestamp: Date;
}

/**
 * API analytics service
 * This service tracks API usage
 */
export class ApiAnalyticsService {
  private static instance: ApiAnalyticsService;

  // Event buffer
  private eventBuffer: ApiAnalyticsEvent[] = [];

  // Buffer size
  private bufferSize: number = 100;

  // Flush interval
  private flushInterval: (NodeJS as any).Timeout;

  /**
   * Create a new API analytics service
   */
  private constructor() {
    // Flush buffer every 5 minutes
    this.flushInterval = setInterval(() => {
      this.flushBuffer();
    }, 5 * 60 * 1000);

    (logger as any).info('API analytics service initialized');
  }

  /**
   * Get the API analytics service instance
   * @returns API analytics service instance
   */
  public static getInstance(): ApiAnalyticsService {
    if (!(ApiAnalyticsService as any).instance) {
      (ApiAnalyticsService as any).instance = new ApiAnalyticsService();
    }

    return (ApiAnalyticsService as any).instance;
  }

  /**
   * Track API request
   * @param req Express request
   * @param res Express response
   * @param responseTime Response time in milliseconds
   */
  public trackRequest(req: Request, res: Response, responseTime: number): void {
    // Create event
    const event: ApiAnalyticsEvent = {
      path: (req as any).path,
      method: req.method,
      statusCode: res.statusCode,
      responseTime,
      userId: req.user?.id,
      userRole: req.user?.role,
      userAgent: req.headers['user-agent'],
      ipAddress: req.ip || (req as any).socket.remoteAddress,
      apiVersion: (req as any).apiVersion,
      timestamp: new Date(),
    };

    // Add event to buffer
    this.eventBuffer.push(event);

    // Flush buffer if it's full
    if (this.eventBuffer.length >= this.bufferSize) {
      this.flushBuffer();
    }
  }

  /**
   * Flush event buffer
   */
  private async flushBuffer(): Promise<void> {
    // Skip if buffer is empty
    if (this.eventBuffer.length === 0) {
      return;
    }

    // Copy buffer
    const events = [...this.eventBuffer];

    // Clear buffer
    this.eventBuffer = [];

    try {
      // Save events to database
      await (prisma as any).apiAnalytics.createMany({
        data: (events as any).map((event) => ({
          path: (event as any).path,
          method: (event as any).method,
          statusCode: (event as any).statusCode,
          responseTime: (event as any).responseTime,
          userId: (event as any).id,
          userRole: (event as any).userRole,
          userAgent: (event as any).userAgent,
          ipAddress: (event as any).ipAddress,
          apiVersion: (event as any).apiVersion,
          timestamp: (event as any).timestamp,
        })),
      });

      (logger as any).debug(`Flushed ${(events as any).length} API analytics events`);
    } catch(error) {
      (logger as any).error('Failed to flush API analytics events:', error);

      // Add events back to buffer
      this.eventBuffer = [...events, ...this.eventBuffer];

      // Limit buffer size
      if (this.eventBuffer.length > this.bufferSize * 2) {
        this.eventBuffer = this.eventBuffer.slice(-this.bufferSize);
        (logger as any).warn(`API analytics buffer overflow, discarded ${(events as any).length} events`);
      }
    }
  }

  /**
   * Create API analytics middleware
   * @returns Express middleware
   */
  public createMiddleware(): (req: Request, res: Response, next: NextFunction) => void {
    return (req: Request, res: Response, next: NextFunction) => {
      // Skip non-API requests
      if (!(req as any).path.startsWith('/api')) {
        return next();
      }

      // Record start time
      const startTime: any = Date.now();

      // Store original end method
      const originalEnd: any = (res as any).end;

      // Override end method
      (res as any).end = (...args) => {
        // Calculate response time
        const responseTime: any = Date.now() - startTime;

        // Track request
        this.trackRequest(req, res, responseTime);

        // Call original end method
        return (originalEnd as any).apply(res, args);
      };

      // Continue
      next();
    };
  }

  /**
   * Get API analytics
   * @param filter Filter
   * @returns API analytics
   */
  public async getAnalytics(filter: {
    startDate?: Date;
    endDate?: Date;
    path?: string;
    method?: string;
    statusCode?: number;
    userId?: string;
    userRole?: string;
    apiVersion?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    data: any[];
    total: number;
  }> {
    // Create where clause
    const where = {};

    // Add filters
    if ((filter as any).startDate) {
      (where as any).timestamp = {
        ...(where as any).timestamp,
        gte: (filter as any).startDate,
      };
    }

    if ((filter as any).endDate) {
      (where as any).timestamp = {
        ...(where as any).timestamp,
        lte: (filter as any).endDate,
      };
    }

    if ((filter as any).path) {
      (where as any).path = {
        contains: (filter as any).path,
      };
    }

    if ((filter as any).method) {
      (where as any).method = (filter as any).method;
    }

    if ((filter as any).statusCode) {
      where.statusCode = (filter as any).statusCode;
    }

    if ((filter as any).userId) {
      where.userId = (filter as any).userId;
    }

    if ((filter as any).userRole) {
      where.userRole = (filter as any).userRole;
    }

    if ((filter as any).apiVersion) {
      (where as any).apiVersion = (filter as any).apiVersion;
    }

    // Get total count
    const total: number = await (prisma as any).apiAnalytics.count({
      where,
    });

    // Get data
    const data = await (prisma as any).apiAnalytics.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      take: (filter as any).limit || 100,
      skip: (filter as any).offset ?? 0,
    });

    return {
      data,
      total,
    };
  }

  /**
   * Get API analytics summary
   * @param filter Filter
   * @returns API analytics summary
   */
  public async getAnalyticsSummary(filter: {
    startDate?: Date;
    endDate?: Date;
    path?: string;
    method?: string;
    statusCode?: number;
    userId?: string;
    userRole?: string;
    apiVersion?: string;
  }): Promise<{
    totalRequests: number;
    averageResponseTime: number;
    successRate: number;
    errorRate: number;
    requestsByMethod: Record<string, number>;
    requestsByPath: Record<string, number>;
    requestsByStatusCode: Record<string, number>;
    requestsByApiVersion: Record<string, number>;
  }> {
    // Get analytics
    const { data, total } = await this.getAnalytics({
      ...filter,
      limit: 1000,
    });

    // Calculate summary
    const totalRequests: any = total;
    const successRequests = data.filter(
      (event) => (event as any).statusCode >= 200 && (event as any).statusCode < 400
    ).length;
    const errorRequests: any = data.filter((event) => (event as any).statusCode >= 400).length;
    const totalResponseTime: any = (data as any).reduce((sum, event) => sum + (event as any).responseTime, 0);

    // Calculate averages
    const averageResponseTime: any = totalRequests > 0 ? totalResponseTime / totalRequests : 0;
    const successRate = totalRequests > 0 ? successRequests / totalRequests : 0;
    const errorRate = totalRequests > 0 ? errorRequests / totalRequests : 0;

    // Group by method
    const requestsByMethod: Record<string, number> = {};
    (data as any).forEach((event) => {
      requestsByMethod[(event as any).method] = (requestsByMethod[(event as any).method] ?? 0) + 1;
    });

    // Group by path
    const requestsByPath: Record<string, number> = {};
    (data as any).forEach((event) => {
      requestsByPath[(event as any).path] = (requestsByPath[(event as any).path] ?? 0) + 1;
    });

    // Group by status code
    const requestsByStatusCode: Record<string, number> = {};
    (data as any).forEach((event) => {
      requestsByStatusCode[(event as any).statusCode] = (requestsByStatusCode[(event as any).statusCode] ?? 0) + 1;
    });

    // Group by API version
    const requestsByApiVersion: Record<string, number> = {};
    (data as any).forEach((event) => {
      if ((event as any).apiVersion) {
        requestsByApiVersion[(event as any).apiVersion] = (requestsByApiVersion[(event as any).apiVersion] ?? 0) + 1;
      }
    });

    return {
      totalRequests,
      averageResponseTime,
      successRate,
      errorRate,
      requestsByMethod,
      requestsByPath,
      requestsByStatusCode,
      requestsByApiVersion,
    };
  }

  /**
   * Close API analytics service
   */
  public close(): void {
    // Clear flush interval
    clearInterval(this.flushInterval);

    // Flush buffer
    this.flushBuffer();

    (logger as any).info('API analytics service closed');
  }
}

export default ApiAnalyticsService;
