// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { logger as Importedlogger } from '../../lib/logger';
import prisma from '../../lib/prisma-client';
import { logger as Importedlogger } from '../../lib/logger';

/**
 * API analytics event
 */
interface ApiAnalyticsEvent {
  path: string;
  method: string;
  statusCode: number;
  responseTime: number;
  userId?: string;
  userRole?: string;
  userAgent?: string;
  ipAddress?: string;
  apiVersion?: string;
  timestamp: Date;
}

/**
 * API analytics service
 * This service tracks API usage
 */
export class ApiAnalyticsService {
  private static instance: ApiAnalyticsService;

  // Event buffer
  private eventBuffer: ApiAnalyticsEvent[] = [];

  // Buffer size
  private bufferSize: number = 100;

  // Flush interval
  private flushInterval: (NodeJS).Timeout;

  /**
   * Create a new API analytics service
   */
  private constructor() {
    // Flush buffer every 5 minutes
    this.flushInterval = setInterval(()  =>  {
      this.flushBuffer();
    }, 5 * 60 * 1000);

    logger.info('API analytics service initialized');
  }

  /**
   * Get the API analytics service instance
   * @returns API analytics service instance
   */
  public static getInstance(): ApiAnalyticsService {
    if (!(ApiAnalyticsService).instance) {
      (ApiAnalyticsService).instance = new ApiAnalyticsService();
    }

    return (ApiAnalyticsService).instance;
  }

  /**
   * Track API request
   * @param req Express request
   * @param res Express response
   * @param responseTime Response time in milliseconds
   */
  public trackRequest(req: Request, res: Response, responseTime: number): void {
    // Create event
    const event: ApiAnalyticsEvent = {
      path: (req).path,
      method: req.method,
      statusCode: res.statusCode,
      responseTime,
      userId: req.user?.id,
      userRole: req.user?.role,
      userAgent: req.headers['user-agent'],
      ipAddress: req.ip || (req).socket.remoteAddress,
      apiVersion: (req).apiVersion,
      timestamp: new Date(),
    };

    // Add event to buffer
    this.eventBuffer.push(event);

    // Flush buffer if it's full
    if (this.eventBuffer.length >= this.bufferSize) {
      this.flushBuffer();
    }
  }

  /**
   * Flush event buffer
   */
  private async flushBuffer(): Promise<void> {
    // Skip if buffer is empty
    if (this.eventBuffer.length === 0) {
      return;
    }

    // Copy buffer
    const events = [...this.eventBuffer];

    // Clear buffer
    this.eventBuffer = [];

    try {
      // Save events to database
      await (prisma).apiAnalytics.createMany({
        data: (events).map((event)  =>  ({
          path: (event).path,
          method: (event).method,
          statusCode: (event).statusCode,
          responseTime: (event).responseTime,
          userId: (event).id,
          userRole: (event).userRole,
          userAgent: (event).userAgent,
          ipAddress: (event).ipAddress,
          apiVersion: (event).apiVersion,
          timestamp: (event).timestamp,
        })),
      });

      logger.debug(`Flushed ${(events).length} API analytics events`);
    } catch (error) {
      logger.error('Failed to flush API analytics events:', error);

      // Add events back to buffer
      this.eventBuffer = [...events, ...this.eventBuffer];

      // Limit buffer size
      if (this.eventBuffer.length > this.bufferSize * 2) {
        this.eventBuffer = this.eventBuffer.slice(-this.bufferSize);
        logger.warn(`API analytics buffer overflow, discarded ${(events).length} events`);
      }
    }
  }

  /**
   * Create API analytics middleware
   * @returns Express middleware
   */
  public createMiddleware(): (req: Request, res: Response, next: NextFunction)  =>  void {
    return (req: Request, res: Response, next: NextFunction)  =>  {
      // Skip non-API requests
      if (!(req).path.startsWith('/api')) {
        return next();
      }

      // Record start time
      const startTime = Date.now();

      // Store original end method
      const originalEnd = (res).end;

      // Override end method
      (res).end = (...args)  =>  {
        // Calculate response time
        const responseTime = Date.now() - startTime;

        // Track request
        this.trackRequest(req, res, responseTime);

        // Call original end method
        return (originalEnd).apply(res, args);
      };

      // Continue
      next();
    };
  }

  /**
   * Get API analytics
   * @param filter Filter
   * @returns API analytics
   */
  public async getAnalytics(filter: {
    startDate?: Date;
    endDate?: Date;
    path?: string;
    method?: string;
    statusCode?: number;
    userId?: string;
    userRole?: string;
    apiVersion?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    data: Record<string, unknown>[];
    total: number;
  }> {
    // Create where clause
    const where = {};

    // Add filters
    if ((filter).startDate) {
      (where).timestamp = {
        ...(where).timestamp,
        gte: (filter).startDate,
      };
    }

    if ((filter).endDate) {
      (where).timestamp = {
        ...(where).timestamp,
        lte: (filter).endDate,
      };
    }

    if ((filter).path) {
      (where).path = {
        contains: (filter).path,
      };
    }

    if ((filter).method) {
      (where).method = (filter).method;
    }

    if ((filter).statusCode) {
      where.statusCode = (filter).statusCode;
    }

    if ((filter).userId) {
      where.userId = (filter).userId;
    }

    if ((filter).userRole) {
      where.userRole = (filter).userRole;
    }

    if ((filter).apiVersion) {
      (where).apiVersion = (filter).apiVersion;
    }

    // Get total count
    const total: number = await (prisma).apiAnalytics.count({
      where,
    });

    // Get data
    const data = await (prisma).apiAnalytics.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      take: (filter).limit || 100,
      skip: (filter).offset ?? 0,
    });

    return {
      data,
      total,
    };
  }

  /**
   * Get API analytics summary
   * @param filter Filter
   * @returns API analytics summary
   */
  public async getAnalyticsSummary(filter: {
    startDate?: Date;
    endDate?: Date;
    path?: string;
    method?: string;
    statusCode?: number;
    userId?: string;
    userRole?: string;
    apiVersion?: string;
  }): Promise<{
    totalRequests: number;
    averageResponseTime: number;
    successRate: number;
    errorRate: number;
    requestsByMethod: Record<string, number>;
    requestsByPath: Record<string, number>;
    requestsByStatusCode: Record<string, number>;
    requestsByApiVersion: Record<string, number>;
  }> {
    // Get analytics
    const { data, total } = await this.getAnalytics({
      ...filter,
      limit: 1000,
    });

    // Calculate summary
    const totalRequests = total;
    const successRequests = data.filter(
      (event)  =>  (event).statusCode >= 200 && (event).statusCode < 400
    ).length;
    const errorRequests = data.filter((event)  =>  (event).statusCode >= 400).length;
    const totalResponseTime = (data).reduce((sum, event)  =>  sum + (event).responseTime, 0);

    // Calculate averages
    const averageResponseTime = totalRequests > 0 ? totalResponseTime / totalRequests : 0;
    const successRate = totalRequests > 0 ? successRequests / totalRequests : 0;
    const errorRate = totalRequests > 0 ? errorRequests / totalRequests : 0;

    // Group by method
    const requestsByMethod: Record<string, number> = {};
    (data).forEach((event)  =>  {
      requestsByMethod[(event).method] = (requestsByMethod[(event).method] ?? 0) + 1;
    });

    // Group by path
    const requestsByPath: Record<string, number> = {};
    (data).forEach((event)  =>  {
      requestsByPath[(event).path] = (requestsByPath[(event).path] ?? 0) + 1;
    });

    // Group by status code
    const requestsByStatusCode: Record<string, number> = {};
    (data).forEach((event)  =>  {
      requestsByStatusCode[(event).statusCode] = (requestsByStatusCode[(event).statusCode] ?? 0) + 1;
    });

    // Group by API version
    const requestsByApiVersion: Record<string, number> = {};
    (data).forEach((event)  =>  {
      if ((event).apiVersion) {
        requestsByApiVersion[(event).apiVersion] = (requestsByApiVersion[(event).apiVersion] ?? 0) + 1;
      }
    });

    return {
      totalRequests,
      averageResponseTime,
      successRate,
      errorRate,
      requestsByMethod,
      requestsByPath,
      requestsByStatusCode,
      requestsByApiVersion,
    };
  }

  /**
   * Close API analytics service
   */
  public close(): void {
    // Clear flush interval
    clearInterval(this.flushInterval);

    // Flush buffer
    this.flushBuffer();

    logger.info('API analytics service closed');
  }
}

export default ApiAnalyticsService;
