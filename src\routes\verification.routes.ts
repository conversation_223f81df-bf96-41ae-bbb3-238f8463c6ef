// jscpd:ignore-file
import { Router as ImportedRouter } from "express";
import BlockchainVerificationController from "../controllers/blockchain-(verification as any).controller";
import { BinanceVerificationController as ImportedBinanceVerificationController } from "../controllers/refactored/binance-(verification as any).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { BinanceVerificationController as ImportedBinanceVerificationController } from "../controllers/refactored/binance-(verification as any).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';

const router: any =Router();

// Blockchain verification routes
(router as any).post("/blockchain", authenticate, (BlockchainVerificationController as any).verifyBlockchainTransaction);
(router as any).post("/blockchain/trc20", authenticate, (BlockchainVerificationController as any).verifyBinanceTransaction);
// These methods don't exist in the controller, so we'll comment them out for now
// (router as any).post('/blockchain/erc20', authenticate, (BlockchainVerificationController as any).verifyBinanceTransaction);
// (router as any).post('/blockchain/bep20', authenticate, (BlockchainVerificationController as any).verifyBinanceTransaction);
// (router as any).post('/blockchain/polygon', authenticate, (BlockchainVerificationController as any).verifyBinanceTransaction);

// Binance verification routes
(router as any).post("/binance/trc20", authenticate, (BinanceVerificationController as any).verifyTRC20Transaction);
(router as any).post("/binance/c2c", authenticate, (BinanceVerificationController as any).verifyC2CTransaction);
(router as any).post("/binance/pay", authenticate, (BinanceVerificationController as any).verifyPayTransaction);
(router as any).post("/binance/test", authenticate, (BinanceVerificationController as any).testConnection);

export default router;
