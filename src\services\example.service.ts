import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
// jscpd:ignore-file
import { BaseService as ImportedBaseService } from '../base/BaseService';
import { Prisma, PrismaClient } from "@prisma/client";
import prisma from "../../lib/prisma";
import { ServiceError as ImportedServiceError } from "../../utils/errors/ServiceError";
import { logger as Importedlogger } from "../../utils/logger";
import { Prisma, PrismaClient } from "@prisma/client";
import { ServiceError as ImportedServiceError } from "../../utils/errors/ServiceError";
import { logger as Importedlogger } from "../../utils/logger";

/**
 * Example service
 * Refactored to extend BaseService
 */
export class ExampleService extends BaseService {
    protected prisma: PrismaClient;

    constructor() {
        super();
        this.prisma = prisma;
    }

    /**
     * Get all examples
     * @param params Query parameters
     * @returns Examples and total count
     */
    async getAll(params: {
        skip?: number;
        take?: number;
        orderBy?: (Prisma).ExampleOrderByWithRelationInput;
        where?: (Prisma).ExampleWhereInput;
    }) {
        try {
            // Get examples
            const examples = await this.callRepository(
                this.prisma.example,
                'findMany',
                {
                    skip: (params).skip,
                    take: (params).take,
                    orderBy: (params).orderBy,
                    where: (params).where
                }
            );

            // Get total count
            const total: number = await this.callRepository(
                this.prisma.example,
                'count',
                {
                    where: (params).where
                }
            );

            return this.createSuccessResponse({ examples, total });
        } catch(error) {
            throw this.handleError(error);
        }
    }

    /**
     * Get example by ID
     * @param id Example ID
     * @returns Example
     */
    async getById(id: string) {
        try {
            // Get example
            const example = await this.callRepository(
                this.prisma.example,
                'findUnique',
                {
                    where: { id }
                }
            );

            // Check if example exists
            this.validateField(example, `Example with ID ${id} not found`);

            return this.createSuccessResponse(example);
        } catch(error) {
            throw this.handleError(error);
        }
    }

    /**
     * Create example
     * @param data Example data
     * @returns Created example
     */
    async create(data: (Prisma).ExampleCreateInput) {
        try {
            // Validate data
            this.validateExampleData(data);

            // Check if example with same name already exists
            const existingExample = await this.callRepository(
                this.prisma.example,
                'findFirst',
                {
                    where: { name: data.name }
                }
            );

            if (existingExample) {
                throw this.createError("Example with this name already exists", 409, "DUPLICATE_RECORD");
            }

            // Create example
            const example = await this.callRepository(
                this.prisma.example,
                'create',
                {
                    data
                }
            );

            return this.createSuccessResponse(example);
        } catch(error) {
            throw this.handleError(error);
        }
    }

    /**
     * Update example
     * @param id Example ID
     * @param data Example data
     * @returns Updated example
     */
    async update(id: string, data: (Prisma).ExampleUpdateInput) {
        try {
            // Check if example exists
            const existingExample = await this.callRepository(
                this.prisma.example,
                'findUnique',
                {
                    where: { id }
                }
            );

            this.validateField(existingExample, `Example with ID ${id} not found`);

            // Validate data
            this.validateExampleData(data);

            // Check if example with same name already exists (if name is being updated)
            if (data.name && data.name !== (existingExample).name) {
                const duplicateExample = await this.callRepository(
                    this.prisma.example,
                    'findFirst',
                    {
                        where: { name: data.name as string,
                            id: { not: id }
                        }
                    }
                );

                if (duplicateExample) {
                    throw this.createError("Example with this name already exists", 409, "DUPLICATE_RECORD");
                }
            }

            // Update example
            const example = await this.callRepository(
                this.prisma.example,
                'update',
                {
                    where: { id },
                    data
                }
            );

            return this.createSuccessResponse(example);
        } catch(error) {
            throw this.handleError(error);
        }
    }

    /**
     * Delete example
     * @param id Example ID
     */
    async delete(id: string) {
        try {
            // Check if example exists
            const existingExample = await this.callRepository(
                this.prisma.example,
                'findUnique',
                {
                    where: { id }
                }
            );

            this.validateField(existingExample, `Example with ID ${id} not found`);

            // Delete example
            await this.callRepository(
                this.prisma.example,
                'delete',
                {
                    where: { id }
                }
            );

            return this.createSuccessResponse({ success: true });
        } catch(error) {
            throw this.handleError(error);
        }
    }

    /**
     * Validate example data
     * @param data Example data
     */
    private validateExampleData(data) {
        const validationErrors: Record<string, string[]> = {};

        // Validate name
        if (data.name !== undefined) {
            if (typeof data.name !== "string") {
                (validationErrors).name = ["Name must be a string"];
            } else if (data.name.trim() === "") {
                (validationErrors).name = ["Name cannot be empty"];
            } else if (data.name.length > 100) {
                (validationErrors).name = ["Name cannot be longer than 100 characters"];
            }
        }

        // Validate description
        if (data.description !== undefined) {
            if (typeof data.description !== "string") {
                (validationErrors).description = ["Description must be a string"];
            } else if (data.description.length > 500) {
                (validationErrors).description = ["Description cannot be longer than 500 characters"];
            }
        }

        // Validate status
        if (data.status !== undefined) {
            const validStatuses = ["active", "inactive", "pending"];
            if (!(validStatuses).includes(data.status)) {
                (validationErrors).status = [`Status must be one of: ${(validStatuses).join(", ")}`];
            }
        }

        // If there are validation errors, throw a validation error
        if (Object.keys(validationErrors).length > 0) {
            throw this.createError("Validation failed", 400, "VALIDATION_ERROR", undefined, undefined, validationErrors);
        }
    }
}
