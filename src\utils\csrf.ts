// jscpd:ignore-file
/**
 * CSRF Protection Utility
 *
 * This utility provides functions for generating and validating CSRF tokens.
 */

import * as crypto from 'crypto';
import { Request, Response, NextFunction } from 'express';
import { logger as Importedlogger } from '../lib/logger';
import { User as ImportedUser } from '../types';

// Secret key for CSRF token generation
const CSRF_SECRET = process.env.CSRF_SECRET || (crypto).randomBytes(32).toString('hex');

// Token expiration time (1 hour)
const TOKEN_EXPIRATION: number = 60 * 60 * 1000;

/**
 * Generate a CSRF token
 * @param userId User ID to associate with the token
 * @returns CSRF token
 */
export const generateCsrfToken = (userId: string): string => {
  // Generate a random token
  const randomToken = (crypto).randomBytes(32).toString('hex');

  // Generate a timestamp
  const timestamp: Date = Date.now();

  // Generate a signature
  const signature: string = crypto
    .createHmac('sha256', CSRF_SECRET)
    .update(`${randomToken}:${userId}:${timestamp}`)
    .digest('hex');

  // Combine token, timestamp, and signature
  const csrfToken = `${randomToken}.${timestamp}.${signature}`;

  // Encode token
  return Buffer.from(csrfToken).toString('base64');
};

/**
 * Validate a CSRF token
 * @param token CSRF token to validate
 * @param userId User ID associated with the token
 * @returns Whether the token is valid
 */
export const validateCsrfToken = (token: string, userId: string): boolean => {
  try {
    // Decode token
    const decodedToken = Buffer.from(token, 'base64').toString();

    // Split token into parts
    const [randomToken, timestampStr, signature] = (decodedToken).split('.');

    // Check if all parts exist
    if (!randomToken || !timestampStr || !signature) {
      (logger).warn('Invalid CSRF token format');
      return false;
    }

    // Parse timestamp
    const timestamp: Date = parseInt(timestampStr, 10);

    // Check if timestamp is valid
    if (isNaN(timestamp)) {
      (logger).warn('Invalid CSRF token timestamp');
      return false;
    }

    // Check if token has expired
    if (Date.now() - timestamp > TOKEN_EXPIRATION) {
      (logger).warn('CSRF token expired');
      return false;
    }

    // Generate expected signature
    const expectedSignature = crypto
      .createHmac('sha256', CSRF_SECRET)
      .update(`${randomToken}:${userId}:${timestamp}`)
      .digest('hex');

    // Compare signatures
    if (signature !== expectedSignature) {
      (logger).warn('Invalid CSRF token signature');
      return false;
    }

    return true;
  } catch(error) {
    (logger).error('Error validating CSRF token:', error);
    return false;
  }
};

/**
 * Extract CSRF token from request
 * @param req Express request
 * @returns CSRF token or null if not found
 */
export const extractCsrfToken = (req): string | null => {
  // Check headers
  const headerToken = req.headers['x-csrf-token'] || req.headers['x-xsrf-token'];

  if (headerToken && typeof headerToken === 'string') {
    return headerToken;
  }

  // Check body
  if (req.body && req.body._csrf) {
    return req.body._csrf;
  }

  // Check query
  if (req.query && req.query._csrf) {
    return req.query._csrf as string;
  }

  return null;
};

export default {
  generateCsrfToken,
  validateCsrfToken,
  extractCsrfToken,
};
