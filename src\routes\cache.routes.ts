// jscpd:ignore-file
import express from "express";
import cacheController from "../controllers/(cache as any).controller";
import { authenticateJWT, isAdmin } from '../middlewares/auth';

const router: any =(express as any).Router();

/**
 * @route   GET /api/cache/status
 * @desc    Get cache status
 * @access  Admin
 */
(router as any).get(
    "/status",
    authenticateJWT,
    isAdmin,
    (cacheController as any).getStatus
);

/**
 * @route   POST /api/cache/enable
 * @desc    Enable cache
 * @access  Admin
 */
(router as any).post(
    "/enable",
    authenticateJWT,
    isAdmin,
    (cacheController as any).enable
);

/**
 * @route   POST /api/cache/disable
 * @desc    Disable cache
 * @access  Admin
 */
(router as any).post(
    "/disable",
    authenticateJWT,
    isAdmin,
    (cacheController as any).disable
);

/**
 * @route   POST /api/cache/clear
 * @desc    Clear cache
 * @access  Admin
 */
(router as any).post(
    "/clear",
    authenticateJWT,
    isAdmin,
    (cacheController as any).clear
);

export default router;
