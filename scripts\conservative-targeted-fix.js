#!/usr/bin/env node

/**
 * Conservative Targeted Fix Script
 * Only fixes specific, safe patterns to avoid introducing new errors
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎯 CONSERVATIVE TARGETED FIX SCRIPT');
console.log('===================================');

// Very conservative fixes - only the safest patterns
const conservativeFixes = {
    // Only fix obvious arrow function syntax issues
    '= >': '=>',
    '= > ': '=> ',
    ' = >': ' =>',
    ' = > ': ' => ',
    
    // Only fix obvious object syntax issues with clear patterns
    '{,': '{',
    ', }': ' }',
    '{ ,': '{ ',
    ' ,}': ' }',
    
    // Only fix the most common nullish coalescing patterns
    " || ''": " ?? ''",
    ' || ""': ' ?? ""',
    ' || 0': ' ?? 0',
    ' || false': ' ?? false',
    ' || true': ' ?? true',
    ' || null': ' ?? null',
    ' || undefined': ' ?? undefined',
    ' || []': ' ?? []',
    ' || {}': ' ?? {}'
};

function findAllTypeScriptFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'coverage'].includes(item)) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

function getErrorCount() {
    try {
        const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { encoding: 'utf8' });
        const errorMatches = output.match(/error TS/g) || [];
        return errorMatches.length;
    } catch (error) {
        const errorMatches = error.stdout.match(/error TS/g) || [];
        return errorMatches.length;
    }
}

function processFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let modifiedContent = content;
        let fixCount = 0;
        
        // Apply only the conservative string replacements
        for (const [oldPattern, newPattern] of Object.entries(conservativeFixes)) {
            const regex = new RegExp(escapeRegExp(oldPattern), 'g');
            const matches = modifiedContent.match(regex);
            if (matches) {
                modifiedContent = modifiedContent.replace(regex, newPattern);
                fixCount += matches.length;
            }
        }
        
        // Apply very conservative regex patterns
        const conservativePatterns = [
            // Only fix obvious arrow function issues
            [/(\w+)\s*=\s*>\s*/g, '$1 => '],
            
            // Only fix obvious logical OR patterns with simple values
            [/(\w+(?:\.\w+)*)\s\|\|\s('.*?'|".*?"|\d+|true|false|null|undefined|\[\]|\{\})/g, '$1 ?? $2'],
            
            // Only fix process.env patterns
            [/process\.env\.(\w+)\s\|\|\s('.*?'|".*?"|\d+)/g, 'process.env.$1 ?? $2'],
        ];
        
        for (const [pattern, replacement] of conservativePatterns) {
            const matches = modifiedContent.match(pattern);
            if (matches) {
                modifiedContent = modifiedContent.replace(pattern, replacement);
                fixCount += matches.length;
            }
        }
        
        if (fixCount > 0) {
            fs.writeFileSync(filePath, modifiedContent, 'utf8');
            return { filePath, fixCount };
        }
        
        return null;
    } catch (error) {
        return { filePath, error: error.message };
    }
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

async function main() {
    console.log('🔍 Scanning for TypeScript files...');
    
    const files = findAllTypeScriptFiles('./src');
    console.log(`📁 Found ${files.length} TypeScript files`);
    
    console.log('📊 Getting initial error count...');
    const initialErrors = getErrorCount();
    console.log(`🚨 Initial TypeScript errors: ${initialErrors}`);
    
    console.log('🚀 Starting conservative targeted fixes...');
    const startTime = Date.now();
    
    const results = [];
    let totalFixedIssues = 0;
    
    for (const file of files) {
        const result = processFile(file);
        if (result) {
            results.push(result);
            if (result.fixCount) {
                totalFixedIssues += result.fixCount;
            }
        }
    }
    
    console.log('📊 Getting final error count...');
    const finalErrors = getErrorCount();
    const totalErrorsFixed = initialErrors - finalErrors;
    
    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;
    
    console.log('\n🎯 CONSERVATIVE TARGETED FIX COMPLETE!');
    console.log('======================================');
    console.log(`⏱️  Processing time: ${processingTime.toFixed(2)} seconds`);
    console.log(`📁 Files processed: ${results.filter(r => !r.error).length}`);
    console.log(`❌ Files with errors: ${results.filter(r => r.error).length}`);
    console.log(`🔧 Total fixes applied: ${totalFixedIssues}`);
    console.log(`🚨 TypeScript errors before: ${initialErrors}`);
    console.log(`✅ TypeScript errors after: ${finalErrors}`);
    console.log(`🎯 Total errors fixed: ${totalErrorsFixed}`);
    
    if (totalErrorsFixed > 0) {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}%`);
        console.log('\n🎉 SUCCESS! Conservative fixes applied successfully!');
        console.log('🏆 Your application now has improved type safety!');
    } else if (totalErrorsFixed === 0) {
        console.log('📈 Success rate: 0.0% (no net change)');
        console.log('\n✨ No errors were fixed, but no new errors were introduced!');
    } else {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}% (negative - new errors introduced)`);
        console.log('\n⚠️  Some fixes may have introduced new errors. Consider reverting.');
    }
    
    const errorFiles = results.filter(r => r.error);
    if (errorFiles.length > 0) {
        console.log('\n❌ Files with processing errors:');
        errorFiles.forEach(({ filePath, error }) => {
            console.log(`   ${path.relative(process.cwd(), filePath)}: ${error}`);
        });
    }
}

main().catch(console.error);
