// jscpd:ignore-file
/**
 * Blockchain network
 */
export enum BlockchainNetwork {
  TRC20 = "trc20",
  ERC20 = "erc20",
  BEP20 = "bep20",
  POLYGON = "polygon",
}

/**
 * Blockchain transaction
 */
export interface BlockchainTransaction {
  txHash: string;
  network: BlockchainNetwork;
  fromAddress: string;
  toAddress: string;
  amount: number;
  currency: string;
  timestamp: number;
  confirmations: number;
  status: "success" | "pending" | "failed";
  blockNumber?: number;
  fee?: number;
}

/**
 * Binance transaction type
 */
export enum BinanceTransactionType {
  PAY = "pay",
  C2C = "c2c",
  TRC20 = "trc20",
}

/**
 * Binance transaction
 */
export interface BinanceTransaction {
  transactionId: string;
  type: BinanceTransactionType;
  fromAccount?: string;
  toAccount?: string;
  amount: number;
  currency: string;
  timestamp: number;
  status: "success" | "pending" | "failed";
  note?: string;
  orderNo?: string;
  merchantTradeNo?: string;
}
