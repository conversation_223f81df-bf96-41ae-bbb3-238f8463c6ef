// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';

/**
 * API error codes
 */
export enum ApiErrorCode {
  /**
   * Generic error
   */
  GENERIC_ERROR = 'GENERIC_ERROR',

  /**
   * Authentication error
   */
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',

  /**
   * Authorization error
   */
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',

  /**
   * Validation error
   */
  VALIDATION_ERROR = 'VALIDATION_ERROR',

  /**
   * Resource not found
   */
  NOT_FOUND = 'NOT_FOUND',

  /**
   * Duplicate resource
   */
  DUPLICATE = 'DUPLICATE',

  /**
   * Rate limit exceeded
   */
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',

  /**
   * Server error
   */
  SERVER_ERROR = 'SERVER_ERROR',

  /**
   * Database error
   */
  DATABASE_ERROR = 'DATABASE_ERROR',

  /**
   * External service error
   */
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',

  /**
   * Payment processing error
   */
  PAYMENT_ERROR = 'PAYMENT_ERROR',

  /**
   * Blockchain error
   */
  BLOCKCHAIN_ERROR = 'BLOCKCHAIN_ERROR',

  /**
   * Webhook error
   */
  WEBHOOK_ERROR = 'WEBHOOK_ERROR',

  /**
   * Notification error
   */
  NOTIFICATION_ERROR = 'NOTIFICATION_ERROR',
}

/**
 * Pagination information
 */
export interface PaginationInfo {
  /**
   * Current page number
   */
  page: number;

  /**
   * Number of items per page
   */
  limit: number;

  /**
   * Total number of items
   */
  total: number;

  /**
   * Total number of pages
   */
  totalPages: number;

  /**
   * Whether there is a next page
   */
  hasNext: boolean;

  /**
   * Whether there is a previous page
   */
  hasPrev: boolean;
}

/**
 * Standard API response format
 */
export interface ApiResponse<T = unknown> {
  /**
   * Whether the request was successful
   */
  success: boolean;

  /**
   * Response data
   */
  data?: T;

  /**
   * Error message if the request failed
   */
  error?: string;

  /**
   * Error code if the request failed
   */
  errorCode?: string;

  /**
   * Validation errors if the request failed due to validation
   */
  validationErrors?: Record<string, string[]>;

  /**
   * Informational message
   */
  message?: string;

  /**
   * Pagination information
   */
  pagination?: PaginationInfo;

  /**
   * Request metadata
   */
  meta?: Record<string, unknown>;

  /**
   * Request timestamp
   */
  timestamp?: string;

  /**
   * Request ID for tracking
   */
  requestId?: string;
}

/**
 * Extend Express Response interface to include standardized API response methods
 */
declare global {
  namespace Express {
    interface Response {
      /**
       * Send a success response
       * @param data Response data
       * @param message Success message
       * @param meta Response metadata
       */
      success<T>(data?: T, message?: string, meta?: Record<string, unknown>): void;

      /**
       * Send a paginated response
       * @param data Response data
       * @param pagination Pagination information
       * @param message Success message
       * @param meta Response metadata
       */
      paginated<T>(
        data: T,
        pagination: PaginationInfo,
        message?: string,
        meta?: Record<string, unknown>
      ): void;

      /**
       * Send an error response
       * @param error Error message
       * @param errorCode Error code
       * @param statusCode HTTP status code
       * @param validationErrors Validation errors
       * @param meta Response metadata
       */
      error(
        error: string,
        errorCode?: ApiErrorCode,
        statusCode?: number,
        validationErrors?: Record<string, string[]>,
        meta?: Record<string, unknown>
      ): void;

      /**
       * Send a validation error response
       * @param validationErrors Validation errors
       * @param message Error message
       * @param meta Response metadata
       */
      validationError(
        validationErrors: Record<string, string[]>,
        message?: string,
        meta?: Record<string, unknown>
      ): void;

      /**
       * Send a not found error response
       * @param resource Resource that was not found
       * @param id ID of the resource
       * @param meta Response metadata
       */
      notFound(resource: string, id?: string | number, meta?: Record<string, unknown>): void;
    }
  }
}

/**
 * Middleware to add standardized API response methods to Express Response object
 */
export function apiResponseMiddleware(req: Request, res: Response, next: NextFunction): void {
  // Generate a unique request ID
  const requestId: string = uuidv4();

  // Add success response method
  res.success = function <T>(data?: T, message?: string, meta?: Record<string, unknown>): void {
    const response: ApiResponse<T> = {
      success: true,
      data,
      message,
      meta,
      timestamp: new Date().toISOString(),
      requestId,
    };

    this.json(response);
  };

  // Add paginated response method
  (res).paginated = function <T>(
    data: T,
    pagination: PaginationInfo,
    message?: string,
    meta?: Record<string, unknown>
  ): void {
    const response: ApiResponse<T> = {
      success: true,
      data,
      pagination,
      message,
      meta,
      timestamp: new Date().toISOString(),
      requestId,
    };

    this.json(response);
  };

  // Add error response method
  res.error = function (
    error: string,
    errorCode: ApiErrorCode = (ApiErrorCode).GENERIC_ERROR,
    statusCode: number = 500,
    validationErrors?: Record<string, string[]>,
    meta?: Record<string, unknown>
  ): void {
    const response: ApiResponse = {
      success: false,
      error,
      errorCode,
      validationErrors,
      meta,
      timestamp: new Date().toISOString(),
      requestId,
    };

    this.status(statusCode).json(response);
  };

  // Add validation error response method
  (res).validationError = function (
    validationErrors: Record<string, string[]>,
    message?: string,
    meta?: Record<string, unknown>
  ): void {
    const response: ApiResponse = {
      success: false,
      error: message || 'Validation failed',
      errorCode: (ApiErrorCode).VALIDATION_ERROR,
      validationErrors,
      meta,
      timestamp: new Date().toISOString(),
      requestId,
    };

    this.status(400).json(response);
  };

  // Add not found response method
  (res).notFound = function (
    resource: string,
    id?: string | number,
    meta?: Record<string, unknown>
  ): void {
    const message = id ? `${resource} with ID ${id} not found` : `${resource} not found`;

    const response: ApiResponse = {
      success: false,
      error: message,
      errorCode: (ApiErrorCode).NOT_FOUND,
      meta,
      timestamp: new Date().toISOString(),
      requestId,
    };

    this.status(404).json(response);
  };

  next();
}

/**
 * Error handling middleware for standardized API error responses
 */
export function errorHandlerMiddleware(
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  console.error('API Error:', err);

  // Handle different types of errors
  if ((err).name === 'ValidationError') {
    // Handle validation errors ((e).g., from Joi or express-validator)
    const validationErrors: Record<string, string[]> = {};

    if ((err).errors) {
      // Handle mongoose validation errors
      Object.keys((err).errors).forEach((key) => {
        validationErrors[key] = [(err).errors[key].message];
      });
    } else if ((err).details) {
      // Handle Joi validation errors
      (err).details.forEach((detail) => {
        const key: string = (detail).path.join('.');
        if (!validationErrors[key]) {
          validationErrors[key] = [];
        }
        validationErrors[key].push((detail).message);
      });
    } else {
      // Generic validation error
      validationErrors['_error'] = [(err).message];
    }

    (res).validationError(validationErrors);
  } else if ((err).name === 'NotFoundError' || (err).statusCode === 404) {
    // Handle not found errors
    (res).notFound((err).resource || 'Resource', (err).id);
  } else if ((err).name === 'UnauthorizedError' || (err).statusCode === 401) {
    // Handle authentication errors
    res.error('Authentication required', (ApiErrorCode).AUTHENTICATION_ERROR, 401);
  } else if ((err).name === 'ForbiddenError' || (err).statusCode === 403) {
    // Handle authorization errors
    res.error('Access denied', (ApiErrorCode).AUTHORIZATION_ERROR, 403);
  } else if ((err).name === 'ConflictError' || (err).statusCode === 409) {
    // Handle conflict errors
    res.error((err).message || 'Resource conflict', (ApiErrorCode).DUPLICATE, 409);
  } else if ((err).name === 'PaymentError') {
    // Handle payment errors
    res.error((err).message || 'Payment processing error', (ApiErrorCode).PAYMENT_ERROR, 400);
  } else if ((err).name === 'BlockchainError') {
    // Handle blockchain errors
    res.error((err).message || 'Blockchain error', (ApiErrorCode).BLOCKCHAIN_ERROR, 400);
  } else {
    // Handle generic errors
    const statusCode: number = (err).statusCode || 500;
    const errorCode =
      statusCode >= 500 ? (ApiErrorCode).SERVER_ERROR : (ApiErrorCode).GENERIC_ERROR;
    res.error((err).message || 'Internal server error', errorCode, statusCode);
  }
}
