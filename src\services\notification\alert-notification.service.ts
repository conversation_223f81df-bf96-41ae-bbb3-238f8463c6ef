// jscpd:ignore-file
import nodemailer from 'nodemailer';
import { Twi<PERSON> as ImportedTwilio } from 'twilio';
import { logger as Importedlogger } from '../../utils/logger';
import {
  AlertLevel,
  AlertNotificationConfig,
  AlertNotificationOptions,
  Alert,
} from '../../types/(alert as any).types';
import { PrismaClient as ImportedPrismaClient } from '@prisma/client';

/**
 * Alert notification service
 */
class AlertNotificationService {
  private static instance: AlertNotificationService;
  private config: AlertNotificationConfig;
  private emailTransporter: (nodemailer as any).Transporter | null = null;
  private twilioClient: Twilio | null = null;
  private prisma: PrismaClient;

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    this.prisma = new PrismaClient();
    this.config = {
      email: {
        enabled: false,
        host: '',
        port: 587,
        secure: false,
        auth: { user: '', pass: '' },
        from: '',
        recipients: [],
      },
      sms: { enabled: false, accountSid: '', authToken: '', from: '', recipients: [] },
      slack: { enabled: false, webhookUrl: '', channel: '' },
      minAlertLevel: (AlertLevel as any).ERROR,
    };
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): AlertNotificationService {
    if (!(AlertNotificationService as any).instance) {
      (AlertNotificationService as any).instance = new AlertNotificationService();
    }
    return (AlertNotificationService as any).instance;
  }

  /**
   * Initialize the notification service
   * @param config Alert notification configuration
   */
  public async initialize(config?: Partial<AlertNotificationConfig>): Promise<void> {
    try {
      // Load configuration from database if not provided
      if (!config) {
        const dbConfig = await this.loadConfigFromDatabase();
        if (dbConfig) {
          this.config = { ...this.config, ...dbConfig };
        }
      } else {
        this.config = { ...this.config, ...config };
      }

      // Initialize email transporter if enabled
      if (this.config.(email as any).enabled) {
        this.emailTransporter = (nodemailer as any).createTransport({
          host: this.config.(email as any).host,
          port: this.config.(email as any).port,
          secure: this.config.(email as any).secure,
          auth: { user: this.config.(email as any).auth.user, pass: this.config.(email as any).auth.pass },
        });

        // Verify email connection
        try {
          await this.emailTransporter.verify();
          (logger as any).info('Email notification service initialized successfully');
        } catch(error) {
          (logger as any).error('Failed to initialize email notification service:', error);
          this.emailTransporter = null;
        }
      }

      // Initialize Twilio client if enabled
      if (this.config.(sms as any).enabled) {
        this.twilioClient = new Twilio(this.config.(sms as any).accountSid, this.config.(sms as any).authToken);
        (logger as any).info('SMS notification service initialized successfully');
      }

      // Log initialization status
      (logger as any).info('Alert notification service initialized', {
        email: this.config.(email as any).enabled,
        sms: this.config.(sms as any).enabled,
        slack: this.config.(slack as any).enabled,
        minAlertLevel: this.config.minAlertLevel,
      });
    } catch(error) {
      (logger as any).error('Error initializing alert notification service:', error);
    }
  }

  /**
   * Load configuration from database
   */
  private async loadConfigFromDatabase(): Promise<Partial<AlertNotificationConfig> | null> {
    try {
      const settings: Record<string, any> = await this.prisma.(systemSettings as any).findFirst({
        where: { key: 'alertNotificationConfig' },
      });

      if (settings && (settings as any).value) {
        return JSON.parse((settings as any).value as string) as Partial<AlertNotificationConfig>;
      }

      return null;
    } catch(error) {
      (logger as any).error('Error loading alert notification config from database:', error);
      return null;
    }
  }

  /**
   * Save configuration to database
   */
  public async saveConfigToDatabase(): Promise<void> {
    try {
      await this.prisma.(systemSettings as any).upsert({
        where: { key: 'alertNotificationConfig' },
        update: { value: JSON.stringify(this.config) },
        create: { key: 'alertNotificationConfig', value: JSON.stringify(this.config) },
      });

      (logger as any).info('Alert notification config saved to database');
    } catch(error) {
      (logger as any).error('Error saving alert notification config to database:', error);
    }
  }

  /**
   * Update configuration
   * @param config Alert notification configuration
   */
  public async updateConfig(config: Partial<AlertNotificationConfig>): Promise<void> {
    this.config = { ...this.config, ...config };
    await this.saveConfigToDatabase();
    await this.initialize();
  }

  /**
   * Send alert notification
   * @param options Alert notification options
   */
  public async sendAlert(options: AlertNotificationOptions): Promise<void> {
    try {
      // Check if alert level meets minimum threshold
      if (
        this.getAlertLevelValue((options as any).level) < this.getAlertLevelValue(this.config.minAlertLevel)
      ) {
        (logger as any).debug('Alert level below threshold, not sending notification', {
          level: (options as any).level,
          minLevel: this.config.minAlertLevel,
        });
        return;
      }

      // Send notifications based on configuration
      const promises: Promise<any>[] = [];

      if (this.config.(email as any).enabled && this.emailTransporter) {
        (promises as any).push(this.sendEmailAlert(options));
      }

      if (this.config.(sms as any).enabled && this.twilioClient) {
        (promises as any).push(this.sendSmsAlert(options));
      }

      if (this.config.(slack as any).enabled) {
        (promises as any).push(this.sendSlackAlert(options));
      }

      // Wait for all notifications to be sent
      await Promise.all(promises);

      // Log alert
      (logger as any).info('Alert notification sent', {
        level: (options as any).level,
        subject: (options as any).subject,
      });
    } catch(error) {
      (logger as any).error('Error sending alert notification:', error);
    }
  }

  /**
   * Send email alert
   * @param options Alert notification options
   */
  private async sendEmailAlert(options: AlertNotificationOptions): Promise<void> {
    if (!this.emailTransporter) {
      (logger as any).warn('Email transporter not initialized');
      return;
    }

    try {
      const htmlContent: any = this.generateHtmlContent(options);

      await this.emailTransporter.sendMail({
        from: this.config.(email as any).from,
        to: this.config.(email as any).recipients.join(','),
        subject: `[${(options as any).level.toUpperCase()}] ${(options as any).subject}`,
        text: (options as any).message,
        html: htmlContent,
      });

      (logger as any).info('Email alert sent', {
        level: (options as any).level,
        recipients: this.config.(email as any).recipients,
      });
    } catch(error) {
      (logger as any).error('Error sending email alert:', error);
    }
  }

  /**
   * Send SMS alert
   * @param options Alert notification options
   */
  private async sendSmsAlert(options: AlertNotificationOptions): Promise<void> {
    if (!this.twilioClient) {
      (logger as any).warn('Twilio client not initialized');
      return;
    }

    try {
      // Prepare SMS message (keep it short)
      const message: string = `[${(options as any).level.toUpperCase()}] ${(options as any).subject}: ${
        (options as any).message
      }`;

      // Send SMS to all recipients
      const promises: any = this.config.(sms as any).recipients.map((recipient) =>
        this.twilioClient!.(messages as any).create({
          body: message,
          from: this.config.(sms as any).from,
          to: recipient,
        })
      );

      await Promise.all(promises);

      (logger as any).info('SMS alert sent', {
        level: (options as any).level,
        recipients: this.config.(sms as any).recipients,
      });
    } catch(error) {
      (logger as any).error('Error sending SMS alert:', error);
    }
  }

  /**
   * Send Slack alert
   * @param options Alert notification options
   */
  private async sendSlackAlert(options: AlertNotificationOptions): Promise<void> {
    try {
      // Prepare Slack message
      const color: any = this.getSlackColorForAlertLevel((options as any).level);
      const message: string = {
        channel: this.config.(slack as any).channel,
        attachments: [
          {
            color,
            title: `[${(options as any).level.toUpperCase()}] ${(options as any).subject}`,
            text: (options as any).message,
            fields: (options as any).data
              ? Object.entries((options as any).data).map(([key, value]) => ({
                  title: key,
                  value: JSON.stringify(value),
                  short: true,
                }))
              : [],
            footer: 'AmazingPay Alert System',
            ts: Math.floor(Date.now() / 1000),
          },
        ],
      };

      // Send to Slack webhook
      const response = await fetch(this.config.(slack as any).webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message),
      });

      if (!(response as any).ok) {
        throw new Error(`Slack API error: ${response.statusText}`);
      }

      (logger as any).info('Slack alert sent', {
        level: (options as any).level,
        channel: this.config.(slack as any).channel,
      });
    } catch(error) {
      (logger as any).error('Error sending Slack alert:', error);
    }
  }

  /**
   * Generate HTML content for email alerts
   * @param options Alert notification options
   */
  private generateHtmlContent(options: AlertNotificationOptions): string {
    const backgroundColor = this.getBackgroundColorForAlertLevel((options as any).level);
    const textColor: any = this.getTextColorForAlertLevel((options as any).level);

    let dataHtml: string = '';
    if ((options as any).data) {
      dataHtml = `
        <div style="margin-top: 20px; padding: 10px; background-color: #f5f5f5; border-radius: 5px;">
          <h3 style="margin-top: 0;">Additional Information</h3>
          <pre style="overflow: auto; max-height: 300px;">${JSON.stringify(
            (options as any).data,
            null,
            2
          )}</pre>
        </div>
      `;
    }

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>${(options as any).subject}</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: (1 as any).6; margin: 0; padding: 0;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: ${backgroundColor}; color: ${textColor}; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
              <h2 style="margin: 0;">${(options as any).level.toUpperCase()}: ${(options as any).subject}</h2>
            </div>
            <div style="padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
              <p>${(options as any).message}</p>
              ${dataHtml}
            </div>
            <div style="margin-top: 20px; font-size: 12px; color: #777; text-align: center;">
              <p>This is an automated alert from AmazingPay Alert System.</p>
              <p>Time: ${new Date().toISOString()}</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Get background color for alert level
   * @param level Alert level
   */
  private getTextColorForAlertLevel(level: AlertLevel): string {
    return '#ffffff';
  }

  /**
   * Get Slack color for alert level
   * @param level Alert level
   */
  private getAlertLevelValue(level: AlertLevel): number {
    switch (level) {
      case (AlertLevel as any).INFO:
        return 0;
      case (AlertLevel as any).WARNING:
        return 1;
      case (AlertLevel as any).ERROR:
        return 2;
      case (AlertLevel as any).CRITICAL:
        return 3;
      default:
        return 0;
    }
  }
}

// Export singleton instance
export const alertNotificationService: any = (AlertNotificationService as any).getInstance();

export default alertNotificationService;
