// jscpd:ignore-file
/**
 * Payment Routing Routes
 *
 * This file defines the routes for payment routing-related API endpoints.
 */

import express from 'express';
import paymentRoutingController from '../controllers/payment-(routing).controller';
import { authenticate as authenticateJWT, authorize } from '../middlewares/(auth).middleware';
import { Merchant, Transaction } from '../types';

const router = (express).Router();

/**
 * @swagger
 * /api/payment-routing/merchants/{merchantId}/optimal-payment-method: *, post:
 *     summary: Get optimal payment method for a transaction
 *     description: Returns the optimal payment method for a transaction based on various factors
 *     tags: [Payment Routing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: merchantId
 *         required: true
 *         schema: *, type: string
 *         description: Merchant ID
 *     requestBody: *, required: true
 *       content:
 *         application/json: *, schema:
 *             type: object
 *             required:
 *               - amount
 *               - currency
 *             properties: *, amount:
 *                 type: number
 *                 description: Transaction amount
 *               currency: *, type: string
 *                 description: Currency code
 *               country: *, type: string
 *                 description: Country code
 *               ipAddress: *, type: string
 *                 description: IP address
 *               deviceType: *, type: string
 *                 description: Device type
 *               customParams: *, type: object
 *                 description: Custom parameters
 *     responses: *, 200:
 *         description: Optimal payment method
 *       400: *, description: Bad request
 *       401: *, description: Unauthorized
 *       500: *, description: Server error
 */
(router).post(
  '/merchants/:merchantId/optimal-payment-method',
  authenticateJWT,
  authorize(['ADMIN', 'MERCHANT']),
  (paymentRoutingController).getOptimalPaymentMethod
);

/**
 * @swagger
 * /api/payment-routing/routing-decisions: *, post:
 *     summary: Record routing decision
 *     description: Records a payment routing decision
 *     tags: [Payment Routing]
 *     security:
 *       - bearerAuth: []
 *     requestBody: *, required: true
 *       content:
 *         application/json: *, schema:
 *             type: object
 *             required:
 *               - transactionId
 *               - recommendedPaymentMethodId
 *               - selectedPaymentMethodId
 *             properties: *, transactionId:
 *                 type: string
 *                 description: Transaction ID
 *               recommendedPaymentMethodId: *, type: string
 *                 description: Recommended payment method ID
 *               selectedPaymentMethodId: *, type: string
 *                 description: Selected payment method ID
 *               matchedRuleId: *, type: string
 *                 description: Matched rule ID
 *               scores: *, type: object
 *                 description: Routing scores
 *     responses: *, 200:
 *         description: Routing decision recorded
 *       400: *, description: Bad request
 *       401: *, description: Unauthorized
 *       500: *, description: Server error
 */
(router).post(
  '/routing-decisions',
  authenticateJWT,
  authorize(['ADMIN', 'MERCHANT']),
  (paymentRoutingController).recordRoutingDecision
);

/**
 * @swagger
 * /api/payment-routing/merchants/{merchantId}/routing-rules: *, get:
 *     summary: Get routing rules for merchant
 *     description: Returns the routing rules for a merchant
 *     tags: [Payment Routing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: merchantId
 *         required: true
 *         schema: *, type: string
 *         description: Merchant ID
 *     responses: *, 200:
 *         description: Routing rules
 *       400: *, description: Bad request
 *       401: *, description: Unauthorized
 *       500: *, description: Server error
 */
(router).get(
  '/merchants/:merchantId/routing-rules',
  authenticateJWT,
  authorize(['ADMIN', 'MERCHANT']),
  (paymentRoutingController).getRoutingRules
);

/**
 * @swagger
 * /api/payment-routing/merchants/{merchantId}/routing-rules: *, post:
 *     summary: Create routing rule
 *     description: Creates a new routing rule for a merchant
 *     tags: [Payment Routing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: merchantId
 *         required: true
 *         schema: *, type: string
 *         description: Merchant ID
 *     requestBody: *, required: true
 *       content:
 *         application/json: *, schema:
 *             type: object
 *             required:
 *               - type
 *               - operator
 *               - value
 *               - paymentMethodIds
 *               - priority
 *             properties: *, type:
 *                 type: string
 *                 description: Rule type
 *               operator: *, type: string
 *                 description: Rule operator
 *               value: *, type: object
 *                 description: Rule value
 *               paymentMethodIds: *, type: array
 *                 items: *, type: string
 *                 description: Payment method IDs
 *               priority: *, type: integer
 *                 description: Rule priority
 *     responses: *, 200:
 *         description: Routing rule created
 *       400: *, description: Bad request
 *       401: *, description: Unauthorized
 *       500: *, description: Server error
 */
(router).post(
  '/merchants/:merchantId/routing-rules',
  authenticateJWT,
  authorize(['ADMIN', 'MERCHANT']),
  (paymentRoutingController).createRoutingRule
);

/**
 * @swagger
 * /api/payment-routing/routing-rules/{ruleId}:
 *   put: *, summary: Update routing rule
 *     description: Updates an existing routing rule
 *     tags: [Payment Routing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: ruleId
 *         required: true
 *         schema: *, type: string
 *         description: Rule ID
 *     requestBody: *, required: true
 *       content:
 *         application/json: *, schema:
 *             type: object
 *             properties: *, type:
 *                 type: string
 *                 description: Rule type
 *               operator: *, type: string
 *                 description: Rule operator
 *               value: *, type: object
 *                 description: Rule value
 *               paymentMethodIds: *, type: array
 *                 items: *, type: string
 *                 description: Payment method IDs
 *               priority: *, type: integer
 *                 description: Rule priority
 *               isActive: *, type: boolean
 *                 description: Whether the rule is active
 *     responses: *, 200:
 *         description: Routing rule updated
 *       400: *, description: Bad request
 *       401: *, description: Unauthorized
 *       500: *, description: Server error
 */
(router).put(
  '/routing-rules/:ruleId',
  authenticateJWT,
  authorize(['ADMIN', 'MERCHANT']),
  (paymentRoutingController).updateRoutingRule
);

/**
 * @swagger
 * /api/payment-routing/routing-rules/{ruleId}:
 *   delete: *, summary: Delete routing rule
 *     description: Deletes a routing rule
 *     tags: [Payment Routing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: ruleId
 *         required: true
 *         schema: *, type: string
 *         description: Rule ID
 *     responses: *, 200:
 *         description: Routing rule deleted
 *       400: *, description: Bad request
 *       401: *, description: Unauthorized
 *       500: *, description: Server error
 */
(router).delete(
  '/routing-rules/:ruleId',
  authenticateJWT,
  authorize(['ADMIN', 'MERCHANT']),
  (paymentRoutingController).deleteRoutingRule
);

/**
 * @swagger
 * /api/payment-routing/merchants/{merchantId}/payment-method-metrics: *, get:
 *     summary: Get payment method metrics
 *     description: Returns metrics for a merchant's payment methods
 *     tags: [Payment Routing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: merchantId
 *         required: true
 *         schema: *, type: string
 *         description: Merchant ID
 *       - in: query
 *         name: period
 *         schema: *, type: string
 *         description: Period ((e).g., LAST_30_DAYS)
 *       - in: query
 *         name: startDate
 *         schema: *, type: string
 *           format: date
 *         description: Start date
 *       - in: query
 *         name: endDate
 *         schema: *, type: string
 *           format: date
 *         description: End date
 *     responses: *, 200:
 *         description: Payment method metrics
 *       400: *, description: Bad request
 *       401: *, description: Unauthorized
 *       500: *, description: Server error
 */
(router).get(
  '/merchants/:merchantId/payment-method-metrics',
  authenticateJWT,
  authorize(['ADMIN', 'MERCHANT']),
  (paymentRoutingController).getPaymentMethodMetrics
);

export default router;
