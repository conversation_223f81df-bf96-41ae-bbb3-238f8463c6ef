import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

export class DashboardController {
  /**
   * Get all dashboards for the current user
   */
  public getDashboards = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }
      
      const dashboards = await prisma.dashboard.findMany({
        where: {
          OR: [
            { createdById: userId },
            { isPublic: true }
          ]
        },
        orderBy: {
          createdAt: 'desc'
        }
      });
      
      res.json({
        success: true,
        data: dashboards
      });
    } catch (error: Error) {
      console.error('Error getting dashboards:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error getting dashboards'
      });
    }
  };

  /**
   * Get a dashboard by ID
   */
  public getDashboardById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }
      
      const dashboard = await prisma.dashboard.findUnique({
        where: { id },
        include: {
          widgets: {
            orderBy: {
              position: 'asc'
            }
          }
        }
      });
      
      if (!dashboard) {
        res.status(404).json({ success: false, message: 'Dashboard not found' });
        return;
      }
      
      // Check if user has access to this dashboard
      if (!dashboard.isPublic && dashboard.createdById !== userId) {
        res.status(403).json({ success: false, message: 'Access denied' });
        return;
      }
      
      res.json({
        success: true,
        data: dashboard
      });
    } catch (error: Error) {
      console.error('Error getting dashboard:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error getting dashboard'
      });
    }
  };

  /**
   * Create a new dashboard
   */
  public createDashboard = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }
      
      const { name, description, layout, isPublic } = req.body;
      
      const dashboard = await prisma.dashboard.create({
        data: {
          name,
          description,
          layout: layout ?? {},
          isPublic: isPublic ?? false,
          createdById: userId
        }
      });
      
      res.status(201).json({
        success: true,
        data: dashboard
      });
    } catch (error: Error) {
      console.error('Error creating dashboard:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error creating dashboard'
      });
    }
  };

  /**
   * Update a dashboard
   */
  public updateDashboard = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }
      
      // Check if dashboard exists and belongs to user
      const existingDashboard = await prisma.dashboard.findUnique({
        where: { id }
      });
      
      if (!existingDashboard) {
        res.status(404).json({ success: false, message: 'Dashboard not found' });
        return;
      }
      
      if (existingDashboard.createdById !== userId) {
        res.status(403).json({ success: false, message: 'Access denied' });
        return;
      }
      
      const { name, description, layout, isPublic } = req.body;
      
      const dashboard = await prisma.dashboard.update({
        where: { id },
        data: {
          name,
          description,
          layout: layout ?? undefined,
          isPublic: isPublic !== undefined ? isPublic : undefined
        }
      });
      
      res.json({
        success: true,
        data: dashboard
      });
    } catch (error: Error) {
      console.error('Error updating dashboard:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error updating dashboard'
      });
    }
  };

  /**
   * Delete a dashboard
   */
  public deleteDashboard = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }
      
      // Check if dashboard exists and belongs to user
      const existingDashboard = await prisma.dashboard.findUnique({
        where: { id }
      });
      
      if (!existingDashboard) {
        res.status(404).json({ success: false, message: 'Dashboard not found' });
        return;
      }
      
      if (existingDashboard.createdById !== userId) {
        res.status(403).json({ success: false, message: 'Access denied' });
        return;
      }
      
      // Delete all widgets first
      await prisma.dashboardWidget.deleteMany({
        where: { dashboardId: id }
      });
      
      // Delete dashboard
      await prisma.dashboard.delete({
        where: { id }
      });
      
      res.json({
        success: true,
        message: 'Dashboard deleted successfully'
      });
    } catch (error: Error) {
      console.error('Error deleting dashboard:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error deleting dashboard'
      });
    }
  };
}
