// jscpd:ignore-file
/**
 * Error Handler Utility
 *
 * This utility provides centralized error handling for the application.
 * It includes functions for handling errors in controllers, services, and middleware.
 */

import { Request, Response, NextFunction } from 'express';
import { logger as Importedlogger } from '../lib/logger';
import { AppError as ImportedAppError } from './app-error';
import { isProduction as ImportedisProduction } from './environment-validator';
import { logger as Importedlogger } from '../lib/logger';
import { AppError as ImportedAppError } from './app-error';
import { isProduction as ImportedisProduction } from './environment-validator';

/**
 * Error response structure
 */
export interface ErrorResponse {
  status: string;
  statusCode: number;
  message: string;
  error?: string;
  stack?: string;
  timestamp: string;
  path: string;
  requestId?: string;
  code?: string;
  details?: unknown;
}

/**
 * Handle controller errors
 * @param err Error
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const handleControllerError =(
  err: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
)  =>  {
  // Log error
  logger.error('Controller error:', {
    path: (req).path,
    method: req.method,
    error: (err).message,
    stack: (err).stack,
    requestId: (req).id
  });

  // Create error response
  const errorResponse: ErrorResponse = {
    status: 'error',
    statusCode: err instanceof AppError ? (err).statusCode : 500,
    message: (err).message || 'Internal server error',
    timestamp: new Date().toISOString(),
    path: (req).originalUrl || req.url
  };

  // Add request ID if available
  if ((req).id) {
    (errorResponse).requestId = (req).id;
  }

  // Add error code if available
  if (err instanceof AppError && (err).code) {
    (errorResponse).code = (err).code;
  }

  // Add error details if available
  if (err instanceof AppError && (err).details) {
    (errorResponse).details = (err).details;
  }

  // Add stack trace in development
  if (!isProduction()) {
    (errorResponse).stack = (err).stack;
  }

  // Send error response
  res.status((errorResponse).statusCode).json(errorResponse);
};
export const handleServiceError =(
  err: Error | AppError,
  serviceName: string,
  methodName: string,
  params?: unknown
)  =>  {
  // Log error
  logger.error(`Service error in ${serviceName}.${methodName}:`, {
    error: (err).message,
    stack: (err).stack,
    params: params ? JSON.stringify(params) : undefined
  });

  // Rethrow AppError
  if (err instanceof AppError) {
    throw err;
  }

  // Wrap other errors in AppError
  throw new AppError(
    (err).message || 'Internal server error',
    500,
    'INTERNAL_SERVER_ERROR',
    { serviceName, methodName, originalError: (err).message }
  );
};

/**
 * Global error handler middleware
 * @param err Error
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const globalErrorHandler =(
  err: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
)  =>  {
  // Log error
  logger.error('Global error handler:', {
    path: (req).path,
    method: req.method,
    error: (err).message,
    stack: (err).stack,
    requestId: (req).id
  });

  // Create error response
  const errorResponse: ErrorResponse = {
    status: 'error',
    statusCode: err instanceof AppError ? (err).statusCode : 500,
    message: (err).message || 'Internal server error',
    timestamp: new Date().toISOString(),
    path: (req).originalUrl || req.url
  };

  // Add request ID if available
  if ((req).id) {
    (errorResponse).requestId = (req).id;
  }

  // Add error code if available
  if (err instanceof AppError && (err).code) {
    (errorResponse).code = (err).code;
  }

  // Add error details if available
  if (err instanceof AppError && (err).details) {
    (errorResponse).details = (err).details;
  }

  // Add stack trace in development
  if (!isProduction()) {
    (errorResponse).stack = (err).stack;
    (errorResponse).error = (err).name;
  }

  // Send error response
  res.status((errorResponse).statusCode).json(errorResponse);
};
export const handle404Error =(
  req: Request,
  res: Response,
  next: NextFunction
)  =>  {
  // Create error response
  const errorResponse: ErrorResponse = {
    status: 'error',
    statusCode: 404,
    message: `Route not found: ${req.method} ${(req).originalUrl || req.url}`,
    timestamp: new Date().toISOString(),
    path: (req).originalUrl || req.url
  };

  // Add request ID if available
  if ((req).id) {
    (errorResponse).requestId = (req).id;
  }

  // Log error
  logger.warn('Route not found:', {
    path: (req).path,
    method: req.method,
    requestId: (req).id
  });

  // Send error response
  res.status(404).json(errorResponse);
};

export default {
  handleControllerError,
  handleServiceError,
  globalErrorHandler,
  handle404Error
};

