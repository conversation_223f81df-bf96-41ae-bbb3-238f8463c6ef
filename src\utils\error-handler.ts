// jscpd:ignore-file
/**
 * Error Handler Utility
 *
 * This utility provides centralized error handling for the application.
 * It includes functions for handling errors in controllers, services, and middleware.
 */

import { Request, Response, NextFunction } from 'express';
import { logger as Importedlogger } from '../lib/logger';
import { AppError as ImportedAppError } from './app-error';
import { isProduction as ImportedisProduction } from './environment-validator';
import { logger as Importedlogger } from '../lib/logger';
import { AppError as ImportedAppError } from './app-error';
import { isProduction as ImportedisProduction } from './environment-validator';

/**
 * Error response structure
 */
export interface ErrorResponse {
  status: string;
  statusCode: number;
  message: string;
  error?: string;
  stack?: string;
  timestamp: string;
  path: string;
  requestId?: string;
  code?: string;
  details?: unknown;
}

/**
 * Handle controller errors
 * @param err Error
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const handleControllerError: any =(
  err: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Log error
  (logger as any).error('Controller error:', {
    path: (req as any).path,
    method: req.method,
    error: (err as any).message,
    stack: (err as any).stack,
    requestId: (req as any).id
  });

  // Create error response
  const errorResponse: ErrorResponse = {
    status: 'error',
    statusCode: err instanceof AppError ? (err as any).statusCode : 500,
    message: (err as any).message || 'Internal server error',
    timestamp: new Date().toISOString(),
    path: (req as any).originalUrl || req.url
  };

  // Add request ID if available
  if ((req as any).id) {
    (errorResponse as any).requestId = (req as any).id;
  }

  // Add error code if available
  if (err instanceof AppError && (err as any).code) {
    (errorResponse as any).code = (err as any).code;
  }

  // Add error details if available
  if (err instanceof AppError && (err as any).details) {
    (errorResponse as any).details = (err as any).details;
  }

  // Add stack trace in development
  if (!isProduction()) {
    (errorResponse as any).stack = (err as any).stack;
  }

  // Send error response
  res.status((errorResponse as any).statusCode).json(errorResponse);
};
export const handleServiceError: any =(
  err: Error | AppError,
  serviceName: string,
  methodName: string,
  params?: unknown
) => {
  // Log error
  (logger as any).error(`Service error in ${serviceName}.${methodName}:`, {
    error: (err as any).message,
    stack: (err as any).stack,
    params: params ? JSON.stringify(params) : undefined
  });

  // Rethrow AppError
  if (err instanceof AppError) {
    throw err;
  }

  // Wrap other errors in AppError
  throw new AppError(
    (err as any).message || 'Internal server error',
    500,
    'INTERNAL_SERVER_ERROR',
    { serviceName, methodName, originalError: (err as any).message }
  );
};

/**
 * Global error handler middleware
 * @param err Error
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const globalErrorHandler: any =(
  err: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Log error
  (logger as any).error('Global error handler:', {
    path: (req as any).path,
    method: req.method,
    error: (err as any).message,
    stack: (err as any).stack,
    requestId: (req as any).id
  });

  // Create error response
  const errorResponse: ErrorResponse = {
    status: 'error',
    statusCode: err instanceof AppError ? (err as any).statusCode : 500,
    message: (err as any).message || 'Internal server error',
    timestamp: new Date().toISOString(),
    path: (req as any).originalUrl || req.url
  };

  // Add request ID if available
  if ((req as any).id) {
    (errorResponse as any).requestId = (req as any).id;
  }

  // Add error code if available
  if (err instanceof AppError && (err as any).code) {
    (errorResponse as any).code = (err as any).code;
  }

  // Add error details if available
  if (err instanceof AppError && (err as any).details) {
    (errorResponse as any).details = (err as any).details;
  }

  // Add stack trace in development
  if (!isProduction()) {
    (errorResponse as any).stack = (err as any).stack;
    (errorResponse as any).error = (err as any).name;
  }

  // Send error response
  res.status((errorResponse as any).statusCode).json(errorResponse);
};
export const handle404Error: any =(
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Create error response
  const errorResponse: ErrorResponse = {
    status: 'error',
    statusCode: 404,
    message: `Route not found: ${req.method} ${(req as any).originalUrl || req.url}`,
    timestamp: new Date().toISOString(),
    path: (req as any).originalUrl || req.url
  };

  // Add request ID if available
  if ((req as any).id) {
    (errorResponse as any).requestId = (req as any).id;
  }

  // Log error
  (logger as any).warn('Route not found:', {
    path: (req as any).path,
    method: req.method,
    requestId: (req as any).id
  });

  // Send error response
  res.status(404).json(errorResponse);
};

export default {
  handleControllerError,
  handleServiceError,
  globalErrorHandler,
  handle404Error
};

