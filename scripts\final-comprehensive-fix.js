#!/usr/bin/env node

/**
 * FINAL COMPREHENSIVE FIX SCRIPT
 * Systematically eliminates ALL remaining TypeScript errors
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎯 FINAL COMPREHENSIVE FIX SCRIPT');
console.log('==================================');
console.log('🚀 TARGET: 0 TypeScript Errors');

// Comprehensive fixes for ALL remaining patterns
const comprehensiveFixes = {
    // Extra parentheses in forEach callbacks
    '.forEach((([': '.forEach(([',
    '.forEach(((': '.forEach((',
    ')) => {': ') => {',
    
    // Object syntax issues
    'orderBy: {, ': 'orderBy: { ',
    'where: {, ': 'where: { ',
    'data: {, ': 'data: { ',
    'select: {, ': 'select: { ',
    'include: {, ': 'include: { ',
    'create: {, ': 'create: { ',
    'update: {, ': 'update: { ',
    'upsert: {, ': 'upsert: { ',
    'connect: {, ': 'connect: { ',
    'disconnect: {, ': 'disconnect: { ',
    'set: {, ': 'set: { ',
    'push: {, ': 'push: { ',
    'unshift: {, ': 'unshift: { ',
    'splice: {, ': 'splice: { ',
    'sort: {, ': 'sort: { ',
    'filter: {, ': 'filter: { ',
    'map: {, ': 'map: { ',
    'reduce: {, ': 'reduce: { ',
    'find: {, ': 'find: { ',
    'findIndex: {, ': 'findIndex: { ',
    'some: {, ': 'some: { ',
    'every: {, ': 'every: { ',
    'forEach: {, ': 'forEach: { ',
    'concat: {, ': 'concat: { ',
    'join: {, ': 'join: { ',
    'slice: {, ': 'slice: { ',
    'reverse: {, ': 'reverse: { ',
    'shift: {, ': 'shift: { ',
    'pop: {, ': 'pop: { ',
    'length: {, ': 'length: { ',
    'indexOf: {, ': 'indexOf: { ',
    'lastIndexOf: {, ': 'lastIndexOf: { ',
    'includes: {, ': 'includes: { ',
    'startsWith: {, ': 'startsWith: { ',
    'endsWith: {, ': 'endsWith: { ',
    'substring: {, ': 'substring: { ',
    'substr: {, ': 'substr: { ',
    'charAt: {, ': 'charAt: { ',
    'charCodeAt: {, ': 'charCodeAt: { ',
    'toLowerCase: {, ': 'toLowerCase: { ',
    'toUpperCase: {, ': 'toUpperCase: { ',
    'trim: {, ': 'trim: { ',
    'replace: {, ': 'replace: { ',
    'split: {, ': 'split: { ',
    'match: {, ': 'match: { ',
    'search: {, ': 'search: { ',
    'test: {, ': 'test: { ',
    'exec: {, ': 'exec: { ',
    'toString: {, ': 'toString: { ',
    'valueOf: {, ': 'valueOf: { ',
    'hasOwnProperty: {, ': 'hasOwnProperty: { ',
    'isPrototypeOf: {, ': 'isPrototypeOf: { ',
    'propertyIsEnumerable: {, ': 'propertyIsEnumerable: { ',
    'constructor: {, ': 'constructor: { ',
    'prototype: {, ': 'prototype: { ',
    'call: {, ': 'call: { ',
    'apply: {, ': 'apply: { ',
    'bind: {, ': 'bind: { ',
    'then: {, ': 'then: { ',
    'catch: {, ': 'catch: { ',
    'finally: {, ': 'finally: { ',
    'resolve: {, ': 'resolve: { ',
    'reject: {, ': 'reject: { ',
    'all: {, ': 'all: { ',
    'race: {, ': 'race: { ',
    'allSettled: {, ': 'allSettled: { ',
    'any: {, ': 'any: { ',
    'from: {, ': 'from: { ',
    'of: {, ': 'of: { ',
    'isArray: {, ': 'isArray: { ',
    'keys: {, ': 'keys: { ',
    'values: {, ': 'values: { ',
    'entries: {, ': 'entries: { ',
    'assign: {, ': 'assign: { ',
    'create: {, ': 'create: { ',
    'defineProperty: {, ': 'defineProperty: { ',
    'defineProperties: {, ': 'defineProperties: { ',
    'getOwnPropertyDescriptor: {, ': 'getOwnPropertyDescriptor: { ',
    'getOwnPropertyDescriptors: {, ': 'getOwnPropertyDescriptors: { ',
    'getOwnPropertyNames: {, ': 'getOwnPropertyNames: { ',
    'getOwnPropertySymbols: {, ': 'getOwnPropertySymbols: { ',
    'getPrototypeOf: {, ': 'getPrototypeOf: { ',
    'setPrototypeOf: {, ': 'setPrototypeOf: { ',
    'is: {, ': 'is: { ',
    'freeze: {, ': 'freeze: { ',
    'seal: {, ': 'seal: { ',
    'preventExtensions: {, ': 'preventExtensions: { ',
    'isFrozen: {, ': 'isFrozen: { ',
    'isSealed: {, ': 'isSealed: { ',
    'isExtensible: {, ': 'isExtensible: { ',
    
    // Missing spaces after = in assignments
    ': unknown =await ': ': unknown = await ',
    ': unknown =req.': ': unknown = req.',
    ': unknown =res.': ': unknown = res.',
    ': unknown =next.': ': unknown = next.',
    ': unknown =new ': ': unknown = new ',
    ': unknown =[': ': unknown = [',
    ': unknown ={': ': unknown = {',
    ': unknown =process.': ': unknown = process.',
    ': unknown =this.': ': unknown = this.',
    ': unknown =super.': ': unknown = super.',
    ': unknown =parseInt(': ': unknown = parseInt(',
    ': unknown =parseFloat(': ': unknown = parseFloat(',
    ': unknown =JSON.': ': unknown = JSON.',
    ': unknown =String(': ': unknown = String(',
    ': unknown =Number(': ': unknown = Number(',
    ': unknown =Boolean(': ': unknown = Boolean(',
    ': unknown =Array.': ': unknown = Array.',
    ': unknown =Object.': ': unknown = Object.',
    ': unknown =Date.': ': unknown = Date.',
    ': unknown =Math.': ': unknown = Math.',
    ': unknown =RegExp(': ': unknown = RegExp(',
    ': unknown =Error(': ': unknown = Error(',
    ': unknown =Promise.': ': unknown = Promise.',
    ': unknown =Buffer.': ': unknown = Buffer.',
    ': unknown =crypto.': ': unknown = crypto.',
    ': unknown =fs.': ': unknown = fs.',
    ': unknown =path.': ': unknown = path.',
    ': unknown =url.': ': unknown = url.',
    ': unknown =util.': ': unknown = util.',
    ': unknown =os.': ': unknown = os.',
    ': unknown =http.': ': unknown = http.',
    ': unknown =https.': ': unknown = https.',
    ': unknown =querystring.': ': unknown = querystring.',
    ': unknown =stream.': ': unknown = stream.',
    ': unknown =events.': ': unknown = events.',
    ': unknown =child_process.': ': unknown = child_process.',
    ': unknown =cluster.': ': unknown = cluster.',
    ': unknown =worker_threads.': ': unknown = worker_threads.',
    ': unknown =async ': ': unknown = async ',
    ': unknown =function': ': unknown = function',
    ': unknown =class ': ': unknown = class ',
    ': unknown =interface ': ': unknown = interface ',
    ': unknown =enum ': ': unknown = enum ',
    ': unknown =namespace ': ': unknown = namespace ',
    ': unknown =module ': ': unknown = module ',
    ': unknown =import ': ': unknown = import ',
    ': unknown =export ': ': unknown = export ',
    ': unknown =require(': ': unknown = require(',
    ': unknown =__dirname': ': unknown = __dirname',
    ': unknown =__filename': ': unknown = __filename',
    ': unknown =global.': ': unknown = global.',
    ': unknown =window.': ': unknown = window.',
    ': unknown =document.': ': unknown = document.',
    ': unknown =console.': ': unknown = console.',
    ': unknown =setTimeout(': ': unknown = setTimeout(',
    ': unknown =setInterval(': ': unknown = setInterval(',
    ': unknown =clearTimeout(': ': unknown = clearTimeout(',
    ': unknown =clearInterval(': ': unknown = clearInterval(',
    ': unknown =setImmediate(': ': unknown = setImmediate(',
    ': unknown =clearImmediate(': ': unknown = clearImmediate(',
    ': unknown =Symbol(': ': unknown = Symbol(',
    ': unknown =BigInt(': ': unknown = BigInt(',
    ': unknown =Proxy(': ': unknown = Proxy(',
    ': unknown =Reflect.': ': unknown = Reflect.',
    ': unknown =WeakMap(': ': unknown = WeakMap(',
    ': unknown =WeakSet(': ': unknown = WeakSet(',
    ': unknown =Map(': ': unknown = Map(',
    ': unknown =Set(': ': unknown = Set(',
    ': unknown =ArrayBuffer(': ': unknown = ArrayBuffer(',
    ': unknown =DataView(': ': unknown = DataView(',
    ': unknown =Int8Array(': ': unknown = Int8Array(',
    ': unknown =Uint8Array(': ': unknown = Uint8Array(',
    ': unknown =Uint8ClampedArray(': ': unknown = Uint8ClampedArray(',
    ': unknown =Int16Array(': ': unknown = Int16Array(',
    ': unknown =Uint16Array(': ': unknown = Uint16Array(',
    ': unknown =Int32Array(': ': unknown = Int32Array(',
    ': unknown =Uint32Array(': ': unknown = Uint32Array(',
    ': unknown =Float32Array(': ': unknown = Float32Array(',
    ': unknown =Float64Array(': ': unknown = Float64Array(',
    ': unknown =BigInt64Array(': ': unknown = BigInt64Array(',
    ': unknown =BigUint64Array(': ': unknown = BigUint64Array(',
    
    // Arrow function syntax fixes
    '= >': '=>',
    '= > ': '=> ',
    ' = >': ' =>',
    ' = > ': ' => ',
    
    // Nullish coalescing fixes
    ' || \'\'': ' ?? \'\'',
    ' || ""': ' ?? ""',
    ' || 0': ' ?? 0',
    ' || false': ' ?? false',
    ' || true': ' ?? true',
    ' || null': ' ?? null',
    ' || undefined': ' ?? undefined',
    ' || []': ' ?? []',
    ' || {}': ' ?? {}'
};

function findAllTypeScriptFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'coverage'].includes(item)) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

function getErrorCount() {
    try {
        const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { encoding: 'utf8' });
        const errorMatches = output.match(/error TS/g) || [];
        return errorMatches.length;
    } catch (error) {
        const errorMatches = error.stdout.match(/error TS/g) || [];
        return errorMatches.length;
    }
}

function processFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let modifiedContent = content;
        let fixCount = 0;
        
        // Apply all comprehensive fixes
        for (const [oldPattern, newPattern] of Object.entries(comprehensiveFixes)) {
            const regex = new RegExp(escapeRegExp(oldPattern), 'g');
            const matches = modifiedContent.match(regex);
            if (matches) {
                modifiedContent = modifiedContent.replace(regex, newPattern);
                fixCount += matches.length;
            }
        }
        
        if (fixCount > 0) {
            fs.writeFileSync(filePath, modifiedContent, 'utf8');
            return { filePath, fixCount };
        }
        
        return null;
    } catch (error) {
        return { filePath, error: error.message };
    }
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

async function main() {
    console.log('🔍 Scanning for TypeScript files...');
    
    const files = findAllTypeScriptFiles('./src');
    console.log(`📁 Found ${files.length} TypeScript files`);
    
    let currentErrors = getErrorCount();
    console.log(`🚨 Starting with ${currentErrors} TypeScript errors`);
    
    let iteration = 1;
    const maxIterations = 5;
    
    while (currentErrors > 0 && iteration <= maxIterations) {
        console.log(`\n🔄 ITERATION ${iteration} - Fixing ${currentErrors} errors...`);
        const startTime = Date.now();
        
        const results = [];
        let totalFixedIssues = 0;
        
        for (const file of files) {
            const result = processFile(file);
            if (result) {
                results.push(result);
                if (result.fixCount) {
                    totalFixedIssues += result.fixCount;
                }
            }
        }
        
        const newErrorCount = getErrorCount();
        const errorsFixed = currentErrors - newErrorCount;
        const endTime = Date.now();
        const processingTime = (endTime - startTime) / 1000;
        
        console.log(`⏱️  Processing time: ${processingTime.toFixed(2)} seconds`);
        console.log(`📁 Files processed: ${results.filter(r => !r.error).length}`);
        console.log(`🔧 Total fixes applied: ${totalFixedIssues}`);
        console.log(`🚨 Errors before: ${currentErrors}`);
        console.log(`✅ Errors after: ${newErrorCount}`);
        console.log(`🎯 Errors fixed this iteration: ${errorsFixed}`);
        
        if (errorsFixed <= 0) {
            console.log('⚠️  No progress made this iteration.');
            break;
        }
        
        currentErrors = newErrorCount;
        iteration++;
        
        if (currentErrors === 0) {
            console.log('\n🎉 SUCCESS! ALL ERRORS ELIMINATED!');
            break;
        }
    }
    
    const finalErrors = getErrorCount();
    
    console.log('\n🏆 FINAL COMPREHENSIVE FIX RESULTS');
    console.log('===================================');
    console.log(`🎯 Final error count: ${finalErrors}`);
    
    if (finalErrors === 0) {
        console.log('🎉 MISSION ACCOMPLISHED! 🎉');
        console.log('✅ Your project is now 100% ERROR-FREE!');
        console.log('🚀 Ready for production deployment!');
    } else {
        console.log(`📊 ${finalErrors} errors remaining`);
        console.log('🔍 These may require manual intervention or more specific patterns');
    }
}

main().catch(console.error);
