// jscpd:ignore-file
/**
 * Enhanced Payment Service
 * 
 * Handles payment operations using the new abstraction layer.
 */

import { PrismaClient } from "@prisma/client";
import { IPaymentMethod, PaymentRequest, PaymentResult } from "../../interfaces/payment/IPaymentMethod";
import { PaymentMethodFactory } from "../../factories/payment/PaymentMethodFactory";
import { PaymentGatewayFactory } from "../../factories/payment/PaymentGatewayFactory";
import { PaymentMethodType } from "../../types/payment-method.types";
import { logger } from "../../lib/logger";
import { SubscriptionService } from "../subscription.service";
import { v4 as uuidv4 } from "uuid";
import { Merchant, PaymentMethodType } from '../types';
import { IPaymentMethod, PaymentRequest, PaymentResult } from "../../interfaces/payment/IPaymentMethod";
import { PaymentMethodFactory } from "../../factories/payment/PaymentMethodFactory";
import { PaymentGatewayFactory } from "../../factories/payment/PaymentGatewayFactory";
import { PaymentMethodType } from "../../types/payment-method.types";
import { logger } from "../../lib/logger";
import { SubscriptionService } from "../subscription.service";
import { v4 as uuidv4 } from "uuid";
import { Merchant, PaymentMethodType } from '../types';


/**
 * Payment pre-processor interface
 */
export interface PaymentPreProcessor {
  /**
   * Get the name of the pre-processor
   */
  getName(): string;
  
  /**
   * Process a payment request
   */
  process(request: PaymentRequest): Promise<PaymentRequest>;
}

/**
 * Payment post-processor interface
 */
export interface PaymentPostProcessor {
  /**
   * Get the name of the post-processor
   */
  getName(): string;
  
  /**
   * Process a payment result
   */
  process(result: PaymentResult, request: PaymentRequest): Promise<PaymentResult>;
}

/**
 * Enhanced payment service
 */
export class EnhancedPaymentService {
    private prisma: PrismaClient;
    private paymentMethodFactory: PaymentMethodFactory;
    private paymentGatewayFactory: PaymentGatewayFactory;
    private subscriptionService: SubscriptionService;
    private preProcessors: PaymentPreProcessor[] = [];
    private postProcessors: PaymentPostProcessor[] = [];
  
    /**
   * Constructor
   */
    constructor(prisma: PrismaClient, subscriptionService: SubscriptionService) {
        this.prisma = prisma;
        this.paymentMethodFactory = PaymentMethodFactory.getInstance();
        this.paymentGatewayFactory = PaymentGatewayFactory.getInstance();
        this.subscriptionService = subscriptionService;
    }
  
    /**
   * Register a pre-processor
   */
    public registerPreProcessor(processor: PaymentPreProcessor): void {
        this.preProcessors.push(processor);
        logger.info(`Registered payment pre-processor: ${processor.getName()}`);
    }
  
    /**
   * Register a post-processor
   */
    public registerPostProcessor(processor: PaymentPostProcessor): void {
        this.postProcessors.push(processor);
        logger.info(`Registered payment post-processor: ${processor.getName()}`);
    }
  
    /**
   * Process a payment
   */
    public async processPayment(request: PaymentRequest): Promise<PaymentResult> {
        try {
            logger.info(`Processing payment with method: ${request.paymentMethodType}`, {
                merchantId: request.merchantId,
                amount: request.amount,
                currency: request.currency
            });
      
            // Check if merchant exists
            const merchant = await this.prisma.merchant.findUnique({
                where: { id: request.merchantId },
                include: { subscription: true }
            });
      
            if (!merchant) {
                return {
                    success: false,
                    transactionId: uuidv4(),
                    message: "Merchant not found",
                    timestamp: new Date()
                };
            }
      
            // Check if merchant is active
            if (!merchant.isActive) {
                return {
                    success: false,
                    transactionId: uuidv4(),
                    message: "Merchant account is inactive",
                    timestamp: new Date()
                };
            }
      
            // Check if merchant subscription allows this payment method
            const canUsePaymentMethod = await this.subscriptionService.canUsePaymentMethod(
                merchant.id,
                request.paymentMethodType
            );
      
            if (!canUsePaymentMethod) {
                return {
                    success: false,
                    transactionId: uuidv4(),
                    message: "Payment method not available for current subscription plan",
                    timestamp: new Date()
                };
            }
      
            // Run pre-processors
            let processedRequest: unknown = request;
            for (const processor of this.preProcessors) {
                processedRequest = await processor.process(processedRequest);
            }
      
            // Get the appropriate payment method
            const paymentMethod: unknown = this.paymentMethodFactory.getPaymentMethod(processedRequest.paymentMethodType);
      
            // Validate payment data
            const validation = paymentMethod.validatePaymentData(processedRequest.paymentData);
            if (!validation.valid) {
                return {
                    success: false,
                    transactionId: uuidv4(),
                    message: `Invalid payment data: ${validation.errors?.join(", ")}`,
                    timestamp: new Date()
                };
            }
      
            // Execute payment
            let result = await paymentMethod.processPayment(processedRequest);
      
            // Run post-processors
            for (const processor of this.postProcessors) {
                result = await processor.process(result, processedRequest);
            }
      
            // Save transaction to database
            await this.saveTransaction(result, processedRequest);
      
            return result;
        } catch (error) {
            logger.error(`Payment processing error: ${(error as Error).message}`, {
                paymentMethodType: request.paymentMethodType,
                merchantId: request.merchantId,
                error
            });
      
            return {
                success: false,
                transactionId: uuidv4(),
                message: `Payment processing error: ${(error as Error).message}`,
                timestamp: new Date()
            };
        }
    }
  
    /**
   * Get available payment methods for a merchant
   */
    public async getAvailablePaymentMethods(merchantId: string, currency?: string): Promise<IPaymentMethod[]> {
        try {
            // Get merchant with subscription
            const merchant = await this.prisma.merchant.findUnique({
                where: { id: merchantId },
                include: { subscription: true }
            });
      
            if (!merchant) {
                return [];
            }
      
            // Get all enabled payment methods
            let paymentMethods: unknown = this.paymentMethodFactory.getEnabledPaymentMethods();
      
            // Filter by currency if specified
            if (currency) {
                paymentMethods = paymentMethods.filter(method => method.getSupportedCurrencies().includes(currency)
                );
            }
      
            // Filter by subscription capabilities
            const allowedMethods: IPaymentMethod[] = [];
      
            for (const method of paymentMethods) {
                const canUse = await this.subscriptionService.canUsePaymentMethod(
                    merchantId,
                    method.getType()
                );
        
                if (canUse) {
                    allowedMethods.push(method);
                }
            }
      
            return allowedMethods;
        } catch (error) {
            logger.error(`Error getting available payment methods: ${(error as Error).message}`, {
                merchantId,
                error
            });
      
            return [];
        }
    }
  
    /**
   * Save transaction to database
   */
    private async saveTransaction(result: PaymentResult, request: PaymentRequest): Promise<void> {
        try {
            await this.prisma.transaction.create({
                data: { id: result.transactionId,
                    merchantId: request.merchantId,
                    amount: request.amount,
                    currency: request.currency,
                    paymentMethodId: request.paymentMethodId,
                    paymentMethodType: request.paymentMethodType,
                    status: result.success ? "SUCCESS" : "FAILED",
                    message: (result as Error).message ?? "",
                    details: result.details as any,
                    metadata: {
                        ...request.metadata,
                        ...result.metadata
                    } as any
                }
            });
        } catch (error) {
            logger.error(`Error saving transaction: ${(error as Error).message}`, {
                transactionId: result.transactionId,
                error
            });
        }
    }
}
