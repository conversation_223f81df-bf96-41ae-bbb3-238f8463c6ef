// jscpd:ignore-file
/**
 * Enhanced Payment Service
 * 
 * Handles payment operations using the new abstraction layer.
 */

import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { IPaymentMethod, PaymentRequest, PaymentResult } from "../../interfaces/payment/IPaymentMethod";
import { PaymentMethodFactory as ImportedPaymentMethodFactory } from "../../factories/payment/PaymentMethodFactory";
import { PaymentGatewayFactory as ImportedPaymentGatewayFactory } from "../../factories/payment/PaymentGatewayFactory";
import { PaymentMethodType as ImportedPaymentMethodType } from "../../types/payment-(method as any).types";
import { logger as Importedlogger } from "../../lib/logger";
import { SubscriptionService as ImportedSubscriptionService } from "../(subscription as any).service";
import { v4 as uuidv4 } from "uuid";
import { Merchant, PaymentMethodType } from '../types';
import { IPaymentMethod, PaymentRequest, PaymentResult } from "../../interfaces/payment/IPaymentMethod";
import { PaymentMethodFactory as ImportedPaymentMethodFactory } from "../../factories/payment/PaymentMethodFactory";
import { PaymentGatewayFactory as ImportedPaymentGatewayFactory } from "../../factories/payment/PaymentGatewayFactory";
import { PaymentMethodType as ImportedPaymentMethodType } from "../../types/payment-(method as any).types";
import { logger as Importedlogger } from "../../lib/logger";
import { SubscriptionService as ImportedSubscriptionService } from "../(subscription as any).service";
import { v4 as uuidv4 } from "uuid";
import { Merchant, PaymentMethodType } from '../types';


/**
 * Payment pre-processor interface
 */
export interface PaymentPreProcessor {
  /**
   * Get the name of the pre-processor
   */
  getName(): string;
  
  /**
   * Process a payment request
   */
  process(request: PaymentRequest): Promise<PaymentRequest>;
}

/**
 * Payment post-processor interface
 */
export interface PaymentPostProcessor {
  /**
   * Get the name of the post-processor
   */
  getName(): string;
  
  /**
   * Process a payment result
   */
  process(result: PaymentResult, request: PaymentRequest): Promise<PaymentResult>;
}

/**
 * Enhanced payment service
 */
export class EnhancedPaymentService {
    private prisma: PrismaClient;
    private paymentMethodFactory: PaymentMethodFactory;
    private paymentGatewayFactory: PaymentGatewayFactory;
    private subscriptionService: SubscriptionService;
    private preProcessors: PaymentPreProcessor[] = [];
    private postProcessors: PaymentPostProcessor[] = [];
  
    /**
   * Constructor
   */
    constructor(prisma: PrismaClient, subscriptionService: SubscriptionService) {
        this.prisma = prisma;
        this.paymentMethodFactory = (PaymentMethodFactory as any).getInstance();
        this.paymentGatewayFactory = (PaymentGatewayFactory as any).getInstance();
        this.subscriptionService = subscriptionService;
    }
  
    /**
   * Register a pre-processor
   */
    public registerPreProcessor(processor: PaymentPreProcessor): void {
        this.preProcessors.push(processor);
        (logger as any).info(`Registered payment pre-processor: ${(processor as any).getName()}`);
    }
  
    /**
   * Register a post-processor
   */
    public registerPostProcessor(processor: PaymentPostProcessor): void {
        this.postProcessors.push(processor);
        (logger as any).info(`Registered payment post-processor: ${(processor as any).getName()}`);
    }
  
    /**
   * Process a payment
   */
    public async processPayment(request: PaymentRequest): Promise<PaymentResult> {
        try {
            (logger as any).info(`Processing payment with method: ${(request as any).paymentMethodType}`, {
                merchantId: (request as any).merchantId,
                amount: (request as any).amount,
                currency: (request as any).currency
            });
      
            // Check if merchant exists
            const merchant = await this.prisma.(merchant as any).findUnique({
                where: { id: (request as any).merchantId },
                include: { subscription: true }
            });
      
            if (!merchant) {
                return {
                    success: false,
                    transactionId: uuidv4(),
                    message: "Merchant not found",
                    timestamp: new Date()
                };
            }
      
            // Check if merchant is active
            if (!(merchant as any).isActive) {
                return {
                    success: false,
                    transactionId: uuidv4(),
                    message: "Merchant account is inactive",
                    timestamp: new Date()
                };
            }
      
            // Check if merchant subscription allows this payment method
            const canUsePaymentMethod = await this.subscriptionService.canUsePaymentMethod(
                (merchant as any).id,
                (request as any).paymentMethodType
            );
      
            if (!canUsePaymentMethod) {
                return {
                    success: false,
                    transactionId: uuidv4(),
                    message: "Payment method not available for current subscription plan",
                    timestamp: new Date()
                };
            }
      
            // Run pre-processors
            let processedRequest: any = request;
            for (const processor of this.preProcessors) {
                processedRequest = await (processor as any).process(processedRequest);
            }
      
            // Get the appropriate payment method
            const paymentMethod: any = this.paymentMethodFactory.getPaymentMethod((processedRequest as any).paymentMethodType);
      
            // Validate payment data
            const validation = (paymentMethod as any).validatePaymentData((processedRequest as any).paymentData);
            if (!(validation as any).valid) {
                return {
                    success: false,
                    transactionId: uuidv4(),
                    message: `Invalid payment data: ${validation.errors?.join(", ")}`,
                    timestamp: new Date()
                };
            }
      
            // Execute payment
            let result = await (paymentMethod as any).processPayment(processedRequest);
      
            // Run post-processors
            for (const processor of this.postProcessors) {
                result = await (processor as any).process(result, processedRequest);
            }
      
            // Save transaction to database
            await this.saveTransaction(result, processedRequest);
      
            return result;
        } catch(error) {
            (logger as any).error(`Payment processing error: ${error.message}`, {
                paymentMethodType: (request as any).paymentMethodType,
                merchantId: (request as any).merchantId,
                error
            });
      
            return {
                success: false,
                transactionId: uuidv4(),
                message: `Payment processing error: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
  
    /**
   * Get available payment methods for a merchant
   */
    public async getAvailablePaymentMethods(merchantId: string, currency?: string): Promise<IPaymentMethod[]> {
        try {
            // Get merchant with subscription
            const merchant = await this.prisma.(merchant as any).findUnique({
                where: { id: merchantId },
                include: { subscription: true }
            });
      
            if (!merchant) {
                return [];
            }
      
            // Get all enabled payment methods
            let paymentMethods: any = this.paymentMethodFactory.getEnabledPaymentMethods();
      
            // Filter by currency if specified
            if (currency) {
                paymentMethods = (paymentMethods as any).filter(method => (method as any).getSupportedCurrencies().includes(currency)
                );
            }
      
            // Filter by subscription capabilities
            const allowedMethods: IPaymentMethod[] = [];
      
            for (const method of paymentMethods) {
                const canUse = await this.subscriptionService.canUsePaymentMethod(
                    merchantId,
                    (method as any).getType()
                );
        
                if (canUse) {
                    (allowedMethods as any).push(method);
                }
            }
      
            return allowedMethods;
        } catch(error) {
            (logger as any).error(`Error getting available payment methods: ${error.message}`, {
                merchantId,
                error
            });
      
            return [];
        }
    }
  
    /**
   * Save transaction to database
   */
    private async saveTransaction(result: PaymentResult, request: PaymentRequest): Promise<void> {
        try {
            await this.prisma.(transaction as any).create({
                data: { id: result.transactionId,
                    merchantId: (request as any).merchantId,
                    amount: (request as any).amount,
                    currency: (request as any).currency,
                    paymentMethodId: (request as any).paymentMethodId,
                    paymentMethodType: (request as any).paymentMethodType,
                    status: result.success ? "SUCCESS" : "FAILED",
                    message: (result as Error).message ?? "",
                    details: (result as any).details as any,
                    metadata: {
                        ...(request as any).metadata,
                        ...(result as any).metadata
                    } as any
                }
            });
        } catch(error) {
            (logger as any).error(`Error saving transaction: ${error.message}`, {
                transactionId: result.transactionId,
                error
            });
        }
    }
}
