// jscpd:ignore-file
/**
 * Production database connection handler
 *
 * Provides database connections for the production environment.
 */

import { PrismaClient } from '@prisma/client';
import { logger } from './logger';
import { OperationalMode } from '../services/system/OperationalModeService';
import { container } from './DIContainer';
import { logger } from './logger';
import { OperationalMode } from '../services/system/OperationalModeService';
import { container } from './DIContainer';

// Connection pools for different modes
const connectionPools: Record<string, PrismaClient> = {};

/**
 * Get a database connection for the specified operational mode
 *
 * @param mode Operational mode
 * @returns PrismaClient instance for the specified mode
 */
export const getDbConnectionForMode = async (
  mode: OperationalMode
): Promise<PrismaClient> => {
  try {
    // Check if connection already exists
    if (connectionPools[mode]) {
      return connectionPools[mode];
    }

    // Always use production connection
    connectionPools[mode] = new PrismaClient({
      log: ['error', 'warn'],
      errorFormat: 'minimal',
    });

    logger.info(`Created database connection for ${mode} mode`);
    return connectionPools[mode];
  } catch (error) {
    logger.error(`<PERSON>rro<PERSON> creating database connection for ${mode} mode:`, error);
    throw error;
  }
};

// Demo mode middleware has been removed - only production is supported

/**
 * Get the current database connection
 *
 * @returns PrismaClient instance for production
 */
export const getCurrentDbConnection = async (): Promise<PrismaClient> => {
  try {
    // Always use production mode
    return await getDbConnectionForMode(OperationalMode.PRODUCTION);
  } catch (error) {
    logger.error('Error getting database connection:', error);

    // Fall back to the default connection
    return new PrismaClient({
      log: ['error', 'warn'],
      errorFormat: 'minimal',
    });
  }
};

/**
 * Close all database connections
 */
export const closeAllConnections = async (): Promise<void> => {
  try {
    // Close all connections
    for (const [mode, connection] of Object.entries(connectionPools)) {
      await connection.$disconnect();
      logger.info(`Closed database connection for ${mode} mode`);
    }

    // Clear connection pools
    Object.keys(connectionPools).forEach((key) => {
      delete connectionPools[key];
    });
  } catch (error) {
    logger.error('Error closing database connections:', error);
    throw error;
  }
};
