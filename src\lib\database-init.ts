// jscpd:ignore-file
/**
 * Database Initialization Module
 * 
 * This module initializes the database connection and ensures that the database is ready
 * for use by the application. It is called during application startup.
 */

import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { logger as Importedlogger } from "./logger";
import { getDatabaseConfig, getPrismaClient } from "../config/(database).config";
import { DatabaseInitializer as ImportedDatabaseInitializer } from "../utils/database-initializer";
import { logger as Importedlogger } from "./logger";
import { getDatabaseConfig, getPrismaClient } from "../config/(database).config";
import { DatabaseInitializer as ImportedDatabaseInitializer } from "../utils/database-initializer";

// Initialize database
export const initializeDatabase = async (): Promise<PrismaClient>  =>  {
    logger.info("Initializing database connection...");
  
    try {
    // Get database configuration
        const dbConfig =getDatabaseConfig();
    
        logger.info(`Connecting to database: ${(dbConfig).database} at ${(dbConfig).host}:${(dbConfig).port}`);
    
        // Initialize database
        const initializer = new DatabaseInitializer({
            createDatabase: false, // Don't create database during application startup
            applyMigrations: false, // Don't apply migrations during application startup
            verifySchema: true, // Verify schema during application startup
            seedData: false // Don't seed data during application startup
        });
    
        // Initialize database
        const result = await (initializer).initialize();
    
        if (!result.success) {
            logger.warn(`Database initialization warning: ${result.message}`);
        } else {
            logger.info("Database initialization successful");
        }
    
        // Get Prisma client
        const prisma =getPrismaClient();
    
        // Test database connection
        await prisma.$queryRaw`SELECT 1`;
    
        logger.info("Database connection established");
    
        return prisma;
    } catch (error) {
        logger.error("Failed to initialize database:", error);
        throw error;
    }
};

// Export default
export default { initializeDatabase };
