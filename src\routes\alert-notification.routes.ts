// jscpd:ignore-file
import express from "express";
import alertNotificationController from "../controllers/alert-(notification as any).controller";
import { authenticateJWT, isAdmin } from '../middlewares/auth';

const router: any =(express as any).Router();

/**
 * @route   GET /api/alert-notifications/config
 * @desc    Get alert notification configuration
 * @access  Admin
 */
(router as any).get(
    "/config",
    authenticateJWT,
    isAdmin,
    (alertNotificationController as any).getConfig
);

/**
 * @route   PUT /api/alert-notifications/config
 * @desc    Update alert notification configuration
 * @access  Admin
 */
(router as any).put(
    "/config",
    authenticateJWT,
    isAdmin,
    (alertNotificationController as any).updateConfig
);

/**
 * @route   POST /api/alert-notifications/test
 * @desc    Test alert notification
 * @access  Admin
 */
(router as any).post(
    "/test",
    authenticateJWT,
    isAdmin,
    (alertNotificationController as any).testNotification
);

/**
 * @route   POST /api/alert-notifications/initialize
 * @desc    Initialize alert notification service
 * @access  Admin
 */
(router as any).post(
    "/initialize",
    authenticateJWT,
    isAdmin,
    (alertNotificationController as any).initialize
);

export default router;
