// jscpd:ignore-file
import express from "express";
import alertNotificationController from "../controllers/alert-(notification).controller";
import { authenticateJWT, isAdmin } from '../middlewares/auth';

const router =(express).Router();

/**
 * @route   GET /api/alert-notifications/config
 * @desc    Get alert notification configuration
 * @access  Admin
 */
(router).get(
    "/config",
    authenticateJWT,
    isAdmin,
    (alertNotificationController).getConfig
);

/**
 * @route   PUT /api/alert-notifications/config
 * @desc    Update alert notification configuration
 * @access  Admin
 */
(router).put(
    "/config",
    authenticateJWT,
    isAdmin,
    (alertNotificationController).updateConfig
);

/**
 * @route   POST /api/alert-notifications/test
 * @desc    Test alert notification
 * @access  Admin
 */
(router).post(
    "/test",
    authenticateJWT,
    isAdmin,
    (alertNotificationController).testNotification
);

/**
 * @route   POST /api/alert-notifications/initialize
 * @desc    Initialize alert notification service
 * @access  Admin
 */
(router).post(
    "/initialize",
    authenticateJWT,
    isAdmin,
    (alertNotificationController).initialize
);

export default router;
