// Service layer types and interfaces
import { PrismaClientType, PrismaTransaction } from './prisma';
import { UserContext, ServiceResult, ValidationError } from './common';

// Base service interface
export interface BaseService {
  readonly name: string;
  readonly version: string;
}

// Service dependencies
export interface ServiceDependencies {
  prisma: PrismaClientType;
  logger?: LoggerService;
  cache?: CacheService;
  eventBus?: EventBusService;
}

// Service configuration
export interface ServiceConfiguration {
  [key: string]: any;
}

// Service context
export interface ServiceContext {
  user?: UserContext;
  requestId?: string;
  transaction?: PrismaTransaction;
  metadata?: Record<string, unknown>;
}

// Service operation result
export interface ServiceOperationResult<T = any> extends ServiceResult<T> {
  operationId?: string;
  duration?: number;
  metadata?: Record<string, unknown>;
}

// User service types
export interface UserService extends BaseService {
  createUser(data: CreateUserData, context?: ServiceContext): Promise<ServiceOperationResult<User>>;
  getUserById(id: string, context?: ServiceContext): Promise<ServiceOperationResult<User | null>>;
  getUserByEmail(email: string, context?: ServiceContext): Promise<ServiceOperationResult<User | null>>;
  updateUser(id: string, data: UpdateUserData, context?: ServiceContext): Promise<ServiceOperationResult<User>>;
  deleteUser(id: string, context?: ServiceContext): Promise<ServiceOperationResult<void>>;
  listUsers(filters?: UserFilters, pagination?: PaginationParams, context?: ServiceContext): Promise<ServiceOperationResult<PaginatedResponse<User>>>;
  validateUser(data: any, context?: ServiceContext): Promise<ServiceOperationResult<ValidationResult>>;
}

// Merchant service types
export interface MerchantService extends BaseService {
  createMerchant(data: CreateMerchantData, context?: ServiceContext): Promise<ServiceOperationResult<Merchant>>;
  getMerchantById(id: string, context?: ServiceContext): Promise<ServiceOperationResult<Merchant | null>>;
  updateMerchant(id: string, data: UpdateMerchantData, context?: ServiceContext): Promise<ServiceOperationResult<Merchant>>;
  deleteMerchant(id: string, context?: ServiceContext): Promise<ServiceOperationResult<void>>;
  listMerchants(filters?: MerchantFilters, pagination?: PaginationParams, context?: ServiceContext): Promise<ServiceOperationResult<PaginatedResponse<Merchant>>>;
  validateMerchant(data: any, context?: ServiceContext): Promise<ServiceOperationResult<ValidationResult>>;
}

// Payment service types
export interface PaymentService extends BaseService {
  createPayment(data: CreatePaymentRequest, context?: ServiceContext): Promise<ServiceOperationResult<Payment>>;
  getPaymentById(id: string, context?: ServiceContext): Promise<ServiceOperationResult<Payment | null>>;
  updatePayment(id: string, data: UpdatePaymentRequest, context?: ServiceContext): Promise<ServiceOperationResult<Payment>>;
  cancelPayment(id: string, reason?: string, context?: ServiceContext): Promise<ServiceOperationResult<Payment>>;
  refundPayment(id: string, data: RefundRequest, context?: ServiceContext): Promise<ServiceOperationResult<RefundResponse>>;
  listPayments(filters?: PaymentFilters, pagination?: PaginationParams, context?: ServiceContext): Promise<ServiceOperationResult<PaginatedResponse<Payment>>>;
  validatePayment(data: any, context?: ServiceContext): Promise<ServiceOperationResult<ValidationResult>>;
}

// Transaction service types
export interface TransactionService extends BaseService {
  createTransaction(data: CreateTransactionData, context?: ServiceContext): Promise<ServiceOperationResult<Transaction>>;
  getTransactionById(id: string, context?: ServiceContext): Promise<ServiceOperationResult<Transaction | null>>;
  updateTransaction(id: string, data: UpdateTransactionData, context?: ServiceContext): Promise<ServiceOperationResult<Transaction>>;
  listTransactions(filters?: TransactionFilters, pagination?: PaginationParams, context?: ServiceContext): Promise<ServiceOperationResult<PaginatedResponse<Transaction>>>;
  validateTransaction(data: any, context?: ServiceContext): Promise<ServiceOperationResult<ValidationResult>>;
}

// Admin service types
export interface AdminService extends BaseService {
  createAdminUser(data: CreateAdminUserRequest, context?: ServiceContext): Promise<ServiceOperationResult<AdminUserResponse>>;
  getAdminUserById(id: string, context?: ServiceContext): Promise<ServiceOperationResult<AdminUserResponse | null>>;
  updateAdminUser(id: string, data: UpdateAdminUserRequest, context?: ServiceContext): Promise<ServiceOperationResult<AdminUserResponse>>;
  deleteAdminUser(id: string, context?: ServiceContext): Promise<ServiceOperationResult<void>>;
  listAdminUsers(filters?: AdminUserFilters, pagination?: PaginationParams, context?: ServiceContext): Promise<ServiceOperationResult<PaginatedResponse<AdminUserResponse>>>;
  validateAdminUser(data: any, context?: ServiceContext): Promise<ServiceOperationResult<ValidationResult>>;
  getDashboardData(context?: ServiceContext): Promise<ServiceOperationResult<DashboardDataResponse>>;
  getSystemHealth(context?: ServiceContext): Promise<ServiceOperationResult<SystemHealthStatus>>;
}

// Authentication service types
export interface AuthenticationService extends BaseService {
  login(credentials: LoginCredentials, context?: ServiceContext): Promise<ServiceOperationResult<AuthTokens>>;
  logout(token: string, context?: ServiceContext): Promise<ServiceOperationResult<void>>;
  refreshToken(refreshToken: string, context?: ServiceContext): Promise<ServiceOperationResult<AuthTokens>>;
  verifyToken(token: string, context?: ServiceContext): Promise<ServiceOperationResult<JwtPayload>>;
  resetPassword(data: PasswordResetRequest, context?: ServiceContext): Promise<ServiceOperationResult<void>>;
  changePassword(data: ChangePassword, context?: ServiceContext): Promise<ServiceOperationResult<void>>;
  validateCredentials(credentials: LoginCredentials, context?: ServiceContext): Promise<ServiceOperationResult<ValidationResult>>;
}

// Authorization service types
export interface AuthorizationService extends BaseService {
  checkPermission(user: UserContext, resource: string, action: string, context?: ServiceContext): Promise<ServiceOperationResult<PermissionResult>>;
  checkRole(user: UserContext, requiredRole: string, context?: ServiceContext): Promise<ServiceOperationResult<boolean>>;
  getUserPermissions(userId: string, context?: ServiceContext): Promise<ServiceOperationResult<string[]>>;
  validatePermissions(user: UserContext, permissions: string[], context?: ServiceContext): Promise<ServiceOperationResult<ValidationResult>>;
}

// Validation service types
export interface ValidationService extends BaseService {
  validateData<T = any>(data: any, schema: ValidationSchema, context?: ServiceContext): Promise<ServiceOperationResult<ValidationResult<T>>>;
  validateId(id: string, fieldName?: string, context?: ServiceContext): Promise<ServiceOperationResult<ValidationResult<string>>>;
  validateEmail(email: string, context?: ServiceContext): Promise<ServiceOperationResult<ValidationResult<string>>>;
  validatePassword(password: string, context?: ServiceContext): Promise<ServiceOperationResult<ValidationResult<string>>>;
  validatePhoneNumber(phoneNumber: string, context?: ServiceContext): Promise<ServiceOperationResult<ValidationResult<string>>>;
}

// Logger service types
export interface LoggerService extends BaseService {
  debug(message: string, meta?: LogMetadata): void;
  info(message: string, meta?: LogMetadata): void;
  warn(message: string, meta?: LogMetadata): void;
  error(message: string, error?: Error, meta?: LogMetadata): void;
  fatal(message: string, error?: Error, meta?: LogMetadata): void;
}

// Cache service types
export interface CacheService extends BaseService {
  get<T = any>(key: string): Promise<T | null>;
  set<T = any>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  exists(key: string): Promise<boolean>;
  ttl(key: string): Promise<number>;
}

// Event bus service types
export interface EventBusService extends BaseService {
  emit(event: string, payload: any, context?: ServiceContext): Promise<void>;
  on(event: string, handler: EventHandler): void;
  off(event: string, handler: EventHandler): void;
  once(event: string, handler: EventHandler): void;
}

// Email service types
export interface EmailService extends BaseService {
  sendEmail(options: EmailOptions, context?: ServiceContext): Promise<ServiceOperationResult<EmailResult>>;
  sendTemplate(templateId: string, data: Record<string, unknown>, to: string | string[], context?: ServiceContext): Promise<ServiceOperationResult<EmailResult>>;
  validateEmail(email: string, context?: ServiceContext): Promise<ServiceOperationResult<ValidationResult<string>>>;
}

// Notification service types
export interface NotificationService extends BaseService {
  sendNotification(options: NotificationOptions, context?: ServiceContext): Promise<ServiceOperationResult<NotificationResult>>;
  sendBulkNotifications(notifications: NotificationOptions[], context?: ServiceContext): Promise<ServiceOperationResult<NotificationResult[]>>;
  getNotificationHistory(userId: string, pagination?: PaginationParams, context?: ServiceContext): Promise<ServiceOperationResult<PaginatedResponse<NotificationHistory>>>;
}

// File service types
export interface FileService extends BaseService {
  uploadFile(file: FileUploadData, options?: FileUploadOptions, context?: ServiceContext): Promise<ServiceOperationResult<FileResult>>;
  downloadFile(fileId: string, context?: ServiceContext): Promise<ServiceOperationResult<FileDownloadResult>>;
  deleteFile(fileId: string, context?: ServiceContext): Promise<ServiceOperationResult<void>>;
  getFileInfo(fileId: string, context?: ServiceContext): Promise<ServiceOperationResult<FileInfo>>;
}

// Analytics service types
export interface AnalyticsService extends BaseService {
  trackEvent(event: AnalyticsEvent, context?: ServiceContext): Promise<ServiceOperationResult<void>>;
  getMetrics(query: MetricsQuery, context?: ServiceContext): Promise<ServiceOperationResult<MetricsResult>>;
  generateReport(reportType: string, parameters: Record<string, unknown>, context?: ServiceContext): Promise<ServiceOperationResult<ReportResult>>;
}

// Supporting types
export interface ValidationSchema {
  type: 'object' | 'array' | 'string' | 'number' | 'boolean';
  properties?: Record<string, ValidationSchema>;
  required?: string[];
  items?: ValidationSchema;
  minLength?: number;
  maxLength?: number;
  minimum?: number;
  maximum?: number;
  pattern?: string;
  enum?: any[];
  format?: string;
}

export interface ValidationResult<T = any> {
  isValid: boolean;
  data?: T;
  errors: ValidationError[];
}

export interface PermissionResult {
  allowed: boolean;
  denied: boolean;
  reason?: string;
}

export interface LogMetadata {
  userId?: string;
  requestId?: string;
  operation?: string;
  duration?: number;
  [key: string]: any;
}

export interface EventHandler {
  (payload: any, context?: ServiceContext): Promise<void> | void;
}

export interface EmailResult {
  messageId: string;
  status: 'sent' | 'failed' | 'queued';
  error?: string;
}

export interface NotificationResult {
  id: string;
  status: 'sent' | 'failed' | 'queued';
  error?: string;
}

export interface NotificationHistory {
  id: string;
  type: string;
  title: string;
  message: string;
  status: string;
  sentAt: Date;
  readAt?: Date;
}

export interface FileUploadData {
  buffer: Buffer;
  originalName: string;
  mimeType: string;
  size: number;
}

export interface FileResult {
  id: string;
  url: string;
  filename: string;
  size: number;
  mimeType: string;
  uploadedAt: Date;
}

export interface FileDownloadResult {
  buffer: Buffer;
  filename: string;
  mimeType: string;
  size: number;
}

export interface FileInfo {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  uploadedAt: Date;
  uploadedBy: string;
}

export interface AnalyticsEvent {
  name: string;
  properties: Record<string, unknown>;
  userId?: string;
  sessionId?: string;
  timestamp?: Date;
}

export interface MetricsQuery {
  metrics: string[];
  filters?: Record<string, unknown>;
  groupBy?: string[];
  timeRange: {
    start: Date;
    end: Date;
  };
  granularity?: 'minute' | 'hour' | 'day' | 'week' | 'month';
}

export interface MetricsResult {
  data: MetricsDataPoint[];
  metadata: {
    query: MetricsQuery;
    executedAt: Date;
    duration: number;
  };
}

export interface MetricsDataPoint {
  timestamp: Date;
  values: Record<string, number>;
  dimensions?: Record<string, string>;
}

export interface ReportResult {
  id: string;
  type: string;
  status: 'generating' | 'completed' | 'failed';
  url?: string;
  generatedAt?: Date;
  error?: string;
}
