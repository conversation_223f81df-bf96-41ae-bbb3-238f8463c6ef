#!/usr/bin/env node

/**
 * Fix Missing Spaces Script
 * Specifically targets missing spaces after = in assignments
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎯 FIX MISSING SPACES SCRIPT');
console.log('============================');

// Specific fixes for missing spaces after =
const missingSpaceFixes = {
    // Missing spaces after = in variable assignments
    ': Date =new ': ': Date = new ',
    ': Date =Date.': ': Date = Date.',
    ': Date =new Date(': ': Date = new Date(',
    ': number =0': ': number = 0',
    ': number =1': ': number = 1',
    ': number =2': ': number = 2',
    ': number =3': ': number = 3',
    ': number =4': ': number = 4',
    ': number =5': ': number = 5',
    ': number =6': ': number = 6',
    ': number =7': ': number = 7',
    ': number =8': ': number = 8',
    ': number =9': ': number = 9',
    ': number =10': ': number = 10',
    ': string =\'': ': string = \'',
    ': string ="': ': string = "',
    ': boolean =true': ': boolean = true',
    ': boolean =false': ': boolean = false',
    ': unknown =new ': ': unknown = new ',
    ': unknown =Date.': ': unknown = Date.',
    ': unknown =new Date(': ': unknown = new Date(',
    ': unknown =0': ': unknown = 0',
    ': unknown =1': ': unknown = 1',
    ': unknown =2': ': unknown = 2',
    ': unknown =3': ': unknown = 3',
    ': unknown =4': ': unknown = 4',
    ': unknown =5': ': unknown = 5',
    ': unknown =6': ': unknown = 6',
    ': unknown =7': ': unknown = 7',
    ': unknown =8': ': unknown = 8',
    ': unknown =9': ': unknown = 9',
    ': unknown =10': ': unknown = 10',
    ': unknown =\'': ': unknown = \'',
    ': unknown ="': ': unknown = "',
    ': unknown =true': ': unknown = true',
    ': unknown =false': ': unknown = false',
    ': any =new ': ': any = new ',
    ': any =Date.': ': any = Date.',
    ': any =new Date(': ': any = new Date(',
    ': any =0': ': any = 0',
    ': any =1': ': any = 1',
    ': any =2': ': any = 2',
    ': any =3': ': any = 3',
    ': any =4': ': any = 4',
    ': any =5': ': any = 5',
    ': any =6': ': any = 6',
    ': any =7': ': any = 7',
    ': any =8': ': any = 8',
    ': any =9': ': any = 9',
    ': any =10': ': any = 10',
    ': any =\'': ': any = \'',
    ': any ="': ': any = "',
    ': any =true': ': any = true',
    ': any =false': ': any = false',
    
    // Missing spaces in const/let/var declarations
    'const startDate: Date =new ': 'const startDate: Date = new ',
    'const endDate: Date =new ': 'const endDate: Date = new ',
    'const date: Date =new ': 'const date: Date = new ',
    'const now: Date =new ': 'const now: Date = new ',
    'const createdAt: Date =new ': 'const createdAt: Date = new ',
    'const resolvedAt: Date =new ': 'const resolvedAt: Date = new ',
    'const updatedAt: Date =new ': 'const updatedAt: Date = new ',
    'let startDate: Date =new ': 'let startDate: Date = new ',
    'let endDate: Date =new ': 'let endDate: Date = new ',
    'let date: Date =new ': 'let date: Date = new ',
    'let now: Date =new ': 'let now: Date = new ',
    'let createdAt: Date =new ': 'let createdAt: Date = new ',
    'let resolvedAt: Date =new ': 'let resolvedAt: Date = new ',
    'let updatedAt: Date =new ': 'let updatedAt: Date = new ',
    
    // Missing spaces in number assignments
    'let medianResolutionTimeMinutes: number =0': 'let medianResolutionTimeMinutes: number = 0',
    'const midIndex: unknown =Math.': 'const midIndex: unknown = Math.',
    'const sortedTimes: unknown =[': 'const sortedTimes: unknown = [',
    'const averageResolutionTimeMinutes: unknown =resolutionTimes': 'const averageResolutionTimeMinutes: unknown = resolutionTimes',
    'const resolutionTimes: unknown =resolvedAlerts': 'const resolutionTimes: unknown = resolvedAlerts',
    'const unresolvedCount: unknown =await ': 'const unresolvedCount: unknown = await ',
    'const resolvedAlerts: unknown =await ': 'const resolvedAlerts: unknown = await ',
    'const currentPeriodEnd: Date =new ': 'const currentPeriodEnd: Date = new ',
    'const currentPeriodStart: Date =new ': 'const currentPeriodStart: Date = new ',
    'const previousPeriodEnd: Date =new ': 'const previousPeriodEnd: Date = new ',
    'const previousPeriodStart: Date =new ': 'const previousPeriodStart: Date = new ',
    'const currentPeriodWhere: unknown ={': 'const currentPeriodWhere: unknown = {',
    'const previousPeriodWhere: unknown ={': 'const previousPeriodWhere: unknown = {',
    'const currentPeriodCount: unknown =await ': 'const currentPeriodCount: unknown = await ',
    'const currentPeriodCriticalCount: unknown =await ': 'const currentPeriodCriticalCount: unknown = await ',
    'const currentPeriodErrorCount: unknown =await ': 'const currentPeriodErrorCount: unknown = await ',
    'const previousPeriodCount: unknown =await ': 'const previousPeriodCount: unknown = await ',
    'const previousPeriodCriticalCount: unknown =await ': 'const previousPeriodCriticalCount: unknown = await ',
    'const previousPeriodErrorCount: unknown =await ': 'const previousPeriodErrorCount: unknown = await ',
    'const percentageChange: unknown =previousPeriodCount': 'const percentageChange: unknown = previousPeriodCount',
    'const now: Date =new ': 'const now: Date = new ',
    
    // Missing spaces in other common patterns
    '=new Array(': '= new Array(',
    '=new Map(': '= new Map(',
    '=new Set(': '= new Set(',
    '=new Promise(': '= new Promise(',
    '=new Error(': '= new Error(',
    '=new RegExp(': '= new RegExp(',
    '=new URL(': '= new URL(',
    '=new Buffer(': '= new Buffer(',
    '=new FormData(': '= new FormData(',
    '=new Headers(': '= new Headers(',
    '=new Request(': '= new Request(',
    '=new Response(': '= new Response(',
    '=new AbortController(': '= new AbortController(',
    '=new EventTarget(': '= new EventTarget(',
    '=new CustomEvent(': '= new CustomEvent(',
    '=new MessageEvent(': '= new MessageEvent(',
    '=new CloseEvent(': '= new CloseEvent(',
    '=new ErrorEvent(': '= new ErrorEvent(',
    '=new ProgressEvent(': '= new ProgressEvent(',
    '=new Blob(': '= new Blob(',
    '=new File(': '= new File(',
    '=new FileReader(': '= new FileReader(',
    '=new XMLHttpRequest(': '= new XMLHttpRequest(',
    '=new WebSocket(': '= new WebSocket(',
    '=new Worker(': '= new Worker(',
    '=new SharedWorker(': '= new SharedWorker(',
    '=new ServiceWorker(': '= new ServiceWorker(',
    '=new Notification(': '= new Notification(',
    '=new IntersectionObserver(': '= new IntersectionObserver(',
    '=new MutationObserver(': '= new MutationObserver(',
    '=new ResizeObserver(': '= new ResizeObserver(',
    '=new PerformanceObserver(': '= new PerformanceObserver(',
    '=new Intl.': '= new Intl.',
    '=new WeakMap(': '= new WeakMap(',
    '=new WeakSet(': '= new WeakSet(',
    '=new Proxy(': '= new Proxy(',
    '=new DataView(': '= new DataView(',
    '=new ArrayBuffer(': '= new ArrayBuffer(',
    '=new SharedArrayBuffer(': '= new SharedArrayBuffer(',
    '=new Int8Array(': '= new Int8Array(',
    '=new Uint8Array(': '= new Uint8Array(',
    '=new Uint8ClampedArray(': '= new Uint8ClampedArray(',
    '=new Int16Array(': '= new Int16Array(',
    '=new Uint16Array(': '= new Uint16Array(',
    '=new Int32Array(': '= new Int32Array(',
    '=new Uint32Array(': '= new Uint32Array(',
    '=new Float32Array(': '= new Float32Array(',
    '=new Float64Array(': '= new Float64Array(',
    '=new BigInt64Array(': '= new BigInt64Array(',
    '=new BigUint64Array(': '= new BigUint64Array('
};

function findAllTypeScriptFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'coverage'].includes(item)) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

function getErrorCount() {
    try {
        const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { encoding: 'utf8' });
        const errorMatches = output.match(/error TS/g) || [];
        return errorMatches.length;
    } catch (error) {
        const errorMatches = error.stdout.match(/error TS/g) || [];
        return errorMatches.length;
    }
}

function processFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let modifiedContent = content;
        let fixCount = 0;
        
        // Apply all missing space fixes
        for (const [oldPattern, newPattern] of Object.entries(missingSpaceFixes)) {
            const regex = new RegExp(escapeRegExp(oldPattern), 'g');
            const matches = modifiedContent.match(regex);
            if (matches) {
                modifiedContent = modifiedContent.replace(regex, newPattern);
                fixCount += matches.length;
            }
        }
        
        if (fixCount > 0) {
            fs.writeFileSync(filePath, modifiedContent, 'utf8');
            return { filePath, fixCount };
        }
        
        return null;
    } catch (error) {
        return { filePath, error: error.message };
    }
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

async function main() {
    console.log('🔍 Scanning for TypeScript files...');
    
    const files = findAllTypeScriptFiles('./src');
    console.log(`📁 Found ${files.length} TypeScript files`);
    
    console.log('📊 Getting initial error count...');
    const initialErrors = getErrorCount();
    console.log(`🚨 Initial TypeScript errors: ${initialErrors}`);
    
    console.log('🚀 Starting missing space fixes...');
    const startTime = Date.now();
    
    const results = [];
    let totalFixedIssues = 0;
    
    for (const file of files) {
        const result = processFile(file);
        if (result) {
            results.push(result);
            if (result.fixCount) {
                totalFixedIssues += result.fixCount;
            }
        }
    }
    
    console.log('📊 Getting final error count...');
    const finalErrors = getErrorCount();
    const totalErrorsFixed = initialErrors - finalErrors;
    
    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;
    
    console.log('\n🎯 MISSING SPACE FIX COMPLETE!');
    console.log('===============================');
    console.log(`⏱️  Processing time: ${processingTime.toFixed(2)} seconds`);
    console.log(`📁 Files processed: ${results.filter(r => !r.error).length}`);
    console.log(`❌ Files with errors: ${results.filter(r => r.error).length}`);
    console.log(`🔧 Total fixes applied: ${totalFixedIssues}`);
    console.log(`🚨 TypeScript errors before: ${initialErrors}`);
    console.log(`✅ TypeScript errors after: ${finalErrors}`);
    console.log(`🎯 Total errors fixed: ${totalErrorsFixed}`);
    
    if (totalErrorsFixed > 0) {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}%`);
        console.log('\n🎉 SUCCESS! Missing space fixes applied successfully!');
        console.log('🏆 Your application now has improved type safety!');
    } else if (totalErrorsFixed === 0) {
        console.log('📈 Success rate: 0.0% (no net change)');
        console.log('\n✨ No errors were fixed, but no new errors were introduced!');
    } else {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}% (negative - new errors introduced)`);
        console.log('\n⚠️  Some fixes may have introduced new errors. Consider reverting.');
    }
    
    const errorFiles = results.filter(r => r.error);
    if (errorFiles.length > 0) {
        console.log('\n❌ Files with processing errors:');
        errorFiles.forEach(({ filePath, error }) => {
            console.log(`   ${path.relative(process.cwd(), filePath)}: ${error}`);
        });
    }
}

main().catch(console.error);
