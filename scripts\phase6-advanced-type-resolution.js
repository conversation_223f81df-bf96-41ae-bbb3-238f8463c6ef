#!/usr/bin/env node

/**
 * Phase 6: Advanced Type Resolution Script
 * Handles complex type errors with sophisticated pattern matching
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎯 PHASE 6: ADVANCED TYPE RESOLUTION');
console.log('====================================');

// Advanced type resolution patterns for complex scenarios
const advancedTypePatterns = {
    // Fix malformed number literals with decimal points
    '(0).5': '0.5',
    '(0).1': '0.1',
    '(0).2': '0.2',
    '(0).3': '0.3',
    '(0).4': '0.4',
    '(0).6': '0.6',
    '(0).7': '0.7',
    '(0).8': '0.8',
    '(0).9': '0.9',
    '(1).0': '1.0',
    '(1).1': '1.1',
    '(1).2': '1.2',
    '(1).3': '1.3',
    '(1).4': '1.4',
    '(1).5': '1.5',
    '(1).6': '1.6',
    '(1).7': '1.7',
    '(1).8': '1.8',
    '(1).9': '1.9',
    '(2).0': '2.0',
    '(2).1': '2.1',
    '(2).2': '2.2',
    '(2).3': '2.3',
    '(2).4': '2.4',
    '(2).5': '2.5',
    '(2).6': '2.6',
    '(2).7': '2.7',
    '(2).8': '2.8',
    '(2).9': '2.9',
    '(3).0': '3.0',
    '(3).5': '3.5',
    '(4).0': '4.0',
    '(5).0': '5.0',
    '(10).0': '10.0',
    
    // Fix complex property access chains
    '(config).velocityThresholds': 'config.velocityThresholds',
    '(config).behavioralAnalysis': 'config.behavioralAnalysis',
    '(config).machineLearning': 'config.machineLearning',
    '(config).riskModel': 'config.riskModel',
    '(config).thresholds': 'config.thresholds',
    '(config).settings': 'config.settings',
    
    '(riskAssessment).factors': 'riskAssessment.factors',
    '(riskAssessment).velocityChecks': 'riskAssessment.velocityChecks',
    '(riskAssessment).score': 'riskAssessment.score',
    '(riskAssessment).level': 'riskAssessment.level',
    '(riskAssessment).isFlagged': 'riskAssessment.isFlagged',
    '(riskAssessment).isBlocked': 'riskAssessment.isBlocked',
    '(riskAssessment).behavioralPattern': 'riskAssessment.behavioralPattern',
    
    '(existingRule).name': 'existingRule.name',
    '(existingRule).description': 'existingRule.description',
    '(existingRule).conditions': 'existingRule.conditions',
    '(existingRule).actions': 'existingRule.actions',
    '(existingRule).isActive': 'existingRule.isActive',
    '(existingRule).priority': 'existingRule.priority',
    
    '(duplicateRule).id': 'duplicateRule.id',
    '(duplicateRule).name': 'duplicateRule.name',
    
    '(updatedRule).id': 'updatedRule.id',
    '(updatedRule).name': 'updatedRule.name',
    '(updatedRule).description': 'updatedRule.description',
    '(updatedRule).conditions': 'updatedRule.conditions',
    '(updatedRule).actions': 'updatedRule.actions',
    
    // Fix complex method chaining
    '(transactions).map(tx => (tx).id)': 'transactions.map(tx => tx.id)',
    '(users).map(user => (user).id)': 'users.map(user => user.id)',
    '(merchants).map(merchant => (merchant).id)': 'merchants.map(merchant => merchant.id)',
    '(payments).map(payment => (payment).id)': 'payments.map(payment => payment.id)',
    '(admins).map(admin => (admin).id)': 'admins.map(admin => admin.id)',
    '(roles).map(role => (role).id)': 'roles.map(role => role.id)',
    '(permissions).map(permission => (permission).id)': 'permissions.map(permission => permission.id)',
    
    '(items).map(item => (item).id)': 'items.map(item => item.id)',
    '(results).map(result => (result).id)': 'results.map(result => result.id)',
    '(data).map(item => (item).id)': 'data.map(item => item.id)',
    '(list).map(item => (item).id)': 'list.map(item => item.id)',
    
    // Fix complex filter operations
    '(users).filter(user => (user).isActive)': 'users.filter(user => user.isActive)',
    '(merchants).filter(merchant => (merchant).isActive)': 'merchants.filter(merchant => merchant.isActive)',
    '(payments).filter(payment => (payment).status === "SUCCESS")': 'payments.filter(payment => payment.status === "SUCCESS")',
    '(transactions).filter(tx => (tx).status === "COMPLETED")': 'transactions.filter(tx => tx.status === "COMPLETED")',
    '(admins).filter(admin => (admin).isActive)': 'admins.filter(admin => admin.isActive)',
    
    '(riskAssessments).filter(assessment => (assessment).isFlagged)': 'riskAssessments.filter(assessment => assessment.isFlagged)',
    '(riskAssessments).filter(assessment => (assessment).isBlocked)': 'riskAssessments.filter(assessment => assessment.isBlocked)',
    
    // Fix complex reduce operations
    '(riskAssessments).reduce((acc, assessment) => {': 'riskAssessments.reduce((acc, assessment) => {',
    '(users).reduce((acc, user) => {': 'users.reduce((acc, user) => {',
    '(merchants).reduce((acc, merchant) => {': 'merchants.reduce((acc, merchant) => {',
    '(payments).reduce((acc, payment) => {': 'payments.reduce((acc, payment) => {',
    '(transactions).reduce((acc, transaction) => {': 'transactions.reduce((acc, transaction) => {',
    
    'acc[(assessment).level]': 'acc[assessment.level]',
    'acc[(assessment).behavioralPattern]': 'acc[assessment.behavioralPattern]',
    'acc[(user).role]': 'acc[user.role]',
    'acc[(merchant).status]': 'acc[merchant.status]',
    'acc[(payment).status]': 'acc[payment.status]',
    'acc[(transaction).status]': 'acc[transaction.status]',
    
    '(acc[(assessment).level] ?? 0)': '(acc[assessment.level] ?? 0)',
    '(acc[(assessment).behavioralPattern] ?? 0)': '(acc[assessment.behavioralPattern] ?? 0)',
    '(acc[(user).role] ?? 0)': '(acc[user.role] ?? 0)',
    '(acc[(merchant).status] ?? 0)': '(acc[merchant.status] ?? 0)',
    '(acc[(payment).status] ?? 0)': '(acc[payment.status] ?? 0)',
    '(acc[(transaction).status] ?? 0)': '(acc[transaction.status] ?? 0)',
    
    'sum + (assessment).score': 'sum + assessment.score',
    'sum + (user).amount': 'sum + user.amount',
    'sum + (merchant).revenue': 'sum + merchant.revenue',
    'sum + (payment).amount': 'sum + payment.amount',
    'sum + (transaction).amount': 'sum + transaction.amount',
    
    // Fix complex JSON operations
    'JSON.parse((config).velocityThresholds)': 'JSON.parse(config.velocityThresholds as string)',
    'JSON.parse((config).behavioralAnalysis)': 'JSON.parse(config.behavioralAnalysis as string)',
    'JSON.parse((config).machineLearning)': 'JSON.parse(config.machineLearning as string)',
    'JSON.parse((config).settings)': 'JSON.parse(config.settings as string)',
    'JSON.parse((config).metadata)': 'JSON.parse(config.metadata as string)',
    
    'JSON.parse((riskAssessment).factors)': 'JSON.parse(riskAssessment.factors as string)',
    'JSON.parse((riskAssessment).velocityChecks)': 'JSON.parse(riskAssessment.velocityChecks as string)',
    'JSON.parse((riskAssessment).metadata)': 'JSON.parse(riskAssessment.metadata as string)',
    
    'JSON.parse((user).metadata)': 'JSON.parse(user.metadata as string)',
    'JSON.parse((merchant).settings)': 'JSON.parse(merchant.settings as string)',
    'JSON.parse((payment).metadata)': 'JSON.parse(payment.metadata as string)',
    'JSON.parse((transaction).metadata)': 'JSON.parse(transaction.metadata as string)',
    
    // Fix complex conditional operations
    '(data).name || (existingRule).name': 'data.name || existingRule.name',
    '(data).description || (existingRule).description': 'data.description || existingRule.description',
    '(data).enabled !== undefined ? (data).enabled : (existingRule).isActive': 'data.enabled !== undefined ? data.enabled : existingRule.isActive',
    
    '(data).type || ((existingRule).conditions as any)?.type': 'data.type || (existingRule.conditions as any)?.type',
    '(data).severity || ((existingRule).conditions as any)?.severity': 'data.severity || (existingRule.conditions as any)?.severity',
    '(data).timeWindow || ((existingRule).conditions as any)?.timeWindow': 'data.timeWindow || (existingRule.conditions as any)?.timeWindow',
    '(data).threshold || ((existingRule).conditions as any)?.threshold': 'data.threshold || (existingRule.conditions as any)?.threshold',
    '(data).groupBy || ((existingRule).conditions as any)?.groupBy': 'data.groupBy || (existingRule.conditions as any)?.groupBy',
    
    // Fix complex spread operations
    '...((existingRule).conditions as any)': '...(existingRule.conditions as any)',
    '...((existingRule).actions as any)': '...(existingRule.actions as any)',
    '...((existingRule).settings as any)': '...(existingRule.settings as any)',
    '...((existingRule).metadata as any)': '...(existingRule.metadata as any)',
    
    '...(config)': '...config',
    '...(user)': '...user',
    '...(merchant)': '...merchant',
    '...(payment)': '...payment',
    '...(transaction)': '...transaction',
    '...(admin)': '...admin',
    '...(role)': '...role',
    '...(permission)': '...permission',
    '...(riskAssessment)': '...riskAssessment',
    
    // Fix complex type assertions in conditions
    '(data).name && (data).name !== (existingRule).name': 'data.name && data.name !== existingRule.name',
    '(data).email && (data).email !== (existingUser).email': 'data.email && data.email !== existingUser.email',
    '(data).businessName && (data).businessName !== (existingMerchant).businessName': 'data.businessName && data.businessName !== existingMerchant.businessName',
    
    // Fix complex array operations with type assertions
    '(requiredEnvVars).filter((envVar) => !process.env[envVar])': 'requiredEnvVars.filter((envVar) => !process.env[envVar])',
    '(missingEnvVars).length': 'missingEnvVars.length',
    '(missingEnvVars).join(", ")': 'missingEnvVars.join(", ")',
    
    '(issues).length === 0': 'issues.length === 0',
    '(issues).forEach': 'issues.forEach',
    '(issues).map': 'issues.map',
    '(issues).filter': 'issues.filter',
    
    // Fix complex service method calls
    '(this.enhancedRiskEngineService).assessTransactionRisk': 'this.enhancedRiskEngineService.assessTransactionRisk',
    '(this.enhancedRiskEngineService).getMerchantRiskConfig': 'this.enhancedRiskEngineService.getMerchantRiskConfig',
    '(this.enhancedRiskEngineService)["defaultConfig"]': 'this.enhancedRiskEngineService["defaultConfig"]',
    
    '(this.adminBusinessService).getAdminUsers': 'this.adminBusinessService.getAdminUsers',
    '(this.adminBusinessService).getAdminUserById': 'this.adminBusinessService.getAdminUserById',
    '(this.adminBusinessService).createAdminUser': 'this.adminBusinessService.createAdminUser',
    '(this.adminBusinessService).updateAdminUser': 'this.adminBusinessService.updateAdminUser',
    '(this.adminBusinessService).deleteAdminUser': 'this.adminBusinessService.deleteAdminUser',
    '(this.adminBusinessService).getDashboardData': 'this.adminBusinessService.getDashboardData',
    '(this.adminBusinessService).getSystemHealth': 'this.adminBusinessService.getSystemHealth',
    
    '(this.adminValidationService).validateCreateAdminUser': 'this.adminValidationService.validateCreateAdminUser',
    '(this.adminValidationService).validateUpdateAdminUser': 'this.adminValidationService.validateUpdateAdminUser',
    '(this.adminValidationService).validateAdminUserFilters': 'this.adminValidationService.validateAdminUserFilters',
    '(this.adminValidationService).validatePagination': 'this.adminValidationService.validatePagination',
    '(this.adminValidationService).validateId': 'this.adminValidationService.validateId',
    
    '(this.adminAuthorizationService).checkAdminPermission': 'this.adminAuthorizationService.checkAdminPermission',
    '(this.adminAuthorizationService).checkAdminRole': 'this.adminAuthorizationService.checkAdminRole',
    '(this.adminAuthorizationService).validateAdminAccess': 'this.adminAuthorizationService.validateAdminAccess',
    
    // Fix complex response mapper calls
    '(AdminResponseMapper).sendError(res, error)': 'AdminResponseMapper.sendError(res, error)',
    '(AdminResponseMapper).sendSuccess(res, message, data)': 'AdminResponseMapper.sendSuccess(res, message, data)',
    '(AdminResponseMapper).sendAdminUsersList(res, result)': 'AdminResponseMapper.sendAdminUsersList(res, result)',
    '(AdminResponseMapper).sendAdminUser(res, admin)': 'AdminResponseMapper.sendAdminUser(res, admin)',
    '(AdminResponseMapper).sendAdminUserCreated(res, admin)': 'AdminResponseMapper.sendAdminUserCreated(res, admin)',
    '(AdminResponseMapper).sendAdminUserUpdated(res, admin)': 'AdminResponseMapper.sendAdminUserUpdated(res, admin)',
    '(AdminResponseMapper).sendAdminUserDeleted(res)': 'AdminResponseMapper.sendAdminUserDeleted(res)',
    '(AdminResponseMapper).sendSystemHealth(res, health)': 'AdminResponseMapper.sendSystemHealth(res, health)',
    '(AdminResponseMapper).sendDashboardData(res, data)': 'AdminResponseMapper.sendDashboardData(res, data)',
    '(AdminResponseMapper).sendValidationError(res, errors)': 'AdminResponseMapper.sendValidationError(res, errors)',
    
    // Fix complex logger calls
    '(logger).info': 'logger.info',
    '(logger).warn': 'logger.warn',
    '(logger).error': 'logger.error',
    '(logger).debug': 'logger.debug',
    '(logger).fatal': 'logger.fatal',
    
    // Fix complex response method calls
    '(res).notFound': 'res.notFound',
    '(res).badRequest': 'res.badRequest',
    '(res).unauthorized': 'res.unauthorized',
    '(res).forbidden': 'res.forbidden',
    '(res).conflict': 'res.conflict',
    '(res).serverError': 'res.serverError',
    '(res).success': 'res.success',
    '(res).created': 'res.created',
    '(res).updated': 'res.updated',
    '(res).deleted': 'res.deleted',
    
    // Fix complex validation patterns
    '(validation).isValid': 'validation.isValid',
    '(validation).errors': 'validation.errors',
    '(validation).data': 'validation.data',
    
    '(validationResult).isValid': 'validationResult.isValid',
    '(validationResult).errors': 'validationResult.errors',
    '(validationResult).data': 'validationResult.data',
    
    // Fix complex permission patterns
    '(permission).allowed': 'permission.allowed',
    '(permission).denied': 'permission.denied',
    '(permission).reason': 'permission.reason',
    '(permission).resource': 'permission.resource',
    '(permission).action': 'permission.action',
    
    '(permissionResult).allowed': 'permissionResult.allowed',
    '(permissionResult).denied': 'permissionResult.denied',
    '(permissionResult).reason': 'permissionResult.reason',
    
    // Fix complex async handler patterns
    '(this.asyncHandler)': 'this.asyncHandler',
    '(asyncHandler)': 'asyncHandler',
    
    // Fix complex middleware patterns
    '(this.authMiddleware)': 'this.authMiddleware',
    '(this.validationMiddleware)': 'this.validationMiddleware',
    '(this.rateLimitMiddleware)': 'this.rateLimitMiddleware',
    
    // Fix complex error handling patterns
    'error instanceof Error ? (error).message : String(error)': 'error instanceof Error ? error.message : String(error)',
    'error instanceof AppError ? (error).message : "Unknown error"': 'error instanceof AppError ? error.message : "Unknown error"',
    'error instanceof ValidationError ? (error).errors : []': 'error instanceof ValidationError ? error.errors : []',
    
    // Fix complex date operations
    'new Date((startDate) as string)': 'new Date(startDate as string)',
    'new Date((endDate) as string)': 'new Date(endDate as string)',
    'new Date((dateString) as string)': 'new Date(dateString as string)',
    
    // Fix complex parseInt operations
    'parseInt((merchantId))': 'parseInt(merchantId)',
    'parseInt((userId))': 'parseInt(userId)',
    'parseInt((paymentId))': 'parseInt(paymentId)',
    'parseInt((transactionId))': 'parseInt(transactionId)',
    'parseInt((adminId))': 'parseInt(adminId)',
    'parseInt((id))': 'parseInt(id)',
    
    // Fix complex Object operations
    'Object.values((RiskModelType))': 'Object.values(RiskModelType)',
    'Object.keys((config))': 'Object.keys(config)',
    'Object.entries((data))': 'Object.entries(data)',
    'Object.assign((target), (source))': 'Object.assign(target, source)',
    
    // Fix complex Array operations
    'Array.isArray((data))': 'Array.isArray(data)',
    'Array.from((iterable))': 'Array.from(iterable)',
    
    // Fix complex Promise operations
    'Promise.all((promises))': 'Promise.all(promises)',
    'Promise.race((promises))': 'Promise.race(promises)',
    'Promise.resolve((value))': 'Promise.resolve(value)',
    'Promise.reject((error))': 'Promise.reject(error)',
    
    // Fix complex Math operations
    'Math.floor((value))': 'Math.floor(value)',
    'Math.ceil((value))': 'Math.ceil(value)',
    'Math.round((value))': 'Math.round(value)',
    'Math.max((a), (b))': 'Math.max(a, b)',
    'Math.min((a), (b))': 'Math.min(a, b)',
    'Math.random()': 'Math.random()',
    
    // Fix complex string operations
    '(string).toLowerCase()': 'string.toLowerCase()',
    '(string).toUpperCase()': 'string.toUpperCase()',
    '(string).trim()': 'string.trim()',
    '(string).split()': 'string.split()',
    '(string).replace()': 'string.replace()',
    '(string).includes()': 'string.includes()',
    '(string).startsWith()': 'string.startsWith()',
    '(string).endsWith()': 'string.endsWith()',
    
    // Fix complex boolean operations
    '!(data)': '!data',
    '!!(data)': '!!data',
    '!(result)': '!result',
    '!!(result)': '!!result',
    '!(error)': '!error',
    '!!(error)': '!!error',
    '!(user)': '!user',
    '!!(user)': '!!user',
    '!(merchant)': '!merchant',
    '!!(merchant)': '!!merchant',
    '!(payment)': '!payment',
    '!!(payment)': '!!payment',
    '!(transaction)': '!transaction',
    '!!(transaction)': '!!transaction',
    '!(admin)': '!admin',
    '!!(admin)': '!!admin',
    
    // Fix complex null/undefined checks
    '(data) === null': 'data === null',
    '(data) === undefined': 'data === undefined',
    '(data) == null': 'data == null',
    '(result) === null': 'result === null',
    '(result) === undefined': 'result === undefined',
    '(result) == null': 'result == null',
    '(error) === null': 'error === null',
    '(error) === undefined': 'error === undefined',
    '(error) == null': 'error == null',
    
    // Fix complex typeof checks
    'typeof (data)': 'typeof data',
    'typeof (result)': 'typeof result',
    'typeof (error)': 'typeof error',
    'typeof (user)': 'typeof user',
    'typeof (merchant)': 'typeof merchant',
    'typeof (payment)': 'typeof payment',
    'typeof (transaction)': 'typeof transaction',
    'typeof (admin)': 'typeof admin',
    'typeof (value)': 'typeof value',
    
    // Fix complex instanceof checks
    '(error) instanceof Error': 'error instanceof Error',
    '(error) instanceof AppError': 'error instanceof AppError',
    '(error) instanceof ValidationError': 'error instanceof ValidationError',
    '(result) instanceof Array': 'Array.isArray(result)',
    '(data) instanceof Object': 'typeof data === "object" && data !== null',
    '(value) instanceof Date': 'value instanceof Date',
    '(value) instanceof RegExp': 'value instanceof RegExp',
};

function findAllTypeScriptFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'coverage'].includes(item)) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

function getErrorCount() {
    try {
        const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { encoding: 'utf8' });
        const errorMatches = output.match(/error TS/g) || [];
        return errorMatches.length;
    } catch (error) {
        const errorMatches = error.stdout.match(/error TS/g) || [];
        return errorMatches.length;
    }
}

function processFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let modifiedContent = content;
        let fixCount = 0;
        
        // Apply all advanced type patterns
        for (const [oldPattern, newPattern] of Object.entries(advancedTypePatterns)) {
            const originalContent = modifiedContent;
            modifiedContent = modifiedContent.split(oldPattern).join(newPattern);
            if (modifiedContent !== originalContent) {
                fixCount++;
            }
        }
        
        if (fixCount > 0) {
            fs.writeFileSync(filePath, modifiedContent, 'utf8');
            return { filePath, fixCount };
        }
        
        return null;
    } catch (error) {
        return { filePath, error: error.message };
    }
}

async function main() {
    console.log('🔍 Scanning for TypeScript files...');
    
    const files = findAllTypeScriptFiles('./src');
    console.log(`📁 Found ${files.length} TypeScript files`);
    
    console.log('📊 Getting initial error count...');
    const initialErrors = getErrorCount();
    console.log(`🚨 Initial TypeScript errors: ${initialErrors}`);
    
    console.log('🚀 Starting advanced type resolution...');
    const startTime = Date.now();
    
    const results = [];
    let totalFixedIssues = 0;
    
    for (const file of files) {
        const result = processFile(file);
        if (result) {
            results.push(result);
            if (result.fixCount) {
                totalFixedIssues += result.fixCount;
            }
        }
    }
    
    console.log('📊 Getting final error count...');
    const finalErrors = getErrorCount();
    const totalErrorsFixed = initialErrors - finalErrors;
    
    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;
    
    console.log('\n🎯 ADVANCED TYPE RESOLUTION COMPLETE!');
    console.log('=====================================');
    console.log(`⏱️  Processing time: ${processingTime.toFixed(2)} seconds`);
    console.log(`📁 Files processed: ${results.filter(r => !r.error).length}`);
    console.log(`❌ Files with errors: ${results.filter(r => r.error).length}`);
    console.log(`🔧 Total fixes applied: ${totalFixedIssues}`);
    console.log(`🚨 TypeScript errors before: ${initialErrors}`);
    console.log(`✅ TypeScript errors after: ${finalErrors}`);
    console.log(`🎯 Total errors fixed: ${totalErrorsFixed}`);
    
    if (totalErrorsFixed > 0) {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}%`);
        console.log('\n🎉 SUCCESS! Advanced type resolution completed successfully!');
        console.log('🏆 Your application now has significantly fewer TypeScript errors!');
    } else if (totalErrorsFixed === 0) {
        console.log('📈 Success rate: 0.0% (no net change)');
        console.log('\n✨ No errors were fixed, but advanced patterns were applied!');
    } else {
        console.log(`📈 Success rate: ${((totalErrorsFixed / initialErrors) * 100).toFixed(1)}% (negative - new errors introduced)`);
        console.log('\n⚠️  Some fixes may have introduced new errors. Consider reverting.');
    }
    
    const errorFiles = results.filter(r => r.error);
    if (errorFiles.length > 0) {
        console.log('\n❌ Files with processing errors:');
        errorFiles.forEach(({ filePath, error }) => {
            console.log(`   ${path.relative(process.cwd(), filePath)}: ${error}`);
        });
    }
    
    const successFiles = results.filter(r => r.fixCount && r.fixCount > 0);
    if (successFiles.length > 0) {
        console.log(`\n✅ Successfully applied advanced fixes to ${successFiles.length} files`);
        console.log('Top files with most advanced fixes:');
        successFiles
            .sort((a, b) => b.fixCount - a.fixCount)
            .slice(0, 10)
            .forEach(({ filePath, fixCount }) => {
                console.log(`   ${path.relative(process.cwd(), filePath)}: ${fixCount} fixes`);
            });
    }
    
    console.log('\n🎯 NEXT STEPS:');
    console.log('1. Review remaining TypeScript errors by category');
    console.log('2. Focus on specific error types (TS2339, TS2345, TS2322, etc.)');
    console.log('3. Create domain-specific interface fixes');
    console.log('4. Add missing type definitions for external libraries');
    console.log('5. Consider enabling stricter TypeScript compiler options');
}

main().catch(console.error);
