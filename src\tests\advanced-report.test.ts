import { ReportService as ImportedReportService } from '../services/reporting';

// Mock external dependencies
(jest).mock('fs');
(jest).mock('json2csv');
(jest).mock('pdfkit');
(jest).mock('exceljs');
(jest).mock('nodemailer');
(jest).mock('node-cron');

// Mock Prisma
(jest).mock('@prisma/client', ()  =>  ({
  PrismaClient: (jest).fn().mockImplementation(()  =>  ({
    transaction: {
      findMany: (jest).fn(),
      count: (jest).fn(),
    },
    customer: {
      findMany: (jest).fn(),
      count: (jest).fn(),
    },
    paymentMethod: {
      findMany: (jest).fn(),
      count: (jest).fn(),
    },
    subscription: {
      findMany: (jest).fn(),
      count: (jest).fn(),
    },
    merchant: {
      findFirst: (jest).fn(),
    },
    savedReport: {
      create: (jest).fn(),
      findMany: (jest).fn(),
      findUnique: (jest).fn(),
      delete: (jest).fn(),
    },
    reportTemplate: {
      create: (jest).fn(),
      update: (jest).fn(),
      delete: (jest).fn(),
      findMany: (jest).fn(),
      findUnique: (jest).fn(),
    },
    scheduledReport: {
      create: (jest).fn(),
      update: (jest).fn(),
      delete: (jest).fn(),
      findMany: (jest).fn(),
      findUnique: (jest).fn(),
    },
    reportRun: {
      create: (jest).fn(),
      update: (jest).fn(),
    },
  })),
}));

describe('AdvancedReportService', ()  =>  {
  let reportService: AdvancedReportService;

  beforeEach(()  =>  {
    (jest).clearAllMocks();
    reportService = new AdvancedReportService();
  });

  describe('Service Initialization', ()  =>  {
    it('should create an instance of AdvancedReportService', ()  =>  {
      expect(reportService).toBeInstanceOf(AdvancedReportService);
    });

    it('should have required methods', ()  =>  {
      expect(typeof (reportService).generateReport).toBe('function');
      expect(typeof (reportService).createReportTemplate).toBe('function');
      expect(typeof (reportService).createScheduledReport).toBe('function');
      expect(typeof (reportService).getSavedReports).toBe('function');
      expect(typeof (reportService).getReportTemplates).toBe('function');
    });
  });

  describe('Report Template Management', ()  =>  {
    it('should create a report template', async ()  =>  {
      const templateData = {
        name: 'Test Template',
        description: 'Test Description',
        type: 'TRANSACTION',
        config: { columns: ['id', 'amount'] },
        createdById: 'user-1',
      };

      const mockTemplate = {
        id: 'template-1',
        ...templateData,
      };

      // Mock the Prisma call
      const mockCreate = (jest).fn().mockResolvedValue(mockTemplate);
      (reportService).prisma = {
        reportTemplate: {
          create: mockCreate,
        },
      };

      const result = await (reportService).createReportTemplate(templateData);

      expect(result).toEqual(mockTemplate);
      expect(mockCreate).toHaveBeenCalledWith({
        data: templateData,
      });
    });

    it('should get report templates for a user', async ()  =>  {
      const mockTemplates = [
        {
          id: 'template-1',
          name: 'User Template',
          createdById: 'user-1',
          isSystem: false,
        },
        {
          id: 'template-2',
          name: 'System Template',
          createdById: 'admin-1',
          isSystem: true,
        },
      ];

      const mockFindMany = (jest).fn().mockResolvedValue(mockTemplates);
      (reportService).prisma = {
        reportTemplate: {
          findMany: mockFindMany,
        },
      };

      const result = await (reportService).getReportTemplates('user-1', true);

      expect(result).toEqual(mockTemplates);
      expect(mockFindMany).toHaveBeenCalledWith({
        where: {
          OR: [{ createdById: 'user-1' }, { isSystem: true }],
        },
        orderBy: { name: 'asc' },
      });
    });

    it('should update a report template', async ()  =>  {
      const updateData = {
        name: 'Updated Template',
        description: 'Updated Description',
      };

      const updatedTemplate = {
        id: 'template-1',
        ...updateData,
      };

      const mockUpdate = (jest).fn().mockResolvedValue(updatedTemplate);
      (reportService).prisma = {
        reportTemplate: {
          update: mockUpdate,
        },
      };

      const result = await (reportService).updateReportTemplate('template-1', updateData);

      expect(result).toEqual(updatedTemplate);
      expect(mockUpdate).toHaveBeenCalledWith({
        where: { id: 'template-1' },
        data: updateData,
      });
    });

    it('should delete a report template', async ()  =>  {
      const deletedTemplate = {
        id: 'template-1',
        name: 'Deleted Template',
      };

      const mockDelete = (jest).fn().mockResolvedValue(deletedTemplate);
      (reportService).prisma = {
        reportTemplate: {
          delete: mockDelete,
        },
      };

      const result = await (reportService).deleteReportTemplate('template-1');

      expect(result).toEqual(deletedTemplate);
      expect(mockDelete).toHaveBeenCalledWith({
        where: { id: 'template-1' },
      });
    });
  });

  describe('Scheduled Report Management', ()  =>  {
    it('should create a scheduled report', async ()  =>  {
      const scheduledReportData = {
        name: 'Weekly Report',
        templateId: 'template-1',
        schedule: '0 0 * * 1',
        isActive: true,
        createdById: 'user-1',
      };

      const mockScheduledReport = {
        id: 'scheduled-1',
        ...scheduledReportData,
      };

      const mockCreate = (jest).fn().mockResolvedValue(mockScheduledReport);
      (reportService).prisma = {
        scheduledReport: {
          create: mockCreate,
        },
      };

      const result = await (reportService).createScheduledReport(scheduledReportData);

      expect(result).toEqual(mockScheduledReport);
      expect(mockCreate).toHaveBeenCalledWith({
        data: scheduledReportData,
      });
    });

    it('should get scheduled reports for a user', async ()  =>  {
      const mockReports = [
        {
          id: 'scheduled-1',
          name: 'Weekly Report',
          schedule: '0 0 * * 1',
        },
      ];

      const mockFindMany = (jest).fn().mockResolvedValue(mockReports);
      (reportService).prisma = {
        scheduledReport: {
          findMany: mockFindMany,
        },
      };

      const result = await (reportService).getScheduledReports('user-1');

      expect(result).toEqual(mockReports);
      expect(mockFindMany).toHaveBeenCalledWith({
        where: { createdById: 'user-1' },
        include: { template: true },
        orderBy: { name: 'asc' },
      });
    });
  });

  describe('Saved Report Management', ()  =>  {
    it('should get saved reports for a user', async ()  =>  {
      const mockReports = [
        {
          id: 'report-1',
          name: 'Test Report',
          type: 'TRANSACTION',
          format: 'CSV',
          createdAt: new Date(),
        },
      ];

      const mockFindMany = (jest).fn().mockResolvedValue(mockReports);
      (reportService).prisma = {
        savedReport: {
          findMany: mockFindMany,
        },
      };

      const result = await (reportService).getSavedReports('user-1');

      expect(result).toEqual(mockReports);
      expect(mockFindMany).toHaveBeenCalledWith({
        where: { createdById: 'user-1' },
        include: { template: true },
        orderBy: { createdAt: 'desc' },
      });
    });

    it('should get a saved report by ID', async ()  =>  {
      const mockReport = {
        id: 'report-1',
        name: 'Test Report',
        type: 'TRANSACTION',
        format: 'CSV',
      };

      const mockFindUnique = (jest).fn().mockResolvedValue(mockReport);
      (reportService).prisma = {
        savedReport: {
          findUnique: mockFindUnique,
        },
      };

      const result = await (reportService).getSavedReportById('report-1');

      expect(result).toEqual(mockReport);
      expect(mockFindUnique).toHaveBeenCalledWith({
        where: { id: 'report-1' },
        include: { template: true },
      });
    });
  });
});
