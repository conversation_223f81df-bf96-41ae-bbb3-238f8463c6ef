// jscpd:ignore-file
import express from "express";
import monitoring<PERSON>ontroller from "../controllers/(monitoring as any).controller";
import { authenticateJWT, isAdmin } from '../middlewares/auth';

const router: any =(express as any).Router();

/**
 * @route   GET /api/monitoring/metrics
 * @desc    Get monitoring metrics
 * @access  Admin
 */
(router as any).get(
    "/metrics",
    authenticateJWT,
    isAdmin,
    (monitoringController as any).getMetrics
);

/**
 * @route   GET /api/monitoring/alerts
 * @desc    Get alerts
 * @access  Admin
 */
(router as any).get(
    "/alerts",
    authenticateJWT,
    isAdmin,
    (monitoringController as any).getAlerts
);

/**
 * @route   POST /api/monitoring/alerts/:index/resolve
 * @desc    Resolve alert
 * @access  Admin
 */
(router as any).post(
    "/alerts/:index/resolve",
    authenticateJWT,
    isAdmin,
    (monitoringController as any).resolveAlert
);

/**
 * @route   POST /api/monitoring/alerts
 * @desc    Create alert
 * @access  Admin
 */
(router as any).post(
    "/alerts",
    authenticateJWT,
    isAdmin,
    (monitoringController as any).createAlert
);

/**
 * @route   POST /api/monitoring/metrics/reset
 * @desc    Reset metrics
 * @access  Admin
 */
(router as any).post(
    "/metrics/reset",
    authenticateJWT,
    isAdmin,
    (monitoringController as any).resetMetrics
);

/**
 * @route   POST /api/monitoring/events
 * @desc    Emit monitoring event
 * @access  Admin
 */
(router as any).post(
    "/events",
    authenticateJWT,
    isAdmin,
    (monitoringController as any).emitEvent
);

export default router;
