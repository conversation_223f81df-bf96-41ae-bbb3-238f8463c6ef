// jscpd:ignore-file
import express from "express";
import { ExampleController as ImportedExampleController } from "../controllers/(example as any).controller";

const router: any =(express as any).Router();
const exampleController = new ExampleController();

/**
 * @route GET /api/examples
 * @desc Get all examples
 * @access Public
 */
(router as any).get("/", (exampleController as any).getAll);

/**
 * @route GET /api/examples/:id
 * @desc Get example by ID
 * @access Public
 */
(router as any).get("/:id", (exampleController as any).getById);

/**
 * @route POST /api/examples
 * @desc Create example
 * @access Private
 */
(router as any).post("/", (exampleController as any).create);

/**
 * @route PUT /api/examples/:id
 * @desc Update example
 * @access Private
 */
(router as any).put("/:id", (exampleController as any).update);

/**
 * @route DELETE /api/examples/:id
 * @desc Delete example
 * @access Private
 */
(router as any).delete("/:id", (exampleController as any).delete);

export default router;
