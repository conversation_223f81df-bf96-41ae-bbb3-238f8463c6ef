// jscpd:ignore-file
import express from "express";
import { ExampleController as ImportedExampleController } from "../controllers/(example).controller";

const router =(express).Router();
const exampleController = new ExampleController();

/**
 * @route GET /api/examples
 * @desc Get all examples
 * @access Public
 */
(router).get("/", (exampleController).getAll);

/**
 * @route GET /api/examples/:id
 * @desc Get example by ID
 * @access Public
 */
(router).get("/:id", (exampleController).getById);

/**
 * @route POST /api/examples
 * @desc Create example
 * @access Private
 */
(router).post("/", (exampleController).create);

/**
 * @route PUT /api/examples/:id
 * @desc Update example
 * @access Private
 */
(router).put("/:id", (exampleController).update);

/**
 * @route DELETE /api/examples/:id
 * @desc Delete example
 * @access Private
 */
(router).delete("/:id", (exampleController).delete);

export default router;
