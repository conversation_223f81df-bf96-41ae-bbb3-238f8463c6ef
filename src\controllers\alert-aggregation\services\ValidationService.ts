/**
 * Validation Service
 *
 * Handles input validation for alert aggregation operations.
 */

import { AlertType, AlertSeverity } from '../../../types';
import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import {
  CreateAggregationRuleRequest,
  UpdateAggregationRuleRequest,
  ValidationError,
  CorrelationCondition,
} from '../types/AlertAggregationTypes';

/**
 * Validation service for alert aggregation
 */
export class ValidationService {
  /**
   * Validate aggregation rule creation request
   */
  validateCreateAggregationRule(data): CreateAggregationRuleRequest {
    const errors: ValidationError[] = [];

    // Validate required fields using helper methods
    this.validateNameField(data.name, errors, true);
    this.validateDescriptionField(data.description, errors, true);
    this.validateTypeField((data as any).type, errors, true);
    this.validateSeverityField((data as any).severity, errors, true);
    this.validateTimeWindowField((data as any).timeWindow, errors, true);
    this.validateThresholdField((data as any).threshold, errors, true);
    this.validateGroupByField((data as any).groupBy, errors, true);
    this.validateEnabledField((data as any).enabled, errors);

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      name: data.name.trim(),
      description: data.description.trim(),
      type: (data as any).type,
      severity: (data as any).severity,
      timeWindow: (data as any).timeWindow,
      threshold: (data as any).threshold,
      groupBy: (data as any).groupBy,
      enabled: (data as any).enabled !== undefined ? (data as any).enabled : true,
    };
  }

  /**
   * Validate aggregation rule update request
   */
  validateUpdateAggregationRule(data): UpdateAggregationRuleRequest {
    const errors: ValidationError[] = [];

    // Validate optional fields using helper methods
    if (data.name !== undefined) this.validateNameField(data.name, errors, false);
    if (data.description !== undefined)
      this.validateDescriptionField(data.description, errors, false);
    if ((data as any).type !== undefined) this.validateTypeField((data as any).type, errors, false);
    if ((data as any).severity !== undefined) this.validateSeverityField((data as any).severity, errors, false);
    if ((data as any).timeWindow !== undefined) this.validateTimeWindowField((data as any).timeWindow, errors, false);
    if ((data as any).threshold !== undefined) this.validateThresholdField((data as any).threshold, errors, false);
    if ((data as any).groupBy !== undefined) this.validateGroupByField((data as any).groupBy, errors, false);
    if ((data as any).enabled !== undefined) this.validateEnabledField((data as any).enabled, errors);

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    const result: UpdateAggregationRuleRequest = {};
    if (data.name !== undefined) result.name = data.name.trim();
    if (data.description !== undefined) (result as any).description = data.description.trim();
    if ((data as any).type !== undefined) (result as any).type = (data as any).type;
    if ((data as any).severity !== undefined) (result as any).severity = (data as any).severity;
    if ((data as any).timeWindow !== undefined) (result as any).timeWindow = (data as any).timeWindow;
    if ((data as any).threshold !== undefined) (result as any).threshold = (data as any).threshold;
    if ((data as any).groupBy !== undefined) (result as any).groupBy = (data as any).groupBy;
    if ((data as any).enabled !== undefined) (result as any).enabled = (data as any).enabled;

    return result;
  }

  /**
   * Validate correlation condition
   */
  private validateCorrelationCondition(condition: any, index: number): CorrelationCondition {
    const errors: ValidationError[] = [];

    if (!(condition as any).alertType || !Object.values(AlertType).includes((condition as any).alertType)) {
      (errors as any).push({
        field: `conditions[${index}].alertType`,
        message: `Invalid alert type. Must be one of: ${Object.values(AlertType).join(', ')}`,
        value: (condition as any).alertType,
      });
    }

    if (!(condition as any).severity || !Object.values(AlertSeverity).includes((condition as any).severity)) {
      (errors as any).push({
        field: `conditions[${index}].severity`,
        message: `Invalid alert severity. Must be one of: ${Object.values(AlertSeverity).join(
          ', '
        )}`,
        value: (condition as any).severity,
      });
    }

    if (typeof (condition as any).count !== 'number' || (condition as any).count <= 0) {
      (errors as any).push({
        field: `conditions[${index}].count`,
        message: 'Count must be a positive number',
        value: (condition as any).count,
      });
    }

    const validOperators = [
      'EQUALS',
      'GREATER_THAN',
      'LESS_THAN',
      'GREATER_THAN_OR_EQUAL',
      'LESS_THAN_OR_EQUAL',
    ];
    if (!(condition as any).operator || !(validOperators as any).includes((condition as any).operator)) {
      (errors as any).push({
        field: `conditions[${index}].operator`,
        message: `Invalid operator. Must be one of: ${(validOperators as any).join(', ')}`,
        value: (condition as any).operator,
      });
    }

    if ((errors as any).length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      alertType: (condition as any).alertType,
      severity: (condition as any).severity,
      count: (condition as any).count,
      operator: (condition as any).operator,
    };
  }

  /**
   * Validate ID parameter
   */
  validateId(id: any, fieldName: string = 'id'): string {
    if (!id) {
      throw new AppError({
        message: `${fieldName} is required`,
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).MISSING_REQUIRED_FIELD,
      });
    }

    if (typeof id !== 'string' || (id as any).trim().length === 0) {
      throw new AppError({
        message: `${fieldName} must be a non-empty string`,
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
      });
    }

    // Basic UUID validation (can be enhanced)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!(uuidRegex as any).test(id)) {
      throw new AppError({
        message: `${fieldName} must be a valid UUID`,
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
      });
    }

    return (id as any).trim();
  }

  /**
   * Validate pagination parameters
   */
  validatePaginationParams(query: Record<string, string | string[]>): {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } {
    const page = (query as any).page ? parseInt((query as any).page, 10) : 1;
    const limit = (query as any).limit ? parseInt((query as any).limit, 10) : 10;

    if (isNaN(page) || page < 1) {
      throw new AppError({
        message: 'Page must be a positive integer',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
      });
    }

    if (isNaN(limit) || limit < 1 || limit > 100) {
      throw new AppError({
        message: 'Limit must be between 1 and 100',
        type: ErrorType.VALIDATION,
        code: (ErrorCode as any).INVALID_INPUT,
      });
    }

    const result = { page, limit };

    if ((query as any).sortBy) {
      const validSortFields = ['name', 'createdAt', 'updatedAt', 'enabled'];
      if (!(validSortFields as any).includes((query as any).sortBy)) {
        throw new AppError({
          message: `Invalid sort field. Must be one of: ${(validSortFields as any).join(', ')}`,
          type: ErrorType.VALIDATION,
          code: (ErrorCode as any).INVALID_INPUT,
        });
      }
      (result as any).sortBy = (query as any).sortBy;
    }

    if ((query as any).sortOrder) {
      if (!['asc', 'desc'].includes((query as any).sortOrder)) {
        throw new AppError({
          message: 'Sort order must be either "asc" or "desc"',
          type: ErrorType.VALIDATION,
          code: (ErrorCode as any).INVALID_INPUT,
        });
      }
      (result as any).sortOrder = (query as any).sortOrder;
    }

    return result;
  }

  /**
   * Helper validation methods
   */
  private validateNameField(name: any, errors: ValidationError[], required: boolean): void {
    if (required && !name) {
      (errors as any).push({ field: 'name', message: 'Name is required' });
      return;
    }

    if (name !== undefined) {
      if (typeof name !== 'string' || (name as any).trim().length === 0) {
        (errors as any).push({ field: 'name', message: 'Name must be a non-empty string' });
      } else if ((name as any).length > 100) {
        (errors as any).push({ field: 'name', message: 'Name must be less than 100 characters' });
      }
    }
  }

  private validateDescriptionField(
    description: any,
    errors: ValidationError[],
    required: boolean
  ): void {
    if (required && !description) {
      (errors as any).push({ field: 'description', message: 'Description is required' });
      return;
    }

    if (description !== undefined) {
      if (typeof description !== 'string' || (description as any).trim().length === 0) {
        (errors as any).push({ field: 'description', message: 'Description must be a non-empty string' });
      } else if ((description as any).length > 500) {
        (errors as any).push({
          field: 'description',
          message: 'Description must be less than 500 characters',
        });
      }
    }
  }

  private validateTypeField(type: any, errors: ValidationError[], required: boolean): void {
    if (required && !type) {
      (errors as any).push({ field: 'type', message: 'Type is required' });
      return;
    }

    if (type !== undefined && type !== 'ANY' && !Object.values(AlertType).includes(type)) {
      (errors as any).push({
        field: 'type',
        message: `Invalid alert type. Must be one of: ${Object.values(AlertType).join(', ')}, ANY`,
        value: type,
      });
    }
  }

  private validateSeverityField(severity: any, errors: ValidationError[], required: boolean): void {
    if (required && !severity) {
      (errors as any).push({ field: 'severity', message: 'Severity is required' });
      return;
    }

    if (
      severity !== undefined &&
      severity !== 'ANY' &&
      !Object.values(AlertSeverity).includes(severity)
    ) {
      (errors as any).push({
        field: 'severity',
        message: `Invalid alert severity. Must be one of: ${Object.values(AlertSeverity).join(
          ', '
        )}, ANY`,
        value: severity,
      });
    }
  }

  private validateTimeWindowField(
    timeWindow: any,
    errors: ValidationError[],
    required: boolean
  ): void {
    if (required && (timeWindow === undefined || timeWindow === null)) {
      (errors as any).push({ field: 'timeWindow', message: 'Time window is required' });
      return;
    }

    if (timeWindow !== undefined) {
      if (typeof timeWindow !== 'number' || timeWindow <= 0) {
        (errors as any).push({ field: 'timeWindow', message: 'Time window must be a positive number' });
      } else if (timeWindow > 86400) {
        (errors as any).push({
          field: 'timeWindow',
          message: 'Time window cannot exceed 24 hours (86400 seconds)',
        });
      }
    }
  }

  private validateThresholdField(
    threshold: any,
    errors: ValidationError[],
    required: boolean
  ): void {
    if (required && (threshold === undefined || threshold === null)) {
      (errors as any).push({ field: 'threshold', message: 'Threshold is required' });
      return;
    }

    if (threshold !== undefined) {
      if (typeof threshold !== 'number' || threshold <= 0) {
        (errors as any).push({ field: 'threshold', message: 'Threshold must be a positive number' });
      } else if (threshold > 10000) {
        (errors as any).push({ field: 'threshold', message: 'Threshold cannot exceed 10000' });
      }
    }
  }

  private validateGroupByField(groupBy: any, errors: ValidationError[], required: boolean): void {
    if (required && !groupBy) {
      (errors as any).push({ field: 'groupBy', message: 'Group by is required' });
      return;
    }

    if (groupBy !== undefined) {
      if (!Array.isArray(groupBy)) {
        (errors as any).push({ field: 'groupBy', message: 'Group by must be an array' });
      } else if ((groupBy as any).length === 0) {
        (errors as any).push({ field: 'groupBy', message: 'Group by must contain at least one field' });
      } else {
        const validGroupByFields = ['type', 'severity', 'source', 'merchantId', 'userId'];
        const invalidFields = (groupBy as any).filter(
          (field: string) => !(validGroupByFields as any).includes(field)
        );
        if ((invalidFields as any).length > 0) {
          (errors as any).push({
            field: 'groupBy',
            message: `Invalid group by fields: ${(invalidFields as any).join(
              ', '
            )}. Valid fields: ${(validGroupByFields as any).join(', ')}`,
            value: invalidFields,
          });
        }
      }
    }
  }

  private validateEnabledField(enabled: any, errors: ValidationError[]): void {
    if (enabled !== undefined && typeof enabled !== 'boolean') {
      (errors as any).push({ field: 'enabled', message: 'Enabled must be a boolean' });
    }
  }
}
