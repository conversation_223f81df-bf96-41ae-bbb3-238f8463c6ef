// jscpd:ignore-file
import { Transaction, Prisma } from "@prisma/client";
import { GenericService } from "../../core/GenericService";
import { TransactionRepository } from "../../repositories/refactored/transaction.repository";
import { ErrorFactory } from "../../utils/errors/ErrorFactory";
import { logger } from "../../lib/logger";
import { RepositoryFactory } from "../../factories/RepositoryFactory";
import { Transaction } from '../types';
import { GenericService } from "../../core/GenericService";
import { TransactionRepository } from "../../repositories/refactored/transaction.repository";
import { ErrorFactory } from "../../utils/errors/ErrorFactory";
import { logger } from "../../lib/logger";
import { RepositoryFactory } from "../../factories/RepositoryFactory";
import { Transaction } from '../types';


/**
 * Transaction service
 * This service handles business logic for transactions
 */
export class TransactionService extends GenericService<
  Transaction,
  Prisma.TransactionCreateInput,
  Prisma.TransactionUpdateInput
> {
  private transactionRepository: TransactionRepository;

  /**
   * Create a new transaction service
   */
  constructor() {
    const repositoryFactory = RepositoryFactory.getInstance();
    const repository = repositoryFactory.getRepository<
      Transaction,
      Prisma.TransactionCreateInput,
      Prisma.TransactionUpdateInput
    >('transaction') as TransactionRepository;

    super(repository, 'Transaction');
    this.transactionRepository = repository;
  }

  /**
   * Get transactions with filters
   * @param options Query options
   * @returns Paginated transactions
   */
  async getTransactions(options: {
    merchantId?: string;
    status?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  }): Promise<{ data: Transaction[]; total: number }> {
    try {
      return await this.transactionRepository.findTransactions(options);
    } catch (error) {
      logger.error('Error getting transactions:', error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Get a transaction by ID
   * @param id Transaction ID
   * @returns Transaction or null
   */
  async getTransactionById(id: string): Promise<Transaction | null> {
    try {
      return await this.repository.findById(id);
    } catch (error) {
      logger.error(`Error getting transaction by ID ${id}:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Create a new transaction
   * @param data Transaction data
   * @returns Created transaction
   */
  async createTransaction(data: { amount: number;
    currency: string;
    method: string;
    description?: string;
    merchantId?: string;
    userId: string;
  }): Promise<Transaction> {
    try {
      // Validate amount
      if (data.amount <= 0) {
        throw ErrorFactory.validation('Amount must be greater than zero');
      }

      // Create transaction
      const transaction = await this.repository.create({
        amount: data.amount,
        currency: data.currency,
        method: data.method,
        description: data.description,
        merchantId: data.merchantId,
        status: 'PENDING',
        createdBy: data.id // Fixed: using id instead of userId
      });

      // Log transaction creation
      logger.info(`Transaction created: ${transaction.id}`, {
        transactionId: transaction.id,
        merchantId: transaction.merchantId,
        amount: transaction.amount,
        currency: transaction.currency,
        method: transaction.method
      });

      return transaction;
    } catch (error) {
      logger.error('Error creating transaction:', error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Update a transaction
   * @param id Transaction ID
   * @param data Transaction data
   * @returns Updated transaction
   */
  async updateTransaction(id: string, data: {
    status?: string;
    notes?: string;
    updatedBy: string;
  }): Promise<Transaction> {
    try {
      // Get transaction
      const transaction = await this.getTransactionById(id);

      // Check if transaction exists
      if (!transaction) {
        throw ErrorFactory.notFound('Transaction', id);
      }

      // Check if transaction can be updated
      if (transaction.status === 'COMPLETED' || transaction.status === 'FAILED') {
        throw ErrorFactory.conflict('Cannot update a completed or failed transaction');
      }

      // Update transaction
      const updatedTransaction = await this.repository.update(id, {
        status: data.status,
        notes: data.notes,
        updatedAt: new Date(),
        updatedBy: data.updatedBy
      });

      // Log transaction update
      logger.info(`Transaction updated: ${id}`, {
        transactionId: id,
        status: data.status,
        updatedBy: data.updatedBy
      });

      return updatedTransaction;
    } catch (error) {
      logger.error(`Error updating transaction ${id}:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Delete a transaction
   * @param id Transaction ID
   * @returns Deleted transaction
   */
  async deleteTransaction(id: string): Promise<Transaction> {
    try {
      // Get transaction
      const transaction = await this.getTransactionById(id);

      // Check if transaction exists
      if (!transaction) {
        throw ErrorFactory.notFound('Transaction', id);
      }

      // Delete transaction
      const deletedTransaction = await this.repository.delete(id);

      // Log transaction deletion
      logger.info(`Transaction deleted: ${id}`, {
        transactionId: id
      });

      return deletedTransaction;
    } catch (error) {
      logger.error(`Error deleting transaction ${id}:`, error);
      throw ErrorFactory.handle(error);
    }
  }

  /**
   * Get transaction statistics
   * @param options Query options
   * @returns Transaction statistics
   */
  async getTransactionStats(options: {
    merchantId?: string;
    startDate: Date;
    endDate: Date;
  }): Promise<{
    totalCount: number;
    totalAmount: number;
    successCount: number;
    successAmount: number;
    failedCount: number;
    pendingCount: number;
    byMethod: { method: string; count: number; amount: number }[];
    byDay: { date: string; count: number; amount: number }[];
  }> {
    try {
      return await this.transactionRepository.getTransactionStats(options);
    } catch (error) {
      logger.error('Error getting transaction statistics:', error);
      throw ErrorFactory.handle(error);
    }
  }
}
