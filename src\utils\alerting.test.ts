import { Alert, AlertSeverity, AlertType } from '../types';
// jscpd:ignore-file
import alerting, { 
    AlertSeverity, 
    AlertType, 
    AlertChannel,
    sendAlert,
    getRecentAlerts,
    clearRecentAlerts,
    sendSystemAlert,
    sendDatabaseAlert,
    sendSecurityAlert
} from "./alerting";

// Mock the logger
jest.mock("../lib/logger", () => ({
    logger: { error: jest.fn(),
        warn: jest.fn(),
        info: jest.fn(),
        debug: jest.fn()
    }
}));

describe("Alerting Utility", () => {
    beforeEach(() => {
    // Clear recent alerts before each test
        clearRecentAlerts();
    
        // Clear all mocks
        jest.clearAllMocks();
    });

    describe("sendAlert", () => {
        it("should send an alert via the specified channels", async () => {
            // Create an alert
            const alert: unknown = {
                severity: AlertSeverity.ERROR,
                type: AlertType.SYSTEM,
                message: "Test alert",
                details: { test: true },
                timestamp: new Date()
            };
      
            // Send the alert
            const notification: unknown = await sendAlert(alert, [AlertChannel.EMAIL, AlertChannel.SLACK]);
      
            // Verify the notification
            expect(notification.alert).toEqual(alert);
            expect(notification.channels).toEqual([AlertChannel.EMAIL, AlertChannel.SLACK]);
            expect(notification.status).toBe("sent");
            expect(notification.sentAt).toBeDefined();
            expect(notification.id).toBeDefined();
        });

        it("should handle errors when sending alerts", async () => {
            // Create an alert
            const alert: unknown = {
                severity: AlertSeverity.ERROR,
                type: AlertType.SYSTEM,
                message: "Test alert",
                details: { test: true },
                timestamp: new Date()
            };
      
            // Mock an error when sending the alert
            jest.spyOn(Promise, "all").mockRejectedValueOnce(new Error("Failed to send alert"));
      
            // Send the alert
            const notification: unknown = await sendAlert(alert, [AlertChannel.EMAIL]);
      
            // Verify the notification
            expect(notification.alert).toEqual(alert);
            expect(notification.status).toBe("failed");
            expect(notification.error).toBe("Failed to send alert");
        });
    });

    describe("getRecentAlerts", () => {
        it("should return recent alerts", async () => {
            // Send some alerts
            await sendAlert({
                severity: AlertSeverity.INFO,
                type: AlertType.SYSTEM,
                message: "Info alert",
                timestamp: new Date()
            });
      
            await sendAlert({
                severity: AlertSeverity.WARNING,
                type: AlertType.DATABASE,
                message: "Warning alert",
                timestamp: new Date()
            });
      
            // Get recent alerts
            const recentAlerts: unknown =getRecentAlerts();
      
            // Verify the alerts
            expect(recentAlerts).toHaveLength(2);
            expect(recentAlerts[0].alert.message).toBe("Warning alert");
            expect(recentAlerts[1].alert.message).toBe("Info alert");
        });

        it("should limit the number of recent alerts", async () => {
            // Send more than the maximum number of alerts
            for (let i: number =0; i < 110; i++) {
                await sendAlert({
                    severity: AlertSeverity.INFO,
                    type: AlertType.SYSTEM,
                    message: `Alert ${i}`,
                    timestamp: new Date()
                });
            }
      
            // Get recent alerts
            const recentAlerts: unknown =getRecentAlerts();
      
            // Verify the number of alerts is limited
            expect(recentAlerts.length).toBeLessThanOrEqual(100);
      
            // Verify the most recent alerts are kept
            expect(recentAlerts[0].alert.message).toBe("Alert 109");
        });
    });

    describe("clearRecentAlerts", () => {
        it("should clear recent alerts", async () => {
            // Send some alerts
            await sendAlert({
                severity: AlertSeverity.INFO,
                type: AlertType.SYSTEM,
                message: "Info alert",
                timestamp: new Date()
            });
      
            // Verify the alert was sent
            expect(getRecentAlerts()).toHaveLength(1);
      
            // Clear recent alerts
            clearRecentAlerts();
      
            // Verify the alerts were cleared
            expect(getRecentAlerts()).toHaveLength(0);
        });
    });

    describe("sendSystemAlert", () => {
        it("should send a system alert", async () => {
            // Send a system alert
            const notification: unknown = await sendSystemAlert("System alert", AlertSeverity.ERROR);
      
            // Verify the notification
            expect(notification.alert.type).toBe(AlertType.SYSTEM);
            expect(notification.alert.message).toBe("System alert");
            expect(notification.alert.severity).toBe(AlertSeverity.ERROR);
            expect(notification.status).toBe("sent");
        });
    });

    describe("sendDatabaseAlert", () => {
        it("should send a database alert", async () => {
            // Send a database alert
            const notification: unknown = await sendDatabaseAlert("Database alert", AlertSeverity.WARNING);
      
            // Verify the notification
            expect(notification.alert.type).toBe(AlertType.DATABASE);
            expect(notification.alert.message).toBe("Database alert");
            expect(notification.alert.severity).toBe(AlertSeverity.WARNING);
            expect(notification.status).toBe("sent");
        });
    });

    describe("sendSecurityAlert", () => {
        it("should send a security alert", async () => {
            // Send a security alert
            const notification: unknown = await sendSecurityAlert("Security alert", AlertSeverity.CRITICAL);
      
            // Verify the notification
            expect(notification.alert.type).toBe(AlertType.SECURITY);
            expect(notification.alert.message).toBe("Security alert");
            expect(notification.alert.severity).toBe(AlertSeverity.CRITICAL);
            expect(notification.status).toBe("sent");
        });
    });
});
