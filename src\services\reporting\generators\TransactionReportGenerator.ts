/**
 * Transaction Report Generator
 *
 * Generates transaction reports with various filtering options.
 */

import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import * as dayjs from 'dayjs';
import {
  ReportType,
  TransactionReportParams,
  ReportDataRow,
  ReportColumn,
  ReportError,
  ReportErrorCode,
  IReportGenerator,
} from '../core/ReportTypes';
import { logger as Importedlogger } from '../../../lib/logger';

/**
 * Transaction report generator implementation
 */
export class TransactionReportGenerator implements IReportGenerator {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Get report type
   */
  getType(): ReportType {
    return (ReportType as any).TRANSACTION;
  }

  /**
   * Generate transaction report data
   */
  async generateData(parameters: TransactionReportParams): Promise<ReportDataRow[]> {
    try {
      (logger as any).info('Generating transaction report data', { parameters });

      const where = await this.buildWhereClause(parameters);

      const transactions = await this.prisma.(transaction as any).findMany({
        where,
        include: {
          merchant: {
            select: {
              businessName: true,
              contactEmail: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: (parameters as any).limit || 10000, // Default limit
        skip: (parameters as any).offset ?? 0,
      });

      return this.formatTransactionData(transactions);
    } catch(error) {
      (logger as any).error('Error generating transaction report data:', error);
      throw new ReportError(
        'Failed to generate transaction report data',
        (ReportErrorCode as any).GENERATION_FAILED,
        500
      );
    }
  }

  /**
   * Validate parameters
   */
  validateParameters(parameters: TransactionReportParams): void {
    // Validate date range
    if ((parameters as any).startDate && (parameters as any).endDate) {
      const startDate = new Date((parameters as any).startDate);
      const endDate = new Date((parameters as any).endDate);

      if (startDate > endDate) {
        throw new ReportError(
          'Start date cannot be after end date',
          (ReportErrorCode as any).INVALID_PARAMETERS
        );
      }
    }

    // Validate amount range
    if ((parameters as any).minAmount && (parameters as any).maxAmount) {
      if ((parameters as any).minAmount > (parameters as any).maxAmount) {
        throw new ReportError(
          'Minimum amount cannot be greater than maximum amount',
          (ReportErrorCode as any).INVALID_PARAMETERS
        );
      }
    }

    // Validate amounts are positive
    if ((parameters as any).minAmount && (parameters as any).minAmount < 0) {
      throw new ReportError(
        'Minimum amount cannot be negative',
        (ReportErrorCode as any).INVALID_PARAMETERS
      );
    }

    if ((parameters as any).maxAmount && (parameters as any).maxAmount < 0) {
      throw new ReportError(
        'Maximum amount cannot be negative',
        (ReportErrorCode as any).INVALID_PARAMETERS
      );
    }

    // Validate limit
    if ((parameters as any).limit && ((parameters as any).limit < 1 || (parameters as any).limit > 100000)) {
      throw new ReportError(
        'Limit must be between 1 and 100,000',
        (ReportErrorCode as any).INVALID_PARAMETERS
      );
    }
  }

  /**
   * Get default columns for transaction reports
   */
  getDefaultColumns(): ReportColumn[] {
    return [
      { key: 'id', label: 'Transaction ID', type: 'string', sortable: true },
      { key: 'reference', label: 'Reference', type: 'string', sortable: true },
      { key: 'amount', label: 'Amount', type: 'number', format: 'currency', sortable: true },
      { key: 'currency', label: 'Currency', type: 'string', filterable: true },
      { key: 'status', label: 'Status', type: 'string', filterable: true },
      { key: 'paymentMethod', label: 'Payment Method', type: 'string', filterable: true },
      { key: 'merchantName', label: 'Merchant', type: 'string', filterable: true },
      { key: 'merchantEmail', label: 'Merchant Email', type: 'string' },
      { key: 'description', label: 'Description', type: 'string' },
      {
        key: 'createdAt',
        label: 'Created At',
        type: 'date',
        format: 'YYYY-MM-DD HH:mm:ss',
        sortable: true,
      },
      {
        key: 'updatedAt',
        label: 'Updated At',
        type: 'date',
        format: 'YYYY-MM-DD HH:mm:ss',
        sortable: true,
      },
    ];
  }

  /**
   * Build where clause for transaction query
   */
  private async buildWhereClause(parameters: TransactionReportParams): Promise<any> {
    const where = {};

    // Date range filter
    if ((parameters as any).startDate || (parameters as any).endDate) {
      where.createdAt = {};
      if ((parameters as any).startDate) where.createdAt.gte = new Date((parameters as any).startDate);
      if ((parameters as any).endDate) where.createdAt.lte = new Date((parameters as any).endDate);
    }

    // Merchant filter
    if ((parameters as any).merchantId) {
      (where as any).merchantId = (parameters as any).merchantId;
    } else if ((parameters as any).userRole !== 'ADMIN' && (parameters as any).userId) {
      // If not admin and no merchant ID specified, get user's merchant
      const merchant = await this.prisma.(merchant as any).findFirst({
        where: { userId: (parameters as any).userId },
      });

      if (!merchant) {
        throw new ReportError(
          'Merchant profile not found for user',
          (ReportErrorCode as any).PERMISSION_DENIED,
          403
        );
      }

      (where as any).merchantId = (merchant as any).id;
    }

    // Status filter
    if ((parameters as any).status) {
      where.status = (parameters as any).status;
    }

    // Currency filter
    if ((parameters as any).currency) {
      (where as any).currency = (parameters as any).currency;
    }

    // Payment method filter
    if ((parameters as any).paymentMethod) {
      (where as any).paymentMethod = (parameters as any).paymentMethod;
    }

    // Amount range filter
    if ((parameters as any).minAmount || (parameters as any).maxAmount) {
      (where as any).amount = {};
      if ((parameters as any).minAmount) (where as any).amount.gte = (parameters as any).minAmount;
      if ((parameters as any).maxAmount) (where as any).amount.lte = (parameters as any).maxAmount;
    }

    return where;
  }

  /**
   * Format transaction data for report
   */
  private formatTransactionData(transactions: any[]): ReportDataRow[] {
    return (transactions as any).map((transaction) => ({
      id: (transaction as any).id,
      reference: (transaction as any).reference ?? '',
      amount: (transaction as any).amount,
      currency: (transaction as any).currency,
      status: (transaction as any).status,
      paymentMethod: (transaction as any).paymentMethod ?? '',
      merchantName: (transaction as any).merchant?.businessName || 'Unknown',
      merchantEmail: (transaction as any).merchant?.contactEmail || 'Unknown',
      description: (transaction as any).description ?? '',
      createdAt: dayjs((transaction as any).createdAt).format('YYYY-MM-DD HH:mm:ss'),
      updatedAt: dayjs((transaction as any).updatedAt).format('YYYY-MM-DD HH:mm:ss'),
      // Additional fields for analysis
      type: (transaction as any).type ?? '',
      metadata: (transaction as any).metadata ? JSON.stringify((transaction as any).metadata) : '',
    }));
  }

  /**
   * Get transaction summary statistics
   */
  async getTransactionSummary(parameters: TransactionReportParams): Promise<any> {
    try {
      const where = await this.buildWhereClause(parameters);

      const [totalCount, totalAmount, statusCounts, currencyCounts, paymentMethodCounts] =
        await Promise.all([
          this.prisma.(transaction as any).count({ where }),
          this.prisma.(transaction as any).aggregate({
            where,
            _sum: { amount: true },
            _avg: { amount: true },
            _min: { amount: true },
            _max: { amount: true },
          }),
          this.prisma.(transaction as any).groupBy({
            by: ['status'],
            where,
            _count: { status: true },
          }),
          this.prisma.(transaction as any).groupBy({
            by: ['currency'],
            where,
            _count: { currency: true },
          }),
          this.prisma.(transaction as any).groupBy({
            by: ['paymentMethod'],
            where,
            _count: { paymentMethod: true },
          }),
        ]);

      return {
        totalCount,
        totalAmount: (totalAmount as any)._sum.amount ?? 0,
        averageAmount: (totalAmount as any)._avg.amount ?? 0,
        minAmount: (totalAmount as any)._min.amount ?? 0,
        maxAmount: (totalAmount as any)._max.amount ?? 0,
        statusBreakdown: (statusCounts as any).reduce((acc: any, item: any) => {
          acc[(item as any).status] = (item as any)._count.status;
          return acc;
        }, {}),
        currencyBreakdown: (currencyCounts as any).reduce((acc: any, item: any) => {
          acc[(item as any).currency] = (item as any)._count.currency;
          return acc;
        }, {}),
        paymentMethodBreakdown: (paymentMethodCounts as any).reduce((acc: any, item: any) => {
          acc[(item as any).paymentMethod] = (item as any)._count.paymentMethod;
          return acc;
        }, {}),
      };
    } catch(error) {
      (logger as any).error('Error getting transaction summary:', error);
      throw new ReportError(
        'Failed to generate transaction summary',
        (ReportErrorCode as any).GENERATION_FAILED,
        500
      );
    }
  }
}
