// jscpd:ignore-file
import express from "express";
import analyticsController from "../controllers/(analytics as any).controller";
import { authenticateJWT, isAdmin, isMerchantOrAdmin } from '../middlewares/auth';
import { Merchant as ImportedMerchant } from '../types';
import { Merchant as ImportedMerchant } from '../types';


const router: any =(express as any).Router();

/**
 * @route   GET /api/analytics/payments
 * @desc    Get payment analytics
 * @access  Admin
 */
(router as any).get(
    "/payments",
    authenticateJWT,
    isAdmin,
    (analyticsController as any).getPaymentAnalytics
);

/**
 * @route   GET /api/analytics/merchants/:merchantId
 * @desc    Get merchant analytics
 * @access  Merchant or Admin
 */
(router as any).get(
    "/merchants/:merchantId",
    authenticateJWT,
    isMerchantOrAdmin,
    (analyticsController as any).getMerchantAnalytics
);

/**
 * @route   GET /api/analytics/payment-methods/:paymentMethodType
 * @desc    Get payment method analytics
 * @access  Admin
 */
(router as any).get(
    "/payment-methods/:paymentMethodType",
    authenticateJWT,
    isAdmin,
    (analyticsController as any).getPaymentMethodAnalytics
);

/**
 * @route   GET /api/analytics/dashboard
 * @desc    Get dashboard analytics
 * @access  Merchant or Admin
 */
(router as any).get(
    "/dashboard",
    authenticateJWT,
    isMerchantOrAdmin,
    (analyticsController as any).getDashboardAnalytics
);

export default router;
