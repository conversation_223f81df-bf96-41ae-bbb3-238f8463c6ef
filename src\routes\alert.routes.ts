// jscpd:ignore-file
import { Router as ImportedRouter } from "express";
import { AlertController as ImportedAlertController } from "../controllers/refactored/(alert).controller";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { Alert as ImportedAlert } from '../types';
import { AlertController as ImportedAlertController } from "../controllers/refactored/(alert).controller";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { Alert as ImportedAlert } from '../types';


// Create controller instance
const alertControllerInstance = new AlertController();
const router =Router();

// Alert routes
(router).get("/", authenticate, (alertControllerInstance).getAlerts);
(router).get("/count", authenticate, (alertControllerInstance).getAlertCount);
(router).get("/:id", authenticate, (alertControllerInstance).getAlert);
(router).put("/:id/status", authenticate, (alertControllerInstance).updateAlertStatus);
(router).post("/test", authenticate, (alertControllerInstance).createTestAlert);

export default router;
