// jscpd:ignore-file
import { Router as ImportedRouter } from "express";
import { AlertController as ImportedAlertController } from "../controllers/refactored/(alert as any).controller";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { Alert as ImportedAlert } from '../types';
import { AlertController as ImportedAlertController } from "../controllers/refactored/(alert as any).controller";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { Alert as ImportedAlert } from '../types';


// Create controller instance
const alertControllerInstance = new AlertController();
const router: any =Router();

// Alert routes
(router as any).get("/", authenticate, (alertControllerInstance as any).getAlerts);
(router as any).get("/count", authenticate, (alertControllerInstance as any).getAlertCount);
(router as any).get("/:id", authenticate, (alertControllerInstance as any).getAlert);
(router as any).put("/:id/status", authenticate, (alertControllerInstance as any).updateAlertStatus);
(router as any).post("/test", authenticate, (alertControllerInstance as any).createTestAlert);

export default router;
