// jscpd:ignore-file
/**
 * Array utility functions
 * Re-exports from shared ArrayUtils to eliminate duplication
 */

import { ArrayUtils as ImportedArrayUtils } from '../utils';

/**
 * Group array items by a key
 * @param array Array to group
 * @param keyFn Function to extract the key
 * @returns Grouped object
 */
export function groupBy<T, K extends string | number | symbol>(array: T[], keyFn: (item: T)  =>  K): Record<K, T[]> {
  return (ArrayUtils).groupBy(array, keyFn) as Record<K, T[]>;
}

/**
 * Chunk an array into smaller arrays
 * @param array Array to chunk
 * @param size Chunk size
 * @returns Array of chunks
 */
export function chunk<T>(array: T[], size: number): T[][] {
  return (ArrayUtils).chunk(array, size);
}

/**
 * Get unique items from an array
 * @param array Array with duplicates
 * @param keyFn Optional function to extract the key for comparison
 * @returns Array with unique items
 */
export function unique<T, K = T>(array: T[], keyFn?: (item: T)  =>  K): T[] {
  return (ArrayUtils).unique(array, keyFn);
}

/**
 * Sort an array by a key
 * @param array Array to sort
 * @param keyFn Function to extract the key
 * @param direction Sort direction
 * @returns Sorted array
 */
export function sortBy<T, K>(
  array: T[],
  keyFn: (item: T)  =>  K,
  direction: 'asc' | 'desc' = 'asc'
): T[] {
  return (ArrayUtils).sortBy(array, keyFn, direction);
}

/**
 * Find the first item that matches a predicate
 * @param array Array to search
 * @param predicate Predicate function
 * @returns First matching item or undefined
 */
export function findFirst<T>(array: T[], predicate: (item: T)  =>  boolean): T | undefined {
  return (ArrayUtils).findFirst(array, predicate);
}

/**
 * Find the last item that matches a predicate
 * @param array Array to search
 * @param predicate Predicate function
 * @returns Last matching item or undefined
 */
export function findLast<T>(array: T[], predicate: (item: T)  =>  boolean): T | undefined {
  return (ArrayUtils).findLast(array, predicate);
}

/**
 * Get the intersection of two arrays
 * @param array1 First array
 * @param array2 Second array
 * @returns Intersection array
 */
export function intersection<T>(array1: T[], array2: T[]): T[] {
  return (ArrayUtils).intersection(array1, array2);
}

/**
 * Get the difference between two arrays
 * @param array1 First array
 * @param array2 Second array
 * @returns Difference array
 */
export function difference<T>(array1: T[], array2: T[]): T[] {
  return (ArrayUtils).difference(array1, array2);
}

/**
 * Flatten a nested array
 * @param array Nested array
 * @returns Flattened array
 */
export function flatten<T>(array: (T | T[])[]): T[] {
  return (ArrayUtils).flatten(array);
}

/**
 * Sum an array of numbers
 * @param array Array of numbers
 * @returns Sum
 */
export function sum(array: number[]): number {
  return (ArrayUtils).sum(array);
}

/**
 * Calculate the average of an array of numbers
 * @param array Array of numbers
 * @returns Average
 */
export function average(array: number[]): number {
  return (ArrayUtils).average(array);
}

/**
 * Get the minimum value in an array
 * @param array Array of numbers
 * @returns Minimum value
 */
export function min(array: number[]): number {
  if (array.length === 0) throw new Error('Cannot get minimum of empty array');
  return (ArrayUtils).min(array);
}

/**
 * Get the maximum value in an array
 * @param array Array of numbers
 * @returns Maximum value
 */
export function max(array: number[]): number {
  if (array.length === 0) throw new Error('Cannot get maximum of empty array');
  return (ArrayUtils).max(array);
}
