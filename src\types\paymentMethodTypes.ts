// jscpd:ignore-file
/**
 * Payment method types
 */
export enum PaymentMethodType {
  BINANCE_PAY = "binance_pay",
  BINANCE_C2C = "binance_c2c",
  BINANCE_TRC20 = "binance_trc20",
  BINANCE_TRC20_DIRECT = "binance_trc20_direct",
  USDT = "usdt",
  USDC = "usdc",
  CRYPTO_TRANSFER = "crypto_transfer"
}

/**
 * Base payment method configuration
 */
export interface BasePaymentMethodConfig {
  supportedCurrencies?: string[];
  instructions?: Record<string, string>;
  verificationTimeout?: number;
  customFields?: CustomField[];
}

/**
 * Binance Pay configuration
 */
export interface BinancePayConfig extends BasePaymentMethodConfig {
  merchantId: string;
  apiKey: string;
  apiSecret: string;
  autoRedirect?: boolean;
}

/**
 * Binance C2C configuration
 */
export interface BinanceC2CConfig extends BasePaymentMethodConfig {
  binanceId: string;
  notePrefix: string;
}

/**
 * Binance TRC20 configuration
 */
export interface BinanceTRC20Config extends BasePaymentMethodConfig {
  walletAddress: string;
  apiKey?: string;
  apiSecret?: string;
  usesMerchantCredentials?: boolean;
  supportedCoins?: string[];
}

/**
 * Network configuration for crypto transfers
 */
export interface NetworkConfig {
  id: string;
  name: string;
  walletAddress: string;
  confirmations: number;
  supportedCurrencies: string[];
  isActive: boolean;
}

/**
 * Crypto transfer configuration
 */
export interface CryptoTransferConfig extends BasePaymentMethodConfig {
  networks: NetworkConfig[];
  walletAddresses?: Record<string, string>;
  supportedNetworks?: string[];
}

/**
 * Custom field for payment methods
 */
export interface CustomField {
  id: string;
  label: string;
  placeholder?: string;
  description?: string;
  required: boolean;
  type: "text" | "number" | "email" | "password";
}

/**
 * Payment method
 */
export interface PaymentMethod {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  type: PaymentMethodType;
  isActive: boolean;
  merchantId: string;
  createdAt: Date;
  updatedAt: Date;
  config: 
    | BinancePayConfig
    | BinanceC2CConfig
    | BinanceTRC20Config
    | CryptoTransferConfig
    | Record<string, unknown>;
}

/**
 * Payment method form data
 */
export interface PaymentMethodFormData {
  name: string;
  displayName: string;
  description?: string;
  isActive: boolean;
  type: PaymentMethodType;
  merchantId: string;
  config: Record<string, any>;
}
