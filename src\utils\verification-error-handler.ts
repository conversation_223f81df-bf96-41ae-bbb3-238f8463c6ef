import { logger } from './logger';

/**
 * Verification error types
 */
export enum VerificationErrorType {
  INVALID_INPUT = 'INVALID_INPUT',
  PROVIDER_ERROR = 'PROVIDER_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * Verification error response interface
 */
export interface VerificationErrorResponse {
  success: boolean;
  error: {
    code: VerificationErrorType;
    message: string;
    details?: unknown;
  };
  data: null;
}

/**
 * Verification error class
 */
export class VerificationError extends Error {
  code: VerificationErrorType;
  originalError?: unknown;
  context?: Record<string, any>;

  constructor(
    code: VerificationErrorType,
    message: string,
    originalError?: unknown,
    context?: Record<string, any>
  ) {
    super(message);
    this.name = 'VerificationError';
    this.code = code;
    this.originalError = originalError;
    this.context = context;

    // Ensure the prototype chain is properly set up
    Object.setPrototypeOf(this, VerificationError.prototype);
  }
}

/**
 * Create a verification error
 * @param code Error code
 * @param message Error message
 * @param originalError Original error (if any)
 * @param context Additional context
 * @returns Verification error
 */
export function createVerificationError(
  code: VerificationErrorType,
  message: string,
  originalError?: unknown,
  context?: Record<string, any>
): VerificationError {
  // Log the error
  logger.error(`Verification error: ${code} - ${message}`, {
    code,
    message,
    originalError,
    context,
  });

  return new VerificationError(code, message, originalError, context);
}

/**
 * Format a verification error response
 * @param error Verification error
 * @returns Formatted error response
 */
export function formatVerificationErrorResponse(
  error: VerificationError
): VerificationErrorResponse {
  const status = 400;
  const responseData: VerificationErrorResponse = {
    success: false,
    error: {
      code: error.code,
      message: error.message,
      details: error.originalError
        ? {
            originalError: error.originalError,
            context: error.context,
          }
        : undefined,
    },
    data: null,
  };

  return responseData;
}

/**
 * Handle a verification error
 * @param error Error to handle
 * @param defaultMessage Default error message
 * @param context Additional context
 * @returns Verification error
 */
export function handleVerificationError(
  error: Error,
  defaultMessage = 'Verification failed',
  context?: Record<string, any>
): VerificationError {
  // If it's already a VerificationError, just return it
  if (error instanceof VerificationError) {
    return error;
  }

  // Determine the error type based on the error
  let errorType = VerificationErrorType.UNKNOWN_ERROR;
  let errorMessage = defaultMessage;

  if (error) {
    // Extract message if available
    if (error.message) {
      errorMessage = error.message;
    }

    // Determine error type based on error properties
    if (
      error.code === 'ECONNREFUSED' ||
      error.code === 'ECONNRESET' ||
      error.code === 'ETIMEDOUT'
    ) {
      errorType = VerificationErrorType.NETWORK_ERROR;
    } else if (error.status === 401 || error.statusCode === 401) {
      errorType = VerificationErrorType.AUTHENTICATION_ERROR;
    } else if (error.status === 403 || error.statusCode === 403) {
      errorType = VerificationErrorType.AUTHORIZATION_ERROR;
    } else if (error.status === 404 || error.statusCode === 404) {
      errorType = VerificationErrorType.NOT_FOUND_ERROR;
    } else if (
      error.status === 422 ||
      error.statusCode === 422 ||
      error.name === 'ValidationError'
    ) {
      errorType = VerificationErrorType.VALIDATION_ERROR;
    } else if (
      error.status === 429 ||
      error.statusCode === 429 ||
      error.message?.includes('rate limit')
    ) {
      errorType = VerificationErrorType.RATE_LIMIT_ERROR;
    } else if (error.timeout || error.message?.includes('timeout')) {
      errorType = VerificationErrorType.TIMEOUT_ERROR;
    } else if (error.status >= 500 || (error.statusCode && error.statusCode >= 500)) {
      errorType = VerificationErrorType.PROVIDER_ERROR;
    }
  }

  return createVerificationError(errorType, errorMessage, error, context);
}
