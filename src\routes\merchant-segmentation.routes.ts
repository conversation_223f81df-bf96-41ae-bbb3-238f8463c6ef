// jscpd:ignore-file
/**
 * Merchant Segmentation Routes
 * 
 * This file defines the routes for merchant segmentation.
 */

import express from "express";
import { MerchantSegmentationController as ImportedMerchantSegmentationController } from "../controllers/merchant-(segmentation as any).controller";
import { authMiddleware as ImportedauthMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware as ImportedroleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant as ImportedMerchant } from '../types';
import { authMiddleware as ImportedauthMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware as ImportedroleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant as ImportedMerchant } from '../types';


const router: any =(express as any).Router();
const merchantSegmentationController = new MerchantSegmentationController();

/**
 * @route POST /api/merchant-segmentation/categories
 * @desc Create a new merchant category
 * @access Private (Admin)
 */
(router as any).post(
    "/categories",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    (merchantSegmentationController as any).createCategory
);

/**
 * @route GET /api/merchant-segmentation/categories
 * @desc Get all merchant categories
 * @access Private (Admin)
 */
(router as any).get(
    "/categories",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    (merchantSegmentationController as any).getAllCategories
);

/**
 * @route POST /api/merchant-segmentation/categories/:categoryId/merchants/:merchantId
 * @desc Add merchant to category
 * @access Private (Admin)
 */
(router as any).post(
    "/categories/:categoryId/merchants/:merchantId",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    (merchantSegmentationController as any).addMerchantToCategory
);

/**
 * @route POST /api/merchant-segmentation/segments
 * @desc Create a new merchant segment
 * @access Private (Admin)
 */
(router as any).post(
    "/segments",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    (merchantSegmentationController as any).createSegment
);

/**
 * @route GET /api/merchant-segmentation/segments
 * @desc Get all merchant segments
 * @access Private (Admin)
 */
(router as any).get(
    "/segments",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    (merchantSegmentationController as any).getAllSegments
);

/**
 * @route POST /api/merchant-segmentation/segments/:segmentId/apply
 * @desc Apply segment to matching merchants
 * @access Private (Admin)
 */
(router as any).post(
    "/segments/:segmentId/apply",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    (merchantSegmentationController as any).applySegment
);

/**
 * @route POST /api/merchant-segmentation/performance-tiers
 * @desc Create a new merchant performance tier
 * @access Private (Admin)
 */
(router as any).post(
    "/performance-tiers",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    (merchantSegmentationController as any).createPerformanceTier
);

/**
 * @route POST /api/merchant-segmentation/performance-tiers/:tierId/apply
 * @desc Apply performance tier to qualifying merchants
 * @access Private (Admin)
 */
(router as any).post(
    "/performance-tiers/:tierId/apply",
    authMiddleware,
    roleMiddleware(["ADMIN"]),
    (merchantSegmentationController as any).applyPerformanceTier
);

export default router;
