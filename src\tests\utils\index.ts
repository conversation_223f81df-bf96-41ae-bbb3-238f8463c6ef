import { PrismaClient } from '@prisma/client';
/**
 * Test Utils Module
 *
 * Centralized exports for the test utility system.
 */

// Core exports
export * from './core/TestTypes';

// Factory exports
export * from './factories/MockFactories';

// Runner exports
export * from './runners/TestRunners';

// Suite builder exports
export * from './suites/TestSuiteBuilders';

// Data generator exports
export * from './generators/DataGenerators';

// Assertion exports
export * from './assertions/CustomAssertions';

// Convenience re-exports for common patterns
export {
  createMockRequest,
  createMockResponse,
  createMockNext,
  createMockPrismaClient,
  createMockJwtToken,
  createMockApiResponse,
  createMockErrorResponse,
} from './factories/MockFactories';

export { testController, testService, testRepository, testMiddleware } from './runners/TestRunners';

export {
  createControllerTestSuite,
  createServiceTestSuite,
  createRepositoryTestSuite,
  createIntegrationTestSuite,
  createPerformanceTestSuite,
  createApiTestSuite,
} from './suites/TestSuiteBuilders';

export {
  generateUUID,
  generateEmail,
  generateRandomString,
  generateMockUser,
  generateMockMerchant,
  generateMockTransaction,
  generateMockArray,
  generateMockDataWithRelationships,
} from './generators/DataGenerators';

export {
  setupCustomMatchers,
  AssertionHelpers,
  DatabaseAssertions,
} from './assertions/CustomAssertions';

// Import functions for internal use
import {
  createMockRequest as _createMockRequest,
  createMockResponse as _createMockResponse,
  createMockNext as _createMockNext,
  createMockPrismaClient as _createMockPrismaClient,
} from './factories/MockFactories';

import {
  generateMockUser as _generateMockUser,
  generateMockMerchant as _generateMockMerchant,
  generateMockDataWithRelationships as _generateMockDataWithRelationships,
} from './generators/DataGenerators';

// Default export - main test utility class
export class TestUtils {
  /**
   * Setup test environment
   */
  static setup(): void {
    // setupCustomMatchers(); // Commented out due to type issues
  }

  /**
   * Create a complete test context
   */
  static createTestContext(name: string): {
    mockRequest: unknown;
    mockResponse: unknown;
    mockNext: unknown;
    mockPrisma: unknown;
    mockUser: unknown;
    mockMerchant: unknown;
    cleanup: () => void;
  } {
    const mockRequest = _createMockRequest();
    const mockResponse = _createMockResponse();
    const mockNext = _createMockNext();
    const mockPrisma = _createMockPrismaClient();
    const mockUser = _generateMockUser();
    const mockMerchant = _generateMockMerchant();

    const cleanup = () => {
      jest.clearAllMocks();
    };

    return {
      mockRequest,
      mockResponse,
      mockNext,
      mockPrisma,
      mockUser,
      mockMerchant,
      cleanup,
    };
  }

  /**
   * Create a test database context
   */
  static createDatabaseTestContext(): {
    mockPrisma: unknown;
    seedData: () => Promise<void>;
    cleanup: () => Promise<void>;
  } {
    const mockPrisma = _createMockPrismaClient();
    const testData = _generateMockDataWithRelationships();

    const seedData = async () => {
      // Mock seeding data
      (mockPrisma.user.findMany as any).mockResolvedValue(testData.users);
      (mockPrisma.merchant.findMany as any).mockResolvedValue(testData.merchants);
      (mockPrisma.transaction.findMany as any).mockResolvedValue(testData.transactions);
      (mockPrisma.paymentMethod.findMany as any).mockResolvedValue(testData.paymentMethods);
    };

    const cleanup = async () => {
      jest.clearAllMocks();
    };

    return {
      mockPrisma,
      seedData,
      cleanup,
    };
  }

  /**
   * Create an API test context
   */
  static createApiTestContext(baseUrl: string = 'http://localhost:3000'): {
    request: unknown;
    authenticate: (token: string) => void;
    expectSuccess: (response: unknown) => void;
    expectError: (response: unknown, status?: number) => void;
  } {
    let authToken: string | null = null;

    const request = {
      get: (path: string) => ({
        url: `${baseUrl}${path}`,
        method: 'GET',
        headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
      }),
      post: (path: string, data?: unknown) => ({
        url: `${baseUrl}${path}`,
        method: 'POST',
        data,
        headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
      }),
      put: (path: string, data?: unknown) => ({
        url: `${baseUrl}${path}`,
        method: 'PUT',
        data,
        headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
      }),
      delete: (path: string) => ({
        url: `${baseUrl}${path}`,
        method: 'DELETE',
        headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
      }),
    };

    const authenticate = (token: string) => {
      authToken = token;
    };

    const expectSuccess = (response: unknown) => {
      // expect(response).toMatchApiResponse(response.data); // Custom matcher not available
      expect(response.success).toBe(true);
      expect(response.status).toBeGreaterThanOrEqual(200);
      expect(response.status).toBeLessThan(300);
    };

    const expectError = (response: unknown, status: number = 400) => {
      expect(response.success).toBe(false);
      expect(response.status).toBe(status);
      expect(response.error).toBeDefined();
    };

    return {
      request,
      authenticate,
      expectSuccess,
      expectError,
    };
  }
}
