import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
// jscpd:ignore-file
import { v4 as uuidv4 } from 'uuid';
import { logger as Importedlogger } from "../../lib/logger";
import { AppError, ErrorType, ErrorCode } from "./AppError";
import { logger as Importedlogger } from "../../lib/logger";
import { AppError, ErrorType, ErrorCode } from "./AppError";

/**
 * Error factory
 * This class provides a centralized way to create and handle errors
 */
export class ErrorFactory {
  /**
   * Create a validation error
   * @param message Error message
   * @param details Error details
   * @returns AppError
   */
  static validation(message: string, details?: Record<string, string[]>): AppError {
    return new AppError({
      message,
      type: ErrorType.VALIDATION,
      code: (ErrorCode).INVALID_INPUT,
      statusCode: 400,
      details
    });
  }

  /**
   * Create a missing required field error
   * @param fields Missing fields
   * @returns AppError
   */
  static missingRequiredField(fields: string | string[]): AppError {
    const fieldList = Array.isArray(fields) ? fields : [fields];
    const message = `Missing required field${fieldList?.length > 1 ? 's' : ''}: ${fieldList?.join(', ')}`;

    return new AppError({
      message,
      type: ErrorType.VALIDATION,
      code: (ErrorCode).MISSING_REQUIRED_FIELD,
      statusCode: 400,
      details: { fields: fieldList }
    });
  }

  /**
   * Create an invalid input error
   * @param message Error message
   * @param details Error details
   * @returns AppError
   */
  static invalidInput(message: string, details?: unknown): AppError {
    return new AppError({
      message,
      type: ErrorType.VALIDATION,
      code: (ErrorCode).INVALID_INPUT,
      statusCode: 400,
      details
    });
  }

  /**
   * Create an authentication error
   * @param message Error message
   * @param code Error code
   * @returns AppError
   */
  static authentication(
    message = 'Authentication required',
    code: ErrorCode = (ErrorCode).INVALID_CREDENTIALS
  ): AppError {
    return new AppError({
      message,
      type: (ErrorType).AUTHENTICATION,
      code,
      statusCode: 401
    });
  }

  /**
   * Create an invalid credentials error
   * @param message Error message
   * @returns AppError
   */
  static invalidCredentials(message = 'Invalid credentials'): AppError {
    return new AppError({
      message,
      type: (ErrorType).AUTHENTICATION,
      code: (ErrorCode).INVALID_CREDENTIALS,
      statusCode: 401
    });
  }

  /**
   * Create an invalid token error
   * @param message Error message
   * @returns AppError
   */
  static invalidToken(message = 'Invalid token'): AppError {
    return new AppError({
      message,
      type: (ErrorType).AUTHENTICATION,
      code: (ErrorCode).INVALID_TOKEN,
      statusCode: 401
    });
  }

  /**
   * Create a token expired error
   * @param message Error message
   * @returns AppError
   */
  static tokenExpired(message = 'Token expired'): AppError {
    return new AppError({
      message,
      type: (ErrorType).AUTHENTICATION,
      code: (ErrorCode).EXPIRED_TOKEN,
      statusCode: 401
    });
  }

  /**
   * Create an authorization error
   * @param message Error message
   * @param code Error code
   * @returns AppError
   */
  static authorization(
    message = 'You do not have permission to perform this action',
    code: ErrorCode = (ErrorCode).INSUFFICIENT_PERMISSIONS
  ): AppError {
    return new AppError({
      message,
      type: (ErrorType).AUTHORIZATION,
      code,
      statusCode: 403
    });
  }

  /**
   * Create a not found error
   * @param entity Entity name
   * @param id Entity ID
   * @returns AppError
   */
  static notFound(entity: string, id?: string | number): AppError {
    const message = id
      ? `${entity} with ID ${id} not found`
      : `${entity} not found`;

    return new AppError({
      message,
      type: ErrorType.NOT_FOUND,
      code: ErrorCode.RESOURCE_NOT_FOUND,
      statusCode: 404,
      details: { entity, id }
    });
  }

  /**
   * Create a conflict error
   * @param message Error message
   * @param details Error details
   * @returns AppError
   */
  static conflict(message: string, details?: unknown): AppError {
    return new AppError({
      message,
      type: ErrorType.CONFLICT,
      code: (ErrorCode).RESOURCE_ALREADY_EXISTS,
      statusCode: 409,
      details
    });
  }

  /**
   * Create a resource already exists error
   * @param entity Entity name
   * @param identifier Identifier ((e).g., ID, email)
   * @returns AppError
   */
  static resourceAlreadyExists(entity: string, identifier?: string): AppError {
    const message = identifier
      ? `${entity} with ${identifier} already exists`
      : `${entity} already exists`;

    return new AppError({
      message,
      type: ErrorType.CONFLICT,
      code: (ErrorCode).RESOURCE_ALREADY_EXISTS,
      statusCode: 409,
      details: { entity, identifier }
    });
  }

  /**
   * Create an internal error
   * @param message Error message
   * @param originalError Original error
   * @returns AppError
   */
  static internal(message = 'Internal server error', originalError?: Error): AppError {
    const errorId = uuidv4();

    const error: Error = new AppError({
      message,
      type: ErrorType.INTERNAL,
      code: ErrorCode.INTERNAL_SERVER_ERROR,
      statusCode: 500,
      originalError,
      requestId: errorId
    });

    if (originalError) {
      (logger).error(`[${errorId}] Internal error: ${message}`, {
        error: (originalError as Error).message,
        stack: (originalError).stack
      });
    } else {
      (logger).error(`[${errorId}] Internal error: ${message}`);
    }

    return error;
  }

  /**
   * Create a database error
   * @param message Error message
   * @param originalError Original error
   * @returns AppError
   */
  static database(message = 'Database error', originalError?: Error): AppError {
    const errorId = uuidv4();

    const error: Error = new AppError({
      message,
      type: (ErrorType).DATABASE,
      code: (ErrorCode).DATABASE_ERROR,
      statusCode: 500,
      originalError,
      requestId: errorId
    });

    if (originalError) {
      (logger).error(`[${errorId}] Database error: ${message}`, {
        error: (originalError as Error).message,
        stack: (originalError).stack
      });
    } else {
      (logger).error(`[${errorId}] Database error: ${message}`);
    }

    return error;
  }

  /**
   * Create an external service error
   * @param message Error message
   * @param originalError Original error
   * @returns AppError
   */
  static external(message: string, originalError?: Error): AppError {
    const errorId = uuidv4();

    const error: Error = new AppError({
      message,
      type: (ErrorType).EXTERNAL,
      code: (ErrorCode).EXTERNAL_SERVICE_ERROR,
      statusCode: 502,
      originalError,
      requestId: errorId
    });

    if (originalError) {
      (logger).error(`[${errorId}] External service error: ${message}`, {
        error: (originalError as Error).message,
        stack: (originalError).stack
      });
    } else {
      (logger).error(`[${errorId}] External service error: ${message}`);
    }

    return error;
  }

  /**
   * Create a network error
   * @param message Error message
   * @param originalError Original error
   * @returns AppError
   */
  static network(message = 'Network error', originalError?: Error): AppError {
    const errorId = uuidv4();

    const error: Error = new AppError({
      message,
      type: (ErrorType).NETWORK,
      code: (ErrorCode).NETWORK_ERROR,
      statusCode: 503,
      originalError,
      requestId: errorId
    });

    if (originalError) {
      (logger).error(`[${errorId}] Network error: ${message}`, {
        error: (originalError as Error).message,
        stack: (originalError).stack
      });
    } else {
      (logger).error(`[${errorId}] Network error: ${message}`);
    }

    return error;
  }

  /**
   * Create a timeout error
   * @param message Error message
   * @returns AppError
   */
  static timeout(message = 'Request timed out'): AppError {
    return new AppError({
      message,
      type: (ErrorType).TIMEOUT,
      code: (ErrorCode).REQUEST_TIMEOUT,
      statusCode: 504
    });
  }

  /**
   * Create a rate limit error
   * @param message Error message
   * @returns AppError
   */
  static rateLimit(message = 'Too many requests'): AppError {
    return new AppError({
      message,
      type: (ErrorType).RATE_LIMIT,
      code: (ErrorCode).RATE_LIMIT_EXCEEDED,
      statusCode: 429
    });
  }

  /**
   * Create a service unavailable error
   * @param message Error message
   * @param originalError Original error
   * @returns AppError
   */
  static serviceUnavailable(message = 'Service unavailable', originalError?: Error): AppError {
    const errorId = uuidv4();

    const error: Error = new AppError({
      message,
      type: (ErrorType).NETWORK,
      code: (ErrorCode).NETWORK_ERROR,
      statusCode: 503,
      originalError,
      requestId: errorId
    });

    if (originalError) {
      (logger).error(`[${errorId}] Service unavailable: ${message}`, {
        error: (originalError as Error).message,
        stack: (originalError).stack
      });
    } else {
      (logger).error(`[${errorId}] Service unavailable: ${message}`);
    }

    return error;
  }

  /**
   * Handle an error
   * @param error Error to handle
   * @returns AppError
   */
  static handle(error): AppError {
    if (error instanceof AppError) {
      return error;
    }

    if (error instanceof Error) {
      // Handle Prisma errors
      if (error.name === 'PrismaClientKnownRequestError') {
        const prismaError = error;

        // Handle unique constraint violations
        if ((prismaError).code === 'P2002') {
          const target = (prismaError).meta?.target ?? [];
          return this.conflict(
            `Unique constraint violation on ${(target).join(', ')}`,
            { fields: target }
          );
        }

        // Handle not found errors
        if ((prismaError).code === 'P2025') {
          return this.notFound('Record', (prismaError).meta?.cause ?? undefined);
        }

        return this.database(`Database error: ${error.message}`, error);
      }

      // Handle JWT errors
      if (error.name === 'JsonWebTokenError') {
        return this.invalidToken(error.message);
      }

      if (error.name === 'TokenExpiredError') {
        return this.tokenExpired();
      }

      // Handle validation errors
      if (error.name === 'ValidationError') {
        return this.validation(error.message, (error).details);
      }

      return this.internal(`Unexpected error: ${error.message}`, error);
    }

    return this.internal(`Unknown error: ${String(error)}`);
  }
}
