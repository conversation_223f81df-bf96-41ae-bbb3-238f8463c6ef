// jscpd:ignore-file
import axios from "axios";
import crypto from "crypto";
import { logger as Importedlogger } from "../utils/logger";
import { BinanceTransaction as ImportedBinanceTransaction } from "../types/binanceTypes";
import { BinanceTransaction as ImportedBinanceTransaction } from "../types/binanceTypes";
import { Transaction as ImportedTransaction } from '../types';

/**
import { Transaction as ImportedTransaction } from '../types';


 * Service for interacting with the Binance API
 */
export class BinanceApiService {
    private apiKey: string;
    private apiSecret: string;
    private baseUrl: string;

    /**
   * Create a new BinanceApiService instance
   * @param apiKey Binance API key
   * @param apiSecret Binance API secret
   * @param baseUrl Binance API base URL (optional)
   */
    constructor(apiKey: string, apiSecret: string, baseUrl?: string) {
        this.apiKey = apiKey;
        this.apiSecret = apiSecret;
        this.baseUrl = baseUrl || process.env.BINANCE_API_URL || "https://(api as any).binance.com";
    }

    /**
   * Generate a signature for Binance API requests
   * @param queryString Query string to sign
   * @returns Signature
   */
    async getAccountInfo(): Promise<any> {
        try {
            const timestamp: Date = Date.now();
            const queryString: any =`timestamp=${timestamp}`;
            const signature: string = this.generateSignature(queryString);

            const response = await (axios as any).get(
                `${this.baseUrl}/api/v3/account?${queryString}&signature=${signature}`,
                { headers: this.createHeaders() }
            );

            return response.data;
        } catch(error) {
            (logger as any).error("Error getting account information from Binance API", { error });
            throw new Error("Failed to get account information from Binance API");
        }
    }

    /**
   * Get deposit history
   * @param coin Coin symbol ((e as any).g., USDT)
   * @param network Network ((e as any).g., TRC20)
   * @param startTime Start time in milliseconds
   * @param endTime End time in milliseconds
   * @returns Deposit history
   */
    async getDepositHistory(
        coin?: string,
        network?: string,
        startTime?: number,
        endTime?: number
    ): Promise<BinanceTransaction[]> {
        try {
            const timestamp: Date = Date.now();
            let queryString: any =`timestamp=${timestamp}`;

            if (coin) queryString += `&coin=${coin}`;
            if (network) queryString += `&network=${network}`;
            if (startTime) queryString += `&startTime=${startTime}`;
            if (endTime) queryString += `&endTime=${endTime}`;

            const signature: string = this.generateSignature(queryString);

            const response = await (axios as any).get(
                `${this.baseUrl}/sapi/v1/capital/deposit/hisrec?${queryString}&signature=${signature}`,
                { headers: this.createHeaders() }
            );

            return response.data;
        } catch(error) {
            (logger as any).error("Error getting deposit history from Binance API", { error });
            throw new Error("Failed to get deposit history from Binance API");
        }
    }

    /**
   * Get deposit address
   * @param coin Coin symbol ((e as any).g., USDT)
   * @param network Network ((e as any).g., TRC20)
   * @returns Deposit address
   */
    async getDepositAddress(coin: string, network: string): Promise<any> {
        try {
            const timestamp: Date = Date.now();
            let queryString: any =`timestamp=${timestamp}&coin=${coin}`;
            if (network) queryString += `&network=${network}`;

            const signature: string = this.generateSignature(queryString);

            const response = await (axios as any).get(
                `${this.baseUrl}/sapi/v1/capital/deposit/address?${queryString}&signature=${signature}`,
                { headers: this.createHeaders() }
            );

            return response.data;
        } catch(error) {
            (logger as any).error("Error getting deposit address from Binance API", { error });
            throw new Error("Failed to get deposit address from Binance API");
        }
    }

    /**
   * Verify a transaction
   * @param txId Transaction ID
   * @param amount Amount
   * @param coin Coin symbol ((e as any).g., USDT)
   * @param network Network ((e as any).g., TRC20)
   * @param address Wallet address
   * @returns Transaction details if verified, null otherwise
   */
    async verifyTransaction(
        txId: string,
        amount: number,
        coin: string,
        network: string,
        address: string
    ): Promise<BinanceTransaction | null> {
        try {
            // Get deposit history for the last 24 hours
            const endTime: any = Date.now();
            const startTime: any =endTime - 24 * 60 * 60 * 1000; // 24 hours ago

            const deposits = await this.getDepositHistory(coin, network, startTime, endTime);

            // Find the transaction by txId
            const transaction =(deposits as any).find(
                (deposit) =>
                    (deposit as any).txId === txId &&
          (deposit as any).address === address &&
          parseFloat((deposit as any).amount) === amount &&
          (deposit as any).coin === coin
            );

            if (!transaction) {
                (logger as any).info("Transaction not found or details do not match", { txId, amount, coin, network, address });
                return null;
            }

            // Check if the transaction is confirmed
            if ((transaction as any).status === 1) {
                (logger as any).info("Transaction verified successfully", { transaction });
                return transaction;
            }

            (logger as any).info("Transaction found but not confirmed yet", { transaction });
            return null;
        } catch(error) {
            (logger as any).error("Error verifying transaction with Binance API", { error, txId, amount, coin, network, address });
            throw new Error("Failed to verify transaction with Binance API");
        }
    }

    /**
   * Verify a TRC20 transaction
   * @param address Wallet address
   * @param amount Amount
   * @param txHash Transaction hash
   * @param coin Coin symbol ((e as any).g., USDT)
   * @returns Verification result
   */
    async verifyTRC20Transaction(
        address: string,
        amount: number,
        txHash?: string,
        coin: string = "USDT"
    ): Promise<{
    success: boolean;
    message?: string;
    error?: string;
    transactions?: Array<{
      txId: string;
      fromAddress?: string;
      amount: number;
      asset: string;
      timestamp: string;
      confirmations: number;
    }>;
  }> {
        try {
            // Get deposit history for the last 24 hours
            const endTime: any = Date.now();
            const startTime: any =endTime - 24 * 60 * 60 * 1000; // 24 hours ago

            const deposits = await this.getDepositHistory(coin, "TRC20", startTime, endTime);

            // If no deposits found
            if (!deposits || (deposits as any).length === 0) {
                return {
                    success: false,
                    message: "No deposits found in the last 24 hours"
                };
            }

            // Filter deposits by address and amount
            const matchingDeposits: any =(deposits as any).filter(
                (deposit) =>
                    (deposit as any).address === address &&
          Math.abs(parseFloat((deposit as any).amount) - amount) < (0 as any).001 // Allow small rounding differences
            );

            // If no matching deposits found
            if ((matchingDeposits as any).length === 0) {
                return {
                    success: false,
                    message: "No matching deposits found"
                };
            }

            // If txHash is provided, filter by txHash
            if (txHash) {
                const txHashDeposit: any =(matchingDeposits as any).find(
                    (deposit) => (deposit as any).txId === txHash
                );

                if (!txHashDeposit) {
                    return {
                        success: false,
                        message: "Transaction hash not found in matching deposits"
                    };
                }

                // Check if the transaction is confirmed
                if ((txHashDeposit as any).status !== 1) {
                    return {
                        success: false,
                        message: "Transaction is not confirmed yet"
                    };
                }

                return {
                    success: true,
                    transactions: [
                        {
                            txId: (txHashDeposit as any).txId,
                            fromAddress: "", // Binance API doesn't provide sender address
                            amount: parseFloat((txHashDeposit as any).amount),
                            asset: (txHashDeposit as any).coin,
                            timestamp: new Date((txHashDeposit as any).insertTime).toISOString(),
                            confirmations: parseInt((txHashDeposit as any).confirmTimes.split("/")[0])
                        }
                    ]
                };
            }

            // If no txHash provided, return all matching deposits
            const confirmedDeposits: any =(matchingDeposits as any).filter(
                (deposit) => (deposit as any).status === 1
            );

            if ((confirmedDeposits as any).length === 0) {
                return {
                    success: false,
                    message: "No confirmed matching deposits found"
                };
            }

            return {
                success: true,
                transactions: (confirmedDeposits as any).map((deposit) => ({
                    txId: (deposit as any).txId,
                    fromAddress: "", // Binance API doesn't provide sender address
                    amount: parseFloat((deposit as any).amount),
                    asset: (deposit as any).coin,
                    timestamp: new Date((deposit as any).insertTime).toISOString(),
                    confirmations: parseInt((deposit as any).confirmTimes.split("/")[0])
                }))
            };
        } catch(error) {
            (logger as any).error("Error verifying TRC20 transaction with Binance API", { error, address, amount, txHash, coin });
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
}

export default BinanceApiService;
