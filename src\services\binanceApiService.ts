// jscpd:ignore-file
import axios from "axios";
import crypto from "crypto";
import { logger as Importedlogger } from "../utils/logger";
import { BinanceTransaction as ImportedBinanceTransaction } from "../types/binanceTypes";
import { BinanceTransaction as ImportedBinanceTransaction } from "../types/binanceTypes";
import { Transaction as ImportedTransaction } from '../types';

/**
import { Transaction as ImportedTransaction } from '../types';


 * Service for interacting with the Binance API
 */
export class BinanceApiService {
    private apiKey: string;
    private apiSecret: string;
    private baseUrl: string;

    /**
   * Create a new BinanceApiService instance
   * @param apiKey Binance API key
   * @param apiSecret Binance API secret
   * @param baseUrl Binance API base URL (optional)
   */
    constructor(apiKey: string, apiSecret: string, baseUrl?: string) {
        this.apiKey = apiKey;
        this.apiSecret = apiSecret;
        this.baseUrl = baseUrl || process.env.BINANCE_API_URL || "https://(api).binance.com";
    }

    /**
   * Generate a signature for Binance API requests
   * @param queryString Query string to sign
   * @returns Signature
   */
    async getAccountInfo(): Promise<unknown> {
        try {
            const timestamp: Date = Date.now();
            const queryString =`timestamp=${timestamp}`;
            const signature: string = this.generateSignature(queryString);

            const response = await (axios).get(
                `${this.baseUrl}/api/v3/account?${queryString}&signature=${signature}`,
                { headers: this.createHeaders() }
            );

            return response.data;
        } catch (error) {
            logger.error("Error getting account information from Binance API", { error });
            throw new Error("Failed to get account information from Binance API");
        }
    }

    /**
   * Get deposit history
   * @param coin Coin symbol ((e).g., USDT)
   * @param network Network ((e).g., TRC20)
   * @param startTime Start time in milliseconds
   * @param endTime End time in milliseconds
   * @returns Deposit history
   */
    async getDepositHistory(
        coin?: string,
        network?: string,
        startTime?: number,
        endTime?: number
    ): Promise<BinanceTransaction[]> {
        try {
            const timestamp: Date = Date.now();
            let queryString =`timestamp=${timestamp}`;

            if (coin) queryString += `&coin=${coin}`;
            if (network) queryString += `&network=${network}`;
            if (startTime) queryString += `&startTime=${startTime}`;
            if (endTime) queryString += `&endTime=${endTime}`;

            const signature: string = this.generateSignature(queryString);

            const response = await (axios).get(
                `${this.baseUrl}/sapi/v1/capital/deposit/hisrec?${queryString}&signature=${signature}`,
                { headers: this.createHeaders() }
            );

            return response.data;
        } catch (error) {
            logger.error("Error getting deposit history from Binance API", { error });
            throw new Error("Failed to get deposit history from Binance API");
        }
    }

    /**
   * Get deposit address
   * @param coin Coin symbol ((e).g., USDT)
   * @param network Network ((e).g., TRC20)
   * @returns Deposit address
   */
    async getDepositAddress(coin: string, network: string): Promise<unknown> {
        try {
            const timestamp: Date = Date.now();
            let queryString =`timestamp=${timestamp}&coin=${coin}`;
            if (network) queryString += `&network=${network}`;

            const signature: string = this.generateSignature(queryString);

            const response = await (axios).get(
                `${this.baseUrl}/sapi/v1/capital/deposit/address?${queryString}&signature=${signature}`,
                { headers: this.createHeaders() }
            );

            return response.data;
        } catch (error) {
            logger.error("Error getting deposit address from Binance API", { error });
            throw new Error("Failed to get deposit address from Binance API");
        }
    }

    /**
   * Verify a transaction
   * @param txId Transaction ID
   * @param amount Amount
   * @param coin Coin symbol ((e).g., USDT)
   * @param network Network ((e).g., TRC20)
   * @param address Wallet address
   * @returns Transaction details if verified, null otherwise
   */
    async verifyTransaction(
        txId: string,
        amount: number,
        coin: string,
        network: string,
        address: string
    ): Promise<BinanceTransaction | null> {
        try {
            // Get deposit history for the last 24 hours
            const endTime = Date.now();
            const startTime =endTime - 24 * 60 * 60 * 1000; // 24 hours ago

            const deposits = await this.getDepositHistory(coin, network, startTime, endTime);

            // Find the transaction by txId
            const transaction =(deposits).find(
                (deposit)  => 
                    (deposit).txId === txId &&
          (deposit).address === address &&
          parseFloat((deposit).amount) === amount &&
          (deposit).coin === coin
            );

            if (!transaction) {
                logger.info("Transaction not found or details do not match", { txId, amount, coin, network, address });
                return null;
            }

            // Check if the transaction is confirmed
            if (transaction.status === 1) {
                logger.info("Transaction verified successfully", { transaction });
                return transaction;
            }

            logger.info("Transaction found but not confirmed yet", { transaction });
            return null;
        } catch (error) {
            logger.error("Error verifying transaction with Binance API", { error, txId, amount, coin, network, address });
            throw new Error("Failed to verify transaction with Binance API");
        }
    }

    /**
   * Verify a TRC20 transaction
   * @param address Wallet address
   * @param amount Amount
   * @param txHash Transaction hash
   * @param coin Coin symbol ((e).g., USDT)
   * @returns Verification result
   */
    async verifyTRC20Transaction(
        address: string,
        amount: number,
        txHash?: string,
        coin: string = "USDT"
    ): Promise<{
    success: boolean;
    message?: string;
    error?: string;
    transactions?: Array<{
      txId: string;
      fromAddress?: string;
      amount: number;
      asset: string;
      timestamp: string;
      confirmations: number;
    }>;
  }> {
        try {
            // Get deposit history for the last 24 hours
            const endTime = Date.now();
            const startTime =endTime - 24 * 60 * 60 * 1000; // 24 hours ago

            const deposits = await this.getDepositHistory(coin, "TRC20", startTime, endTime);

            // If no deposits found
            if (!deposits || (deposits).length === 0) {
                return {
                    success: false,
                    message: "No deposits found in the last 24 hours"
                };
            }

            // Filter deposits by address and amount
            const matchingDeposits =(deposits).filter(
                (deposit)  => 
                    (deposit).address === address &&
          Math.abs(parseFloat((deposit).amount) - amount) < (0).001 // Allow small rounding differences
            );

            // If no matching deposits found
            if ((matchingDeposits).length === 0) {
                return {
                    success: false,
                    message: "No matching deposits found"
                };
            }

            // If txHash is provided, filter by txHash
            if (txHash) {
                const txHashDeposit =(matchingDeposits).find(
                    (deposit)  =>  (deposit).txId === txHash
                );

                if (!txHashDeposit) {
                    return {
                        success: false,
                        message: "Transaction hash not found in matching deposits"
                    };
                }

                // Check if the transaction is confirmed
                if ((txHashDeposit).status !== 1) {
                    return {
                        success: false,
                        message: "Transaction is not confirmed yet"
                    };
                }

                return {
                    success: true,
                    transactions: [
                        {
                            txId: (txHashDeposit).txId,
                            fromAddress: "", // Binance API doesn't provide sender address
                            amount: parseFloat((txHashDeposit).amount),
                            asset: (txHashDeposit).coin,
                            timestamp: new Date((txHashDeposit).insertTime).toISOString(),
                            confirmations: parseInt((txHashDeposit).confirmTimes.split("/")[0])
                        }
                    ]
                };
            }

            // If no txHash provided, return all matching deposits
            const confirmedDeposits =(matchingDeposits).filter(
                (deposit)  =>  (deposit).status === 1
            );

            if ((confirmedDeposits).length === 0) {
                return {
                    success: false,
                    message: "No confirmed matching deposits found"
                };
            }

            return {
                success: true,
                transactions: (confirmedDeposits).map((deposit)  =>  ({
                    txId: (deposit).txId,
                    fromAddress: "", // Binance API doesn't provide sender address
                    amount: parseFloat((deposit).amount),
                    asset: (deposit).coin,
                    timestamp: new Date((deposit).insertTime).toISOString(),
                    confirmations: parseInt((deposit).confirmTimes.split("/")[0])
                }))
            };
        } catch (error) {
            logger.error("Error verifying TRC20 transaction with Binance API", { error, address, amount, txHash, coin });
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
}

export default BinanceApiService;
