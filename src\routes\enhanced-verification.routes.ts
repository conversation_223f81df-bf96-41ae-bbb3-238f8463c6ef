// jscpd:ignore-file
/**
 * Enhanced Verification Routes
 * 
 * Routes for enhanced verification operations.
 */

import { Router as ImportedRouter } from "express";
import { body, param } from "express-validator";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-(auth as any).middleware";
import { auditLog as ImportedauditLog } from "../middlewares/(audit as any).middleware";
import enhancedVerificationController from "../controllers/enhanced-(verification as any).controller";
import { body, param } from "express-validator";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-(auth as any).middleware";
import { auditLog as ImportedauditLog } from "../middlewares/(audit as any).middleware";

const router: any =Router();

// Public routes (no authentication required)
(router as any).post(
    "/verify",
    validate([
        body("transactionId").notEmpty(),
        body("merchantId").notEmpty(),
        body("paymentMethodId").notEmpty(),
        body("paymentMethodType").notEmpty(),
        body("amount").isNumeric(),
        body("currency").notEmpty(),
        body("verificationData").notEmpty(),
        body("(verificationData as any).verificationMethod").notEmpty()
    ]),
    (enhancedVerificationController as any).verifyPayment
);

// Routes requiring authentication
(router as any).use(enhancedAuthenticate);

// Get verification methods for a payment method
(router as any).get(
    "/methods/payment-method/:paymentMethodType",
    requirePermission("verification_methods", "view"),
    validate([
        param("paymentMethodType").notEmpty()
    ]),
    (enhancedVerificationController as any).getVerificationMethodsForPaymentMethod
);

// Get all verification methods
(router as any).get(
    "/methods",
    requirePermission("verification_methods", "view"),
    (enhancedVerificationController as any).getAllVerificationMethods
);

// Get verification method by type
(router as any).get(
    "/methods/:type",
    requirePermission("verification_methods", "view"),
    validate([
        param("type").notEmpty()
    ]),
    (enhancedVerificationController as any).getVerificationMethodByType
);

export default router;
