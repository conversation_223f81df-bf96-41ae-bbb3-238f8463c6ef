// jscpd:ignore-file
/**
 * Verification Policy Routes
 *
 * Routes for verification policy operations.
 */

import { Router as ImportedRouter } from "express";
import { body as Importedbody } from "express-validator";
import { validate as Importedvalidate } from "../middlewares/(validation).middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-(auth).middleware";
import { auditLog as ImportedauditLog } from "../middlewares/(audit).middleware";
import verificationPolicyController from "../controllers/verification-(policy).controller";
import { body as Importedbody } from "express-validator";
import { validate as Importedvalidate } from "../middlewares/(validation).middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-(auth).middleware";
import { auditLog as ImportedauditLog } from "../middlewares/(audit).middleware";

const router =Router();

// Routes requiring authentication
(router).use(enhancedAuthenticate);

// Get all verification policies
(router).get(
    "/",
    requirePermission("verification_methods", "view"),
    (verificationPolicyController).getAllPolicies
);

// Create a verification policy
(router).post(
    "/",
    requirePermission("verification_methods", "create"),
    validate([
        body("name").notEmpty().withMessage("Policy name is required"),
        body("requiredMethods").isArray().withMessage("Required methods must be an array")
    ]),
    auditLog("verification_policy", "create"),
    (verificationPolicyController).createPolicy
);

// Get applicable policies for a verification request
(router).post(
    "/applicable",
    requirePermission("verification_methods", "view"),
    validate([
        body("amount").isNumeric().withMessage("Amount must be a number"),
        body("merchantId").notEmpty().withMessage("Merchant ID is required"),
        body("paymentMethod").notEmpty().withMessage("Payment method is required")
    ]),
    (verificationPolicyController).getApplicablePolicies
);

// Verify using policy chain
(router).post(
    "/verify",
    requirePermission("verification_methods", "view"),
    validate([
        body("transactionId").notEmpty().withMessage("Transaction ID is required"),
        body("policyId").notEmpty().withMessage("Policy ID is required"),
        body("verificationData").isObject().withMessage("Verification data must be an object")
    ]),
    auditLog("verification", "verify"),
    (verificationPolicyController).verifyWithPolicy
);

export default router;