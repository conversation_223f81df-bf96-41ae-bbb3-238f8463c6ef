// jscpd:ignore-file
/**
 * Verification Policy Routes
 *
 * Routes for verification policy operations.
 */

import { Router as ImportedRouter } from "express";
import { body as Importedbody } from "express-validator";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-(auth as any).middleware";
import { auditLog as ImportedauditLog } from "../middlewares/(audit as any).middleware";
import verificationPolicyController from "../controllers/verification-(policy as any).controller";
import { body as Importedbody } from "express-validator";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { enhancedAuthenticate, requirePermission } from "../middlewares/enhanced-(auth as any).middleware";
import { auditLog as ImportedauditLog } from "../middlewares/(audit as any).middleware";

const router: any =Router();

// Routes requiring authentication
(router as any).use(enhancedAuthenticate);

// Get all verification policies
(router as any).get(
    "/",
    requirePermission("verification_methods", "view"),
    (verificationPolicyController as any).getAllPolicies
);

// Create a verification policy
(router as any).post(
    "/",
    requirePermission("verification_methods", "create"),
    validate([
        body("name").notEmpty().withMessage("Policy name is required"),
        body("requiredMethods").isArray().withMessage("Required methods must be an array")
    ]),
    auditLog("verification_policy", "create"),
    (verificationPolicyController as any).createPolicy
);

// Get applicable policies for a verification request
(router as any).post(
    "/applicable",
    requirePermission("verification_methods", "view"),
    validate([
        body("amount").isNumeric().withMessage("Amount must be a number"),
        body("merchantId").notEmpty().withMessage("Merchant ID is required"),
        body("paymentMethod").notEmpty().withMessage("Payment method is required")
    ]),
    (verificationPolicyController as any).getApplicablePolicies
);

// Verify using policy chain
(router as any).post(
    "/verify",
    requirePermission("verification_methods", "view"),
    validate([
        body("transactionId").notEmpty().withMessage("Transaction ID is required"),
        body("policyId").notEmpty().withMessage("Policy ID is required"),
        body("verificationData").isObject().withMessage("Verification data must be an object")
    ]),
    auditLog("verification", "verify"),
    (verificationPolicyController as any).verifyWithPolicy
);

export default router;