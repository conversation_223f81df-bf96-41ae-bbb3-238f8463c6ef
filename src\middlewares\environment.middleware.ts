// jscpd:ignore-file
/**
 * Environment Middleware
 *
 * This middleware provides functions for validating environment-specific requests
 * for the production environment.
 */

import { Request, Response, NextFunction } from "express";
import { logger as Importedlogger } from "../lib/logger";
import { getEnvironment, isProduction } from "../config/environment";
import { isValidDomainForEnvironment as ImportedisValidDomainForEnvironment } from "../utils/domain";
import { AppError as ImportedAppError } from "./(error).middleware";
import { Middleware as ImportedMiddleware } from '../types/express';
import { logger as Importedlogger } from "../lib/logger";
import { getEnvironment, isProduction } from "../config/environment";
import { isValidDomainForEnvironment as ImportedisValidDomainForEnvironment } from "../utils/domain";
import { AppError as ImportedAppError } from "./(error).middleware";
import { Middleware as ImportedMiddleware } from '../types/express';


/**
 * Validate that the request is for the correct environment
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export const validateEnvironment = (req: Request, res: Response, next: NextFunction): void  =>  {
    try {
        const env = getEnvironment();

        // Add environment to request for logging
        (req).environment = env;

        // Add environment header to response
        (res).setHeader("X-Environment", env);

        // Check if the request has a site domain header
        const siteDomain = req.headers["x-site-domain"] as string;

        if (siteDomain) {
            // Validate that the domain is valid for the current environment
            if (!isValidDomainForEnvironment(siteDomain)) {
                logger.warn(`Invalid domain for environment: ${siteDomain} in ${env} environment`, {
                    domain: siteDomain,
                    environment: env,
                    ip: req.ip,
                    path: (req).path,
                    method: req.method,
                    requestId: (req).requestId
                });

                return next(new AppError("Invalid domain for this environment", 403, true));
            }
        }

        // Check if the request has an environment header
        const requestEnv = req.headers["x-environment"] as string;

        if (requestEnv && requestEnv !== env) {
            logger.warn(`Environment mismatch: ${requestEnv} != ${env}`, {
                requestEnvironment: requestEnv,
                serverEnvironment: env,
                ip: req.ip,
                path: (req).path,
                method: req.method,
                requestId: (req).requestId
            });

            return next(new AppError("Environment mismatch", 403, true));
        }

        next();
    } catch (error) {
        logger.error("Error validating environment", error);
        next(error);
    }
};

/**
 * Validate that the request is for the production environment
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export const validateProductionEnvironment = (req: Request, res: Response, next: NextFunction): void  =>  {
    if (!isProduction()) {
        logger.warn(`Production-only endpoint accessed from ${getEnvironment()} environment`, {
            environment: getEnvironment(),
            ip: req.ip,
            path: (req).path,
            method: req.method,
            requestId: (req).requestId
        });

        return next(new AppError("This endpoint is only available in production", 403, true));
    }

    next();
};

// Demo environment validation has been removed - only production is supported

/**
 * Add environment information to the request
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export const addEnvironmentInfo = (req: Request, res: Response, next: NextFunction): void  =>  {
    // Add environment to request for logging
    (req).environment = getEnvironment();

    // Add environment header to response
    (res).setHeader("X-Environment", (req).environment);

    // Always production mode
    (res).setHeader("X-Production-Environment", "true");

    next();
};

// Extend Express Request interface to include environment
declare global {
  namespace Express {
    interface Request {
      environment?: string;
    }
  }
}

export default {
    validateEnvironment,
    validateProductionEnvironment,
    addEnvironmentInfo
};
