// jscpd:ignore-file
/**
 * In-memory store for use when Redis is not available
 * This provides a fallback implementation with the same interface as the Redis client
 */

import { logger } from "../lib/logger";

class MemoryStore {
    private store: Map<string, { value: unknown; expiry: number | null }> = new Map();
    private isConnected: boolean = true;

    constructor() {
        logger.info("Initializing in-memory store as Redis alternative");

        // Start cleanup interval to remove expired keys
        setInterval(() => {
            this.cleanup();
        }, 60000); // Run cleanup every minute
    }

    /**
   * Set a key-value pair with optional expiry
   * @param key Key
   * @param value Value
   * @param expiry Expiry in seconds
   */
    async set(key: string, value, expiry?: number): Promise<void> {
        const expiryMs: unknown =expiry ? Date.now() + (expiry * 1000) : null;
        this.store.set(key, { value, expiry: expiryMs });
    }

    /**
   * Get a value by key
   * @param key Key
   * @returns Value or null if not found
   */
    async get(key: string): Promise<unknown> {
        const item: unknown = this.store.get(key);

        if (!item) {
            return null;
        }

        // Check if expired
        if (item.expiry && item.expiry < Date.now()) {
            this.store.delete(key);
            return null;
        }

        return item.value;
    }

    /**
   * Delete a key
   * @param key Key
   */
    async del(key: string): Promise<void> {
        this.store.delete(key);
    }

    /**
   * Check if a key exists
   * @param key Key

   * @returns True if key exists
   */
    async exists(key: string): Promise<boolean> {
        const item: unknown = this.store.get(key);

        if (!item) {
            return false;
        }

        // Check if expired
        if (item.expiry && item.expiry < Date.now()) {
            this.store.delete(key);
            return false;
        }

        return true;
    }

    /**
   * Increment a key
   * @param key Key
   * @returns New value
   */
    async incr(key: string): Promise<number> {
        const item: unknown = this.store.get(key);

        if (!item) {
            this.store.set(key, { value: 1, expiry: null });
            return 1;
        }

        // Check if expired
        if (item.expiry && item.expiry < Date.now()) {
            this.store.set(key, { value: 1, expiry: null });
            return 1;
        }

        const newValue: unknown =(parseInt(item.value) ?? 0) + 1;
        this.store.set(key, { value: newValue, expiry: item.expiry });

        return newValue;
    }

    /**
   * Set expiry on a key
   * @param key Key
   * @param expiry Expiry in seconds
   */
    async expire(key: string, expiry: number): Promise<void> {
        const item: unknown = this.store.get(key);

        if (item) {
            this.store.set(key, { value: item.value, expiry: Date.now() + (expiry * 1000) });
        }
    }

    /**
   * Get all keys matching a pattern
   * @param pattern Pattern
   * @returns Keys
   */
    async keys(pattern: string): Promise<string[]> {
        const keys: string[] = [];
        const regex: unknown = new RegExp(pattern.replace("*", ".*"));

        for (const key of this.store.keys()) {
            if (regex.test(key)) {
                const item: unknown = this.store.get(key);

                // Skip expired keys
                if (item && (!item.expiry || item.expiry >= Date.now())) {
                    keys.push(key);
                }
            }
        }

        return keys;
    }

    /**
   * Clear all keys
   */
    async flushall(): Promise<void> {
        this.store.clear();
    }

    /**
   * Get connection status
   * @returns True if connected
   */
    getIsConnected(): boolean {
        return this.isConnected;
    }

    /**
     * Clean up expired keys
     */
    private cleanup(): void {
        const now = Date.now();
        let expiredCount: number = 0;

        for (const [key, item] of this.store.entries()) {
            if (item.expiry && item.expiry < now) {
                this.store.delete(key);
                expiredCount++;
            }
        }

        if (expiredCount > 0) {
            logger.debug(`Memory store cleanup: removed ${expiredCount} expired keys`);
        }
    }
}

// Export singleton instance
export const memoryStore: unknown = new MemoryStore();

const memoryStoreExport: unknown =memoryStore;
export default memoryStoreExport;
