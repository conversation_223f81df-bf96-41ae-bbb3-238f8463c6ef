// jscpd:ignore-file
/**
 * Verification Policy
 * 
 * Implements a policy system for complex verification rules.
 */

import { VerificationRequest as ImportedVerificationRequest } from "../../../interfaces/verification/IVerificationStrategy";
import { logger as Importedlogger } from "../../../lib/logger";
import { Merchant as ImportedMerchant } from '../types';
import { logger as Importedlogger } from "../../../lib/logger";
import { Merchant as ImportedMerchant } from '../types';


/**
 * Verification policy condition
 */
export type VerificationPolicyCondition = (request: VerificationRequest) => boolean;

/**
 * Verification policy
 */
export class VerificationPolicy {
    private name: string;
    private requiredMethods: string[] = [];
    private conditions: VerificationPolicyCondition[] = [];
    private description: string = "";
  
    /**
   * Constructor
   * 
   * @param name Policy name
   */
    constructor(name: string = "") {
        this.name = name || `policy_${Date.now()}`;
    }
  
    /**
   * Set policy name
   * 
   * @param name Policy name
   * @returns This policy for chaining
   */
    public setName(name: string): VerificationPolicy {
        this.name = name;
        return this;
    }
  
    /**
   * Set policy description
   * 
   * @param description Policy description
   * @returns This policy for chaining
   */
    public setDescription(description: string): VerificationPolicy {
        this.description = description;
        return this;
    }
  
    /**
   * Require a verification method
   * 
   * @param method Verification method type
   * @returns This policy for chaining
   */
    public requireMethod(method: string): VerificationPolicy {
        this.requiredMethods.push(method);
        return this;
    }
  
    /**
   * Require multiple verification methods
   * 
   * @param methods Array of verification method types
   * @returns This policy for chaining
   */
    public requireMethods(methods: string[]): VerificationPolicy {
        this.requiredMethods.push(...methods);
        return this;
    }
  
    /**
   * Add a condition
   * 
   * @param condition Verification policy condition
   * @returns This policy for chaining
   */
    public addCondition(condition: VerificationPolicyCondition): VerificationPolicy {
        this.conditions.push(condition);
        return this;
    }
  
    /**
   * Add a condition that applies when the amount exceeds a threshold
   * 
   * @param amount Amount threshold
   * @returns This policy for chaining
   */
    public whenAmountExceeds(amount: number): VerificationPolicy {
        return this.addCondition(request => (request).amount > amount);
    }
  
    /**
   * Add a condition that applies when the amount is below a threshold
   * 
   * @param amount Amount threshold
   * @returns This policy for chaining
   */
    public whenAmountBelow(amount: number): VerificationPolicy {
        return this.addCondition(request => (request).amount < amount);
    }
  
    /**
   * Add a condition that applies for a specific currency
   * 
   * @param currency Currency code
   * @returns This policy for chaining
   */
    public forCurrency(currency: string): VerificationPolicy {
        return this.addCondition(request => (request).currency === currency);
    }
  
    /**
   * Add a condition that applies for a specific payment method type
   * 
   * @param paymentMethodType Payment method type
   * @returns This policy for chaining
   */
    public forPaymentMethod(paymentMethodType: string): VerificationPolicy {
        return this.addCondition(request => (request).paymentMethodType === paymentMethodType);
    }
  
    /**
   * Add a condition that applies for a specific merchant
   * 
   * @param merchantId Merchant ID
   * @returns This policy for chaining
   */
    public forMerchant(merchantId: string): VerificationPolicy {
        return this.addCondition(request => (request).merchantId === merchantId);
    }
  
    /**
   * Check if the policy applies to a verification request
   * 
   * @param request Verification request
   * @returns True if the policy applies
   */
    public appliesTo(request: VerificationRequest): boolean {
    // Check if all conditions are met
        return this.conditions.every(condition => condition(request));
    }
  
    /**
   * Get the required verification methods
   * 
   * @returns Array of required verification method types
   */
    public getRequiredMethods(): string[] {
        return [...this.requiredMethods];
    }
  
    /**
   * Get the policy name
   * 
   * @returns Policy name
   */
    public getName(): string {
        return this.name;
    }
  
    /**
   * Get the policy description
   * 
   * @returns Policy description
   */
    public getDescription(): string {
        return this.description;
    }
}
