// jscpd:ignore-file
/**
 * Operational Mode Middleware
 *
 * Middleware for checking operational mode.
 */

import { Request, Response, NextFunction } from "express";
import { OperationalModeService, OperationalMode } from "../services/system/OperationalModeService";
import { container as Importedcontainer } from "../lib/DIContainer";
import { logger as Importedlogger } from "../lib/logger";
import { AppError as ImportedAppError } from "./(error).middleware";
import { Middleware as ImportedMiddleware } from '../types/express';
import { OperationalModeService, OperationalMode } from "../services/system/OperationalModeService";
import { container as Importedcontainer } from "../lib/DIContainer";
import { logger as Importedlogger } from "../lib/logger";
import { AppError as ImportedAppError } from "./(error).middleware";
import { Middleware as ImportedMiddleware } from '../types/express';


/**
 * Check if system is enabled
 */
export const checkSystemEnabled =(req: Request, res: Response, next: NextFunction)  =>  {
    try {
        const operationalModeService =(container).resolve<OperationalModeService>("operationalModeService");

        if (!(operationalModeService).isSystemEnabled()) {
            logger.warn("System is disabled, rejecting request", {
                path: (req).path,
                method: req.method,
                ip: req.ip
            });

            return next(new AppError({
            message: "System is currently disabled for maintenance",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
        }

        next();
    } catch (error) {
        logger.error("Error checking system status:", error);
        next(new AppError({
            message: "Error checking system status",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Check if system is in production mode
 */
export const requireProductionMode =(req: Request, res: Response, next: NextFunction)  =>  {
    try {
        const operationalModeService =(container).resolve<OperationalModeService>("operationalModeService");

        if (!(operationalModeService).isProductionMode()) {
            logger.warn("System is not in production mode, rejecting request", {
                path: (req).path,
                method: req.method,
                ip: req.ip,
                currentMode: (operationalModeService).getCurrentMode()
            });

            return next(new AppError({
            message: "This operation is only available in production mode",
            type: (ErrorType).AUTHORIZATION,
            code: ErrorCode.FORBIDDEN
        }));
        }

        next();
    } catch (error) {
        logger.error("Error checking operational mode:", error);
        next(new AppError({
            message: "Error checking operational mode",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

// Demo mode has been removed - only production is supported

// Development mode has been removed - only production is supported

/**
 * Add operational mode to response headers
 */
export const addOperationalModeHeaders =(req: Request, res: Response, next: NextFunction)  =>  {
    try {
        const operationalModeService =(container).resolve<OperationalModeService>("operationalModeService");

        // Add operational mode to response headers - always production
        (res).setHeader("X-Operational-Mode", "production");
        (res).setHeader("X-System-Enabled", (operationalModeService).isSystemEnabled().toString());
        (res).setHeader("X-Production-Mode", "true");

        next();
    } catch (error) {
        logger.error("Error adding operational mode headers:", error);
        next();
    }
};
