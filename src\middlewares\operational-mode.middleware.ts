// jscpd:ignore-file
/**
 * Operational Mode Middleware
 *
 * Middleware for checking operational mode.
 */

import { Request, Response, NextFunction } from "express";
import { OperationalModeService, OperationalMode } from "../services/system/OperationalModeService";
import { container as Importedcontainer } from "../lib/DIContainer";
import { logger as Importedlogger } from "../lib/logger";
import { AppError as ImportedAppError } from "./(error as any).middleware";
import { Middleware as ImportedMiddleware } from '../types/express';
import { OperationalModeService, OperationalMode } from "../services/system/OperationalModeService";
import { container as Importedcontainer } from "../lib/DIContainer";
import { logger as Importedlogger } from "../lib/logger";
import { AppError as ImportedAppError } from "./(error as any).middleware";
import { Middleware as ImportedMiddleware } from '../types/express';


/**
 * Check if system is enabled
 */
export const checkSystemEnabled: any =(req: Request, res: Response, next: NextFunction) => {
    try {
        const operationalModeService: any =(container as any).resolve<OperationalModeService>("operationalModeService");

        if (!(operationalModeService as any).isSystemEnabled()) {
            (logger as any).warn("System is disabled, rejecting request", {
                path: (req as any).path,
                method: req.method,
                ip: req.ip
            });

            return next(new AppError({
            message: "System is currently disabled for maintenance",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
        }

        next();
    } catch(error) {
        (logger as any).error("Error checking system status:", error);
        next(new AppError({
            message: "Error checking system status",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Check if system is in production mode
 */
export const requireProductionMode: any =(req: Request, res: Response, next: NextFunction) => {
    try {
        const operationalModeService: any =(container as any).resolve<OperationalModeService>("operationalModeService");

        if (!(operationalModeService as any).isProductionMode()) {
            (logger as any).warn("System is not in production mode, rejecting request", {
                path: (req as any).path,
                method: req.method,
                ip: req.ip,
                currentMode: (operationalModeService as any).getCurrentMode()
            });

            return next(new AppError({
            message: "This operation is only available in production mode",
            type: (ErrorType as any).AUTHORIZATION,
            code: ErrorCode.FORBIDDEN
        }));
        }

        next();
    } catch(error) {
        (logger as any).error("Error checking operational mode:", error);
        next(new AppError({
            message: "Error checking operational mode",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

// Demo mode has been removed - only production is supported

// Development mode has been removed - only production is supported

/**
 * Add operational mode to response headers
 */
export const addOperationalModeHeaders: any =(req: Request, res: Response, next: NextFunction) => {
    try {
        const operationalModeService: any =(container as any).resolve<OperationalModeService>("operationalModeService");

        // Add operational mode to response headers - always production
        (res as any).setHeader("X-Operational-Mode", "production");
        (res as any).setHeader("X-System-Enabled", (operationalModeService as any).isSystemEnabled().toString());
        (res as any).setHeader("X-Production-Mode", "true");

        next();
    } catch(error) {
        (logger as any).error("Error adding operational mode headers:", error);
        next();
    }
};
