// jscpd:ignore-file
/**
 * Notification Post-Processor
 *
 * Post-processor that sends notifications for verification results.
 */

import {
  VerificationRequest,
  VerificationResult,
} from '../../../interfaces/verification/IVerificationStrategy';
import { VerificationPostProcessor } from './VerificationPostProcessor';
import { logger } from '../../../lib/logger';
import { PrismaClient } from '@prisma/client';
import { VerificationResult, Merchant } from '../types';
import { VerificationPostProcessor } from './VerificationPostProcessor';
import { logger } from '../../../lib/logger';
import { PrismaClient } from '@prisma/client';
import { VerificationResult, Merchant } from '../types';

/**
 * Notification post-processor
 */
export class NotificationPostProcessor implements VerificationPostProcessor {
  private prisma: PrismaClient;

  /**
   * Constructor
   */
  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Get the name of the post-processor
   */
  public getName(): string {
    return 'notification_post_processor';
  }

  /**
   * Process a verification result
   */
  public async process(
    result: VerificationResult,
    request: VerificationRequest
  ): Promise<VerificationResult> {
    try {
      logger.info(`Processing verification result for transaction: ${result.transactionId}`, {
        success: result.success,
        verificationId: result.verificationId,
      });

      // Get the merchant
      const merchant: unknown = await this.prisma.merchant.findUnique({
        where: { id: request.merchantId },
        include: { user: true },
      });

      if (!merchant) {
        logger.warn(`Merchant not found for verification notification: ${request.merchantId}`);
        return result;
      }

      // Create a notification
      await this.prisma.notification.create({
        data: {
          userId: merchant.id,
          title: result.success ? 'Payment Verified' : 'Payment Verification Failed',
          message: result.success
            ? `Payment of ${request.amount} ${request.currency} has been verified successfully.`
            : `Payment verification failed: ${(result as Error).message}`,
          type: result.success ? 'success' : 'error',
          read: false,
          metadata: {
            transactionId: result.transactionId,
            verificationId: result.verificationId,
            amount: request.amount,
            currency: request.currency,
            paymentMethodType: request.paymentMethodType,
          },
        },
      });

      logger.info(`Notification created for merchant: ${merchant.id}`);

      // Return the result unchanged
      return result;
    } catch (error) {
      logger.error(`Error in notification post-processor: ${(error as Error).message}`, {
        transactionId: result.transactionId,
        error,
      });

      // Return the result unchanged even if there's an error
      return result;
    }
  }
}
