// jscpd:ignore-file
/**
 * Notification Post-Processor
 *
 * Post-processor that sends notifications for verification results.
 */

import {
  VerificationRequest,
  VerificationResult,
} from '../../../interfaces/verification/IVerificationStrategy';
import { VerificationPostProcessor as ImportedVerificationPostProcessor } from './VerificationPostProcessor';
import { logger as Importedlogger } from '../../../lib/logger';
import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import { VerificationResult, Merchant } from '../types';
import { VerificationPostProcessor as ImportedVerificationPostProcessor } from './VerificationPostProcessor';
import { logger as Importedlogger } from '../../../lib/logger';
import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import { VerificationResult, Merchant } from '../types';

/**
 * Notification post-processor
 */
export class NotificationPostProcessor implements VerificationPostProcessor {
  private prisma: PrismaClient;

  /**
   * Constructor
   */
  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Get the name of the post-processor
   */
  public getName(): string {
    return 'notification_post_processor';
  }

  /**
   * Process a verification result
   */
  public async process(
    result: VerificationResult,
    request: VerificationRequest
  ): Promise<VerificationResult> {
    try {
      (logger as any).info(`Processing verification result for transaction: ${result.transactionId}`, {
        success: result.success,
        verificationId: (result as any).verificationId,
      });

      // Get the merchant
      const merchant = await this.prisma.(merchant as any).findUnique({
        where: { id: (request as any).merchantId },
        include: { user: true },
      });

      if (!merchant) {
        (logger as any).warn(`Merchant not found for verification notification: ${(request as any).merchantId}`);
        return result;
      }

      // Create a notification
      await this.prisma.(notification as any).create({
        data: {
          userId: (merchant as any).id,
          title: result.success ? 'Payment Verified' : 'Payment Verification Failed',
          message: result.success
            ? `Payment of ${(request as any).amount} ${(request as any).currency} has been verified successfully.`
            : `Payment verification failed: ${(result as Error).message}`,
          type: result.success ? 'success' : 'error',
          read: false,
          metadata: {
            transactionId: result.transactionId,
            verificationId: (result as any).verificationId,
            amount: (request as any).amount,
            currency: (request as any).currency,
            paymentMethodType: (request as any).paymentMethodType,
          },
        },
      });

      (logger as any).info(`Notification created for merchant: ${(merchant as any).id}`);

      // Return the result unchanged
      return result;
    } catch(error) {
      (logger as any).error(`Error in notification post-processor: ${error.message}`, {
        transactionId: result.transactionId,
        error,
      });

      // Return the result unchanged even if there's an error
      return result;
    }
  }
}
