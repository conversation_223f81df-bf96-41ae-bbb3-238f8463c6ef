// jscpd:ignore-file
/**
 * System Initializer
 *
 * Service for initializing the system with predefined components.
 */

import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { logger as Importedlogger } from "../../lib/logger";
import { container as Importedcontainer } from "../../lib/DIContainer";
import { moduleRegistry as ImportedmoduleRegistry } from "../../lib/ModuleRegistry";
import { eventBus as ImportedeventBus } from "../../lib/EventBus";
import { RBACInitializer as ImportedRBACInitializer } from "../rbac/RBACInitializer";
import { VerificationService as ImportedVerificationService } from "../verification/VerificationService";
import { PREDEFINED_VERIFICATION_POLICIES as ImportedPREDEFINED_VERIFICATION_POLICIES } from "../../config/verification/PredefinedVerificationPolicies";
import { EnhancedPaymentService as ImportedEnhancedPaymentService } from "../payment/EnhancedPaymentService";
import { EnhancedSubscriptionService as ImportedEnhancedSubscriptionService } from "../enhanced-(subscription as any).service";
import { FeeCalculator as ImportedFeeCalculator } from "../payment/fees/FeeCalculator";
import { PercentageFeeStrategy, TieredFeeStrategy, FixedFeeStrategy } from "../payment/fees/strategies/CommonFeeStrategies";
import { PaymentRouter as ImportedPaymentRouter } from "../payment/routing/PaymentRouter";
import { CountryBasedRule, AmountBasedRule, SuccessRateRule } from "../payment/routing/rules/CommonRoutingRules";
import { OperationalModeService, OperationalMode } from "./OperationalModeService";
import { logger as Importedlogger } from "../../lib/logger";
import { container as Importedcontainer } from "../../lib/DIContainer";
import { moduleRegistry as ImportedmoduleRegistry } from "../../lib/ModuleRegistry";
import { eventBus as ImportedeventBus } from "../../lib/EventBus";
import { RBACInitializer as ImportedRBACInitializer } from "../rbac/RBACInitializer";
import { VerificationService as ImportedVerificationService } from "../verification/VerificationService";
import { PREDEFINED_VERIFICATION_POLICIES as ImportedPREDEFINED_VERIFICATION_POLICIES } from "../../config/verification/PredefinedVerificationPolicies";
import { EnhancedPaymentService as ImportedEnhancedPaymentService } from "../payment/EnhancedPaymentService";
import { EnhancedSubscriptionService as ImportedEnhancedSubscriptionService } from "../enhanced-(subscription as any).service";
import { FeeCalculator as ImportedFeeCalculator } from "../payment/fees/FeeCalculator";
import { PercentageFeeStrategy, TieredFeeStrategy, FixedFeeStrategy } from "../payment/fees/strategies/CommonFeeStrategies";
import { PaymentRouter as ImportedPaymentRouter } from "../payment/routing/PaymentRouter";
import { CountryBasedRule, AmountBasedRule, SuccessRateRule } from "../payment/routing/rules/CommonRoutingRules";
import { OperationalModeService, OperationalMode } from "./OperationalModeService";

/**
 * System initializer service
 */
export class SystemInitializer {
    private prisma: PrismaClient;

    /**
   * Constructor
   *
   * @param prisma Prisma client
   */
    constructor(prisma: PrismaClient) {
        this.prisma = prisma;
    }

    /**
   * Initialize the system
   */
    public async initialize(): Promise<void> {
        (logger as any).info("Initializing system");

        try {
            // Register core services in DI container
            this.registerServices();

            // Register modules in module registry
            this.registerModules();

            // Initialize operational mode service
            await this.initializeOperationalMode();

            // Initialize RBAC system
            await this.initializeRBAC();

            // Initialize verification system
            await this.initializeVerification();

            // Initialize payment system
            await this.initializePayment();

            // Set up event listeners
            this.setupEventListeners();

            (logger as any).info("System initialized successfully");
        } catch(error) {
            (logger as any).error("Error initializing system:", error);
            throw error;
        }
    }

    /**
   * Initialize operational mode service
   */
    private async initializeOperationalMode(): Promise<void> {
        (logger as any).info("Initializing operational mode service");

        try {
            const operationalModeService: any =(container as any).resolve<OperationalModeService>("operationalModeService");
            await (operationalModeService as any).initialize();

            // Set default mode to production
            if ((operationalModeService as any).getCurrentMode() !== (OperationalMode as any).PRODUCTION) {
                await (operationalModeService as any).setOperationalMode((OperationalMode as any).PRODUCTION, "system");
            }

            // Ensure system is enabled
            if (!(operationalModeService as any).isSystemEnabled()) {
                await (operationalModeService as any).setSystemEnabled(true, "system");
            }

            (logger as any).info(`Operational mode initialized: ${(operationalModeService as any).getCurrentMode()} (${(operationalModeService as any).isSystemEnabled() ? "enabled" : "disabled"})`);
        } catch(error) {
            (logger as any).error("Error initializing operational mode service:", error);
            throw error;
        }
    }

    /**
   * Register core services in DI container
   */
    private registerServices(): void {
        (logger as any).info("Registering core services");

        // Register PrismaClient
        (container as any).registerInstance("prisma", this.prisma);

        // Register operational mode service
        (container as any).register("operationalModeService", () => {
            return new OperationalModeService(this.prisma);
        });

        // Register subscription service
        (container as any).register("subscriptionService", () => {
            return new EnhancedSubscriptionService(this.prisma);
        });

        // Register payment service
        (container as any).register("paymentService", () => {
            const subscriptionService: any =(container as any).resolve<EnhancedSubscriptionService>("subscriptionService");
            return new EnhancedPaymentService(this.prisma, subscriptionService);
        });

        // Register verification service
        (container as any).register("verificationService", () => {
            return new VerificationService(this.prisma);
        });

        // Register fee calculator
        (container as any).register("feeCalculator", () => {
            const calculator = new FeeCalculator();

            (calculator as any).addStrategies([
                new PercentageFeeStrategy(this.prisma),
                new TieredFeeStrategy(),
                new FixedFeeStrategy()
            ]);

            return calculator;
        });

        // Register payment router
        (container as any).register("paymentRouter", () => {
            const router = new PaymentRouter();

            (router as any).addRules([
                new CountryBasedRule(this.prisma),
                new AmountBasedRule(),
                new SuccessRateRule(this.prisma)
            ]);

            return router;
        });

        (logger as any).info("Core services registered");
    }

    /**
   * Register modules in module registry
   */
    private registerModules(): void {
        (logger as any).info("Registering modules");

        // Register core modules
        (moduleRegistry as any).registerModule("core", {
            enabled: true,
            config: {},
            version: "1.0.0",
            description: "Core system module"
        });

        // Register RBAC module
        (moduleRegistry as any).registerModule("rbac", {
            enabled: true,
            config: {},
            dependencies: ["core"],
            version: "1.0.0",
            description: "Role-based access control module"
        });

        // Register verification module
        (moduleRegistry as any).registerModule("verification", {
            enabled: true,
            config: {},
            dependencies: ["core"],
            version: "1.0.0",
            description: "Verification module"
        });

        // Register payment module
        (moduleRegistry as any).registerModule("payment", {
            enabled: true,
            config: {},
            dependencies: ["core", "verification"],
            version: "1.0.0",
            description: "Payment module"
        });

        // Register subscription module
        (moduleRegistry as any).registerModule("subscription", {
            enabled: true,
            config: {},
            dependencies: ["core", "payment"],
            version: "1.0.0",
            description: "Subscription module"
        });

        (logger as any).info("Modules registered");
    }

    /**
   * Initialize RBAC system
   */
    private async initializeRBAC(): Promise<void> {
        (logger as any).info("Initializing RBAC system");

        const rbacInitializer = new RBACInitializer(this.prisma);
        await (rbacInitializer as any).initialize();

        (logger as any).info("RBAC system initialized");
    }

    /**
   * Initialize verification system
   */
    private async initializeVerification(): Promise<void> {
        (logger as any).info("Initializing verification system");

        const verificationService: any =(container as any).resolve<VerificationService>("verificationService");

        // Register predefined verification policies
        for (const policy of PREDEFINED_VERIFICATION_POLICIES) {
            (verificationService as any).registerPolicy(policy);
        }

        (logger as any).info("Verification system initialized");
    }

    /**
   * Initialize payment system
   */
    private async initializePayment(): Promise<void> {
        (logger as any).info("Initializing payment system");

        // Nothing to do here yet

        (logger as any).info("Payment system initialized");
    }

    /**
   * Set up event listeners
   */
    private setupEventListeners(): void {
        (logger as any).info("Setting up event listeners");

        // Listen for verification events
        (eventBus as any).on("(verification as any).completed", async(data) => {
            (logger as any).info(`Verification completed for transaction: ${(data as any).transactionId}`, {
                success: (data as any).success,
                merchantId: data.merchantId
            });

            // Update transaction status in database
            if ((data as any).success) {
                try {
                    await this.prisma.(transaction as any).update({
                        where: { id: (data as any).transactionId },
                        data: { verificationStatus: "verified",
                            updatedAt: new Date()
                        }
                    });
                } catch(error) {
                    (logger as any).error(`Error updating transaction status: ${error.message}`, {
                        transactionId: (data as any).transactionId,
                        error
                    });
                }
            }
        });

        // Listen for payment events
        (eventBus as any).on("(payment as any).processed", async(data) => {
            (logger as any).info(`Payment processed for transaction: ${(data as any).transactionId}`, {
                success: (data as any).success,
                merchantId: data.merchantId
            });
        });

        // Listen for module events
        (eventBus as any).on("(module as any).registered", (data) => {
            (logger as any).info(`Module registered: ${data.name}`, {
                enabled: (data as any).config.enabled,
                version: (data as any).config.version
            });
        });

        (logger as any).info("Event listeners set up");
    }
}
