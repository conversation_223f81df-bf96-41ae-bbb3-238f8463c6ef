// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from "./base/BaseController";
import { PaymentVerificationService } from "../services/verification/payment-verification.service";
import { PaymentMethod } from "../types/payment";
import { BaseController } from "./base/BaseController";
import { PaymentVerificationService } from "../services/verification/payment-verification.service";
import { PaymentMethod } from "../types/payment";

export class PaymentVerificationController extends BaseController {
    private paymentVerificationService: PaymentVerificationService;

    constructor() {
        super();
        this.paymentVerificationService = new PaymentVerificationService();
    }

    /**
     * Send success response
     * @param res Response
     * @param data Response data
     * @param statusCode Status code
     * @returns Response
     */
    private sendSuccess(res: Response, data: unknown, statusCode: number = 200): Response {
        return res.status(statusCode).json({
            success: true,
            data
        });
    }

    /**
     * Verify a payment
     */
    verifyPayment = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const {
                method,
                transactionId,
                amount,
                currency,
                recipientAddress,
                merchantApiKey,
                merchantSecretKey
            } = req.body;

            // Validate required fields
            if (!method || !transactionId || !amount || !currency) {
                return this.sendValidationError(res, {
                    method: !method ? ["Payment method is required"] : [],
                    transactionId: !transactionId ? ["Transaction ID is required"] : [],
                    amount: !amount ? ["Amount is required"] : [],
                    currency: !currency ? ["Currency is required"] : []
                });
            }

            // Validate payment method
            if (!Object.values(PaymentMethod).includes(method as PaymentMethod)) {
                return this.sendValidationError(res, {
                    method: [`Invalid payment method: ${method}. Valid methods are: ${Object.values(PaymentMethod).join(", ")}`]
                });
            }

            // Check if recipient address is required
            if ((method === PaymentMethod.BINANCE_TRC20 || method === PaymentMethod.CRYPTO_TRANSFER) && !recipientAddress) {
                return this.sendValidationError(res, {
                    recipientAddress: ["Recipient address is required for this payment method"]
                });
            }

            // Check if merchant API keys are required
            if ((method === PaymentMethod.BINANCE_PAY || method === PaymentMethod.BINANCE_C2C || method === PaymentMethod.BINANCE_TRC20) && (!merchantApiKey || !merchantSecretKey)) {
                return this.sendValidationError(res, {
                    merchantApiKey: !merchantApiKey ? ["Merchant API key is required for this payment method"] : [],
                    merchantSecretKey: !merchantSecretKey ? ["Merchant secret key is required for this payment method"] : []
                });
            }

            // Verify payment
            const result: unknown = await this.paymentVerificationService.verifyPayment(
                method as PaymentMethod,
                transactionId,
                amount,
                currency,
                recipientAddress,
                merchantApiKey,
                merchantSecretKey
            );

            // Send success response
            return this.sendSuccess(res, result, 200);
        } catch (error) {
            return this.sendError(res, error);
        }
    });

    /**
     * Verify a Binance Pay payment
     */
    verifyBinancePayPayment = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const {
                transactionId,
                amount,
                currency,
                merchantApiKey,
                merchantSecretKey
            } = req.body;

            // Validate required fields
            if (!transactionId || !amount || !currency || !merchantApiKey || !merchantSecretKey) {
                return this.sendValidationError(res, {
                    transactionId: !transactionId ? ["Transaction ID is required"] : [],
                    amount: !amount ? ["Amount is required"] : [],
                    currency: !currency ? ["Currency is required"] : [],
                    merchantApiKey: !merchantApiKey ? ["Merchant API key is required"] : [],
                    merchantSecretKey: !merchantSecretKey ? ["Merchant secret key is required"] : []
                });
            }

            // Verify payment
            const result: unknown = await this.paymentVerificationService.verifyPayment(
                PaymentMethod.BINANCE_PAY,
                transactionId,
                amount,
                currency,
                "",
                merchantApiKey,
                merchantSecretKey
            );

            // Send success response
            return this.sendSuccess(res, result, 200);
        } catch (error) {
            return this.sendError(res, error);
        }
    });

    /**
     * Verify a Binance C2C payment
     */
    verifyBinanceC2CPayment = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const {
                note,
                amount,
                currency,
                merchantApiKey,
                merchantSecretKey
            } = req.body;

            // Validate required fields
            if (!note || !amount || !currency || !merchantApiKey || !merchantSecretKey) {
                return this.sendValidationError(res, {
                    note: !note ? ["Note is required"] : [],
                    amount: !amount ? ["Amount is required"] : [],
                    currency: !currency ? ["Currency is required"] : [],
                    merchantApiKey: !merchantApiKey ? ["Merchant API key is required"] : [],
                    merchantSecretKey: !merchantSecretKey ? ["Merchant secret key is required"] : []
                });
            }

            // Verify payment
            const result: unknown = await this.paymentVerificationService.verifyPayment(
                PaymentMethod.BINANCE_C2C,
                note,
                amount,
                currency,
                "",
                merchantApiKey,
                merchantSecretKey
            );

            // Send success response
            return this.sendSuccess(res, result, 200);
        } catch (error) {
            return this.sendError(res, error);
        }
    });

    /**
     * Verify a Binance TRC20 payment
     */
    verifyBinanceTRC20Payment = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const {
                txHash,
                amount,
                currency,
                recipientAddress,
                merchantApiKey,
                merchantSecretKey
            } = req.body;

            // Validate required fields
            if (!txHash || !amount || !currency || !recipientAddress || !merchantApiKey || !merchantSecretKey) {
                return this.sendValidationError(res, {
                    txHash: !txHash ? ["Transaction hash is required"] : [],
                    amount: !amount ? ["Amount is required"] : [],
                    currency: !currency ? ["Currency is required"] : [],
                    recipientAddress: !recipientAddress ? ["Recipient address is required"] : [],
                    merchantApiKey: !merchantApiKey ? ["Merchant API key is required"] : [],
                    merchantSecretKey: !merchantSecretKey ? ["Merchant secret key is required"] : []
                });
            }

            // Verify payment
            const result: unknown = await this.paymentVerificationService.verifyPayment(
                PaymentMethod.BINANCE_TRC20,
                txHash,
                amount,
                currency,
                recipientAddress,
                merchantApiKey,
                merchantSecretKey
            );

            // Send success response
            return this.sendSuccess(res, result, 200);
        } catch (error) {
            return this.sendError(res, error);
        }
    });

    /**
     * Verify a crypto transfer payment
     */
    verifyCryptoTransferPayment = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const {
                txHash,
                amount,
                currency,
                recipientAddress
            } = req.body;

            // Validate required fields
            if (!txHash || !amount || !currency || !recipientAddress) {
                return this.sendValidationError(res, {
                    txHash: !txHash ? ["Transaction hash is required"] : [],
                    amount: !amount ? ["Amount is required"] : [],
                    currency: !currency ? ["Currency is required"] : [],
                    recipientAddress: !recipientAddress ? ["Recipient address is required"] : []
                });
            }

            // Verify payment
            const result: unknown = await this.paymentVerificationService.verifyPayment(
                PaymentMethod.CRYPTO_TRANSFER,
                txHash,
                amount,
                currency,
                recipientAddress
            );

            // Send success response
            return this.sendSuccess(res, result, 200);
        } catch (error) {
            return this.sendError(res, error);
        }
    });
}
