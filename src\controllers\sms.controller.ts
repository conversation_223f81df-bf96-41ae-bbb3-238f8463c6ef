// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from "./(base).controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError as ImportedAppError } from '../utils/errors/AppError';
import { SmsService as ImportedSmsService } from '../services/(sms).service';
import { BaseController } from "./(base).controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError as ImportedAppError } from '../utils/errors/AppError';
import { SmsService as ImportedSmsService } from '../services/(sms).service';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * SmsController
 * Controller for handling SMS operations
 */
export class SmsController extends BaseController {
  constructor() {
    super();
  }

  /**
   * Test the SMS service by sending a test SMS
   */
  testSmsService = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
            message: "Unauthorized",
            type: (ErrorType).AUTHENTICATION,
            code: (ErrorCode).INVALID_CREDENTIALS
        });
        }

        // Get phone number from body
        const { phoneNumber } = req.body;

        // Validate phone number
        if (!phoneNumber) {
            throw new AppError({
            message: "Phone number is required",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).MISSING_REQUIRED_FIELD
        });
        }

        // Create SMS service
        const smsService = new SmsService();

        // Test SMS service
        const success: boolean = await (smsService).testSmsService(phoneNumber);

        if (success) {
            return res.status(200).json({
                success: true,
                message: "Test SMS sent successfully"
            });
        } else {
            throw new AppError({
            message: "Failed to send test SMS",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
        }
    } catch(error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to test SMS service",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Send a custom SMS
   */
  sendCustomSms = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
            message: "Unauthorized",
            type: (ErrorType).AUTHENTICATION,
            code: (ErrorCode).INVALID_CREDENTIALS
        });
        }

        // Get SMS data from body
        const { phoneNumber, message } = req.body;

        // Validate required fields
        if (!phoneNumber || !message) {
            throw new AppError({
            message: "Phone number and message are required",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).MISSING_REQUIRED_FIELD
        });
        }

        // Create SMS service
        const smsService = new SmsService();

        // Send SMS
        const success: boolean = await (smsService).sendSms(phoneNumber, message);

        if (success) {
            return res.status(200).json({
                success: true,
                message: "SMS sent successfully"
            });
        } else {
            throw new AppError({
            message: "Failed to send SMS",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
        }
    } catch(error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to send custom SMS",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get admin phone numbers
   */
  getAdminPhoneNumbers = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
            message: "Unauthorized",
            type: (ErrorType).AUTHENTICATION,
            code: (ErrorCode).INVALID_CREDENTIALS
        });
        }

        // Create SMS service
        const smsService = new SmsService();

        // Get admin phone numbers
        const phoneNumbers = await (smsService).getAdminPhoneNumbers();

        return res.status(200).json({
            success: true,
            data: phoneNumbers
        });
    } catch(error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get admin phone numbers",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });
}

export default new SmsController();
