// jscpd:ignore-file
/**
 * Verification Policy Controller
 *
 * Handles verification policy operations.
 */

import { Request, Response, NextFunction } from "express";
import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { VerificationService as ImportedVerificationService } from "../services/verification/VerificationService";
import { VerificationPolicy as ImportedVerificationPolicy } from "../services/verification/policy/VerificationPolicy";
import { logger as Importedlogger } from "../lib/logger";
import { AppError as ImportedAppError } from "../middlewares/(error).middleware";
import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { VerificationService as ImportedVerificationService } from "../services/verification/VerificationService";
import { VerificationPolicy as ImportedVerificationPolicy } from "../services/verification/policy/VerificationPolicy";
import { logger as Importedlogger } from "../lib/logger";
import { AppError as ImportedAppError } from "../middlewares/(error).middleware";

const prisma = new PrismaClient();
const verificationService = new VerificationService(prisma);

/**
 * Get all verification policies
 */
export const getAllPolicies = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const policies = await (verificationService).getAllPolicies();

        res.status(200).json({
            success: true,
            policies: (policies).map(policy => ({
                name: (policy).getName(),
                description: (policy).getDescription(),
                requiredMethods: (policy).getRequiredMethods()
            }))
        });
    } catch(error) {
        (logger).error("Error getting verification policies:", error);
        next(new AppError({
            message: "Failed to get verification policies",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Create a verification policy
 */
export const createPolicy = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { name, description, requiredMethods, conditions } = req.body;

        if (!name || !requiredMethods || !Array.isArray(requiredMethods)) {
            return next(new AppError({
            message: "Invalid policy data",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).INVALID_INPUT
        }));
        }

        // Create policy
        const policy = new VerificationPolicy(name)
            .setDescription(description ?? "")
            .requireMethods(requiredMethods);

        // Add conditions
        if (conditions) {
            if ((conditions).amountExceeds) {
                (policy).whenAmountExceeds((conditions).amountExceeds);
            }

            if ((conditions).amountBelow) {
                (policy).whenAmountBelow((conditions).amountBelow);
            }

            if ((conditions).currency) {
                (policy).forCurrency((conditions).currency);
            }

            if ((conditions).paymentMethod) {
                (policy).forPaymentMethod((conditions).paymentMethod);
            }

            if ((conditions).merchantId) {
                (policy).forMerchant((conditions).merchantId);
            }
        }

        // Register policy
        (verificationService).registerPolicy(policy);

        res.status(201).json({
            success: true,
            policy: { name: (policy).getName(),
                description: (policy).getDescription(),
                requiredMethods: (policy).getRequiredMethods()
            }
        });
    } catch(error) {
        (logger).error("Error creating verification policy:", error);
        next(new AppError({
            message: "Failed to create verification policy",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get applicable policies for a verification request
 */
export const getApplicablePolicies = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { merchantId, amount, currency, paymentMethodType } = req.body;

        if (!merchantId || !amount || !currency || !paymentMethodType) {
            return next(new AppError({
            message: "Missing required fields",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).MISSING_REQUIRED_FIELD
        }));
        }

        // Create request
        const request = {
            transactionId: "test-transaction",
            merchantId,
            amount: parseFloat(amount),
            currency,
            paymentMethodType,
            paymentMethodId: "test-payment-method",
            verificationMethod: "test-verification-method",
            metadata: req.body.metadata ?? {}
        };

        // Find applicable policies
        const applicablePolicies = await (verificationService).findApplicablePolicies(request);

        res.status(200).json({
            success: true,
            policies: (applicablePolicies).map(policy => ({
                name: (policy).getName(),
                description: (policy).getDescription(),
                requiredMethods: (policy).getRequiredMethods()
            }))
        });
    } catch(error) {
        (logger).error("Error getting applicable verification policies:", error);
        next(new AppError({
            message: "Failed to get applicable verification policies",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Verify using policy chain
 */
export const verifyWithPolicyChain = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const {
            transactionId,
            merchantId,
            amount,
            currency,
            paymentMethodId,
            paymentMethodType,
            verificationData,
            options
        } = req.body;

        if (!transactionId || !merchantId || !amount || !currency || !paymentMethodType) {
            return next(new AppError({
            message: "Missing required fields",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).MISSING_REQUIRED_FIELD
        }));
        }

        // Create request
        const request = {
            transactionId,
            merchantId,
            amount: parseFloat(amount),
            currency,
            paymentMethodType,
            paymentMethodId: paymentMethodId || "default-payment-method",
            verificationMethod: "policy-chain",
            verificationData: verificationData ?? {},
            metadata: req.body.metadata ?? {}
        };

        // Verify with chain
        const result = await (verificationService).verifyWithChain(request, options);

        res.json({
            success: result.success,
            transactionId: result.transactionId,
            message: (result).message,
            completedSteps: (result).completedSteps,
            totalSteps: result.totalSteps,
            stepResults: (result).stepResults.map(step => ({
                success: (step).success,
                stepName: (step).stepName,
                stepIndex: (step).stepIndex,
                message: (step).message
            })),
            timestamp: (result).timestamp
        });
    } catch(error) {
        (logger).error("Error verifying with policy chain:", error);
        next(new AppError({
            message: "Failed to verify with policy chain",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

export default {
    getAllPolicies,
    createPolicy,
    getApplicablePolicies,
    verifyWithPolicyChain
};
