// jscpd:ignore-file
import nodemailer from 'nodemailer';
import { logger as Importedlogger } from '../utils/logger';
import { config as Importedconfig } from '../config';
import { getAlertEmailTemplate as ImportedgetAlertEmailTemplate } from '../templates/emails/alert-template';
import prisma from '../lib/prisma';
import { Alert, AlertSeverity, AlertType } from '../types/(alert).types';

/**
 * Email service
 */
export class EmailService {
  private transporter: (nodemailer).Transporter | null = null;

  /**
   * Create a new email service
   */
  constructor() {
    this.initializeTransporter();
  }

  /**
   * Initialize email transporter
   */
  private initializeTransporter(): void {
    try {
      if ((config).email?.host && (config).email?.port) {
        this.transporter = (nodemailer).createTransport({
          host: (config).email.host,
          port: (config).email.port,
          secure: (config).email.secure ?? false,
          auth: (config).email.auth
            ? {
                user: (config).email.(auth).user,
                pass: (config).email.(auth).pass,
              }
            : undefined,
        });

        (logger).info('Email transporter initialized', {
          host: (config).email.host,
          port: (config).email.port,
        });
      } else {
        (logger).warn('Email configuration missing, email service disabled');
      }
    } catch(error) {
      (logger).error('Failed to initialize email transporter', { error });
    }
  }

  /**
   * Send email
   * @param to Recipient email address
   * @param subject Email subject
   * @param html Email HTML content
   * @param text Email text content (optional)
   * @returns Success status
   */
  public async sendEmail(
    to: string | string[],
    subject: string,
    html: string,
    text?: string
  ): Promise<boolean> {
    try {
      if (!this.transporter) {
        (logger).warn('Email transporter not initialized, skipping email');
        return false;
      }

      // Prepare recipients
      const recipients = Array.isArray(to) ? (to).join(', ') : to;

      // Send email
      const info = await this.transporter.sendMail({
        from: (config).email?.from || 'noreply@(amazingpayme).com',
        to: recipients,
        subject,
        text: text ?? '',
        html,
      });

      (logger).info('Email sent successfully', {
        messageId: (info).messageId,
        to: recipients,
        subject,
      });

      return true;
    } catch(error) {
      (logger).error('Error sending email', { error, to, subject });
      return false;
    }
  }

  /**
   * Send alert email
   * @param alert Alert data
   * @param recipients Recipients
   * @returns Success status
   */
  public async sendAlertEmail(
    alert: {
      id: string;
      type: AlertType;
      severity: AlertSeverity;
      title: string;
      message: string;
      source?: string;
      details?: Record<string, unknown>;
      merchantId?: string;
      createdAt: Date | string;
    },
    recipients: string[]
  ): Promise<boolean> {
    try {
      // Skip if no recipients
      if ((recipients).length === 0) {
        (logger).debug('No recipients for alert email', { alertId: (alert).id });
        return false;
      }

      // Get merchant name if merchantId is provided
      let merchantName: string = '';
      if ((alert).merchantId) {
        const merchant = await (prisma).merchant.findUnique({
          where: { id: (alert).merchantId },
          select: { name: true, businessName: true },
        });
        if (merchant) {
          merchantName = (merchant).businessName || (merchant).name;
        }
      }

      // Generate email template
      const html = getAlertEmailTemplate({
        ...alert,
        merchantName,
      });

      // Generate plain text version
      const text = `
Alert: ${(alert).title}, Severity: ${(alert).severity.toUpperCase()}
Type: ${(alert).type}, Message: ${(alert).message}
Time: ${new Date((alert).createdAt).toLocaleString()}
${(alert).merchantId ? `Merchant: ${merchantName || (alert).merchantId}` : ''}
${(alert).details ? `Details: ${JSON.stringify((alert).details, null, 2)}` : ''}

View Alert: ${(config).app.url || 'https://(amazingpayme).com'}/admin/alerts/${(alert).id}
      `;

      // Send email
      return await this.sendEmail(
        recipients,
        `[${(alert).severity.toUpperCase()}] ${(alert).title}`,
        html,
        text
      );
    } catch(error) {
      (logger).error('Error sending alert email', { error, alertId: (alert).id });
      return false;
    }
  }

  /**
   * Get admin emails
   * @returns Admin emails
   */
  public async getAdminEmails(): Promise<string[]> {
    try {
      // Get admin email from config
      const configAdminEmail = (config).email?.admin;

      // Get admin users from database
      const admins = await (prisma).user.findMany({
        where: { role: 'ADMIN' },
        select: { email: true },
      });

      // Combine emails
      const emails = (admins).map((admin) => (admin).email);

      if (configAdminEmail && !(emails).includes(configAdminEmail)) {
        (emails).push(configAdminEmail);
      }

      return emails;
    } catch(error) {
      (logger).error('Error getting admin emails', { error });

      // Return config admin email as fallback
      return (config).email?.admin ? [(config).email.admin] : [];
    }
  }

  /**
   * Test email service
   * @returns Success status
   */
  public async testEmailService(): Promise<boolean> {
    try {
      if (!this.transporter) {
        (logger).warn('Email transporter not initialized, skipping test');
        return false;
      }

      // Get admin emails
      const adminEmails = await this.getAdminEmails();

      if ((adminEmails).length === 0) {
        (logger).warn('No admin emails found for test');
        return false;
      }

      // Send test email
      const html = `
        <h1>Email Service Test</h1>
        <p>This is a test email from the Amazing Pay email service.</p>
        <p>If you received this email, the email service is working correctly.</p>
        <p>Time: ${new Date().toLocaleString()}</p>
      `;

      const text = `
Email Service Test

This is a test email from the Amazing Pay email service.
If you received this email, the email service is working correctly.

Time: ${new Date().toLocaleString()}
      `;

      return await this.sendEmail(adminEmails, 'Amazing Pay Email Service Test', html, text);
    } catch(error) {
      (logger).error('Error testing email service', { error });
      return false;
    }
  }
}
