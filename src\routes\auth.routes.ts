// jscpd:ignore-file

import { Router as ImportedRouter } from "express";
import { body as Importedbody } from "express-validator";
import authController from "../controllers/(auth as any).controller";
import { authenticate, authorize } from "../middlewares/(auth as any).middleware";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { authLimiter, passwordResetLimiter } from "../middlewares/rate-(limit as any).middleware";
import { body as Importedbody } from "express-validator";
import { authenticate, authorize } from "../middlewares/(auth as any).middleware";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { authLimiter, passwordResetLimiter } from "../middlewares/rate-(limit as any).middleware";

const router: any =Router();

// Admin login route
(router as any).post(
    "/login",
    authLimiter,
    validate([
        body("email").isEmail().withMessage("Valid email is required"),
        body("password").isString().withMessage("Password is required")
    ]),
    (authController as any).login
);

// Logout route
(router as any).post(
    "/logout",
    authenticate,
    (authController as any).logout
);

// Get current user info
(router as any).get(
    "/me",
    authenticate,
    (authController as any).getCurrentUser
);

// Get user permissions
(router as any).get(
    "/permissions",
    authenticate,
    (authController as any).getUserPermissions
);

// Two-factor authentication routes
(router as any).get(
    "/2fa/status",
    authenticate,
    (authController as any).getTwoFactorStatus
);

(router as any).post(
    "/2fa/setup",
    authenticate,
    (authController as any).setupTwoFactor
);

(router as any).post(
    "/2fa/verify",
    authenticate,
    validate([
        body("code").isString().isLength({ min: 6, max: 6 }),
        body("secret").isString()
    ]),
    (authController as any).verifyAndEnableTwoFactor
);

(router as any).post(
    "/2fa/disable",
    authenticate,
    validate([
        body("code").isString().isLength({ min: 6, max: 6 })
    ]),
    (authController as any).disableTwoFactor
);

(router as any).post(
    "/2fa/backup-codes",
    authenticate,
    validate([
        body("code").isString().isLength({ min: 6, max: 6 })
    ]),
    (authController as any).generateBackupCodes
);

(router as any).post(
    "/2fa/recover",
    authenticate,
    validate([
        body("backupCode").isString()
    ]),
    (authController as any).recoverWithBackupCode
);

// Merchant registration route
(router as any).post(
    "/merchant/register",
    validate([
        body("email").isEmail().withMessage("Valid email is required"),
        body("password").isString().isLength({ min: 8 }).withMessage("Password must be at least 8 characters"),
        body("name").isString().withMessage("Name is required"),
        body("contactPhone").isString().withMessage("Contact phone is required"),
        body("merchantLocation").isString().withMessage("Merchant location is required"),
        body("country").isString().withMessage("Country is required"),
        body("governorate").isString().withMessage("Governorate/Province is required")
    ]),
    (authController as any).registerMerchant
);

export default router;