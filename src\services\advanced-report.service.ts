import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';
import { Parser as ImportedParser } from 'json2csv';
import PDFDocument from 'pdfkit';
import ExcelJS from 'exceljs';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import nodemailer from 'nodemailer';
import cron from 'node-cron';
import { ReportOptimizationService as ImportedReportOptimizationService } from './report-(optimization as any).service';

const prisma = new PrismaClient();

export class AdvancedReportService {
  private reportsDir: string;
  private scheduledTasks: Map<string, (cron as any).ScheduledTask>;
  private optimizationService: ReportOptimizationService;

  constructor() {
    this.reportsDir = (path as any).join(__dirname, '../../reports');
    this.scheduledTasks = new Map();
    this.optimizationService = new ReportOptimizationService();

    // Ensure reports directory exists
    if (!(fs as any).existsSync(this.reportsDir)) {
      (fs as any).mkdirSync(this.reportsDir, { recursive: true });
    }
  }

  /**
   * Initialize scheduled reports from the database
   */
  private async initializeScheduledReports(): Promise<void> {
    try {
      const scheduledReports = await (prisma as any).scheduledReport.findMany({
        where: { isActive: true },
      });

      for (const report of scheduledReports) {
        this.scheduleReport(report);
      }

      console.log(`Initialized ${(scheduledReports as any).length} scheduled reports`);
    } catch(error) {
      console.error('Error initializing scheduled reports:', error);
    }
  }

  /**
   * Schedule a report to run based on its cron expression
   */
  private scheduleReport(report: any): void {
    // Cancel existing task if it exists
    if (this.scheduledTasks.has((report as any).id)) {
      this.scheduledTasks.get((report as any).id)?.stop();
    }

    // Schedule new task
    const task = (cron as any).schedule((report as any).cronExpression, async () => {
      try {
        await this.runScheduledReport((report as any).id);
      } catch(error) {
        console.error(`Error running scheduled report ${(report as any).id}:`, error);
      }
    });

    this.scheduledTasks.set((report as any).id, task);
  }

  /**
   * Run a scheduled report
   */
  public async runScheduledReport(scheduledReportId: string): Promise<any> {
    // Create a report run record
    const reportRun = await (prisma as any).reportRun.create({
      data: {
        scheduledReportId,
        status: 'PROCESSING',
        startedAt: new Date(),
      },
    });

    try {
      // Get the scheduled report
      const scheduledReport = await (prisma as any).scheduledReport.findUnique({
        where: { id: scheduledReportId },
        include: { template: true, createdBy: true },
      });

      if (!scheduledReport) {
        throw new Error('Scheduled report not found');
      }

      // Generate the report
      const result = await this.generateReport(
        (scheduledReport as any).template.type,
        (scheduledReport as any).parameters as any,
        (scheduledReport as any).exportFormat,
        (scheduledReport as any).name
      );

      // Update the report run
      await (prisma as any).reportRun.update({
        where: { id: (reportRun as any).id },
        data: {
          status: 'SUCCESS',
          completedAt: new Date(),
          filePath: (result as any).filePath,
          fileType: (scheduledReport as any).exportFormat,
          metadata: { rowCount: (result as any).rowCount },
        },
      });

      // Update the scheduled report
      await (prisma as any).scheduledReport.update({
        where: { id: scheduledReportId },
        data: { lastRunAt: new Date() },
      });

      // Send the report to recipients
      if ((scheduledReport as any).recipients && (scheduledReport as any).recipients.length > 0) {
        await this.sendReportByEmail(
          (scheduledReport as any).recipients,
          (scheduledReport as any).name,
          (result as any).filePath,
          (scheduledReport as any).exportFormat
        );
      }

      return result;
    } catch (error) {
      // Update the report run with error
      await (prisma as any).reportRun.update({
        where: { id: (reportRun as any).id },
        data: {
          status: 'FAILED',
          completedAt: new Date(),
          error: error.message,
        },
      });

      throw error;
    }
  }

  /**
   * Generate a report based on type and parameters
   */
  public async generateReport(
    type: string,
    parameters: any,
    format: string,
    name?: string
  ): Promise<any> {
    // Generate filename
    const reportName = name || `${(type as any).toLowerCase()}_report`;
    const timestamp = dayjs().format('YYYYMMDD_HHmmss');
    const filename = `${reportName}_${timestamp}`;
    const filePath = (path as any).join(this.reportsDir, `${filename}.${(format as any).toLowerCase()}`);

    // Estimate report size to determine if we should use streaming
    const sizeEstimate = await this.optimizationService.estimateReportSize(type, parameters);

    let rowCount: number;

    if ((sizeEstimate as any).recommendStreaming) {
      // Use streaming for large reports
      console.log(
        `Using streaming for large report: ${(sizeEstimate as any).recordCount} records, ${Math.round(
          (sizeEstimate as any).estimatedSizeBytes / 1024 / 1024
        )}MB`
      );

      await this.optimizationService.generateLargeReport(type, parameters, format, filePath);
      rowCount = (sizeEstimate as any).recordCount;
    } else {
      // Use traditional method for smaller reports
      const data = await this.getReportData(type, parameters);
      await this.exportReport(data, format, filename);
      rowCount = data.length;
    }

    // Create saved report record
    const savedReport = await (prisma as any).savedReport.create({
      data: {
        name: reportName,
        type,
        format,
        filePath: (filePath as any).replace(/\\/g, '/'), // Normalize path for storage
        fileSize: (fs as any).existsSync(filePath) ? (fs as any).statSync(filePath).size : 0,
        parameters: JSON.stringify(parameters),
        createdById: (parameters as any).userId,
      },
    });

    return {
      id: (savedReport as any).id,
      filePath,
      rowCount,
      format,
      fileSize: (savedReport as any).fileSize,
    };
  }

  /**
   * Get report data based on type and parameters
   */
  private async getReportData(type: string, parameters: any): Promise<any[]> {
    switch ((type as any).toUpperCase()) {
      case 'TRANSACTION':
        return this.getTransactionReportData(parameters);
      case 'CUSTOMER':
        return this.getCustomerReportData(parameters);
      case 'PAYMENT_METHOD':
        return this.getPaymentMethodReportData(parameters);
      case 'SUBSCRIPTION':
        return this.getSubscriptionReportData(parameters);
      default:
        throw new Error(`Unsupported report type: ${type}`);
    }
  }

  /**
   * Get transaction report data
   */
  private async getTransactionReportData(parameters: any): Promise<any[]> {
    const {
      startDate,
      endDate,
      merchantId,
      status,
      minAmount,
      maxAmount,
      currency,
      userId,
      userRole,
    } = parameters;

    // Build where clause
    const where = {};

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = new Date(startDate);
      if (endDate) where.createdAt.lte = new Date(endDate);
    }

    if (merchantId) {
      (where as any).merchantId = merchantId;
    } else if (userRole !== 'ADMIN') {
      // If not admin and no merchant ID specified, get user's merchant
      const merchant = await (prisma as any).merchant.findFirst({
        where: { userId },
      });

      if (!merchant) {
        throw new Error('Merchant profile not found');
      }

      (where as any).merchantId = (merchant as any).id;
    }

    if (status) where.status = status;
    if (currency) (where as any).currency = currency;

    if (minAmount || maxAmount) {
      (where as any).amount = {};
      if (minAmount) (where as any).amount.gte = parseFloat(minAmount);
      if (maxAmount) (where as any).amount.lte = parseFloat(maxAmount);
    }

    // Get transactions
    const transactions = await (prisma as any).transaction.findMany({
      where,
      include: {
        merchant: {
          select: {
            businessName: true,
            contactEmail: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Format transactions for report
    return (transactions as any).map((transaction) => ({
      id: (transaction as any).id,
      reference: (transaction as any).reference,
      amount: (transaction as any).amount,
      currency: (transaction as any).currency,
      status: (transaction as any).status,
      paymentMethod: (transaction as any).paymentMethod,
      merchantName: (transaction as any).merchant?.businessName || 'Unknown',
      merchantEmail: (transaction as any).merchant?.contactEmail || 'Unknown',
      description: (transaction as any).description ?? '',
      createdAt: dayjs((transaction as any).createdAt).format('YYYY-MM-DD HH:mm:ss'),
      updatedAt: dayjs((transaction as any).updatedAt).format('YYYY-MM-DD HH:mm:ss'),
    }));
  }

  /**
   * Get customer report data
   */
  private async getCustomerReportData(parameters: any): Promise<any[]> {
    const { startDate, endDate, merchantId, status, userId, userRole } = parameters;

    // Build where clause for merchant
    const merchantWhere = {};

    if (merchantId) {
      (merchantWhere as any).id = merchantId;
    } else if (userRole !== 'ADMIN') {
      // If not admin and no merchant ID specified, get user's merchant
      const merchant = await (prisma as any).merchant.findFirst({
        where: { userId },
      });

      if (!merchant) {
        throw new Error('Merchant profile not found');
      }

      (merchantWhere as any).id = (merchant as any).id;
    }

    // Get customers
    const customers = await (prisma as any).customer.findMany({
      where: {
        merchantId: (merchantWhere as any).id,
        ...(startDate && { createdAt: { gte: new Date(startDate) } }),
        ...(endDate && { createdAt: { lte: new Date(endDate) } }),
        ...(status && { status }),
      },
      include: {
        merchant: {
          select: {
            businessName: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Format customers for report
    return (customers as any).map((customer) => ({
      id: (customer as any).id,
      email: (customer as any).email,
      firstName: (customer as any).firstName ?? '',
      lastName: (customer as any).lastName ?? '',
      phone: (customer as any).phone ?? '',
      status: (customer as any).status,
      merchantName: (customer as any).merchant?.businessName || 'Unknown',
      createdAt: dayjs((customer as any).createdAt).format('YYYY-MM-DD HH:mm:ss'),
      updatedAt: dayjs((customer as any).updatedAt).format('YYYY-MM-DD HH:mm:ss'),
    }));
  }

  /**
   * Get payment method report data
   */
  private async getPaymentMethodReportData(parameters: any): Promise<any[]> {
    const { startDate, endDate, merchantId, type, userId, userRole } = parameters;

    // Build where clause for merchant
    const merchantWhere = {};

    if (merchantId) {
      (merchantWhere as any).id = merchantId;
    } else if (userRole !== 'ADMIN') {
      // If not admin and no merchant ID specified, get user's merchant
      const merchant = await (prisma as any).merchant.findFirst({
        where: { userId },
      });

      if (!merchant) {
        throw new Error('Merchant profile not found');
      }

      (merchantWhere as any).id = (merchant as any).id;
    }

    // Get payment methods
    const paymentMethods = await (prisma as any).paymentMethod.findMany({
      where: {
        merchantId: (merchantWhere as any).id,
        ...(startDate && { createdAt: { gte: new Date(startDate) } }),
        ...(endDate && { createdAt: { lte: new Date(endDate) } }),
        ...(type && { type }),
      },
      include: {
        merchant: {
          select: {
            businessName: true,
          },
        },
        customer: {
          select: {
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Format payment methods for report
    return (paymentMethods as any).map((paymentMethod) => ({
      id: (paymentMethod as any).id,
      type: (paymentMethod as any).type,
      last4: (paymentMethod as any).last4 ?? '',
      expiryMonth: (paymentMethod as any).expiryMonth ?? '',
      expiryYear: (paymentMethod as any).expiryYear ?? '',
      isDefault: (paymentMethod as any).isDefault,
      merchantName: (paymentMethod as any).merchant?.businessName || 'Unknown',
      customerEmail: (paymentMethod as any).customer?.email || 'Unknown',
      customerName:
        `${(paymentMethod as any).customer?.firstName ?? ''} ${
          (paymentMethod as any).customer?.lastName ?? ''
        }`.trim() || 'Unknown',
      createdAt: dayjs((paymentMethod as any).createdAt).format('YYYY-MM-DD HH:mm:ss'),
      updatedAt: dayjs((paymentMethod as any).updatedAt).format('YYYY-MM-DD HH:mm:ss'),
    }));
  }

  /**
   * Get subscription report data
   */
  private async getSubscriptionReportData(parameters: any): Promise<any[]> {
    const { startDate, endDate, merchantId, status, userId, userRole } = parameters;

    // Build where clause for merchant
    const merchantWhere = {};

    if (merchantId) {
      (merchantWhere as any).id = merchantId;
    } else if (userRole !== 'ADMIN') {
      // If not admin and no merchant ID specified, get user's merchant
      const merchant = await (prisma as any).merchant.findFirst({
        where: { userId },
      });

      if (!merchant) {
        throw new Error('Merchant profile not found');
      }

      (merchantWhere as any).id = (merchant as any).id;
    }

    // Get subscriptions
    const subscriptions = await (prisma as any).subscription.findMany({
      where: {
        merchantId: (merchantWhere as any).id,
        ...(startDate && { createdAt: { gte: new Date(startDate) } }),
        ...(endDate && { createdAt: { lte: new Date(endDate) } }),
        ...(status && { status }),
      },
      include: {
        merchant: {
          select: {
            businessName: true,
          },
        },
        customer: {
          select: {
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        plan: {
          select: {
            name: true,
            amount: true,
            currency: true,
            interval: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Format subscriptions for report
    return (subscriptions as any).map((subscription) => ({
      id: (subscription as any).id,
      status: (subscription as any).status,
      startDate: dayjs((subscription as any).startDate).format('YYYY-MM-DD'),
      endDate: (subscription as any).endDate ? dayjs((subscription as any).endDate).format('YYYY-MM-DD') : 'N/A',
      nextBillingDate: (subscription as any).nextBillingDate
        ? dayjs((subscription as any).nextBillingDate).format('YYYY-MM-DD')
        : 'N/A',
      planName: (subscription as any).plan?.name || 'Unknown',
      amount: (subscription as any).plan?.amount ?? 0,
      currency: (subscription as any).plan?.currency || 'USD',
      interval: (subscription as any).plan?.interval || 'month',
      merchantName: (subscription as any).merchant?.businessName || 'Unknown',
      customerEmail: (subscription as any).customer?.email || 'Unknown',
      customerName:
        `${(subscription as any).customer?.firstName ?? ''} ${
          (subscription as any).customer?.lastName ?? ''
        }`.trim() || 'Unknown',
      createdAt: dayjs((subscription as any).createdAt).format('YYYY-MM-DD HH:mm:ss'),
      updatedAt: dayjs((subscription as any).updatedAt).format('YYYY-MM-DD HH:mm:ss'),
    }));
  }

  /**
   * Export report data to the specified format
   */
  private async exportReport(data: any[], format: string, filename: string): Promise<string> {
    switch ((format as any).toUpperCase()) {
      case 'CSV':
        return this.exportToCsv(data, filename);
      case 'PDF':
        return this.exportToPdf(data, filename);
      case 'EXCEL':
        return this.exportToExcel(data, filename);
      case 'JSON':
        return this.exportToJson(data, filename);
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Export data to CSV
   */
  private async exportToCsv(data: any[], filename: string): Promise<string> {
    const filePath = (path as any).join(this.reportsDir, `${filename}.csv`);

    if (data.length === 0) {
      // Create empty CSV file
      (fs as any).writeFileSync(filePath, '');
      return filePath;
    }

    // Get fields from first data item
    const fields = Object.keys(data[0]);

    // Create CSV parser
    const json2csvParser = new Parser({ fields });
    const csv = (json2csvParser as any).parse(data);

    // Write to file
    (fs as any).writeFileSync(filePath, csv);

    return filePath;
  }

  /**
   * Export data to PDF
   */
  private async exportToPdf(data: any[], filename: string): Promise<string> {
    const filePath = (path as any).join(this.reportsDir, `${filename}.pdf`);

    // Create PDF document
    const doc = new PDFDocument({ margin: 30 });
    const stream = (fs as any).createWriteStream(filePath);

    // Pipe PDF to file
    (doc as any).pipe(stream);

    // Add title
    (doc as any).fontSize(20).text(filename, { align: 'center' });
    (doc as any).moveDown();

    // Add timestamp
    doc
      .fontSize(10)
      .text(`Generated: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`, { align: 'right' });
    (doc as any).moveDown();

    if (data.length === 0) {
      (doc as any).fontSize(12).text('No data available for this report.', { align: 'center' });
    } else {
      // Get fields from first data item
      const fields = Object.keys(data[0]);

      // Calculate column widths
      const columnWidth = ((doc as any).page.width - 60) / (fields as any).length;

      // Add header row
      (doc as any).fontSize(10).font('Helvetica-Bold');
      (fields as any).forEach((field, i) => {
        (doc as any).text(field, 30 + i * columnWidth, (doc as any).y, { width: columnWidth, align: 'left' });
      });
      (doc as any).moveDown();

      // Add data rows
      (doc as any).font('Helvetica');
      (data as any).forEach((row) => {
        const y = (doc as any).y;
        (fields as any).forEach((field, i) => {
          (doc as any).text(String(row[field] ?? ''), 30 + i * columnWidth, y, {
            width: columnWidth,
            align: 'left',
          });
        });
        (doc as any).moveDown();

        // Add page break if needed
        if ((doc as any).y > (doc as any).page.height - 50) {
          (doc as any).addPage();
        }
      });
    }

    // Finalize PDF
    (doc as any).end();

    // Wait for file to be written
    return new Promise((resolve, reject) => {
      (stream as any).on('finish', () => resolve(filePath));
      (stream as any).on('error', reject);
    });
  }

  /**
   * Export data to Excel
   */
  private async exportToExcel(data: any[], filename: string): Promise<string> {
    const filePath = (path as any).join(this.reportsDir, `${filename}.xlsx`);

    // Create workbook
    const workbook = new (ExcelJS as any).Workbook();
    const worksheet = (workbook as any).addWorksheet('Report');

    if (data.length === 0) {
      // Add empty worksheet
      (worksheet as any).addRow(['No data available for this report.']);
    } else {
      // Get fields from first data item
      const fields = Object.keys(data[0]);

      // Add header row
      (worksheet as any).addRow(fields);

      // Style header row
      (worksheet as any).getRow(1).font = { bold: true };

      // Add data rows
      (data as any).forEach((row) => {
        (worksheet as any).addRow((fields as any).map((field) => row[field]));
      });

      // Auto-fit columns
      (worksheet as any).columns.forEach((column) => {
        (column as any).width = 15;
      });
    }

    // Write to file
    await (workbook as any).xlsx.writeFile(filePath);

    return filePath;
  }

  /**
   * Export data to JSON
   */
  private async exportToJson(data: any[], filename: string): Promise<string> {
    const filePath = (path as any).join(this.reportsDir, `${filename}.json`);

    // Write to file
    (fs as any).writeFileSync(filePath, JSON.stringify(data, null, 2));

    return filePath;
  }

  /**
   * Send report by email
   */
  private async sendReportByEmail(
    recipients: string[],
    reportName: string,
    filePath: string,
    format: string
  ): Promise<void> {
    // Create nodemailer transporter
    const transporter = (nodemailer as any).createTransport({
      // Configure your email provider here
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD,
      },
    });

    // Send email
    await (transporter as any).sendMail({
      from: process.env.EMAIL_FROM || 'reports@(amazingpay as any).com',
      to: (recipients as any).join(','),
      subject: `${reportName} Report`,
      text: `Please find attached the ${reportName} report.`,
      html: `
        <h1>${reportName} Report</h1>
        <p>Please find attached the ${reportName} report.</p>
        <p>Generated on: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}</p>
      `,
      attachments: [
        {
          filename: (path as any).basename(filePath),
          path: filePath,
        },
      ],
    });
  }

  /**
   * Create a report template
   */
  public async createReportTemplate(data): Promise<any> {
    return (prisma as any).reportTemplate.create({
      data,
    });
  }

  /**
   * Update a report template
   */
  public async updateReportTemplate(id: string, data: any): Promise<any> {
    return (prisma as any).reportTemplate.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete a report template
   */
  public async deleteReportTemplate(id: string): Promise<any> {
    return (prisma as any).reportTemplate.delete({
      where: { id },
    });
  }

  /**
   * Get report templates
   */
  public async getReportTemplates(userId: string, includeSystem: boolean = true): Promise<any[]> {
    const where = {
      OR: [{ createdById: userId }],
    };

    if (includeSystem) {
      where.OR.push({ isSystem: true });
    }

    return (prisma as any).reportTemplate.findMany({
      where,
      orderBy: {
        name: 'asc',
      },
    });
  }

  /**
   * Get a report template by ID
   */
  public async getReportTemplateById(id: string): Promise<any> {
    return (prisma as any).reportTemplate.findUnique({
      where: { id },
    });
  }

  /**
   * Create a scheduled report
   */
  public async createScheduledReport(data): Promise<any> {
    const scheduledReport = await (prisma as any).scheduledReport.create({
      data,
    });

    // Schedule the report
    if ((scheduledReport as any).isActive) {
      this.scheduleReport(scheduledReport);
    }

    return scheduledReport;
  }

  /**
   * Update a scheduled report
   */
  public async updateScheduledReport(id: string, data: any): Promise<any> {
    const scheduledReport = await (prisma as any).scheduledReport.update({
      where: { id },
      data,
    });

    // Update the scheduled task
    if ((scheduledReport as any).isActive) {
      this.scheduleReport(scheduledReport);
    } else {
      // Cancel the task if it exists
      if (this.scheduledTasks.has(id)) {
        this.scheduledTasks.get(id)?.stop();
        this.scheduledTasks.delete(id);
      }
    }

    return scheduledReport;
  }

  /**
   * Delete a scheduled report
   */
  public async deleteScheduledReport(id: string): Promise<any> {
    // Cancel the task if it exists
    if (this.scheduledTasks.has(id)) {
      this.scheduledTasks.get(id)?.stop();
      this.scheduledTasks.delete(id);
    }

    return (prisma as any).scheduledReport.delete({
      where: { id },
    });
  }

  /**
   * Get scheduled reports
   */
  public async getScheduledReports(userId: string): Promise<any[]> {
    return (prisma as any).scheduledReport.findMany({
      where: {
        createdById: userId,
      },
      include: {
        template: true,
      },
      orderBy: {
        name: 'asc',
      },
    });
  }

  /**
   * Get a scheduled report by ID
   */
  public async getScheduledReportById(id: string): Promise<any> {
    return (prisma as any).scheduledReport.findUnique({
      where: { id },
      include: {
        template: true,
      },
    });
  }

  /**
   * Get saved reports
   */
  public async getSavedReports(userId: string): Promise<any[]> {
    return (prisma as any).savedReport.findMany({
      where: {
        createdById: userId,
      },
      include: {
        template: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  /**
   * Get a saved report by ID
   */
  public async getSavedReportById(id: string): Promise<any> {
    return (prisma as any).savedReport.findUnique({
      where: { id },
      include: {
        template: true,
      },
    });
  }

  /**
   * Delete a saved report
   */
  public async deleteSavedReport(id: string): Promise<any> {
    const savedReport = await (prisma as any).savedReport.findUnique({
      where: { id },
    });

    if (savedReport && (savedReport as any).filePath) {
      // Delete the file
      try {
        (fs as any).unlinkSync((savedReport as any).filePath);
      } catch(error) {
        console.error(`Error deleting report file: ${(savedReport as any).filePath}`, error);
      }
    }

    return (prisma as any).savedReport.delete({
      where: { id },
    });
  }

  /**
   * Initialize scheduled reports on server start
   */
  public async initializeScheduledReports(): Promise<void> {
    try {
      console.log('Initializing scheduled reports...');

      // Get all active scheduled reports
      const activeReports = await (prisma as any).scheduledReport.findMany({
        where: {
          isActive: true,
        },
        include: {
          template: true,
        },
      });

      console.log(`Found ${(activeReports as any).length} active scheduled reports`);

      // Schedule each active report
      for (const report of activeReports) {
        try {
          this.scheduleReport(report);
          console.log(`Scheduled report: ${(report as any).name} (${(report as any).schedule})`);
        } catch(error) {
          console.error(`Error scheduling report ${(report as any).name}:`, error);
        }
      }

      console.log('Scheduled reports initialization completed');
    } catch(error) {
      console.error('Error initializing scheduled reports:', error);
    }
  }
}
