// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from "../../core/BaseController";
import { PaymentMethodService } from "../../services/refactored/payment-method.service";
import { ErrorFactory } from "../../utils/errors/ErrorFactory";
import { logger } from "../../lib/logger";
import { Merchant } from '../types';
import { BaseController } from "../../core/BaseController";
import { PaymentMethodService } from "../../services/refactored/payment-method.service";
import { ErrorFactory } from "../../utils/errors/ErrorFactory";
import { logger } from "../../lib/logger";
import { Merchant } from '../types';


/**
 * Payment method controller
 * This controller handles payment method-related operations
 */
export class PaymentMethodController extends BaseController {
  private paymentMethodService: PaymentMethodService;
  
  /**
   * Create a new payment method controller
   */
  constructor() {
    super();
    this.paymentMethodService = new PaymentMethodService();
  }
  
  /**
   * Get all payment methods
   */
  getPaymentMethods = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);
    
    // Parse query parameters
    const requestedMerchantId: unknown = req.query.merchantId as string;
    
    // Determine target merchant ID
    const targetMerchantId: unknown = this.determineTargetMerchantId(
      userRole,
      merchantId,
      requestedMerchantId
    );
    
    // Parse pagination parameters
    const { limit, offset } = this.parsePagination(req);
    
    // Get payment methods
    const result = await this.paymentMethodService.getPaymentMethods({
      merchantId: targetMerchantId,
      limit,
      offset
    });
    
    // Send paginated response
    return this.sendPaginatedSuccess(
      res,
      result.data,
      result.total,
      limit,
      offset
    );
  });
  
  /**
   * Get a payment method by ID
   */
  getPaymentMethod = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);
    
    // Get payment method ID from params
    const { id } = req.params;
    
    // Get payment method
    const paymentMethod = await this.paymentMethodService.getPaymentMethodById(id);
    
    // Check if payment method exists
    if (!paymentMethod) {
      throw ErrorFactory.notFound('Payment method', id);
    }
    
    // Check if user has permission to view this payment method
    if (userRole !== 'ADMIN' && paymentMethod.merchantId !== merchantId) {
      throw ErrorFactory.authorization('You do not have permission to view this payment method');
    }
    
    // Send success response
    return this.sendSuccess(res, paymentMethod);
  });
  
  /**
   * Create a new payment method
   */
  createPaymentMethod = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, userId, merchantId } = this.checkAuthorization(req);
    
    // Get request body
    const { name, type, config, isDefault, merchantId: requestedMerchantId } = req.body;
    
    // Validate required fields
    if (!name || !type) {
      throw ErrorFactory.validation('Name and type are required');
    }
    
    // Determine target merchant ID
    let targetMerchantId: unknown =merchantId;
    
    // Admins can create payment methods for any merchant
    if (userRole === 'ADMIN' && requestedMerchantId) {
      targetMerchantId = requestedMerchantId;
    }
    
    // Check if merchant ID exists
    if (!targetMerchantId) {
      throw ErrorFactory.validation('Merchant ID is required');
    }
    
    // Create payment method
    const paymentMethod = await this.paymentMethodService.createPaymentMethod({
      name,
      type,
      config: config ?? {},
      isDefault: isDefault ?? false,
      merchantId: targetMerchantId,
      createdBy: userId
    });
    
    // Send success response
    return this.sendSuccess(res, paymentMethod, 201);
  });
  
  /**
   * Update a payment method
   */
  updatePaymentMethod = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, userId, merchantId } = this.checkAuthorization(req);
    
    // Get payment method ID from params
    const { id } = req.params;
    
    // Get payment method
    const paymentMethod = await this.paymentMethodService.getPaymentMethodById(id);
    
    // Check if payment method exists
    if (!paymentMethod) {
      throw ErrorFactory.notFound('Payment method', id);
    }
    
    // Check if user has permission to update this payment method
    if (userRole !== 'ADMIN' && paymentMethod.merchantId !== merchantId) {
      throw ErrorFactory.authorization('You do not have permission to update this payment method');
    }
    
    // Get request body
    const { name, type, config, isDefault } = req.body;
    
    // Prepare update data
    const updateData = {
      updatedBy: userId
    };
    
    if (name) updateData.name = name;
    if (type) updateData.type = type;
    if (config) updateData.config = config;
    if (isDefault !== undefined) updateData.isDefault = isDefault;
    
    // Update payment method
    const updatedPaymentMethod = await this.paymentMethodService.updatePaymentMethod(id, updateData);
    
    // Send success response
    return this.sendSuccess(res, updatedPaymentMethod);
  });
  
  /**
   * Delete a payment method
   */
  deletePaymentMethod = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);
    
    // Get payment method ID from params
    const { id } = req.params;
    
    // Get payment method
    const paymentMethod = await this.paymentMethodService.getPaymentMethodById(id);
    
    // Check if payment method exists
    if (!paymentMethod) {
      throw ErrorFactory.notFound('Payment method', id);
    }
    
    // Check if user has permission to delete this payment method
    if (userRole !== 'ADMIN' && paymentMethod.merchantId !== merchantId) {
      throw ErrorFactory.authorization('You do not have permission to delete this payment method');
    }
    
    // Delete payment method
    await this.paymentMethodService.deletePaymentMethod(id);
    
    // Send success response
    return this.sendSuccess(res, { message: 'Payment method deleted successfully' });
  });
  
  /**
   * Set a payment method as default
   */
  setDefaultPaymentMethod = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, userId, merchantId } = this.checkAuthorization(req);
    
    // Get payment method ID from params
    const { id } = req.params;
    
    // Get payment method
    const paymentMethod = await this.paymentMethodService.getPaymentMethodById(id);
    
    // Check if payment method exists
    if (!paymentMethod) {
      throw ErrorFactory.notFound('Payment method', id);
    }
    
    // Check if user has permission to update this payment method
    if (userRole !== 'ADMIN' && paymentMethod.merchantId !== merchantId) {
      throw ErrorFactory.authorization('You do not have permission to update this payment method');
    }
    
    // Set payment method as default
    await this.paymentMethodService.setDefaultPaymentMethod(id, paymentMethod.merchantId);
    
    // Send success response
    return this.sendSuccess(res, { message: 'Payment method set as default successfully' });
  });
  
  /**
   * Verify a payment method
   */
  verifyPaymentMethod = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);
    
    // Get payment method ID from params
    const { id } = req.params;
    
    // Get payment method
    const paymentMethod = await this.paymentMethodService.getPaymentMethodById(id);
    
    // Check if payment method exists
    if (!paymentMethod) {
      throw ErrorFactory.notFound('Payment method', id);
    }
    
    // Check if user has permission to verify this payment method
    if (userRole !== 'ADMIN' && paymentMethod.merchantId !== merchantId) {
      throw ErrorFactory.authorization('You do not have permission to verify this payment method');
    }
    
    // Get request body
    const { verificationData } = req.body;
    
    // Verify payment method
    const verificationResult = await this.paymentMethodService.verifyPaymentMethod(id, verificationData);
    
    // Send success response
    return this.sendSuccess(res, verificationResult);
  });
}
