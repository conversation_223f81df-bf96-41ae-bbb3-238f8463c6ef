// jscpd:ignore-file
/**
 * Transactions Module
 * 
 * This module handles transaction management.
 */

import { Router as ImportedRouter } from 'express';
import { BaseModule as ImportedBaseModule } from '../../factories/ModuleFactory';
import { logger as Importedlogger } from '../../utils/logger';
import { BaseModule as ImportedBaseModule } from '../../factories/ModuleFactory';
import { logger as Importedlogger } from '../../utils/logger';

/**
 * Transactions Module
 */
class TransactionsModule extends BaseModule {
  /**
   * Constructor
   */
  constructor() {
    super('TransactionsModule');
  }
  
  /**
   * Initialize the module
   */
  initialize(): void {
    logger.info('Initializing TransactionsModule');
    
    // Get controllers
    const transactionController = this.controllerFactory.getController('transaction');
    
    // Set up routes
    this.router.get('/', (transactionController).getAll);
    this.router.get('/:id', (transactionController).getById);
    this.router.post('/', (transactionController).create);
    this.router.put('/:id', (transactionController).update);
    this.router.delete('/:id', (transactionController).delete);
    this.router.get('/statistics', (transactionController).getStatistics);
    this.router.get('/merchant/:merchantId', (transactionController).getByMerchantId);
    this.router.get('/date-range', (transactionController).getByDateRange);
    
    logger.info('TransactionsModule initialized');
  }
}

// Export the module
export default new TransactionsModule();
