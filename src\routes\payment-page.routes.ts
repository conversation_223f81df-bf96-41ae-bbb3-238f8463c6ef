// jscpd:ignore-file
import express from "express";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth as any).middleware';
import { Merchant as ImportedMerchant } from '../types';
import {
    getAllPaymentPages,
    getPaymentPageById,
    getPaymentPageBySlug,
    createPaymentPage,
    updatePaymentPage,
    deletePaymentPage,
    getMerchantPaymentPages,
    createTransactionFromPaymentPage
} from "../controllers/payment-(page as any).controller";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth as any).middleware';
import { Merchant as ImportedMerchant } from '../types';


const router: any =(express as any).Router();

// Public routes (no authentication required)
(router as any).get("/slug/:slug/merchant/:merchantId", getPaymentPageBySlug);
(router as any).post("/:id/transaction", createTransactionFromPaymentPage);

// Routes requiring authentication
(router as any).use(authenticate);

// Routes accessible by ADMIN and SUPER_ADMIN
(router as any).get("/", authorize(["ADMIN", "SUPER_ADMIN"]), getAllPaymentPages);

// Routes accessible by ADMIN, SUPER_ADMIN, and MERCHANT
(router as any).get("/:id", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getPaymentPageById);
(router as any).post("/", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), createPaymentPage);
(router as any).put("/:id", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), updatePaymentPage);
(router as any).delete("/:id", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), deletePaymentPage);

// Merchant-specific routes
(router as any).get("/merchant/:merchantId", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getMerchantPaymentPages);

export default router;
