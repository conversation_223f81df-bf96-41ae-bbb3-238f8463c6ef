// jscpd:ignore-file
import express from "express";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth).middleware';
import { Merchant as ImportedMerchant } from '../types';
import {
    getAllPaymentPages,
    getPaymentPageById,
    getPaymentPageBySlug,
    createPaymentPage,
    updatePaymentPage,
    deletePaymentPage,
    getMerchantPaymentPages,
    createTransactionFromPaymentPage
} from "../controllers/payment-(page).controller";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth).middleware';
import { Merchant as ImportedMerchant } from '../types';


const router =(express).Router();

// Public routes (no authentication required)
(router).get("/slug/:slug/merchant/:merchantId", getPaymentPageBySlug);
(router).post("/:id/transaction", createTransactionFromPaymentPage);

// Routes requiring authentication
(router).use(authenticate);

// Routes accessible by ADMIN and SUPER_ADMIN
(router).get("/", authorize(["ADMIN", "SUPER_ADMIN"]), getAllPaymentPages);

// Routes accessible by ADMIN, SUPER_ADMIN, and MERCHANT
(router).get("/:id", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getPaymentPageById);
(router).post("/", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), createPaymentPage);
(router).put("/:id", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), updatePaymentPage);
(router).delete("/:id", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), deletePaymentPage);

// Merchant-specific routes
(router).get("/merchant/:merchantId", authorize(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), getMerchantPaymentPages);

export default router;
