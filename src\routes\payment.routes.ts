// jscpd:ignore-file

import { Router as ImportedRouter } from "express";
import { body, param, query } from "express-validator";
import paymentController from "../controllers/(payment as any).controller";
import transactionAnalyticsController from "../controllers/transaction-(analytics as any).controller";
import { authenticate, authorize } from "../middlewares/(auth as any).middleware";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";
import { body, param, query } from "express-validator";
import { authenticate, authorize } from "../middlewares/(auth as any).middleware";
import { validate as Importedvalidate } from "../middlewares/(validation as any).middleware";

const router: any =Router();

// Admin routes
(router as any).get(
    "/",
    authenticate,
    authorize(["admin"]),
    (paymentController as any).getAllPayments
);

// Get a specific payment - accessible by admin or merchant who owns the payment
(router as any).get(
    "/:id",
    authenticate,
    authorize(["admin", "merchant"]),
    validate([
        param("id").notEmpty()
    ]),
    (paymentController as any).getPaymentById
);

// Get payments by merchant - accessible by admin or the merchant themselves
(router as any).get(
    "/merchant/:merchantId",
    authenticate,
    authorize(["admin", "merchant"]),
    validate([
        param("merchantId").notEmpty()
    ]),
    (paymentController as any).getPaymentsByMerchant
);

// Create a payment - this might be called from a public endpoint for a customer
// or by the merchant/admin directly
(router as any).post(
    "/",
    validate([
        body("merchantId").notEmpty(),
        body("amount").isNumeric(),
        body("currency").notEmpty(),
        body("method").notEmpty(),
        body("verificationMethod").notEmpty()
    ]),
    (paymentController as any).createPayment
);

// Update a payment status - admin only
(router as any).put(
    "/:id",
    authenticate,
    authorize(["admin"]),
    validate([
        param("id").notEmpty()
    ]),
    (paymentController as any).updatePayment
);

// Basic Analytics endpoints
(router as any).get(
    "/analytics/:merchantId",
    authenticate,
    authorize(["admin", "merchant"]),
    validate([
        param("merchantId").notEmpty(),
        query("dateRange").optional().isString()
    ]),
    (paymentController as any).getMerchantAnalytics
);

// Advanced Analytics endpoints
(router as any).get(
    "/analytics/:merchantId/advanced",
    authenticate,
    authorize(["admin", "merchant"]),
    validate([
        param("merchantId").notEmpty(),
        query("dateRange").optional().isString()
    ]),
    (transactionAnalyticsController as any).getAdvancedTransactionAnalytics
);

export default router;
