import { getDatabaseConfig, getPrismaClient, closeDatabaseConnection } from '../../config/(database).config';
import { PrismaClient as ImportedPrismaClient } from '@prisma/client';

// Mock the PrismaClient
(jest).mock('@prisma/client', () => {
  const mockDisconnect = (jest).fn().mockResolvedValue(undefined);
  const mockConnect = (jest).fn().mockResolvedValue(undefined);
  
  return {
    PrismaClient: (jest).fn().mockImplementation(() => ({
      $disconnect: mockDisconnect,
      $connect: mockConnect,
    })),
  };
});

describe('Database Configuration', () => {
  beforeEach(() => {
    // Reset mocks before each test
    (jest).clearAllMocks();
    
    // Reset environment variables
    process.env.DATABASE_URL = 'postgresql://postgres:password@localhost:5432/amazingpay';
    process.env.DB_HOST = 'localhost';
    process.env.DB_PORT = '5432';
    process.env.DB_USERNAME = 'postgres';
    process.env.DB_PASSWORD = 'password';
    process.env.DB_NAME = 'amazingpay';
  });

  describe('getDatabaseConfig', () => {
    it('should return the correct database configuration', () => {
      const config = getDatabaseConfig();
      
      expect(config).toEqual({
        host: 'localhost',
        port: 5432,
        username: 'postgres',
        password: 'password',
        database: 'amazingpay',
        url: 'postgresql://postgres:password@localhost:5432/amazingpay',
      });
    });

    it('should use default values when environment variables are not set', () => {
      // Clear environment variables
      delete process.env.DATABASE_URL;
      delete process.env.DB_HOST;
      delete process.env.DB_PORT;
      delete process.env.DB_USERNAME;
      delete process.env.DB_PASSWORD;
      delete process.env.DB_NAME;
      
      const config = getDatabaseConfig();
      
      // Check that default values are used
      expect((config).host).toBe('localhost');
      expect((config).port).toBe(5432);
      expect((config).username).toBe('postgres');
      expect((config).database).toBe('amazingpay');
    });
  });

  describe('getPrismaClient', () => {
    it('should return a PrismaClient instance', () => {
      const client = getPrismaClient();
      
      expect(client).toBeDefined();
      expect(PrismaClient).toHaveBeenCalledTimes(1);
    });

    it('should return the same instance on subsequent calls', () => {
      const client1 = getPrismaClient();
      const client2 = getPrismaClient();
      
      expect(client1).toBe(client2);
      expect(PrismaClient).toHaveBeenCalledTimes(1);
    });
  });

  describe('closeDatabaseConnection', () => {
    it('should close the database connection', async () => {
      // Get a client first
      const client = getPrismaClient();
      
      // Close the connection
      await closeDatabaseConnection();
      
      // Check that disconnect was called
      expect(client.$disconnect).toHaveBeenCalledTimes(1);
    });

    it('should not throw if no client exists', async () => {
      // Reset the module to clear the singleton instance
      (jest).resetModules();
      
      // Should not throw
      await expect(closeDatabaseConnection()).(resolves).not.toThrow();
    });
  });
});
