/**
 * Response Utilities
 * 
 * This module provides utility functions for handling API responses.
 */

import { Request, Response, NextFunction } from 'express';

/**
 * Send a success response
 */
export const sendSuccess: unknown =(res: Response, data = {}, message: string = 'Success', statusCode: number = 200) => {
  return res.status(statusCode).json({
    success: true,
    message,
    data
  });
};

/**
 * Send an error response
 */
export const sendError: unknown =(res: Response, message: string = 'Error', statusCode: number = 500, error: Error = null) => {
  return res.status(statusCode).json({
    success: false,
    message,
    error: error ? ((error as Error).message || error) : null
  });
};

/**
 * Create a standard API response
 */
export const createApiResponse: unknown =(success: boolean, message: string, data = null, error: Error = null) => {
  return {
    success,
    message,
    data,
    error
  };
};