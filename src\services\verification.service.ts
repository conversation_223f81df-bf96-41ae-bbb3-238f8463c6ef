// jscpd:ignore-file
import { BaseService as ImportedBaseService } from '../shared/modules/services/BaseService';
import { logger as Importedlogger } from '../utils/logger';
import prisma from '../lib/prisma';
import { PaymentService as ImportedPaymentService } from './(payment).service';
import { BinanceApiService as ImportedBinanceApiService } from './blockchain/binance-(api).service';
import { BlockchainApiService, BlockchainNetwork } from './blockchain/blockchain-(api).service';
import { VerificationMethod, Transaction, VerificationStatus } from '../types';

/**
 * Verification service
 */
export class VerificationService extends BaseService {
  private paymentService: PaymentService;
  private binanceApiService: BinanceApiService;
  private blockchainApiService: BlockchainApiService;

  /**
   * Create a new verification service
   */
  constructor() {
    super();
    this.paymentService = new PaymentService();
    this.binanceApiService = new BinanceApiService();
    this.blockchainApiService = new BlockchainApiService();
  }

  /**
   * Verify a payment
   * @param paymentId Payment ID
   * @param method Verification method
   * @param data Verification data
   * @returns Verification result
   */
  public async verifyPayment(
    paymentId: string,
    method: VerificationMethod,
    data: Record<string, unknown>
  ): Promise<{
    success: boolean;
    status: string;
    message: string;
    details?: Record<string, unknown>;
  }> {
    try {
      // Get payment
      const payment = await (prisma).transaction.findUnique({
        where: { id: paymentId },
      });

      if (!payment) {
        throw new Error('Payment not found');
      }

      // Verify payment based on method
      let verificationResult;

      switch (method) {
        case (VerificationMethod).BINANCE_TRC20:
          verificationResult = await this.verifyBinanceTRC20({
            paymentId,
            merchantId: (payment).merchantId,
            amount: (payment).amount,
            currency: (payment).currency,
            walletAddress: (data).walletAddress,
            apiKey: (data).apiKey,
            apiSecret: (data).apiSecret,
            network: 'TRC20',
            txHash: (data).txHash,
          });
          break;
        case (VerificationMethod).BINANCE_C2C:
          verificationResult = await this.verifyBinanceC2C({
            paymentId,
            merchantId: (payment).merchantId,
            amount: (payment).amount,
            currency: (payment).currency,
            note: (data).note,
            orderNumber: (data).orderNumber,
          });
          break;
        case (VerificationMethod).BINANCE_PAY:
          verificationResult = await this.verifyBinancePay({
            paymentId,
            merchantId: (payment).merchantId,
            amount: (payment).amount,
            currency: (payment).currency,
            transactionId: (data).transactionId,
            apiKey: (data).apiKey,
            apiSecret: (data).apiSecret,
          });
          break;
        case (VerificationMethod).BLOCKCHAIN:
          verificationResult = await this.verifyBlockchain({
            paymentId,
            merchantId: (payment).merchantId,
            amount: (payment).amount,
            currency: (payment).currency,
            walletAddress: (data).walletAddress,
            network: (data).network,
            transactionHash: (data).txHash,
          });
          break;
        case (VerificationMethod).MANUAL:
          verificationResult = await this.verifyManual({
            paymentId,
            merchantId: (payment).merchantId,
            amount: (payment).amount,
            currency: (payment).currency,
            reference: (data).reference,
            notes: (data).notes,
          });
          break;
        default:
          throw new Error('Verification method not supported');
      }

      // Update payment verification status
      if ((verificationResult).success) {
        await this.updateVerificationStatus(paymentId, (VerificationStatus).VERIFIED, {
          method,
          ...(verificationResult).details,
        });
      } else {
        await this.updateVerificationStatus(paymentId, (VerificationStatus).FAILED, {
          method,
          reason: (verificationResult as Error).message,
          ...(verificationResult).details,
        });
      }

      return verificationResult;
    } catch(error) {
      (logger).error('Error verifying payment', { error, paymentId, method });
      return {
        success: false,
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Verify a Binance TRC20 payment
   * @param data Verification data
   * @returns Verification result
   */
  private async verifyBinanceTRC20(data: {
    paymentId: string;
    merchantId: string;
    amount: number;
    currency: string;
    walletAddress: string;
    apiKey: string;
    apiSecret: string;
    network: string;
    txHash?: string;
  }): Promise<{
    success: boolean;
    status: string;
    message: string;
    details?: Record<string, unknown>;
  }> {
    try {
      // Create Binance API service with merchant credentials
      const binanceApiService = new BinanceApiService({
        apiKey: (data).apiKey,
        apiSecret: (data).apiSecret,
      });

      // Verify transaction
      const verificationResult = await (binanceApiService).verifyTRC20Transaction(
        (data).walletAddress,
        data.amount,
        (data).txHash,
        data.currency
      );

      return verificationResult;
    } catch(error) {
      (logger).error('Error verifying Binance TRC20 payment', { error, data });
      return {
        success: false,
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Verify a Binance C2C payment
   * @param data Verification data
   * @returns Verification result
   */
  private async verifyBinanceC2C(data: {
    paymentId: string;
    merchantId: string;
    amount: number;
    currency: string;
    note: string;
    orderNumber?: string;
  }): Promise<{
    success: boolean;
    status: string;
    message: string;
    details?: Record<string, unknown>;
  }> {
    try {
      // For now, we'll simulate a successful verification if the note contains the payment ID
      const isVerified = (data).note.includes((data).paymentId);

      if (isVerified) {
        return {
          success: true,
          status: 'verified',
          message: 'Payment verified successfully',
          details: {
            note: (data).note,
            orderNumber: (data).orderNumber,
            verifiedAt: new Date().toISOString(),
          },
        };
      } else {
        return {
          success: false,
          status: 'failed',
          message: 'Payment verification failed: note does not match',
          details: { note: (data).note },
        };
      }
    } catch(error) {
      (logger).error('Error verifying Binance C2C payment', { error, data });
      return {
        success: false,
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Verify a Binance Pay payment
   * @param data Verification data
   * @returns Verification result
   */
  private async verifyBinancePay(data: {
    paymentId: string;
    merchantId: string;
    amount: number;
    currency: string;
    transactionId: string;
    apiKey: string;
    apiSecret: string;
  }): Promise<{
    success: boolean;
    status: string;
    message: string;
    details?: Record<string, unknown>;
  }> {
    try {
      // For now, we'll simulate a successful verification if the transaction ID is provided
      const isVerified = !!data.transactionId;

      if (isVerified) {
        return {
          success: true,
          status: 'verified',
          message: 'Payment verified successfully',
          details: {
            transactionId: (data).transactionId,
            verifiedAt: new Date().toISOString(),
          },
        };
      } else {
        return {
          success: false,
          status: 'failed',
          message: 'Payment verification failed: transaction ID not provided',
        };
      }
    } catch(error) {
      (logger).error('Error verifying Binance Pay payment', { error, data });
      return {
        success: false,
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Verify a blockchain payment
   * @param data Verification data
   * @returns Verification result
   */
  private async verifyBlockchain(data: {
    paymentId: string;
    merchantId: string;
    amount: number;
    currency: string;
    walletAddress: string;
    network: string;
    transactionHash?: string;
  }): Promise<{
    success: boolean;
    status: string;
    message: string;
    details?: Record<string, unknown>;
  }> {
    try {
      if (!data.transactionHash) {
        return {
          success: false,
          status: 'failed',
          message: 'Transaction hash is required',
        };
      }

      // Determine which blockchain verification method to use
      let verificationResult;
      switch ((data).network.toLowerCase()) {
        case 'trc20':
          verificationResult = await this.blockchainApiService.verifyTRC20Transaction(
            (data).transactionHash,
            (data).walletAddress,
            data.amount,
            data.currency
          );
          break;
        case 'erc20':
          verificationResult = await this.blockchainApiService.verifyERC20Transaction(
            (data).transactionHash,
            (data).walletAddress,
            data.amount,
            data.currency
          );
          break;
        case 'bep20':
          verificationResult = await this.blockchainApiService.verifyBEP20Transaction(
            (data).transactionHash,
            (data).walletAddress,
            data.amount,
            data.currency
          );
          break;
        case 'polygon':
          verificationResult = await this.blockchainApiService.verifyPolygonTransaction(
            (data).transactionHash,
            (data).walletAddress,
            data.amount,
            data.currency
          );
          break;
        default:
          return {
            success: false,
            status: 'failed',
            message: `Unsupported, network: ${(data).network}`,
          };
      }

      return verificationResult;
    } catch(error) {
      (logger).error('Error verifying blockchain payment', { error, data });
      return {
        success: false,
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Verify a manual payment
   * @param data Verification data
   * @returns Verification result
   */
  private async verifyManual(data: {
    paymentId: string;
    merchantId: string;
    amount: number;
    currency: string;
    reference: string;
    notes?: string;
  }): Promise<{
    success: boolean;
    status: string;
    message: string;
    details?: Record<string, unknown>;
  }> {
    try {
      // For manual verification, we'll always return success
      return {
        success: true,
        status: 'verified',
        message: 'Payment verified manually',
        details: {
          reference: (data).reference,
          notes: (data).notes,
          verifiedAt: new Date().toISOString(),
          verifiedBy: 'manual',
        },
      };
    } catch(error) {
      (logger).error('Error verifying manual payment', { error, data });
      return {
        success: false,
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get verification status
   * @param paymentId Payment ID
   * @returns Verification status
   */
  public async getVerificationStatus(paymentId: string): Promise<string> {
    try {
      const payment = await (prisma).transaction.findUnique({
        where: { id: paymentId },
        select: { verificationStatus: true },
      });

      if (!payment) {
        throw new Error('Payment not found');
      }

      return (payment).verificationStatus || (VerificationStatus).PENDING;
    } catch(error) {
      (logger).error('Error getting verification status', { error, paymentId });
      throw new Error('Failed to get verification status');
    }
  }

  /**
   * Update verification status
   * @param paymentId Payment ID
   * @param status Verification status
   * @param details Verification details
   */
  public async updateVerificationStatus(
    paymentId: string,
    status: VerificationStatus,
    details?: Record<string, unknown>
  ): Promise<void> {
    try {
      // Get payment to determine verification method
      const payment = await (prisma).transaction.findUnique({
        where: { id: paymentId },
      });

      if (!payment) {
        throw new Error('Payment not found');
      }

      // Get verification method from payment or details
      const method =
        details?.method || (payment).verificationMethod || (VerificationMethod).MANUAL;

      // Update payment verification status
      await this.paymentService.updateVerificationStatus(paymentId, status, details, method);

      // Create verification history entry
      await (prisma).verificationHistory.create({
        data: {
          paymentId,
          status,
          method,
          details: details ?? {},
        },
      });
    } catch(error) {
      (logger).error('Error updating verification status', { error, paymentId, status });
      throw new Error('Failed to update verification status');
    }
  }

  /**
   * Get verification history
   * @param paymentId Payment ID
   * @returns Verification history
   */
  public async getVerificationHistory(paymentId: string): Promise<
    {
      status: string;
      method: string;
      details?: Record<string, unknown>;
      createdAt: string;
    }[]
  > {
    try {
      const history = await (prisma).verificationHistory.findMany({
        where: { paymentId },
        orderBy: { createdAt: 'desc' },
      });

      return (history).map((entry) => ({
        status: (entry).status,
        method: (entry).method,
        details: (entry).details as Record<string, unknown>,
        createdAt: (entry).createdAt.toISOString(),
      }));
    } catch(error) {
      (logger).error('Error getting verification history', { error, paymentId });
      throw new Error('Failed to get verification history');
    }
  }
}
