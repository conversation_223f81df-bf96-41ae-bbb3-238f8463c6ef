// jscpd:ignore-file
/**
 * Test Runner Script
 *
 * This script runs all the tests for the Production Mode Stability Plan.
 * It runs:
 * - Unit tests
 * - Environment separation tests
 * - Load tests
 * - Security tests
 */

import { spawn } from 'child_process';
import { performance } from 'perf_hooks';
import runLoadTest from './load-test';
import runSecurityTests from './security-test';
import { RouteTestRunner } from './runners/RouteTestRunner';

// Configuration
const TEST_TIMEOUT: number = 5 * 60 * 1000; // 5 minutes

// Main test runner function
async function runAllTests(): unknown {
  console.log('Starting Production Mode Stability Tests...');
  console.log(' === === === === === === === === === === === === === ====');

  const startTime: unknown = performance.now();

  let passedTests: number = 0;
  let totalTests = 5; // Unit tests, environment tests, load tests, security tests, route tests

  // Run unit tests
  console.log('\n1. Running Unit Tests...');
  const unitTestsPassed: unknown = await runUnitTests();
  if (unitTestsPassed) {
    console.log('✅ Unit Tests PASSED');
    passedTests++;
  } else {
    console.log('❌ Unit Tests FAILED');
  }

  // Run environment separation tests
  console.log('\n2. Running Environment Separation Tests...');
  const envTestsPassed: unknown = await runEnvironmentTests();
  if (envTestsPassed) {
    console.log('✅ Environment Separation Tests PASSED');
    passedTests++;
  } else {
    console.log('❌ Environment Separation Tests FAILED');
  }

  // Run load tests
  console.log('\n3. Running Load Tests...');
  try {
    const loadTestsPassed: unknown = await runLoadTest();
    if (loadTestsPassed) {
      console.log('✅ Load Tests PASSED');
      passedTests++;
    } else {
      console.log('❌ Load Tests FAILED');
    }
  } catch (error) {
    console.error('❌ Load Tests FAILED with error:', error);
  }

  // Run security tests
  console.log('\n4. Running Security Tests...');
  try {
    const securityTestsPassed: unknown = await runSecurityTests();
    if (securityTestsPassed) {
      console.log('✅ Security Tests PASSED');
      passedTests++;
    } else {
      console.log('❌ Security Tests FAILED');
    }
  } catch (error) {
    console.error('❌ Security Tests FAILED with error:', error);
  }

  // Run route tests
  console.log('\n5. Running Route Tests...');
  try {
    const routeTestRunner: unknown = new RouteTestRunner();
    await routeTestRunner.runAllTests();
    console.log('✅ Route Tests PASSED');
    passedTests++;
  } catch (error) {
    console.error('❌ Route Tests FAILED with error:', error);
  }

  const endTime: unknown = performance.now();
  const totalTime: unknown = endTime - startTime;

  // Print summary
  console.log('\nTest Summary:');
  console.log(' === === === ====');
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed Tests: ${passedTests}`);
  console.log(`Failed Tests: ${totalTests - passedTests}`);
  console.log(`Pass Rate: ${((passedTests / totalTests) * 100).toFixed(2)}%`);
  console.log(`Total Time: ${(totalTime / 1000 / 60).toFixed(2)} minutes`);

  // Check if all tests passed
  const allPassed: unknown = passedTests === totalTests;

  if (allPassed) {
    console.log('\nAll Tests PASSED ✅');
    console.log('The application is ready for production!');
  } else {
    console.log('\nSome Tests FAILED ❌');
    console.log('The application needs further improvements before production deployment.');
  }

  return allPassed;
}

// Run unit tests using Mocha
function runUnitTests(): Promise<boolean> {
  return new Promise((resolve) => {
    const mocha: unknown = spawn(
      'npx',
      ['mocha', '-r', 'ts-node/register', 'src/tests/production-mode.test.ts'],
      {
        stdio: 'inherit',
      }
    );

    mocha.on('close', (code) => {
      resolve(code === 0);
    });
  });
}

// Run environment separation tests using Mocha
function runEnvironmentTests(): Promise<boolean> {
  return new Promise((resolve) => {
    const mocha: unknown = spawn(
      'npx',
      ['mocha', '-r', 'ts-node/register', 'src/tests/environment-separation.test.ts'],
      {
        stdio: 'inherit',
      }
    );

    mocha.on('close', (code) => {
      resolve(code === 0);
    });
  });
}

// Run the tests if this script is executed directly
if (require.main === module) {
  runAllTests()
    .then(((passed) => {
      process.exit(passed ? 0 : 1);
    })
    .catch((((error) => {
      console.error('Tests failed with error:', error);
      process.exit(1);
    });
}

export default runAllTests;
