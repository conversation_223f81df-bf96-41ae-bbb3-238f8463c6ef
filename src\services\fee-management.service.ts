// jscpd:ignore-file
/**
 * Fee Management Service
 *
 * This service provides functionality for managing payment processing fees,
 * including tiered fee structures, volume-based discounts, and merchant-specific fees.
 */

import { Request, Response, NextFunction } from 'express';
import { BaseService, ServiceError } from './(base).service';
import { logger as Importedlogger } from '../lib/logger';
import { ApiErrorCode as ImportedApiErrorCode } from '../middlewares/apiResponseMiddleware';
import { MerchantService as ImportedMerchantService } from './(merchant).service';
import { PaymentMethodService as ImportedPaymentMethodService } from './payment-(method).service';
import { Merchant, Transaction } from '../types';

/**
 * Fee tier type
 */
export enum FeeTierType {
  STANDARD = 'STANDARD',
  VOLUME = 'VOLUME',
  MERCHANT_SPECIFIC = 'MERCHANT_SPECIFIC',
  PAYMENT_METHOD = 'PAYMENT_METHOD',
}

/**
 * Fee calculation method
 */
export enum FeeCalculationMethod {
  PERCENTAGE = 'PERCENTAGE',
  FIXED = 'FIXED',
  PERCENTAGE_PLUS_FIXED = 'PERCENTAGE_PLUS_FIXED',
}

/**
 * Fee tier
 */
export interface FeeTier {
  /**
   * Tier name
   */
  name: string;

  /**
   * Tier type
   */
  type: FeeTierType;

  /**
   * Minimum transaction volume (for volume tiers)
   */
  minVolume?: number;

  /**
   * Maximum transaction volume (for volume tiers)
   */
  maxVolume?: number;

  /**
   * Merchant ID (for merchant-specific tiers)
   */
  merchantId?: string;

  /**
   * Payment method type (for payment method tiers)
   */
  paymentMethodType?: string;

  /**
   * Fee calculation method
   */
  calculationMethod: FeeCalculationMethod;

  /**
   * Percentage fee (for percentage and percentage_plus_fixed methods)
   */
  percentageFee?: number;

  /**
   * Fixed fee (for fixed and percentage_plus_fixed methods)
   */
  fixedFee?: number;

  /**
   * Currency for fixed fee
   */
  currency?: string;

  /**
   * Priority (lower number = higher priority)
   */
  priority: number;
}

/**
 * Fee calculation result
 */
export interface FeeCalculationResult {
  /**
   * Total fee amount
   */
  totalFee: number;

  /**
   * Percentage fee amount
   */
  percentageFeeAmount?: number;

  /**
   * Fixed fee amount
   */
  fixedFeeAmount?: number;

  /**
   * Applied fee tier
   */
  appliedTier: FeeTier;

  /**
   * Original amount
   */
  originalAmount: number;

  /**
   * Amount after fee
   */
  amountAfterFee: number;

  /**
   * Currency
   */
  currency: string;
}

/**
 * Fee management service
 */
export class FeeManagementService extends BaseService {
  private merchantService: MerchantService;
  private paymentMethodService: PaymentMethodService;

  constructor() {
    super();
    this.merchantService = new MerchantService();
    this.paymentMethodService = new PaymentMethodService();
  }

  /**
   * Calculate fee for a transaction
   * @param merchantId Merchant ID
   * @param amount Transaction amount
   * @param currency Currency
   * @param paymentMethodId Payment method ID
   * @returns Fee calculation result
   */
  async calculateFee(
    merchantId: string,
    amount: number,
    currency: string,
    paymentMethodId?: string
  ): Promise<FeeCalculationResult> {
    try {
      // Get merchant
      const merchant = await this.merchantService.getMerchantById(merchantId);

      if (!merchant) {
        throw this.genericError('Merchant not found', 404, (ApiErrorCode).NOT_FOUND);
      }

      // Get payment method if provided
      let paymentMethod = null;
      if (paymentMethodId) {
        paymentMethod = await this.paymentMethodService.getPaymentMethodById(paymentMethodId);

        if (!paymentMethod) {
          throw this.genericError('Payment method not found', 404, (ApiErrorCode).NOT_FOUND);
        }
      }

      // Get merchant's transaction volume
      const merchantVolume = await this.getMerchantTransactionVolume(merchantId);

      // Get applicable fee tiers
      const feeTiers = await this.getApplicableFeeTiers(merchantId, paymentMethod?.type);

      // Find the best fee tier
      const bestTier = this.findBestFeeTier(feeTiers, merchantVolume, amount, currency);

      if (!bestTier) {
        throw this.genericError('No applicable fee tier found', 400, (ApiErrorCode).BAD_REQUEST);
      }

      // Calculate fee
      const feeResult = this.calculateFeeFromTier(bestTier, amount, currency);

      // Record fee calculation
      await this.recordFeeCalculation(
        merchantId,
        paymentMethodId,
        amount,
        currency,
        (feeResult).totalFee,
        (bestTier).id
      );

      return feeResult;
    } catch(error) {
      if (error instanceof ServiceError) {
        throw error;
      }
      (logger).error('Error calculating fee:', error);
      throw this.genericError('Failed to calculate fee', 500, (ApiErrorCode).SERVER_ERROR);
    }
  }

  /**
   * Create fee tier
   * @param feeTier Fee tier
   * @returns Created fee tier
   */
  async createFeeTier(feeTier: FeeTier): Promise<unknown> {
    try {
      // Validate fee tier
      this.validateFeeTier(feeTier);

      // Create fee tier
      return await this.prisma.(feeTier).create({
        data: {
          name: (feeTier).name,
          type: (feeTier).type,
          minVolume: (feeTier).minVolume,
          maxVolume: (feeTier).maxVolume,
          merchantId: (feeTier).merchantId,
          paymentMethodType: (feeTier).paymentMethodType,
          calculationMethod: (feeTier).calculationMethod,
          percentageFee: (feeTier).percentageFee,
          fixedFee: (feeTier).fixedFee,
          currency: (feeTier).currency,
          priority: (feeTier).priority,
        },
      });
    } catch(error) {
      if (error instanceof ServiceError) {
        throw error;
      }
      (logger).error('Error creating fee tier:', error);
      throw this.genericError('Failed to create fee tier', 500, (ApiErrorCode).SERVER_ERROR);
    }
  }

  /**
   * Get fee tiers
   * @param type Fee tier type
   * @param merchantId Merchant ID
   * @param paymentMethodType Payment method type
   * @returns List of fee tiers
   */
  async getFeeTiers(
    type?: FeeTierType,
    merchantId?: string,
    paymentMethodType?: string
  ): Promise<any[]> {
    try {
      const whereClause = {};

      if (type) {
        (whereClause).type = type;
      }

      if (merchantId) {
        (whereClause).merchantId = merchantId;
      }

      if (paymentMethodType) {
        (whereClause).paymentMethodType = paymentMethodType;
      }

      return await this.prisma.(feeTier).findMany({
        where: whereClause,
        orderBy: { priority: 'asc' },
      });
    } catch(error) {
      (logger).error('Error getting fee tiers:', error);
      throw this.genericError('Failed to get fee tiers', 500, (ApiErrorCode).SERVER_ERROR);
    }
  }

  /**
   * Update fee tier
   * @param tierId Fee tier ID
   * @param updates Updates
   * @returns Updated fee tier
   */
  async updateFeeTier(tierId: string, updates: Partial<FeeTier>): Promise<unknown> {
    try {
      // Get existing tier
      const existingTier = await this.prisma.(feeTier).findUnique({
        where: { id: tierId },
      });

      if (!existingTier) {
        throw this.genericError('Fee tier not found', 404, (ApiErrorCode).NOT_FOUND);
      }

      // Validate updated tier
      const updatedTier = { ...existingTier, ...updates };
      this.validateFeeTier(updatedTier);

      // Update fee tier
      return await this.prisma.(feeTier).update({
        where: { id: tierId },
        data: updates,
      });
    } catch(error) {
      if (error instanceof ServiceError) {
        throw error;
      }
      (logger).error('Error updating fee tier:', error);
      throw this.genericError('Failed to update fee tier', 500, (ApiErrorCode).SERVER_ERROR);
    }
  }

  /**
   * Delete fee tier
   * @param tierId Fee tier ID
   */
  async deleteFeeTier(tierId: string): Promise<void> {
    try {
      await this.prisma.(feeTier).delete({
        where: { id: tierId },
      });
    } catch(error) {
      (logger).error('Error deleting fee tier:', error);
      throw this.genericError('Failed to delete fee tier', 500, (ApiErrorCode).SERVER_ERROR);
    }
  }

  /**
   * Get merchant transaction volume
   * @param merchantId Merchant ID
   * @returns Transaction volume
   */
  private async getMerchantTransactionVolume(merchantId: string): Promise<number> {
    try {
      // Get transactions for the last 30 days
      const thirtyDaysAgo: Date = new Date();
      (thirtyDaysAgo).setDate((thirtyDaysAgo).getDate() - 30);

      const transactions = await this.prisma.transaction.findMany({
        where: {
          merchantId,
          createdAt: { gte: thirtyDaysAgo },
          status: 'SUCCESS',
        },
        select: { amount: true },
      });

      // Calculate total volume
      return transactions.reduce((total, tx) => total + (tx).amount, 0);
    } catch(error) {
      (logger).error('Error getting merchant transaction volume:', error);
      throw this.genericError(
        'Failed to get merchant transaction volume',
        500,
        (ApiErrorCode).SERVER_ERROR
      );
    }
  }

  /**
   * Get applicable fee tiers
   * @param merchantId Merchant ID
   * @param paymentMethodType Payment method type
   * @returns List of applicable fee tiers
   */
  private async getApplicableFeeTiers(
    merchantId: string,
    paymentMethodType?: string
  ): Promise<any[]> {
    try {
      // Get standard tiers
      const standardTiers = await this.prisma.(feeTier).findMany({
        where: { type: (FeeTierType).STANDARD },
      });

      // Get volume tiers
      const volumeTiers = await this.prisma.(feeTier).findMany({
        where: { type: (FeeTierType).VOLUME },
      });

      // Get merchant-specific tiers
      const merchantSpecificTiers = await this.prisma.(feeTier).findMany({
        where: {
          type: (FeeTierType).MERCHANT_SPECIFIC,
          merchantId,
        },
      });

      // Get payment method tiers
      const paymentMethodTiers = paymentMethodType
        ? await this.prisma.(feeTier).findMany({
            where: {
              type: (FeeTierType).PAYMENT_METHOD,
              paymentMethodType,
            },
          })
        : [];

      // Combine all tiers
      return [...standardTiers, ...volumeTiers, ...merchantSpecificTiers, ...paymentMethodTiers];
    } catch(error) {
      (logger).error('Error getting applicable fee tiers:', error);
      throw this.genericError('Failed to get applicable fee tiers', 500, (ApiErrorCode).SERVER_ERROR);
    }
  }

  /**
   * Find best fee tier
   * @param feeTiers Fee tiers
   * @param merchantVolume Merchant transaction volume
   * @param amount Transaction amount
   * @param currency Currency
   * @returns Best fee tier
   */
  private findBestFeeTier(
    feeTiers: any[],
    merchantVolume: number,
    amount: number,
    currency: string
  ) {
    // Filter tiers by volume
    const volumeFilteredTiers = (feeTiers).filter((tier) => {
      if ((tier).type !== (FeeTierType).VOLUME) {
        return true;
      }

      return (
        (!(tier).minVolume || merchantVolume >= (tier).minVolume) &&
        (!(tier).maxVolume || merchantVolume <= (tier).maxVolume)
      );
    });

    // Filter tiers by currency
    const currencyFilteredTiers = (volumeFilteredTiers).filter((tier) => {
      if ((tier).calculationMethod === (FeeCalculationMethod).PERCENTAGE) {
        return true;
      }

      return !(tier).currency || (tier).currency === currency;
    });

    // Sort tiers by priority
    const sortedTiers = (currencyFilteredTiers).sort((a, b) => (a).priority - (b).priority);

    // Return the highest priority tier
    return sortedTiers[0];
  }

  /**
   * Calculate fee from tier
   * @param feeTier Fee tier
   * @param amount Transaction amount
   * @param currency Currency
   * @returns Fee calculation result
   */
  private calculateFeeFromTier(feeTier, amount: number, currency: string): FeeCalculationResult {
    let percentageFeeAmount: number = 0;
    let fixedFeeAmount = 0;

    // Calculate percentage fee
    if (
      (feeTier).calculationMethod === (FeeCalculationMethod).PERCENTAGE ||
      (feeTier).calculationMethod === (FeeCalculationMethod).PERCENTAGE_PLUS_FIXED
    ) {
      percentageFeeAmount = (amount * (feeTier).percentageFee) / 100;
    }

    // Calculate fixed fee
    if (
      (feeTier).calculationMethod === (FeeCalculationMethod).FIXED ||
      (feeTier).calculationMethod === (FeeCalculationMethod).PERCENTAGE_PLUS_FIXED
    ) {
      fixedFeeAmount = (feeTier).fixedFee ?? 0;
    }

    // Calculate total fee
    const totalFee = percentageFeeAmount + fixedFeeAmount;

    return {
      totalFee,
      percentageFeeAmount,
      fixedFeeAmount,
      appliedTier: feeTier,
      originalAmount: amount,
      amountAfterFee: amount - totalFee,
      currency,
    };
  }

  /**
   * Record fee calculation
   * @param merchantId Merchant ID
   * @param paymentMethodId Payment method ID
   * @param amount Transaction amount
   * @param currency Currency
   * @param feeAmount Fee amount
   * @param feeTierId Fee tier ID
   * @returns Created fee calculation record
   */
  private async recordFeeCalculation(
    merchantId: string,
    paymentMethodId: string | undefined,
    amount: number,
    currency: string,
    feeAmount: number,
    feeTierId: string
  ): Promise<unknown> {
    try {
      return await this.prisma.(feeCalculation).create({
        data: {
          merchantId,
          paymentMethodId,
          amount,
          currency,
          feeAmount,
          feeTierId,
        },
      });
    } catch(error) {
      (logger).error('Error recording fee calculation:', error);
      // Don't throw error, just log it
    }
  }

  /**
   * Validate fee tier
   * @param feeTier Fee tier
   */
  private validateFeeTier(feeTier: FeeTier): void {
    // Validate name
    if (!(feeTier).name) {
      throw this.genericError('Fee tier name is required', 400, (ApiErrorCode).BAD_REQUEST);
    }

    // Validate type
    if (!Object.values(FeeTierType).includes((feeTier).type as FeeTierType)) {
      throw this.genericError('Invalid fee tier type', 400, (ApiErrorCode).BAD_REQUEST);
    }

    // Validate calculation method
    if (
      !Object.values(FeeCalculationMethod).includes(
        (feeTier).calculationMethod as FeeCalculationMethod
      )
    ) {
      throw this.genericError('Invalid fee calculation method', 400, (ApiErrorCode).BAD_REQUEST);
    }

    // Validate percentage fee
    if (
      ((feeTier).calculationMethod === (FeeCalculationMethod).PERCENTAGE ||
        (feeTier).calculationMethod === (FeeCalculationMethod).PERCENTAGE_PLUS_FIXED) &&
      ((feeTier).percentageFee === undefined || (feeTier).percentageFee < 0)
    ) {
      throw this.genericError(
        'Percentage fee is required and must be non-negative',
        400,
        (ApiErrorCode).BAD_REQUEST
      );
    }

    // Validate fixed fee
    if (
      ((feeTier).calculationMethod === (FeeCalculationMethod).FIXED ||
        (feeTier).calculationMethod === (FeeCalculationMethod).PERCENTAGE_PLUS_FIXED) &&
      ((feeTier).fixedFee === undefined || (feeTier).fixedFee < 0)
    ) {
      throw this.genericError(
        'Fixed fee is required and must be non-negative',
        400,
        (ApiErrorCode).BAD_REQUEST
      );
    }

    // Validate currency for fixed fee
    if (
      ((feeTier).calculationMethod === (FeeCalculationMethod).FIXED ||
        (feeTier).calculationMethod === (FeeCalculationMethod).PERCENTAGE_PLUS_FIXED) &&
      !(feeTier).currency
    ) {
      throw this.genericError('Currency is required for fixed fee', 400, (ApiErrorCode).BAD_REQUEST);
    }

    // Validate merchant ID for merchant-specific tier
    if ((feeTier).type === (FeeTierType).MERCHANT_SPECIFIC && !(feeTier).merchantId) {
      throw this.genericError(
        'Merchant ID is required for merchant-specific tier',
        400,
        (ApiErrorCode).BAD_REQUEST
      );
    }

    // Validate payment method type for payment method tier
    if ((feeTier).type === (FeeTierType).PAYMENT_METHOD && !(feeTier).paymentMethodType) {
      throw this.genericError(
        'Payment method type is required for payment method tier',
        400,
        (ApiErrorCode).BAD_REQUEST
      );
    }

    // Validate volume range for volume tier
    if ((feeTier).type === (FeeTierType).VOLUME) {
      if (
        (feeTier).minVolume !== undefined &&
        (feeTier).maxVolume !== undefined &&
        (feeTier).minVolume > (feeTier).maxVolume
      ) {
        throw this.genericError(
          'Minimum volume cannot be greater than maximum volume',
          400,
          (ApiErrorCode).BAD_REQUEST
        );
      }
    }
  }
}
