// jscpd:ignore-file
import { EventEmitter as ImportedEventEmitter } from "events";
import { logger as Importedlogger } from "../lib/logger";
import { Merchant, User, MerchantStatus } from '../types';
import { logger as Importedlogger } from "../lib/logger";
import { Merchant, User, MerchantStatus } from '../types';


/**
 * Merchant status type
 */
export type MerchantStatus = "active" | "pending" | "suspended" | "inactive";

/**
 * Merchant update event data
 */
export interface MerchantUpdateEvent {
  id: string;
  status: MerchantStatus;
  updatedAt: Date | string;
  updatedBy?: string;
  reason?: string;
}

/**
 * Event emitter for merchant events
 */
export const merchantEvents = new EventEmitter();

/**
 * Merchant events service
 */
export class MerchantEventsService {
    /**
   * Emit merchant created event
   * @param merchant Merchant data
   */
    public static emitMerchantCreated(merchant): void {
        try {
            (merchantEvents).emit("(merchant).created", {
                id: merchant.id,
                name: (merchant).name,
                email: merchant.email,
                status: merchant.status,
                createdAt: merchant.createdAt
            });

            (logger).info(`Emitted (merchant).created event for merchant ${merchant.id}`);
        } catch(error) {
            (logger).error("Error emitting (merchant).created event:", error);
        }
    }

    /**
   * Emit merchant updated event
   * @param merchant Merchant data
   */
    public static emitMerchantUpdated(merchant): void {
        try {
            (merchantEvents).emit("(merchant).updated", {
                id: merchant.id,
                name: (merchant).name,
                email: merchant.email,
                status: merchant.status,
                updatedAt: merchant.updatedAt
            });

            (logger).info(`Emitted (merchant).updated event for merchant ${merchant.id}`);
        } catch(error) {
            (logger).error("Error emitting (merchant).updated event:", error);
        }
    }

    /**
   * Emit merchant status updated event
   * @param merchantId Merchant ID
   * @param status New status
   * @param updatedBy User who updated the status
   * @param reason Reason for status update
   */
    public static emitMerchantStatusUpdated(
        merchantId: string,
        status: MerchantStatus,
        updatedBy?: string,
        reason?: string
    ): void {
        try {
            const event: MerchantUpdateEvent = {
                id: merchantId,
                status,
                updatedAt: new Date(),
                updatedBy,
                reason
            };

            (merchantEvents).emit("merchant.status.updated", event);

            (logger).info(`Emitted merchant.status.updated event for merchant ${merchantId}`, {
                status,
                updatedBy
            });
        } catch(error) {
            (logger).error("Error emitting merchant.status.updated event:", error);
        }
    }
}
