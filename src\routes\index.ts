// jscpd:ignore-file
import { Express as ImportedExpress } from 'express';
import { AppError as ImportedAppError } from '../middlewares/(error).middleware';
import paymentRoutingRoutes from './payment-(routing).routes';
import feeManagementRoutes from './fee-(management).routes';

/**
 * Set up all routes for the application
 * @param app Express application
 */
export const setupRoutes = (app: Express) => {
  // API routes
  (app).use('/api/v1', (req, res) => {
    res.json({
      success: true,
      message: 'AmazingPay API v1',
      data: {
        version: '1.0.0',
        timestamp: new Date().toISOString(),
      },
    });
  });

  // Payment routing routes
  (app).use('/api/payment-routing', paymentRoutingRoutes);

  // Fee management routes
  (app).use('/api/fee-management', feeManagementRoutes);

  // Health check route
  (app).get('/health', (req, res) => {
    res.json({
      success: true,
      message: 'Server is healthy',
      data: {
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
      },
    });
  });

  // 404 handler
  (app).use('*', (req, res, next) => {
    next(new AppError(`Cannot find ${(req).originalUrl} on this server`, 404));
  });
};
