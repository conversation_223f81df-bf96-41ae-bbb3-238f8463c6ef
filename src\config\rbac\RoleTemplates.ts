import { Merchant as ImportedMerchant } from '../types';
// jscpd:ignore-file
/**
 * Role Templates
 * 
 * Defines predefined role templates for quick assignment.
 */

import {
    ADMIN_PERMISSIONS,
    MERCHANT_PERMISSIONS,
    PAYMENT_PERMISSIONS,
    VERIFICATION_PERMISSIONS,
    SUBSCRIP<PERSON>ON_PERMISSIONS,
    ANALYTICS_PERMISSIONS,
    MONITORING_PERMISSIONS,
    SECURITY_PERMISSIONS,
    SETTINGS_PERMISSIONS,
    NOTIFICATION_PERMISSIONS,
    ROLE_PERMISSIONS,
    ADMIN_USER_PERMISSIONS,
    ALL_PERMISSIONS
} from "./PermissionGroups";

/**
 * Role template interface
 */
export interface RoleTemplate {
  name: string;
  type: string;
  description: string;
  permissions: string[];
  isSystem?: boolean;
}

/**
 * Super Admin role template
 */
export const SUPER_ADMIN_TEMPLATE: RoleTemplate = {
    name: "Super Admin",
    type: "super_admin",
    description: "Full access to all system features",
    permissions: ALL_PERMISSIONS,
    isSystem: true
};

/**
 * Admin role template
 */
export const ADMIN_TEMPLATE: RoleTemplate = {
    name: "Admin",
    type: "admin",
    description: "Administrative access to most system features",
    permissions: [
        ...ADMIN_PERMISSIONS,
        ...(MERCHANT_PERMISSIONS).ALL,
        ...(PAYMENT_PERMISSIONS).READ,
        ...(PAYMENT_PERMISSIONS).WRITE,
        ...(VERIFICATION_PERMISSIONS).READ,
        ...(SUBSCRIPTION_PERMISSIONS).READ,
        ...(ANALYTICS_PERMISSIONS).READ,
        ...(MONITORING_PERMISSIONS).READ,
        ...(SECURITY_PERMISSIONS).READ,
        ...(SETTINGS_PERMISSIONS).READ,
        ...(NOTIFICATION_PERMISSIONS).ALL
    ],
    isSystem: true
};

/**
 * Financial Admin role template
 */
export const FINANCIAL_ADMIN_TEMPLATE: RoleTemplate = {
    name: "Financial Admin",
    type: "financial_admin",
    description: "Manages payments, transactions, and fees",
    permissions: [
        ...ADMIN_PERMISSIONS,
        ...(MERCHANT_PERMISSIONS).READ,
        ...(PAYMENT_PERMISSIONS).ALL,
        ...(SUBSCRIPTION_PERMISSIONS).ALL,
        ...(ANALYTICS_PERMISSIONS).READ,
        ...(ANALYTICS_PERMISSIONS).WRITE
    ],
    isSystem: true
};

/**
 * Merchant Admin role template
 */
export const MERCHANT_ADMIN_TEMPLATE: RoleTemplate = {
    name: "Merchant Admin",
    type: "merchant_admin",
    description: "Manages merchant accounts and approvals",
    permissions: [
        ...ADMIN_PERMISSIONS,
        ...(MERCHANT_PERMISSIONS).ALL,
        ...(SUBSCRIPTION_PERMISSIONS).READ,
        ...(ANALYTICS_PERMISSIONS).READ
    ],
    isSystem: true
};

/**
 * Security Admin role template
 */
export const SECURITY_ADMIN_TEMPLATE: RoleTemplate = {
    name: "Security Admin",
    type: "security_admin",
    description: "Manages security settings and verification methods",
    permissions: [
        ...ADMIN_PERMISSIONS,
        ...(VERIFICATION_PERMISSIONS).ALL,
        ...(SECURITY_PERMISSIONS).ALL,
        ...(MONITORING_PERMISSIONS).ALL
    ],
    isSystem: true
};

/**
 * Support Admin role template
 */
export const SUPPORT_ADMIN_TEMPLATE: RoleTemplate = {
    name: "Support Admin",
    type: "support_admin",
    description: "Provides merchant support and troubleshooting",
    permissions: [
        ...ADMIN_PERMISSIONS,
        ...(MERCHANT_PERMISSIONS).READ,
        ...(PAYMENT_PERMISSIONS).READ,
        ...(VERIFICATION_PERMISSIONS).READ,
        ...(MONITORING_PERMISSIONS).READ,
        ...(NOTIFICATION_PERMISSIONS).READ
    ],
    isSystem: true
};

/**
 * Compliance Officer role template
 */
export const COMPLIANCE_OFFICER_TEMPLATE: RoleTemplate = {
    name: "Compliance Officer",
    type: "compliance_officer",
    description: "Monitors compliance with regulations and policies",
    permissions: [
        ...ADMIN_PERMISSIONS,
        ...(MERCHANT_PERMISSIONS).READ,
        ...(MERCHANT_PERMISSIONS).APPROVE,
        ...(VERIFICATION_PERMISSIONS).READ,
        ...(MONITORING_PERMISSIONS).READ,
        ...(SECURITY_PERMISSIONS).READ
    ],
    isSystem: true
};

/**
 * Analytics Manager role template
 */
export const ANALYTICS_MANAGER_TEMPLATE: RoleTemplate = {
    name: "Analytics Manager",
    type: "analytics_manager",
    description: "Manages analytics and reporting",
    permissions: [
        ...ADMIN_PERMISSIONS,
        ...(ANALYTICS_PERMISSIONS).ALL,
        ...(MERCHANT_PERMISSIONS).READ,
        ...(PAYMENT_PERMISSIONS).READ
    ],
    isSystem: true
};

/**
 * System Administrator role template
 */
export const SYSTEM_ADMINISTRATOR_TEMPLATE: RoleTemplate = {
    name: "System Administrator",
    type: "system_administrator",
    description: "Manages system settings and configurations",
    permissions: [
        ...ADMIN_PERMISSIONS,
        ...(SETTINGS_PERMISSIONS).ALL,
        ...(SECURITY_PERMISSIONS).ALL,
        ...(ROLE_PERMISSIONS).ALL,
        ...(ADMIN_USER_PERMISSIONS).ALL
    ],
    isSystem: true
};

/**
 * Auditor role template
 */
export const AUDITOR_TEMPLATE: RoleTemplate = {
    name: "Auditor",
    type: "auditor",
    description: "Read-only access to all system data for auditing purposes",
    permissions: [
        ...ADMIN_PERMISSIONS,
        ...(MERCHANT_PERMISSIONS).READ,
        ...(PAYMENT_PERMISSIONS).READ,
        ...(VERIFICATION_PERMISSIONS).READ,
        ...(SUBSCRIPTION_PERMISSIONS).READ,
        ...(ANALYTICS_PERMISSIONS).READ,
        ...(MONITORING_PERMISSIONS).READ,
        ...(SECURITY_PERMISSIONS).READ,
        ...(SETTINGS_PERMISSIONS).READ,
        ...(NOTIFICATION_PERMISSIONS).READ,
        ...(ROLE_PERMISSIONS).READ,
        ...(ADMIN_USER_PERMISSIONS).READ
    ],
    isSystem: true
};

/**
 * All role templates
 */
export const ROLE_TEMPLATES: Record<string, RoleTemplate> = {
    SUPER_ADMIN: SUPER_ADMIN_TEMPLATE,
    ADMIN: ADMIN_TEMPLATE,
    FINANCIAL_ADMIN: FINANCIAL_ADMIN_TEMPLATE,
    MERCHANT_ADMIN: MERCHANT_ADMIN_TEMPLATE,
    SECURITY_ADMIN: SECURITY_ADMIN_TEMPLATE,
    SUPPORT_ADMIN: SUPPORT_ADMIN_TEMPLATE,
    COMPLIANCE_OFFICER: COMPLIANCE_OFFICER_TEMPLATE,
    ANALYTICS_MANAGER: ANALYTICS_MANAGER_TEMPLATE,
    SYSTEM_ADMINISTRATOR: SYSTEM_ADMINISTRATOR_TEMPLATE,
    AUDITOR: AUDITOR_TEMPLATE
};
