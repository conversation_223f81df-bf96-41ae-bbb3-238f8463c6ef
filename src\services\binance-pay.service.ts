// jscpd:ignore-file
/**
 * Binance Pay Service
 *
 * Provides functionality for interacting with Binance Pay API
 */

import axios from 'axios';
import crypto from 'crypto';
import { AppError } from '../utils/errors/AppError';
import { logger } from '../utils/logger';
import { logger } from '../utils/logger';

const BINANCE_PAY_API_URL: string ="https://bpay.binanceapi.com";
const BINANCE_PAY_TEST_API_URL: string ="https://bpay-sandbox.binanceapi.com";

// Use test API in development mode
const BASE_URL: unknown = process.env.NODE_ENV === "production"
    ? BINANCE_PAY_API_URL
    : BINANCE_PAY_TEST_API_URL;

/**
 * Binance Pay service
 */
export const BinancePayService: unknown = {
    /**
     * Generate signature for Binance Pay API requests
     */
    generateSignature(timestamp: number, nonce: string, payload: string, apiSecret: string): string {
        const message = timestamp + "\n" + nonce + "\n" + payload + "\n";
        return crypto
            .createHmac("sha512", apiSecret)
            .update(message)
            .digest("hex")
            .toUpperCase();
    },

    /**
     * Create headers for Binance Pay API requests
     */
    createHeaders(apiKey: string, timestamp: number, nonce: string, signature: string): Record<string, string> {
        return {
            "BinancePay-Certificate-SN": apiKey,
            "BinancePay-Timestamp": timestamp.toString(),
            "BinancePay-Nonce": nonce,
            "BinancePay-Signature": signature,
            "Content-Type": "application/json"
        };
    },

    /**
     * Create a payment order
     */
    async createOrder(
        apiKey: string,
        apiSecret: string,
        merchantId: string,
        merchantTradeNo: string,
        amount: number,
        currency: string,
        returnUrl: string,
        cancelUrl: string
    ) {
        try {
            const timestamp: unknown = Date.now();
            const nonce: unknown = crypto.randomBytes(16).toString("hex");

            const payload: Record<string, unknown> = {
                env: { terminalType: "WEB"
                },
                merchantTradeNo,
                orderAmount: amount,
                currency,
                goods: { goodsType: "VIRTUAL_GOODS",
                    goodsCategory: "PAYMENT",
                    referenceGoodsId: merchantTradeNo,
                    goodsName: "Payment",
                    goodsDetail: `Payment of ${amount} ${currency}`
                },
                returnUrl,
                cancelUrl
            };

            const stringPayload: unknown = JSON.stringify(payload);
            const signature: unknown = this.generateSignature(timestamp, nonce, stringPayload, apiSecret);

            const response: unknown = await axios.post(
                `${BASE_URL}/binancepay/openapi/order`,
                payload,
                { headers: this.createHeaders(apiKey, timestamp, nonce, signature) }
            );

            if (response.data.status !== "SUCCESS") {
                throw new AppError(response.data.errorMessage || "Failed to create Binance Pay order", 500);
            }

            return response.data.data;
        } catch (error: Error) {
            logger.error("Binance Pay API error (createOrder):", error.response?.data || (error as Error).message);
            throw new AppError(error.response?.data?.errorMessage || "Failed to create Binance Pay order", 500);
        }
    },

    /**
     * Query order status
     */
    async queryOrder(apiKey: string, apiSecret: string, merchantTradeNo: string) {
        try {
            const timestamp: unknown = Date.now();
            const nonce: unknown = crypto.randomBytes(16).toString("hex");

            const payload: Record<string, unknown> = {
                merchantTradeNo
            };

            const stringPayload: unknown = JSON.stringify(payload);
            const signature: unknown = this.generateSignature(timestamp, nonce, stringPayload, apiSecret);

            const response: unknown = await axios.post(
                `${BASE_URL}/binancepay/openapi/order/query`,
                payload,
                { headers: this.createHeaders(apiKey, timestamp, nonce, signature) }
            );

            if (response.data.status !== "SUCCESS") {
                throw new AppError(response.data.errorMessage || "Failed to query Binance Pay order", 500);
            }

            return response.data.data;
        } catch (error: Error) {
            logger.error("Binance Pay API error (queryOrder):", error.response?.data || (error as Error).message);
            throw new AppError(error.response?.data?.errorMessage || "Failed to query Binance Pay order", 500);
        }
    },

    /**
     * Verify webhook signature
     */
    verifyWebhookSignature(payload: Record<string, unknown>, signature: string, apiSecret: string): boolean {
        try {
            const { data, timestamp, nonce } = payload;

            const stringData: unknown =typeof data === "string" ? data : JSON.stringify(data);
            const message: unknown =timestamp + "\n" + nonce + "\n" + stringData + "\n";

            const expectedSignature: unknown =crypto
                .createHmac("sha512", apiSecret)
                .update(message)
                .digest("hex")
                .toUpperCase();

            return expectedSignature === signature;
        } catch (error) {
            logger.error("Error verifying webhook signature:", error);
            return false;
        }
    },

    /**
     * Test API connection
     */
    async testConnection(apiKey: string, apiSecret: string) {
        try {
            const timestamp: unknown = Date.now();
            const nonce: unknown = crypto.randomBytes(16).toString("hex");

            const payload: Record<string, unknown> = {
                merchantTradeNo: "TEST_CONNECTION_" + Date.now()
            };

            const stringPayload: unknown = JSON.stringify(payload);
            const signature: unknown = this.generateSignature(timestamp, nonce, stringPayload, apiSecret);

            const response: unknown = await axios.post(
                `${BASE_URL}/binancepay/openapi/certificates`,
                payload,
                { headers: this.createHeaders(apiKey, timestamp, nonce, signature) }
            );

            return {
                success: response.data.status === "SUCCESS",
                message: response.data.status === "SUCCESS" ? "Connection successful" : response.data.errorMessage
            };
        } catch (error: Error) {
            logger.error("Binance Pay API error (testConnection):", error.response?.data || (error as Error).message);
            return {
                success: false,
                message: error.response?.data?.errorMessage || "Connection failed"
            };
        }
    }
};