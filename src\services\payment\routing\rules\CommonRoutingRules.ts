// jscpd:ignore-file
/**
 * Common Payment Routing Rules
 * 
 * Implements common payment routing rules.
 */

import { IPaymentRoutingRule, PaymentRoutingContext } from "../PaymentRouter";
import { PaymentMethodType } from "../../../../types/payment-method.types";
import { PrismaClient } from "@prisma/client";
import { logger } from "../../../../lib/logger";
import { PaymentMethodType } from '../types';
import { PaymentMethodType } from "../../../../types/payment-method.types";
import { PrismaClient } from "@prisma/client";
import { logger } from "../../../../lib/logger";
import { PaymentMethodType } from '../types';


/**
 * Country-based routing rule
 * 
 * Scores payment methods based on their popularity in the user's country.
 */
export class CountryBasedRule implements IPaymentRoutingRule {
    private prisma: PrismaClient;
    private countryPreferences: Record<string, Record<PaymentMethodType, number>> = {};
  
    /**
   * Constructor
   * 
   * @param prisma Prisma client
   */
    constructor(prisma: PrismaClient) {
        this.prisma = prisma;
        this.loadCountryPreferences();
    }
  
    /**
   * Get the rule name
   */
    public getName(): string {
        return "country_based_rule";
    }
  
    /**
   * Get the rule weight
   */
    public getWeight(): number {
        return 0.3; // 30% weight
    }
  
    /**
   * Apply the rule
   */
    public apply(context: PaymentRoutingContext): Record<PaymentMethodType, number> {
        const scores: Record<PaymentMethodType, number> = {};
        const country: unknown = context.country || "UNKNOWN";
    
        // Initialize scores
        context.paymentMethods.forEach((method) => {
            scores[method.getType()] = 0;
        });
    
        // Apply country preferences if available
        if (this.countryPreferences[country]) {
            const preferences: unknown = this.countryPreferences[country];
      
            Object.entries(preferences).forEach(([methodType, score]) => {
                if (scores[methodType as PaymentMethodType] !== undefined) {
                    scores[methodType as PaymentMethodType] = score;
                }
            });
        }
    
        return scores;
    }
  
    /**
   * Load country preferences from database or cache
   */
    private async loadCountryPreferences(): Promise<void> {
        try {
            // In a real implementation, this would load from database
            // For now, we'll use some hardcoded preferences
            this.countryPreferences = {
                "US": {
                    "credit_card": 0.9,
                    "paypal": 0.8,
                    "crypto_transfer": 0.5,
                    "binance_pay": 0.3
                },
                "UK": {
                    "credit_card": 0.8,
                    "paypal": 0.7,
                    "crypto_transfer": 0.4,
                    "binance_pay": 0.3
                },
                "CN": {
                    "alipay": 0.9,
                    "wechat_pay": 0.8,
                    "binance_pay": 0.7,
                    "binance_c2c": 0.6,
                    "binance_trc20": 0.5
                },
                "JP": {
                    "credit_card": 0.7,
                    "crypto_transfer": 0.6,
                    "binance_pay": 0.5
                }
            };
        } catch (error) {
            logger.error("Error loading country preferences:", error);
        }
    }
}

/**
 * Amount-based routing rule
 * 
 * Scores payment methods based on the transaction amount.
 */
export class AmountBasedRule implements IPaymentRoutingRule {
    /**
   * Get the rule name
   */
    public getName(): string {
        return "amount_based_rule";
    }
  
    /**
   * Get the rule weight
   */
    public getWeight(): number {
        return 0.2; // 20% weight
    }
  
    /**
   * Apply the rule
   */
    public apply(context: PaymentRoutingContext): Record<PaymentMethodType, number> {
        const scores: Record<PaymentMethodType, number> = {};
        const amount: number = context.amount;
    
        // Initialize scores
        context.paymentMethods.forEach((method) => {
            scores[method.getType()] = 0;
        });
    
        // Apply amount-based scoring
        context.paymentMethods.forEach((method) => {
            const methodType: unknown = method.getType();
            const minAmount: unknown = method.getMinimumAmount();
            const maxAmount: unknown = method.getMaximumAmount();
      
            // Check if amount is within method's limits
            if (amount < minAmount || amount > maxAmount) {
                scores[methodType] = 0;
                return;
            }
      
            // Score based on amount range
            if (amount < 10) {
                // Small amounts favor fast, low-fee methods
                if (methodType.includes("crypto")) {
                    scores[methodType] = 0.3; // Crypto has higher fees for small amounts
                } else if (methodType.includes("card")) {
                    scores[methodType] = 0.7; // Cards are good for small amounts
                } else {
                    scores[methodType] = 0.5;
                }
            } else if (amount < 100) {
                // Medium amounts
                if (methodType.includes("crypto")) {
                    scores[methodType] = 0.5;
                } else if (methodType.includes("card")) {
                    scores[methodType] = 0.8;
                } else {
                    scores[methodType] = 0.6;
                }
            } else if (amount < 1000) {
                // Larger amounts
                if (methodType.includes("crypto")) {
                    scores[methodType] = 0.7;
                } else if (methodType.includes("card")) {
                    scores[methodType] = 0.6; // Cards may have limits
                } else {
                    scores[methodType] = 0.5;
                }
            } else {
                // Very large amounts
                if (methodType.includes("crypto")) {
                    scores[methodType] = 0.9; // Crypto is good for large amounts
                } else if (methodType.includes("card")) {
                    scores[methodType] = 0.3; // Cards often have limits
                } else if (methodType.includes("bank")) {
                    scores[methodType] = 0.8; // Bank transfers good for large amounts
                } else {
                    scores[methodType] = 0.4;
                }
            }
        });
    
        return scores;
    }
}

/**
 * Success rate routing rule
 * 
 * Scores payment methods based on their historical success rate.
 */
export class SuccessRateRule implements IPaymentRoutingRule {
    private prisma: PrismaClient;
    private successRates: Record<PaymentMethodType, number> = {};
  
    /**
   * Constructor
   * 
   * @param prisma Prisma client
   */
    constructor(prisma: PrismaClient) {
        this.prisma = prisma;
        this.loadSuccessRates();
    }
  
    /**
   * Get the rule name
   */
    public getName(): string {
        return "success_rate_rule";
    }
  
    /**
   * Get the rule weight
   */
    public getWeight(): number {
        return 0.5; // 50% weight - success rate is important
    }
  
    /**
   * Apply the rule
   */
    public apply(context: PaymentRoutingContext): Record<PaymentMethodType, number> {
        const scores: Record<PaymentMethodType, number> = {};
    
        // Initialize scores
        context.paymentMethods.forEach((method) => {
            const methodType: unknown = method.getType();
            scores[methodType] = this.successRates[methodType] ?? 0.5; // Default to 0.5 if unknown
        });
    
        return scores;
    }
  
    /**
   * Load success rates from database or cache
   */
    private async loadSuccessRates(): Promise<void> {
        try {
            // In a real implementation, this would query the database
            // For now, we'll use some hardcoded success rates
            this.successRates = {
                "credit_card": 0.95,
                "paypal": 0.93,
                "crypto_transfer": 0.89,
                "binance_pay": 0.92,
                "binance_c2c": 0.85,
                "binance_trc20": 0.91,
                "alipay": 0.94,
                "wechat_pay": 0.93
            };
        } catch (error) {
            logger.error("Error loading success rates:", error);
        }
    }
}
