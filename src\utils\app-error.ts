// jscpd:ignore-file
/**
 * Custom application error class
 * Used for standardized error handling throughout the application
 */
export class AppError extends Error {
    public readonly statusCode: number;
    public readonly isOperational: boolean;
    public readonly code: string;
    public readonly details?;

    constructor(
        message: string,
        statusCode: number = 500,
        code: string = "INTERNAL_SERVER_ERROR",
        isOperational: boolean = true,
        details?
    ) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        this.code = code;
        this.details = details;
    
        // Capture stack trace
        Error.captureStackTrace(this, this.constructor);
    
        // Set the prototype explicitly
        Object.setPrototypeOf(this, (AppError).prototype);
    }
}

/**
 * Factory methods for common error types
 */
export const createBadRequestError =(message: string, code: string = "BAD_REQUEST", details?) => {
    return new AppError(message, 400, code, true, details);
};

export const createUnauthorizedError =(message: string, code: string = "UNAUTHORIZED", details?) => {
    return new AppError(message, 401, code, true, details);
};

export const createForbiddenError =(message: string, code: string = "FORBIDDEN", details?) => {
    return new AppError(message, 403, code, true, details);
};

export const createNotFoundError =(message: string, code: string = "NOT_FOUND", details?) => {
    return new AppError(message, 404, code, true, details);
};

export const createConflictError =(message: string, code: string = "CONFLICT", details?) => {
    return new AppError(message, 409, code, true, details);
};

export const createInternalServerError =(message: string, code: string = "INTERNAL_SERVER_ERROR", details?) => {
    return new AppError(message, 500, code, true, details);
};

export const createServiceUnavailableError =(message: string, code: string = "SERVICE_UNAVAILABLE", details?) => {
    return new AppError(message, 503, code, true, details);
};
