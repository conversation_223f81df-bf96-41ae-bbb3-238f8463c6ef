// Prisma-specific types and interfaces
import { PrismaClient, Prisma } from '@prisma/client';

// Prisma client type
export type PrismaClientType = PrismaClient;

// Prisma transaction type
export type PrismaTransaction = Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use'>;

// Common Prisma query options
export interface PrismaQueryOptions<T = any> {
  where?: T;
  select?: any;
  include?: any;
  orderBy?: any;
  skip?: number;
  take?: number;
  cursor?: any;
  distinct?: any;
}

// Prisma pagination options
export interface PrismaPaginationOptions {
  skip?: number;
  take?: number;
  cursor?: any;
}

// Prisma sorting options
export interface PrismaSortOptions {
  orderBy?: Record<string, 'asc' | 'desc'> | Array<Record<string, 'asc' | 'desc'>>;
}

// Prisma filtering options
export interface PrismaFilterOptions<T = any> {
  where?: T;
}

// Prisma selection options
export interface PrismaSelectionOptions {
  select?: Record<string, boolean | any>;
  include?: Record<string, boolean | any>;
}

// Combined Prisma find options
export interface PrismaFindOptions<T = any> extends 
  PrismaPaginationOptions, 
  PrismaSortOptions, 
  PrismaFilterOptions<T>, 
  PrismaSelectionOptions {}

// Prisma aggregate result types
export interface PrismaAggregateResult<T = any> {
  _count?: T;
  _sum?: T;
  _avg?: T;
  _min?: T;
  _max?: T;
}

// Prisma count result
export interface PrismaCountResult {
  _all?: number;
  [key: string]: number | undefined;
}

// User model types
export interface UserWhereInput {
  id?: string | Prisma.StringFilter;
  email?: string | Prisma.StringFilter;
  firstName?: string | Prisma.StringFilter;
  lastName?: string | Prisma.StringFilter;
  role?: string | Prisma.StringFilter;
  isActive?: boolean | Prisma.BoolFilter;
  emailVerified?: boolean | Prisma.BoolFilter;
  phoneVerified?: boolean | Prisma.BoolFilter;
  createdAt?: Date | Prisma.DateTimeFilter;
  updatedAt?: Date | Prisma.DateTimeFilter;
  AND?: UserWhereInput[];
  OR?: UserWhereInput[];
  NOT?: UserWhereInput[];
}

export interface UserOrderByInput {
  id?: Prisma.SortOrder;
  email?: Prisma.SortOrder;
  firstName?: Prisma.SortOrder;
  lastName?: Prisma.SortOrder;
  role?: Prisma.SortOrder;
  isActive?: Prisma.SortOrder;
  createdAt?: Prisma.SortOrder;
  updatedAt?: Prisma.SortOrder;
}

export interface UserSelectInput {
  id?: boolean;
  email?: boolean;
  firstName?: boolean;
  lastName?: boolean;
  role?: boolean;
  isActive?: boolean;
  emailVerified?: boolean;
  phoneNumber?: boolean;
  phoneVerified?: boolean;
  avatar?: boolean;
  timezone?: boolean;
  language?: boolean;
  lastLoginAt?: boolean;
  createdAt?: boolean;
  updatedAt?: boolean;
  merchant?: boolean | MerchantSelectInput;
  admin?: boolean | AdminSelectInput;
}

export interface UserIncludeInput {
  merchant?: boolean | MerchantIncludeInput;
  admin?: boolean | AdminIncludeInput;
}

// Merchant model types
export interface MerchantWhereInput {
  id?: string | Prisma.StringFilter;
  userId?: string | Prisma.StringFilter;
  businessName?: string | Prisma.StringFilter;
  businessType?: string | Prisma.StringFilter;
  businessCategory?: string | Prisma.StringFilter;
  email?: string | Prisma.StringFilter;
  phoneNumber?: string | Prisma.StringFilter;
  status?: string | Prisma.StringFilter;
  verificationStatus?: string | Prisma.StringFilter;
  isActive?: boolean | Prisma.BoolFilter;
  createdAt?: Date | Prisma.DateTimeFilter;
  updatedAt?: Date | Prisma.DateTimeFilter;
  AND?: MerchantWhereInput[];
  OR?: MerchantWhereInput[];
  NOT?: MerchantWhereInput[];
  user?: UserWhereInput;
}

export interface MerchantOrderByInput {
  id?: Prisma.SortOrder;
  userId?: Prisma.SortOrder;
  businessName?: Prisma.SortOrder;
  businessType?: Prisma.SortOrder;
  businessCategory?: Prisma.SortOrder;
  email?: Prisma.SortOrder;
  phoneNumber?: Prisma.SortOrder;
  status?: Prisma.SortOrder;
  verificationStatus?: Prisma.SortOrder;
  isActive?: Prisma.SortOrder;
  createdAt?: Prisma.SortOrder;
  updatedAt?: Prisma.SortOrder;
}

export interface MerchantSelectInput {
  id?: boolean;
  userId?: boolean;
  businessName?: boolean;
  businessType?: boolean;
  businessCategory?: boolean;
  businessDescription?: boolean;
  website?: boolean;
  email?: boolean;
  phoneNumber?: boolean;
  address?: boolean;
  taxId?: boolean;
  registrationNumber?: boolean;
  status?: boolean;
  verificationStatus?: boolean;
  isActive?: boolean;
  settings?: boolean;
  createdAt?: boolean;
  updatedAt?: boolean;
  user?: boolean | UserSelectInput;
  payments?: boolean | PaymentSelectInput;
  transactions?: boolean | TransactionSelectInput;
}

export interface MerchantIncludeInput {
  user?: boolean | UserIncludeInput;
  payments?: boolean | PaymentIncludeInput;
  transactions?: boolean | TransactionIncludeInput;
}

// Payment model types
export interface PaymentWhereInput {
  id?: string | Prisma.StringFilter;
  merchantId?: string | Prisma.StringFilter;
  amount?: number | Prisma.FloatFilter;
  currency?: string | Prisma.StringFilter;
  status?: string | Prisma.StringFilter;
  paymentMethodId?: string | Prisma.StringFilter;
  transactionId?: string | Prisma.StringFilter;
  description?: string | Prisma.StringFilter;
  createdAt?: Date | Prisma.DateTimeFilter;
  updatedAt?: Date | Prisma.DateTimeFilter;
  AND?: PaymentWhereInput[];
  OR?: PaymentWhereInput[];
  NOT?: PaymentWhereInput[];
  merchant?: MerchantWhereInput;
  paymentMethod?: PaymentMethodWhereInput;
  transaction?: TransactionWhereInput;
}

export interface PaymentOrderByInput {
  id?: Prisma.SortOrder;
  merchantId?: Prisma.SortOrder;
  amount?: Prisma.SortOrder;
  currency?: Prisma.SortOrder;
  status?: Prisma.SortOrder;
  paymentMethodId?: Prisma.SortOrder;
  transactionId?: Prisma.SortOrder;
  description?: Prisma.SortOrder;
  createdAt?: Prisma.SortOrder;
  updatedAt?: Prisma.SortOrder;
}

export interface PaymentSelectInput {
  id?: boolean;
  merchantId?: boolean;
  amount?: boolean;
  currency?: boolean;
  status?: boolean;
  paymentMethodId?: boolean;
  transactionId?: boolean;
  description?: boolean;
  metadata?: boolean;
  createdAt?: boolean;
  updatedAt?: boolean;
  merchant?: boolean | MerchantSelectInput;
  paymentMethod?: boolean | PaymentMethodSelectInput;
  transaction?: boolean | TransactionSelectInput;
}

export interface PaymentIncludeInput {
  merchant?: boolean | MerchantIncludeInput;
  paymentMethod?: boolean | PaymentMethodIncludeInput;
  transaction?: boolean | TransactionIncludeInput;
}

// Transaction model types
export interface TransactionWhereInput {
  id?: string | Prisma.StringFilter;
  paymentId?: string | Prisma.StringFilter;
  merchantId?: string | Prisma.StringFilter;
  amount?: number | Prisma.FloatFilter;
  currency?: string | Prisma.StringFilter;
  status?: string | Prisma.StringFilter;
  type?: string | Prisma.StringFilter;
  reference?: string | Prisma.StringFilter;
  externalId?: string | Prisma.StringFilter;
  createdAt?: Date | Prisma.DateTimeFilter;
  updatedAt?: Date | Prisma.DateTimeFilter;
  AND?: TransactionWhereInput[];
  OR?: TransactionWhereInput[];
  NOT?: TransactionWhereInput[];
  payment?: PaymentWhereInput;
  merchant?: MerchantWhereInput;
}

export interface TransactionOrderByInput {
  id?: Prisma.SortOrder;
  paymentId?: Prisma.SortOrder;
  merchantId?: Prisma.SortOrder;
  amount?: Prisma.SortOrder;
  currency?: Prisma.SortOrder;
  status?: Prisma.SortOrder;
  type?: Prisma.SortOrder;
  reference?: Prisma.SortOrder;
  externalId?: Prisma.SortOrder;
  createdAt?: Prisma.SortOrder;
  updatedAt?: Prisma.SortOrder;
}

export interface TransactionSelectInput {
  id?: boolean;
  paymentId?: boolean;
  merchantId?: boolean;
  amount?: boolean;
  currency?: boolean;
  status?: boolean;
  type?: boolean;
  reference?: boolean;
  externalId?: boolean;
  metadata?: boolean;
  createdAt?: boolean;
  updatedAt?: boolean;
  payment?: boolean | PaymentSelectInput;
  merchant?: boolean | MerchantSelectInput;
}

export interface TransactionIncludeInput {
  payment?: boolean | PaymentIncludeInput;
  merchant?: boolean | MerchantIncludeInput;
}

// PaymentMethod model types
export interface PaymentMethodWhereInput {
  id?: string | Prisma.StringFilter;
  merchantId?: string | Prisma.StringFilter;
  type?: string | Prisma.StringFilter;
  name?: string | Prisma.StringFilter;
  isActive?: boolean | Prisma.BoolFilter;
  createdAt?: Date | Prisma.DateTimeFilter;
  updatedAt?: Date | Prisma.DateTimeFilter;
  AND?: PaymentMethodWhereInput[];
  OR?: PaymentMethodWhereInput[];
  NOT?: PaymentMethodWhereInput[];
  merchant?: MerchantWhereInput;
}

export interface PaymentMethodOrderByInput {
  id?: Prisma.SortOrder;
  merchantId?: Prisma.SortOrder;
  type?: Prisma.SortOrder;
  name?: Prisma.SortOrder;
  isActive?: Prisma.SortOrder;
  createdAt?: Prisma.SortOrder;
  updatedAt?: Prisma.SortOrder;
}

export interface PaymentMethodSelectInput {
  id?: boolean;
  merchantId?: boolean;
  type?: boolean;
  name?: boolean;
  isActive?: boolean;
  configuration?: boolean;
  createdAt?: boolean;
  updatedAt?: boolean;
  merchant?: boolean | MerchantSelectInput;
  payments?: boolean | PaymentSelectInput;
}

export interface PaymentMethodIncludeInput {
  merchant?: boolean | MerchantIncludeInput;
  payments?: boolean | PaymentIncludeInput;
}

// Admin model types
export interface AdminWhereInput {
  id?: string | Prisma.StringFilter;
  userId?: string | Prisma.StringFilter;
  department?: string | Prisma.StringFilter;
  isActive?: boolean | Prisma.BoolFilter;
  createdAt?: Date | Prisma.DateTimeFilter;
  updatedAt?: Date | Prisma.DateTimeFilter;
  AND?: AdminWhereInput[];
  OR?: AdminWhereInput[];
  NOT?: AdminWhereInput[];
  user?: UserWhereInput;
}

export interface AdminOrderByInput {
  id?: Prisma.SortOrder;
  userId?: Prisma.SortOrder;
  department?: Prisma.SortOrder;
  isActive?: Prisma.SortOrder;
  createdAt?: Prisma.SortOrder;
  updatedAt?: Prisma.SortOrder;
}

export interface AdminSelectInput {
  id?: boolean;
  userId?: boolean;
  department?: boolean;
  permissions?: boolean;
  isActive?: boolean;
  createdAt?: boolean;
  updatedAt?: boolean;
  user?: boolean | UserSelectInput;
}

export interface AdminIncludeInput {
  user?: boolean | UserIncludeInput;
}

// Generic Prisma result types
export type PrismaFindManyResult<T> = T[];
export type PrismaFindUniqueResult<T> = T | null;
export type PrismaFindFirstResult<T> = T | null;
export type PrismaCreateResult<T> = T;
export type PrismaUpdateResult<T> = T;
export type PrismaDeleteResult<T> = T;
export type PrismaUpsertResult<T> = T;
