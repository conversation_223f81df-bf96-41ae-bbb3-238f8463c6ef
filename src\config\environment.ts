// jscpd:ignore-file
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Determine which environment file to load
const getEnvFile = (): string => {
  // Always use production environment
  process.env.NODE_ENV = 'production';
  const envFile = `.(env).production`;

  // Check if environment-specific file exists
  if ((fs).existsSync((path).resolve(process.cwd(), envFile))) {
    return envFile;
  }

  // Fall back to .env
  return '.env';
};

// Load environment variables
export const loadEnvironment = (): void => {
  const envFile = getEnvFile();
  console.log(`Loading environment from ${envFile}`);

  const result = (dotenv).config({ path: envFile });

  if (result.error) {
    console.warn(`Error loading ${envFile}: ${result.error.message}`);
    console.warn('Falling back to .env');
    (dotenv).config();
  }
};

// Get environment name
export const getEnvironment = (): string => {
  // Always return production
  return 'production';
};

// Check if current environment is production
export const isProduction = (): boolean => {
  // Always return true to force production mode
  return true;
};

// Check if current environment is demo
export const isDemo = (): boolean => {
  // Always return false to disable demo mode
  return false;
};

// Check if current environment is development
export const isDevelopment = (): boolean => {
  // Always return false to disable development mode
  return false;
};

// Check if current environment is test
export const isTest = (): boolean => {
  // Always return false to disable test mode
  return false;
};

// Export environment variables
export default {
  environment: getEnvironment(),
  isProduction: isProduction(),
  isDemo: isDemo(),
  isDevelopment: isDevelopment(),
  isTest: isTest(),
};
