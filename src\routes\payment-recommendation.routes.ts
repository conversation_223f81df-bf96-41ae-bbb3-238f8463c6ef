// jscpd:ignore-file
import express from 'express';
import { PaymentRecommendationController } from '../controllers/payment-recommendation.controller';
import { authMiddleware } from '../middlewares/auth.middleware';
import { roleMiddleware } from '../middlewares/role.middleware';
import { Merchant } from '../types';

const router: unknown = express.Router();
const paymentRecommendationController = new PaymentRecommendationController();

/**
 * @route GET /api/payment-recommendation
 * @desc Get payment method recommendations
 * @access Private (Merchant, Admin)
 */
router.get(
  '/',
  authMiddleware,
  roleMiddleware(['MERCHANT', 'ADMIN']),
  paymentRecommendationController.getRecommendations
);

/**
 * @route GET /api/payment-recommendation/transaction/:transactionId
 * @desc Get payment method recommendation for a specific transaction
 * @access Private (Merchant, Admin)
 */
router.get(
  '/transaction/:transactionId',
  authMiddleware,
  roleMiddleware(['MERCHANT', 'ADMIN']),
  paymentRecommendationController.getTransactionRecommendation
);

/**
 * @route GET /api/payment-recommendation/weights/:merchantId
 * @desc Get merchant recommendation weights
 * @access Private (Merchant, Admin)
 */
router.get(
  '/weights/:merchantId',
  authMiddleware,
  roleMiddleware(['MERCHANT', 'ADMIN']),
  paymentRecommendationController.getRecommendationWeights
);

/**
 * @route PUT /api/payment-recommendation/weights/:merchantId
 * @desc Update merchant recommendation weights
 * @access Private (Merchant, Admin)
 */
router.put(
  '/weights/:merchantId',
  authMiddleware,
  roleMiddleware(['MERCHANT', 'ADMIN']),
  paymentRecommendationController.updateRecommendationWeights
);

export default router;
