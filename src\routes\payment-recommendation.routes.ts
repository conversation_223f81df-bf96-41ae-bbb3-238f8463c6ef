// jscpd:ignore-file
import express from 'express';
import { PaymentRecommendationController as ImportedPaymentRecommendationController } from '../controllers/payment-(recommendation as any).controller';
import { authMiddleware as ImportedauthMiddleware } from '../middlewares/(auth as any).middleware';
import { roleMiddleware as ImportedroleMiddleware } from '../middlewares/(role as any).middleware';
import { Merchant as ImportedMerchant } from '../types';

const router: any = (express as any).Router();
const paymentRecommendationController = new PaymentRecommendationController();

/**
 * @route GET /api/payment-recommendation
 * @desc Get payment method recommendations
 * @access Private (Merchant, Admin)
 */
(router as any).get(
  '/',
  authMiddleware,
  roleMiddleware(['MERCHANT', 'ADMIN']),
  (paymentRecommendationController as any).getRecommendations
);

/**
 * @route GET /api/payment-recommendation/transaction/:transactionId
 * @desc Get payment method recommendation for a specific transaction
 * @access Private (Merchant, Admin)
 */
(router as any).get(
  '/transaction/:transactionId',
  authMiddleware,
  roleMiddleware(['MERCHANT', 'ADMIN']),
  (paymentRecommendationController as any).getTransactionRecommendation
);

/**
 * @route GET /api/payment-recommendation/weights/:merchantId
 * @desc Get merchant recommendation weights
 * @access Private (Merchant, Admin)
 */
(router as any).get(
  '/weights/:merchantId',
  authMiddleware,
  roleMiddleware(['MERCHANT', 'ADMIN']),
  (paymentRecommendationController as any).getRecommendationWeights
);

/**
 * @route PUT /api/payment-recommendation/weights/:merchantId
 * @desc Update merchant recommendation weights
 * @access Private (Merchant, Admin)
 */
(router as any).put(
  '/weights/:merchantId',
  authMiddleware,
  roleMiddleware(['MERCHANT', 'ADMIN']),
  (paymentRecommendationController as any).updateRecommendationWeights
);

export default router;
