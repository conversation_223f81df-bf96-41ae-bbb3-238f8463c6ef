// jscpd:ignore-file
/**
 * Auth Module
 * 
 * This module handles authentication and authorization.
 */

import { Router } from 'express';
import { BaseModule } from '../../factories/ModuleFactory';
import { logger } from '../../utils/logger';
import { BaseModule } from '../../factories/ModuleFactory';
import { logger } from '../../utils/logger';

/**
 * Auth Module
 */
class AuthModule extends BaseModule {
  /**
   * Constructor
   */
  constructor() {
    super('AuthModule');
  }
  
  /**
   * Initialize the module
   */
  initialize(): void {
    logger.info('Initializing AuthModule');
    
    // Get controllers
    const authController = this.controllerFactory.getController('auth');
    
    // Set up routes
    this.router.post('/login', authController.login);
    this.router.post('/register', authController.register);
    this.router.post('/refresh-token', authController.refreshToken);
    this.router.post('/logout', authController.logout);
    this.router.post('/forgot-password', authController.forgotPassword);
    this.router.post('/reset-password', authController.resetPassword);
    
    logger.info('AuthModule initialized');
  }
}

// Export the module
export default new AuthModule();
