// jscpd:ignore-file
/**
 * Verification Pre-Processor
 * 
 * Interface for verification pre-processors.
 */

import { VerificationRequest } from "../../../interfaces/verification/IVerificationStrategy";

/**
 * Verification pre-processor interface
 */
export interface VerificationPreProcessor {
  /**
   * Get the name of the pre-processor
   */
  getName(): string;
  
  /**
   * Process a verification request
   */
  process(request: VerificationRequest): Promise<VerificationRequest>;
}
