// jscpd:ignore-file
import { logger } from '../utils/logger';
import prisma from '../lib/prisma';
import { PaymentStatus } from './payment.service';
import { VerificationStatus } from './verification.service';
import { WebhookDeliveryStatus } from './webhook.service';
import { Merchant } from '../types';

/**
 * Monitoring metrics
 */
export interface MonitoringMetrics {
  // System metrics
  systemHealth: {
    status: 'healthy' | 'degraded' | 'unhealthy';
    uptime: number;
    memoryUsage: number;
    cpuUsage: number;
    activeConnections: number;
    apiLatency: number;
  };

  // Payment metrics
  payments: {
    total: number;
    completed: number;
    failed: number;
    pending: number;
    expired: number;
    refunded: number;
    successRate: number;
    averageAmount: number;
    totalVolume: number;
  };

  // Verification metrics
  verification: {
    total: number;
    verified: number;
    failed: number;
    pending: number;
    successRate: number;
    averageTime: number;
    methodDistribution: Record<string, number>;
  };

  // Webhook metrics
  webhooks: {
    total: number;
    delivered: number;
    failed: number;
    pending: number;
    retrying: number;
    deliveryRate: number;
    averageLatency: number;
    eventDistribution: Record<string, number>;
  };

  // Merchant metrics
  merchants: {
    total: number;
    active: number;
    inactive: number;
    verified: number;
    unverified: number;
    averageTransactionsPerMerchant: number;
  };

  // API metrics
  api: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    requestsPerMinute: number;
    errorRate: number;
    endpointDistribution: Record<string, number>;
  };

  // Time-based metrics
  timeBasedMetrics: {
    hourlyTransactions: Record<string, number>;
    dailyTransactions: Record<string, number>;
    weeklyTransactions: Record<string, number>;
    monthlyTransactions: Record<string, number>;
  };
}

/**
 * Monitoring service
 */
export class MonitoringService {
  private startTime: number;
  private requestCounts: Record<string, number>;
  private responseTimes: Record<string, number[]>;
  private errorCounts: Record<string, number>;

  /**
   * Create a new monitoring service
   */
  constructor() {
    this.startTime = Date.now();
    this.requestCounts = {};
    this.responseTimes = {};
    this.errorCounts = {};
  }

  /**
   * Get system uptime in seconds
   * @returns Uptime in seconds
   */
  public getUptime(): number {
    return Math.floor((Date.now() - this.startTime) / 1000);
  }

  /**
   * Track API request
   * @param endpoint Endpoint
   * @param startTime Start time
   * @param statusCode Status code
   */
  public trackApiRequest(endpoint: string, startTime: number, statusCode: number): void {
    const responseTime = Date.now() - startTime;

    // Initialize counters if needed
    if (!this.requestCounts[endpoint]) {
      this.requestCounts[endpoint] = 0;
      this.responseTimes[endpoint] = [];
      this.errorCounts[endpoint] = 0;
    }

    // Increment request count
    this.requestCounts[endpoint]++;

    // Add response time
    this.responseTimes[endpoint].push(responseTime);

    // Track errors
    if (statusCode >= 400) {
      this.errorCounts[endpoint]++;
    }

    // Log request
    logger.debug('API request tracked', {
      endpoint,
      responseTime,
      statusCode,
    });
  }

  /**
   * Get all monitoring metrics
   * @returns Monitoring metrics
   */
  public async getMetrics(): Promise<MonitoringMetrics> {
    try {
      // Get system metrics
      const systemHealth = await this.getSystemHealthMetrics();

      // Get payment metrics
      const payments = await this.getPaymentMetrics();

      // Get verification metrics
      const verification = await this.getVerificationMetrics();

      // Get webhook metrics
      const webhooks = await this.getWebhookMetrics();

      // Get merchant metrics
      const merchants = await this.getMerchantMetrics();

      // Get API metrics
      const api: unknown = this.getApiMetrics();

      // Get time-based metrics
      const timeBasedMetrics = await this.getTimeBasedMetrics();

      return {
        systemHealth,
        payments,
        verification,
        webhooks,
        merchants,
        api,
        timeBasedMetrics,
      };
    } catch (error) {
      logger.error('Error getting monitoring metrics', { error });
      throw new Error('Failed to get monitoring metrics');
    }
  }

  /**
   * Get system health metrics
   * @returns System health metrics
   */
  private async getSystemHealthMetrics(): Promise<MonitoringMetrics['systemHealth']> {
    try {
      // Get memory usage
      const memoryUsage: unknown = process.memoryUsage().heapUsed / 1024 / 1024; // MB

      // Get CPU usage (simplified)
      const cpuUsage: unknown = process.cpuUsage().user / 1000000; // seconds

      // Get active connections (simplified)
      const activeConnections: unknown = Object.keys(this.requestCounts).reduce(
        (sum, key) => sum + this.requestCounts[key],
        0
      );

      // Get API latency
      const apiLatency: unknown = this.getAverageResponseTime();

      // Determine system health status
      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      if (memoryUsage > 1024 || cpuUsage > 10 || apiLatency > 1000) {
        status = 'degraded';
      }
      if (memoryUsage > 2048 || cpuUsage > 20 || apiLatency > 5000) {
        status = 'unhealthy';
      }

      return {
        status,
        uptime: this.getUptime(),
        memoryUsage,
        cpuUsage,
        activeConnections,
        apiLatency,
      };
    } catch (error) {
      logger.error('Error getting system health metrics', { error });
      return {
        status: 'degraded',
        uptime: this.getUptime(),
        memoryUsage: 0,
        cpuUsage: 0,
        activeConnections: 0,
        apiLatency: 0,
      };
    }
  }

  /**
   * Get payment metrics
   * @returns Payment metrics
   */
  private async getPaymentMetrics(): Promise<MonitoringMetrics['payments']> {
    try {
      // Get payment counts by status
      const paymentCounts = await prisma.transaction.groupBy({
        by: ['status'],
        _count: {
          id: true,
        },
        _avg: {
          amount: true,
        },
        _sum: {
          amount: true,
        },
      });

      // Initialize metrics
      const metrics: MonitoringMetrics['payments'] = {
        total: 0,
        completed: 0,
        failed: 0,
        pending: 0,
        expired: 0,
        refunded: 0,
        successRate: 0,
        averageAmount: 0,
        totalVolume: 0,
      };

      // Calculate metrics
      paymentCounts.forEach((count) => {
        const countValue: unknown = count._count.id;
        metrics.total += countValue;

        switch (count.status) {
          case PaymentStatus.COMPLETED:
            metrics.completed += countValue;
            break;
          case PaymentStatus.FAILED:
            metrics.failed += countValue;
            break;
          case PaymentStatus.PENDING:
            metrics.pending += countValue;
            break;
          case PaymentStatus.EXPIRED:
            metrics.expired += countValue;
            break;
          case PaymentStatus.REFUNDED:
            metrics.refunded += countValue;
            break;
        }

        // Add to total volume
        if (count._sum.amount) {
          metrics.totalVolume += count._sum.amount;
        }
      });

      // Calculate success rate
      if (metrics.total > 0) {
        metrics.successRate = (metrics.completed / metrics.total) * 100;
      }

      // Calculate average amount
      if (metrics.total > 0) {
        metrics.averageAmount = metrics.totalVolume / metrics.total;
      }

      return metrics;
    } catch (error) {
      logger.error('Error getting payment metrics', { error });
      return {
        total: 0,
        completed: 0,
        failed: 0,
        pending: 0,
        expired: 0,
        refunded: 0,
        successRate: 0,
        averageAmount: 0,
        totalVolume: 0,
      };
    }
  }

  /**
   * Get verification metrics
   * @returns Verification metrics
   */
  private async getVerificationMetrics(): Promise<MonitoringMetrics['verification']> {
    try {
      // Get verification counts by status
      const verificationCounts = await prisma.transaction.groupBy({
        by: ['verificationStatus'],
        _count: {
          id: true,
        },
      });

      // Get verification method distribution
      const methodDistribution = await prisma.transaction.groupBy({
        by: ['verificationMethod'],
        _count: {
          id: true,
        },
      });

      // Initialize metrics
      const metrics: MonitoringMetrics['verification'] = {
        total: 0,
        verified: 0,
        failed: 0,
        pending: 0,
        successRate: 0,
        averageTime: 0,
        methodDistribution: {},
      };

      // Calculate metrics
      verificationCounts.forEach((count) => {
        const countValue: unknown = count._count.id;
        metrics.total += countValue;

        switch (count.verificationStatus) {
          case VerificationStatus.VERIFIED:
            metrics.verified += countValue;
            break;
          case VerificationStatus.FAILED:
            metrics.failed += countValue;
            break;
          case VerificationStatus.PENDING:
            metrics.pending += countValue;
            break;
        }
      });

      // Calculate success rate
      if (metrics.total > 0) {
        metrics.successRate = (metrics.verified / metrics.total) * 100;
      }

      // Calculate method distribution
      methodDistribution.forEach((method) => {
        if (method.verificationMethod) {
          metrics.methodDistribution[method.verificationMethod] = method._count.id;
        }
      });

      // Calculate average verification time (simplified)
      metrics.averageTime = 5000; // 5 seconds (placeholder)

      return metrics;
    } catch (error) {
      logger.error('Error getting verification metrics', { error });
      return {
        total: 0,
        verified: 0,
        failed: 0,
        pending: 0,
        successRate: 0,
        averageTime: 0,
        methodDistribution: {},
      };
    }
  }

  /**
   * Get webhook metrics
   * @returns Webhook metrics
   */
  private async getWebhookMetrics(): Promise<MonitoringMetrics['webhooks']> {
    try {
      // Get webhook counts by status
      const webhookCounts = await prisma.webhook.groupBy({
        by: ['status'],
        _count: {
          id: true,
        },
      });

      // Get webhook event distribution
      const eventDistribution = await prisma.webhook.groupBy({
        by: ['event'],
        _count: {
          id: true,
        },
      });

      // Initialize metrics
      const metrics: MonitoringMetrics['webhooks'] = {
        total: 0,
        delivered: 0,
        failed: 0,
        pending: 0,
        retrying: 0,
        deliveryRate: 0,
        averageLatency: 0,
        eventDistribution: {},
      };

      // Calculate metrics
      webhookCounts.forEach((count) => {
        const countValue: unknown = count._count.id;
        metrics.total += countValue;

        switch (count.status) {
          case WebhookDeliveryStatus.SUCCESS:
            metrics.delivered += countValue;
            break;
          case WebhookDeliveryStatus.FAILED:
            metrics.failed += countValue;
            break;
          case WebhookDeliveryStatus.PENDING:
            metrics.pending += countValue;
            break;
          case WebhookDeliveryStatus.RETRYING:
            metrics.retrying += countValue;
            break;
        }
      });

      // Calculate delivery rate
      if (metrics.total > 0) {
        metrics.deliveryRate = (metrics.delivered / metrics.total) * 100;
      }

      // Calculate event distribution
      eventDistribution.forEach((event) => {
        metrics.eventDistribution[event.event] = event._count.id;
      });

      // Calculate average latency (simplified)
      metrics.averageLatency = 500; // 500ms (placeholder)

      return metrics;
    } catch (error) {
      logger.error('Error getting webhook metrics', { error });
      return {
        total: 0,
        delivered: 0,
        failed: 0,
        pending: 0,
        retrying: 0,
        deliveryRate: 0,
        averageLatency: 0,
        eventDistribution: {},
      };
    }
  }

  /**
   * Get merchant metrics
   * @returns Merchant metrics
   */
  private async getMerchantMetrics(): Promise<MonitoringMetrics['merchants']> {
    try {
      // Get merchant counts
      const totalMerchants = await prisma.merchant.count();
      const activeMerchants = await prisma.merchant.count({
        where: { isActive: true },
      });
      const verifiedMerchants = await prisma.merchant.count({
        where: { isVerified: true },
      });

      // Get transaction counts per merchant
      const transactionsPerMerchant = await prisma.transaction.groupBy({
        by: ['merchantId'],
        _count: {
          id: true,
        },
      });

      // Calculate average transactions per merchant
      let totalTransactions: number = 0;
      transactionsPerMerchant.forEach((merchant) => {
        totalTransactions += merchant._count.id;
      });

      const averageTransactionsPerMerchant: unknown =
        totalMerchants > 0 ? totalTransactions / totalMerchants : 0;

      return {
        total: totalMerchants,
        active: activeMerchants,
        inactive: totalMerchants - activeMerchants,
        verified: verifiedMerchants,
        unverified: totalMerchants - verifiedMerchants,
        averageTransactionsPerMerchant,
      };
    } catch (error) {
      logger.error('Error getting merchant metrics', { error });
      return {
        total: 0,
        active: 0,
        inactive: 0,
        verified: 0,
        unverified: 0,
        averageTransactionsPerMerchant: 0,
      };
    }
  }

  /**
   * Get API metrics
   * @returns API metrics
   */
  private getApiMetrics(): MonitoringMetrics['api'] {
    try {
      // Calculate total requests
      const totalRequests: unknown = Object.values(this.requestCounts).reduce(
        (sum, count) => sum + count,
        0
      );

      // Calculate total errors
      const totalErrors: unknown = Object.values(this.errorCounts).reduce(
        (sum, count) => sum + count,
        0
      );

      // Calculate error rate
      const errorRate: unknown = totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;

      // Calculate average response time
      const averageResponseTime: unknown = this.getAverageResponseTime();

      // Calculate requests per minute
      const uptime: unknown = this.getUptime();
      const requestsPerMinute: unknown = uptime > 0 ? (totalRequests / uptime) * 60 : 0;

      // Calculate endpoint distribution
      const endpointDistribution: Record<string, number> = {};
      Object.keys(this.requestCounts).forEach((endpoint) => {
        endpointDistribution[endpoint] = this.requestCounts[endpoint];
      });

      return {
        totalRequests,
        successfulRequests: totalRequests - totalErrors,
        failedRequests: totalErrors,
        averageResponseTime,
        requestsPerMinute,
        errorRate,
        endpointDistribution,
      };
    } catch (error) {
      logger.error('Error getting API metrics', { error });
      return {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
        requestsPerMinute: 0,
        errorRate: 0,
        endpointDistribution: {},
      };
    }
  }

  /**
   * Get time-based metrics
   * @returns Time-based metrics
   */
  private async getTimeBasedMetrics(): Promise<MonitoringMetrics['timeBasedMetrics']> {
    try {
      // Get current date
      const now: Date = new Date();
      const today: Date = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday: Date = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      // Get hourly transactions for today
      const hourlyTransactions: Record<string, number> = {};
      for (let i: number = 0; i < 24; i++) {
        const hour: unknown = i.toString().padStart(2, '0');
        hourlyTransactions[hour] = 0;
      }

      // Get daily transactions for the last 7 days
      const dailyTransactions: Record<string, number> = {};
      for (let i: number = 6; i >= 0; i--) {
        const date: Date = new Date(today);
        date.setDate(date.getDate() - i);
        const dateString: unknown = date.toISOString().split('T')[0];
        dailyTransactions[dateString] = 0;
      }

      // Get weekly transactions for the last 4 weeks
      const weeklyTransactions: Record<string, number> = {};
      for (let i: number = 3; i >= 0; i--) {
        const date: Date = new Date(today);
        date.setDate(date.getDate() - i * 7);
        const weekString = `Week ${i + 1}`;
        weeklyTransactions[weekString] = 0;
      }

      // Get monthly transactions for the last 6 months
      const monthlyTransactions: Record<string, number> = {};
      for (let i: number = 5; i >= 0; i--) {
        const date: Date = new Date(today);
        date.setMonth(date.getMonth() - i);
        const monthString: unknown = date.toISOString().split('T')[0].substring(0, 7);
        monthlyTransactions[monthString] = 0;
      }

      // Get transactions for the last 6 months
      const transactions = await prisma.transaction.findMany({
        where: {
          createdAt: {
            gte: new Date(today.getFullYear(), today.getMonth() - 5, 1),
          },
        },
        select: {
          createdAt: true,
        },
      });

      // Populate metrics
      transactions.forEach((transaction) => {
        const date: Date = new Date(transaction.createdAt);
        const hour: unknown = date.getHours().toString().padStart(2, '0');
        const dateString: unknown = date.toISOString().split('T')[0];
        const monthString: unknown = dateString.substring(0, 7);

        // Add to hourly transactions if today
        if (date >= today) {
          hourlyTransactions[hour]++;
        }

        // Add to daily transactions if in the last 7 days
        if (date >= new Date(today.getFullYear(), today.getMonth(), today.getDate() - 6)) {
          dailyTransactions[dateString] = (dailyTransactions[dateString] ?? 0) + 1;
        }

        // Add to weekly transactions
        const weekDiff: unknown = Math.floor(
          (today.getTime() - date.getTime()) / (7 * 24 * 60 * 60 * 1000)
        );
        if (weekDiff < 4) {
          const weekString = `Week ${4 - weekDiff}`;
          weeklyTransactions[weekString]++;
        }

        // Add to monthly transactions
        if (monthString in monthlyTransactions) {
          monthlyTransactions[monthString]++;
        }
      });

      return {
        hourlyTransactions,
        dailyTransactions,
        weeklyTransactions,
        monthlyTransactions,
      };
    } catch (error) {
      logger.error('Error getting time-based metrics', { error });
      return {
        hourlyTransactions: {},
        dailyTransactions: {},
        weeklyTransactions: {},
        monthlyTransactions: {},
      };
    }
  }

  /**
   * Get average response time across all endpoints
   * @returns Average response time in milliseconds
   */
  private getAverageResponseTime(): number {
    let totalTime = 0;
    let totalRequests: number = 0;

    Object.keys(this.responseTimes).forEach((endpoint) => {
      this.responseTimes[endpoint].forEach((time) => {
        totalTime += time;
        totalRequests++;
      });
    });

    return totalRequests > 0 ? totalTime / totalRequests : 0;
  }
}
