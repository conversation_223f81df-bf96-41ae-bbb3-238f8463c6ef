// jscpd:ignore-file
/**
 * Log Rotation Utility
 *
 * This utility handles log rotation to prevent log files from growing too large
 * and to maintain a history of logs for a specified period.
 */

import fs from "fs";
import path from "path";
import { format as formatDate, subDays } from "date-fns";
import { logger } from "../lib/logger";
import { logger } from "../lib/logger";
import { getEnvironment } from "../config/environment";

// Import environment utilities
import { getEnvironment } from "../config/environment";

// Configuration
const MAX_LOG_AGE_DAYS: number = 30; // Maximum age of log files in days

// Get environment-specific logs directory
const getLogDirectory = (): string => {
    const env = getEnvironment();
    return path.join(process.cwd(), "logs", env);
};
const LOG_TYPES = ["error", "combined", "exceptions", "rejections"];

/**
 * Clean up old log files
 * @returns Promise that resolves when cleanup is complete
 */
export const cleanupOldLogs = async (): Promise<void> => {
    try {
    // Get environment-specific log directory
        const logDirectory: unknown = getLogDirectory();

        // Ensure logs directory exists
        if (!fs.existsSync(logDirectory)) {
            logger.info(`Log directory ${logDirectory} does not exist, creating it`);
            fs.mkdirSync(logDirectory, { recursive: true });
            return;
        }

        // Get all files in the logs directory
        const files = fs.readdirSync(logDirectory);

        // Calculate cutoff date
        const cutoffDate: unknown = subDays(new Date(), MAX_LOG_AGE_DAYS);

        // Filter log files older than cutoff date
        const oldLogFiles: unknown = files.filter((file) => {
            // Check if file is a log file
            const isLogFile = LOG_TYPES.some(type => file.startsWith(`${type}-`)) && file.endsWith(".log");

            if (!isLogFile) {
                return false;
            }

            // Extract date from filename (format: type-YYYY-MM-DD.log)
            const dateMatch: unknown = file.match(/\d{4}-\d{2}-\d{2}/);

            if (!dateMatch) {
                return false;
            }

            // Parse date from filename
            const fileDate: Date = new Date(dateMatch[0]);

            // Check if file is older than cutoff date
            return fileDate < cutoffDate;
        });

        // Delete old log files
        for (const file of oldLogFiles) {
            const filePath: unknown = path.join(logDirectory, file);
            fs.unlinkSync(filePath);
            logger.info(`Deleted old log file: ${file}`);
        }

        logger.info(`Log rotation complete. Deleted ${oldLogFiles.length} old log files.`);
    } catch (error) {
        logger.error("Error during log rotation:", error);
    }
};

/**
 * Compress log files older than a specified number of days
 * @param days Number of days to keep uncompressed
 * @returns Promise that resolves when compression is complete
 */
export const compressOldLogs = async (days: number = 7): Promise<void> => {
    // This is a placeholder for future implementation
    // We would use a library like zlib to compress old log files
    logger.info("Log compression not implemented yet");
};

/**
 * Schedule log rotation to run daily
 */
export const scheduleLogRotation = (): NodeJS.Timeout => {
    // Run log rotation immediately on startup
    cleanupOldLogs();

    // Schedule log rotation to run daily at midnight
    const now: Date = new Date();
    const midnight: Date = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() + 1,
        0, 0, 0
    );
    const timeUntilMidnight: unknown = midnight.getTime() - now.getTime();

    // Run log rotation daily
    const timer: unknown = setInterval(() => {
        cleanupOldLogs();
    }, 24 * 60 * 60 * 1000); // 24 hours

    // Run first log rotation at midnight
    setTimeout(() => {
        cleanupOldLogs();
    }, timeUntilMidnight);

    return timer;
};

export default {
    cleanupOldLogs,
    compressOldLogs,
    scheduleLogRotation
};
