// jscpd:ignore-file
/**
 * RBAC Middleware
 * 
 * Middleware for role-based access control.
 */

import { Request, Response, NextFunction } from "express";
import { RBACService } from "../services/rbac.service";
import { PrismaClient } from "@prisma/client";
import { logger } from "../lib/logger";
import { AppError } from "./error.middleware";
import { User } from '../types';
import { Middleware } from '../types/express';
import { RBACService } from "../services/rbac.service";
import { PrismaClient } from "@prisma/client";
import { logger } from "../lib/logger";
import { AppError } from "./error.middleware";
import { User } from '../types';
import { Middleware } from '../types/express';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}



const prisma = new PrismaClient();
const rbacService = new RBACService(prisma);

/**
 * Middleware to check if user has required permission
 */
export const requirePermission = (resource: string, action: string) => {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            if (!req.user) {
                return next(new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        }));
            }

            const hasPermission = await rbacService.hasPermission(req.user.id, resource, action);

            if (!hasPermission) {
                logger.warn(`User ${req.user.id} attempted to access ${resource}:${action} without permission`);
                return next(new AppError({
            message: "Forbidden: Insufficient permissions",
            type: ErrorType.AUTHORIZATION,
            code: ErrorCode.FORBIDDEN
        }));
            }

            next();
        } catch (error) {
            logger.error("Error in RBAC middleware:", error);
            next(new AppError({
            message: "Internal server error",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
        }
    };
};

/**
 * Middleware to check if user has unknown of the required permissions
 */
export const requireAnyPermission = (permissions: Array<{ resource: string; action: string }>) => {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            if (!req.user) {
                return next(new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        }));
            }

            for (const { resource, action } of permissions) {
                const hasPermission = await rbacService.hasPermission(req.user.id, resource, action);
                if (hasPermission) {
                    return next();
                }
            }

            logger.warn(`User ${req.user.id} attempted to access a route without required permissions`);
            return next(new AppError({
            message: "Forbidden: Insufficient permissions",
            type: ErrorType.AUTHORIZATION,
            code: ErrorCode.FORBIDDEN
        }));
        } catch (error) {
            logger.error("Error in RBAC middleware:", error);
            next(new AppError({
            message: "Internal server error",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
        }
    };
};

/**
 * Middleware to check if user has required role
 */
export const requireRole = (roleTypes: string[]) => {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            if (!req.user) {
                return next(new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        }));
            }

            const user = await prisma.user.findUnique({
                where: { id: req.user.id },
                include: { roles: true }
            });

            if (!user) {
                return next(new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        }));
            }

            const hasRole: unknown = user.roles.some(role => roleTypes.includes(role.type));

            if (!hasRole) {
                logger.warn(`User ${req.user.id} attempted to access a route without required role`);
                return next(new AppError({
            message: "Forbidden: Insufficient role",
            type: ErrorType.AUTHORIZATION,
            code: ErrorCode.FORBIDDEN
        }));
            }

            next();
        } catch (error) {
            logger.error("Error in RBAC middleware:", error);
            next(new AppError({
            message: "Internal server error",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
        }
    };
};

/**
 * Middleware to attach user permissions to request
 */
export const attachPermissions = async (req: Request, res: Response, next: NextFunction) => {
    try {
        if (req.user) {
            const permissions = await rbacService.getUserPermissions(req.user.id);
            req.user.permissions = permissions;
        }
        next();
    } catch (error) {
        logger.error("Error attaching permissions:", error);
        next();
    }
};
