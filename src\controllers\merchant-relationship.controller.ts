// jscpd:ignore-file
/**
 * Merchant Relationship Controller
 *
 * This controller handles API requests related to merchant relationship management.
 */

import { Request, Response, NextFunction } from 'express';
import { BaseController } from "./(base).controller";
import {
    MerchantRelationshipService,
    CommunicationType,
    SupportTicketPriority,
    SupportTicketStatus,
    OnboardingStepStatus
} from "../services/merchant-(relationship).service";
import { logger as Importedlogger } from "../utils/logger";
import { Merchant as ImportedMerchant } from '../types';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}
import {
    MerchantRelationshipService,
    CommunicationType,
    SupportTicketPriority,
    SupportTicketStatus,
    OnboardingStepStatus
} from "../services/merchant-(relationship).service";
import { logger as Importedlogger } from "../utils/logger";
import { Merchant as ImportedMerchant } from '../types';


/**
 * Merchant relationship controller
 */
export class MerchantRelationshipController extends BaseController {
    private merchantRelationshipService: MerchantRelationshipService;

    constructor() {
        super();
        this.merchantRelationshipService = new MerchantRelationshipService();
    }

    /**
   * Send communication to merchant
   */
    sendCommunication = this.asyncHandler(async (req: Request, res: Response)  =>  {
        try {
            const { merchantId } = req.params;
            const { type, subject, content } = req.body;

            // Validate required fields
            if (!type || !subject || !content) {
                return res.badRequest("Type, subject, and content are required");
            }

            // Validate communication type
            if (!Object.values(CommunicationType).includes(type)) {
                return res.badRequest(`Invalid communication type. Must be one of: ${Object.values(CommunicationType).join(", ")}`);
            }

            // Send communication
            const communication = await this.merchantRelationshipService.sendCommunication(
                merchantId,
                type,
                subject,
                content
            );

            return res.success("Communication sent to merchant", communication);
        } catch (error) {
            logger.error("Error sending communication to merchant:", error);
            return res.serverError("Failed to send communication to merchant");
        }
    });

    /**
   * Get merchant communications
   */
    getMerchantCommunications = this.asyncHandler(async (req: Request, res: Response)  =>  {
        try {
            const { merchantId } = req.params;

            // Get communications
            const communications = await this.merchantRelationshipService.getMerchantCommunications(merchantId);

            return res.success("Merchant communications retrieved", communications);
        } catch (error) {
            logger.error("Error getting merchant communications:", error);
            return res.serverError("Failed to get merchant communications");
        }
    });

    /**
   * Mark communication as read
   */
    markCommunicationAsRead = this.asyncHandler(async (req: Request, res: Response)  =>  {
        try {
            const { communicationId } = req.params;

            // Mark as read
            const communication = await this.merchantRelationshipService.markCommunicationAsRead(communicationId);

            return res.success("Communication marked as read", communication);
        } catch (error) {
            logger.error("Error marking communication as read:", error);
            return res.serverError("Failed to mark communication as read");
        }
    });

    /**
   * Create support ticket
   */
    createSupportTicket = this.asyncHandler(async (req: Request, res: Response)  =>  {
        try {
            const { merchantId } = req.params;
            const { subject, description, priority, category } = req.body;

            // Validate required fields
            if (!subject || !description) {
                return res.badRequest("Subject and description are required");
            }

            // Validate priority if provided
            if (priority && !Object.values(SupportTicketPriority).includes(priority)) {
                return res.badRequest(`Invalid priority. Must be one of: ${Object.values(SupportTicketPriority).join(", ")}`);
            }

            // Create ticket
            const ticket = await this.merchantRelationshipService.createSupportTicket(
                merchantId,
                subject,
                description,
                priority || (SupportTicketPriority).MEDIUM,
                category
            );

            return res.success("Support ticket created", ticket);
        } catch (error) {
            logger.error("Error creating support ticket:", error);
            return res.serverError("Failed to create support ticket");
        }
    });

    /**
   * Get merchant support tickets
   */
    getMerchantSupportTickets = this.asyncHandler(async (req: Request, res: Response)  =>  {
        try {
            const { merchantId } = req.params;

            // Get tickets
            const tickets = await this.merchantRelationshipService.getMerchantSupportTickets(merchantId);

            return res.success("Merchant support tickets retrieved", tickets);
        } catch (error) {
            logger.error("Error getting merchant support tickets:", error);
            return res.serverError("Failed to get merchant support tickets");
        }
    });

    /**
   * Add message to support ticket
   */
    addMessageToSupportTicket = this.asyncHandler(async (req: Request, res: Response)  =>  {
        try {
            const { ticketId } = req.params;
            const { content } = req.body;

            // Validate required fields
            if (!content) {
                return res.badRequest("Message content is required");
            }

            // Determine sender type based on user role
            const senderType = req.user?.role === "ADMIN" ? "ADMIN" : "MERCHANT";

            // Add message
            const message: string = await this.merchantRelationshipService.addMessageToSupportTicket(
                ticketId,
                req.user?.id ?? "",
                senderType,
                content
            );

            return res.success("Message added to support ticket", message);
        } catch (error) {
            logger.error("Error adding message to support ticket:", error);
            return res.serverError("Failed to add message to support ticket");
        }
    });

    /**
   * Update support ticket status
   */
    updateSupportTicketStatus = this.asyncHandler(async (req: Request, res: Response)  =>  {
        try {
            const { ticketId } = req.params;
            const { status, assignedTo } = req.body;

            // Validate required fields
            if (!status) {
                return res.badRequest("Status is required");
            }

            // Validate status
            if (!Object.values(SupportTicketStatus).includes(status)) {
                return res.badRequest(`Invalid status. Must be one of: ${Object.values(SupportTicketStatus).join(", ")}`);
            }

            // Update status
            const ticket = await this.merchantRelationshipService.updateSupportTicketStatus(
                ticketId,
                status,
                assignedTo
            );

            return res.success("Support ticket status updated", ticket);
        } catch (error) {
            logger.error("Error updating support ticket status:", error);
            return res.serverError("Failed to update support ticket status");
        }
    });

    /**
   * Initialize merchant onboarding
   */
    initializeOnboarding = this.asyncHandler(async (req: Request, res: Response)  =>  {
        try {
            const { merchantId } = req.params;

            // Initialize onboarding
            const onboarding = await this.merchantRelationshipService.initializeOnboarding(merchantId);

            return res.success("Merchant onboarding initialized", onboarding);
        } catch (error) {
            logger.error("Error initializing merchant onboarding:", error);
            return res.serverError("Failed to initialize merchant onboarding");
        }
    });

    /**
   * Get merchant onboarding
   */
    getMerchantOnboarding = this.asyncHandler(async (req: Request, res: Response)  =>  {
        try {
            const { merchantId } = req.params;

            // Get onboarding
            const onboarding = await this.merchantRelationshipService.getMerchantOnboarding(merchantId);

            if (!onboarding) {
                return res.notFound("Merchant onboarding", merchantId);
            }

            return res.success("Merchant onboarding retrieved", onboarding);
        } catch (error) {
            logger.error("Error getting merchant onboarding:", error);
            return res.serverError("Failed to get merchant onboarding");
        }
    });

    /**
   * Update onboarding step status
   */
    updateOnboardingStepStatus = this.asyncHandler(async (req: Request, res: Response)  =>  {
        try {
            const { stepId } = req.params;
            const { status } = req.body;

            // Validate required fields
            if (!status) {
                return res.badRequest("Status is required");
            }

            // Validate status
            if (!Object.values(OnboardingStepStatus).includes(status)) {
                return res.badRequest(`Invalid status. Must be one of: ${Object.values(OnboardingStepStatus).join(", ")}`);
            }

            // Update status
            const onboarding = await this.merchantRelationshipService.updateOnboardingStepStatus(
                stepId,
                status
            );

            return res.success("Onboarding step status updated", onboarding);
        } catch (error) {
            logger.error("Error updating onboarding step status:", error);
            return res.serverError("Failed to update onboarding step status");
        }
    });
}
