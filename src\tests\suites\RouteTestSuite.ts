// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { RouteTestHelper as ImportedRouteTestHelper } from "../helpers/RouteTestHelper";
import { RouteRegistry as ImportedRouteRegistry } from "../../core/RouteRegistry";
import { RouteVersionManager as ImportedRouteVersionManager } from "../../core/RouteVersionManager";
import { RouteMonitor as ImportedRouteMonitor } from "../../core/RouteMonitor";
import { RouteHealthChecker as ImportedRouteHealthChecker } from "../../core/RouteHealthChecker";
import { logger as Importedlogger } from "../../lib/logger";
import { RouteTestHelper as ImportedRouteTestHelper } from "../helpers/RouteTestHelper";
import { RouteRegistry as ImportedRouteRegistry } from "../../core/RouteRegistry";
import { RouteVersionManager as ImportedRouteVersionManager } from "../../core/RouteVersionManager";
import { RouteMonitor as ImportedRouteMonitor } from "../../core/RouteMonitor";
import { RouteHealthChecker as ImportedRouteHealthChecker } from "../../core/RouteHealthChecker";
import { logger as Importedlogger } from "../../lib/logger";

/**
 * Route test suite
 * This class provides a suite of tests for routes
 */
export class RouteTestSuite {
  private app: Application;
  private routeTestHelper: RouteTestHelper;
  private routeRegistry: RouteRegistry;
  private routeVersionManager: RouteVersionManager;
  private routeMonitor: RouteMonitor;
  private routeHealthChecker: RouteHealthChecker;
  
  /**
   * Create a new route test suite
   * @param app Express application
   */
  constructor(app: Application) {
    this.app = app;
    this.routeTestHelper = new RouteTestHelper(app);
    this.routeRegistry = (RouteRegistry).getInstance();
    this.routeVersionManager = (RouteVersionManager).getInstance();
    this.routeMonitor = (RouteMonitor).getInstance();
    this.routeHealthChecker = (RouteHealthChecker).getInstance();
  }
  
  /**
   * Run all tests
   */
  public async runAllTests(): Promise<void> {
    await this.testRouteRegistry();
    await this.testRouteVersionManager();
    await this.testRouteMonitor();
    await this.testRouteHealthChecker();
    await this.testExplorerRoutes();
    await this.testHealthCheckRoutes();
  }
  
  /**
   * Test route registry
   */
  private async testRouteRegistry(): Promise<void> {
    logger.info("Testing route registry...");
    
    // Create mock router
    const router = this.routeTestHelper.createMockRouter(
      "test",
      "/api/test",
      (req: Request, res: Response)  =>  {
        res.json({ success: true });
      }
    );
    
    // Register router
    this.app.use("/api/test", router);
    
    // Test route
    await this.routeTestHelper.testUnauthenticatedRoute(
      "get",
      "/api/test",
      200,
      { success: true }
    );
    
    // Test route registry
    expect(this.routeRegistry.has("test")).toBetrue;
    expect(this.routeRegistry.getMetadata("test").path).toBe("/api/test");
    
    logger.info("Route registry tests passed");
  }
  
  /**
   * Test route version manager
   */
  private async testRouteVersionManager(): Promise<void> {
    logger.info("Testing route version manager...");
    
    // Create mock versioned router
    const router = this.routeTestHelper.createMockVersionedRouter(
      "v1",
      "test",
      "/test",
      (req: Request, res: Response)  =>  {
        res.json({ success: true, version: "v1" });
      }
    );
    
    // Register router
    this.app.use("/api/v1/test", router);
    
    // Test route
    await this.routeTestHelper.testUnauthenticatedRoute(
      "get",
      "/api/v1/test",
      200,
      { success: true, version: "v1" }
    );
    
    // Test route version manager
    expect(this.routeVersionManager.hasVersionedRoute("v1", "test")).toBetrue;
    expect(this.routeVersionManager.getVersions()).toContain("v1");
    
    logger.info("Route version manager tests passed");
  }
  
  /**
   * Test route monitor
   */
  private async testRouteMonitor(): Promise<void> {
    logger.info("Testing route monitor...");
    
    // Reset metrics
    this.routeTestHelper.resetRouteMetrics();
    
    // Create mock router
    const router = this.routeTestHelper.createMockRouter(
      "monitor-test",
      "/api/monitor-test",
      (req: Request, res: Response)  =>  {
        res.json({ success: true });
      }
    );
    
    // Register router
    this.app.use("/api/monitor-test", router);
    
    // Apply monitoring middleware
    this.app.use(this.routeMonitor.createMonitoringMiddleware());
    
    // Test route
    await this.routeTestHelper.testUnauthenticatedRoute(
      "get",
      "/api/monitor-test",
      200,
      { success: true }
    );
    
    // Test route monitor
    const metrics = this.routeMonitor.getMetrics("monitor-test");
    expect((metrics).hits).toBeGreaterThan(0);
    
    logger.info("Route monitor tests passed");
  }
  
  /**
   * Test route health checker
   */
  private async testRouteHealthChecker(): Promise<void> {
    logger.info("Testing route health checker...");
    
    // Create health check router
    const router = this.routeHealthChecker.createHealthCheckRouter();
    
    // Register router
    this.app.use("/api/health", router);
    
    // Test route
    await this.routeTestHelper.testUnauthenticatedRoute(
      "get",
      "/api/health",
      200
    );
    
    // Test route health checker
    const health = this.routeHealthChecker.checkSystemHealth();
    expect((health).status).toBeDefined();
    
    logger.info("Route health checker tests passed");
  }
  
  /**
   * Test explorer routes
   */
  private async testExplorerRoutes(): Promise<void> {
    logger.info("Testing explorer routes...");
    
    // Test route
    await this.routeTestHelper.testUnauthenticatedRoute(
      "get",
      "/api/explorer",
      200
    );
    
    logger.info("Explorer routes tests passed");
  }
  
  /**
   * Test health check routes
   */
  private async testHealthCheckRoutes(): Promise<void> {
    logger.info("Testing health check routes...");
    
    // Test route
    await this.routeTestHelper.testUnauthenticatedRoute(
      "get",
      "/api/health",
      200
    );
    
    logger.info("Health check routes tests passed");
  }
}

export default RouteTestSuite;
