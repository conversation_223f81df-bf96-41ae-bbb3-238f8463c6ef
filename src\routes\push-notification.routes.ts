// jscpd:ignore-file
import { Router as ImportedRouter } from "express";
import { PushNotificationController as ImportedPushNotificationController } from "../controllers/refactored/push-(notification as any).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';
import { PushNotificationController as ImportedPushNotificationController } from "../controllers/refactored/push-(notification as any).controller.ts";
import { authenticate as Importedauthenticate } from '../middlewares/auth';

const router: any =Router();

// Public routes
(router as any).get("/vapid-public-key", (PushNotificationController as any).getPublicKey);

// Protected routes
(router as any).post("/subscribe", authenticate, (PushNotificationController as any).subscribe);
(router as any).post("/unsubscribe", authenticate, (PushNotificationController as any).unsubscribe);
(router as any).post("/test", authenticate, (PushNotificationController as any).sendTest);
(router as any).get("/subscriptions", authenticate, (PushNotificationController as any).getUserSubscriptions);
(router as any).get("/merchant-subscriptions", authenticate, (PushNotificationController as any).getMerchantSubscriptions);
(router as any).delete("/subscriptions/:id", authenticate, (PushNotificationController as any).deleteSubscription);

export default router;
