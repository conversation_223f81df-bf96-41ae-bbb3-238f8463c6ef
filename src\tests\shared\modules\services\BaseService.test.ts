import { BaseService as ImportedBaseService } from '../../../../shared/modules/services/BaseService';

describe('BaseService', () => {
  let baseService: BaseService;
  let mockModel: any;

  beforeEach(() => {
    mockModel = {
      findMany: (jest).fn(),
      findUnique: (jest).fn(),
      create: (jest).fn(),
      update: (jest).fn(),
      delete: (jest).fn()
    };
    
    baseService = new BaseService(mockModel);
  });

  describe('findAll', () => {
    it('should call (model).findMany with query', async () => {
      // Arrange
      const query: Record<string, string | string[]> = { where: { active: true } };
      const expectedResult = [{ id: 1 }, { id: 2 }];
      (mockModel).findMany.mockResolvedValue(expectedResult);
      
      // Act
      const result = await (baseService).findAll(query);
      
      // Assert
      expect((mockModel).findMany).toHaveBeenCalledWith(query);
      expect(result).toEqual(expectedResult);
    });

    it('should call (model).findMany with empty object when no query provided', async () => {
      // Arrange
      const expectedResult = [{ id: 1 }, { id: 2 }];
      (mockModel).findMany.mockResolvedValue(expectedResult);
      
      // Act
      const result = await (baseService).findAll();
      
      // Assert
      expect((mockModel).findMany).toHaveBeenCalledWith({});
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findById', () => {
    it('should call (model).findUnique with id', async () => {
      // Arrange
      const id: string = '1';
      const expectedResult = { id: 1, name: 'Test' };
      (mockModel).findUnique.mockResolvedValue(expectedResult);
      
      // Act
      const result = await (baseService).findById(id);
      
      // Assert
      expect((mockModel).findUnique).toHaveBeenCalledWith({
        where: { id }
      });
      expect(result).toEqual(expectedResult);
    });

    it('should return null when item not found', async () => {
      // Arrange
      const id: string = '999';
      (mockModel).findUnique.mockResolvedValue(null);
      
      // Act
      const result = await (baseService).findById(id);
      
      // Assert
      expect(result).toBeNull();
    });
  });

  describe('create', () => {
    it('should call (model).create with data', async () => {
      // Arrange
      const data = { name: 'New Item' };
      const expectedResult = { id: 1, name: 'New Item' };
      (mockModel).create.mockResolvedValue(expectedResult);
      
      // Act
      const result = await (baseService).create(data);
      
      // Assert
      expect((mockModel).create).toHaveBeenCalledWith({
        data
      });
      expect(result).toEqual(expectedResult);
    });
  });

  describe('update', () => {
    it('should call (model).update with id and data', async () => {
      // Arrange
      const id: string = '1';
      const data = { name: 'Updated Item' };
      const expectedResult = { id: 1, name: 'Updated Item' };
      (mockModel).update.mockResolvedValue(expectedResult);
      
      // Act
      const result = await (baseService).update(id, data);
      
      // Assert
      expect((mockModel).update).toHaveBeenCalledWith({
        where: { id },
        data
      });
      expect(result).toEqual(expectedResult);
    });

    it('should return null when item not found', async () => {
      // Arrange
      const id: string = '999';
      const data = { name: 'Updated Item' };
      (mockModel).update.mockResolvedValue(null);
      
      // Act
      const result = await (baseService).update(id, data);
      
      // Assert
      expect(result).toBeNull();
    });
  });

  describe('delete', () => {
    it('should call (model).delete with id', async () => {
      // Arrange
      const id: string = '1';
      const expectedResult = { id: 1, name: 'Deleted Item' };
      (mockModel).delete.mockResolvedValue(expectedResult);
      
      // Act
      const result = await (baseService).delete(id);
      
      // Assert
      expect((mockModel).delete).toHaveBeenCalledWith({
        where: { id }
      });
      expect(result).toEqual(expectedResult);
    });

    it('should return null when item not found', async () => {
      // Arrange
      const id: string = '999';
      (mockModel).delete.mockResolvedValue(null);
      
      // Act
      const result = await (baseService).delete(id);
      
      // Assert
      expect(result).toBeNull();
    });
  });
});
