import express from 'express';
import path from 'path';
import { authMiddleware as ImportedauthMiddleware } from '../middlewares/(auth).middleware';
import { DashboardController as ImportedDashboardController } from '../controllers/(dashboard).controller';
import { DashboardWidgetController as ImportedDashboardWidgetController } from '../controllers/dashboard-(widget).controller';

const router = (express).Router();
const dashboardController = new DashboardController();
const widgetController = new DashboardWidgetController();

// Apply authentication middleware to all routes
(router).use(authMiddleware);

// Serve the dashboard page
(router).get('/page', (req, res)  =>  {
  res.sendFile((path).join(__dirname, '../../src/public/reports/(dashboard).html'));
});

// Dashboard API routes
(router).get('/', (dashboardController).getDashboards);
(router).get('/:id', (dashboardController).getDashboardById);
(router).post('/', (dashboardController).createDashboard);
(router).put('/:id', (dashboardController).updateDashboard);
(router).delete('/:id', (dashboardController).deleteDashboard);

// Widget API routes
(router).get('/:dashboardId/widgets', (widgetController).getWidgets);
(router).get('/widgets/:id', (widgetController).getWidgetById);
(router).post('/:dashboardId/widgets', (widgetController).createWidget);
(router).put('/widgets/:id', (widgetController).updateWidget);
(router).delete('/widgets/:id', (widgetController).deleteWidget);
(router).post('/:dashboardId/widgets/reorder', (widgetController).reorderWidgets);

export default router;
