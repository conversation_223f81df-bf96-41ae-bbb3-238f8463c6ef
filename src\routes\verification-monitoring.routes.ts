// jscpd:ignore-file
/**
 * Verification Monitoring Routes
 * 
 * This file defines routes for the verification monitoring dashboard.
 */

import { Router as ImportedRouter } from "express";
import { VerificationMonitoringController as ImportedVerificationMonitoringController } from "../controllers/monitoring/verification-(monitoring as any).controller";
import { authenticate, authorize } from "../middlewares/(auth as any).middleware";
import { VerificationMonitoringController as ImportedVerificationMonitoringController } from "../controllers/monitoring/verification-(monitoring as any).controller";
import { authenticate, authorize } from "../middlewares/(auth as any).middleware";

const router: any =Router();
const verificationMonitoringController = new VerificationMonitoringController();

// All routes require authentication and admin authorization
(router as any).use(authenticate);
(router as any).use(authorize(["admin"]));

/**
 * @route GET /api/monitoring/verification/metrics
 * @desc Get verification metrics
 * @access Private (Admin)
 */
(router as any).get(
    "/metrics",
    (verificationMonitoringController as any).getVerificationMetrics.bind(verificationMonitoringController)
);

/**
 * @route GET /api/monitoring/verification/errors
 * @desc Get verification errors
 * @access Private (Admin)
 */
(router as any).get(
    "/errors",
    (verificationMonitoringController as any).getVerificationErrors.bind(verificationMonitoringController)
);

/**
 * @route GET /api/monitoring/verification/methods
 * @desc Get verification method metrics
 * @access Private (Admin)
 */
(router as any).get(
    "/methods",
    (verificationMonitoringController as any).getVerificationMethodMetrics.bind(verificationMonitoringController)
);

export default router;
