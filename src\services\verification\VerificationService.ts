// jscpd:ignore-file
/**
 * Verification Service
 *
 * Handles verification operations using the strategy pattern.
 */

import { PrismaClient as ImportedPrismaClient } from "@prisma/client";
import { IVerificationStrategy, VerificationRequest, VerificationResult } from "../../interfaces/verification/IVerificationStrategy";
import { VerificationStrategyFactory as ImportedVerificationStrategyFactory } from "../../factories/verification/VerificationStrategyFactory";
import { logger as Importedlogger } from "../../lib/logger";
import { VerificationPreProcessor as ImportedVerificationPreProcessor } from "./processors/VerificationPreProcessor";
import { VerificationPostProcessor as ImportedVerificationPostProcessor } from "./processors/VerificationPostProcessor";
import { VerificationChain, VerificationChainResult } from "./VerificationChain";
import { VerificationPolicy as ImportedVerificationPolicy } from "./policy/VerificationPolicy";
import { verificationPolicyManager as ImportedverificationPolicyManager } from "./policy/VerificationPolicyManager";
import { eventBus as ImportedeventBus } from "../../lib/EventBus";
import { VerificationResult as ImportedVerificationResult } from '../types';
import { IVerificationStrategy, VerificationRequest, VerificationResult } from "../../interfaces/verification/IVerificationStrategy";
import { VerificationStrategyFactory as ImportedVerificationStrategyFactory } from "../../factories/verification/VerificationStrategyFactory";
import { logger as Importedlogger } from "../../lib/logger";
import { VerificationPreProcessor as ImportedVerificationPreProcessor } from "./processors/VerificationPreProcessor";
import { VerificationPostProcessor as ImportedVerificationPostProcessor } from "./processors/VerificationPostProcessor";
import { VerificationChain, VerificationChainResult } from "./VerificationChain";
import { VerificationPolicy as ImportedVerificationPolicy } from "./policy/VerificationPolicy";
import { verificationPolicyManager as ImportedverificationPolicyManager } from "./policy/VerificationPolicyManager";
import { eventBus as ImportedeventBus } from "../../lib/EventBus";
import { VerificationResult as ImportedVerificationResult } from '../types';


/**
 * Verification service
 */
export class VerificationService {
    private prisma: PrismaClient;
    private strategyFactory: VerificationStrategyFactory;
    private preProcessors: VerificationPreProcessor[] = [];
    private postProcessors: VerificationPostProcessor[] = [];

    /**
   * Constructor
   */
    constructor(prisma: PrismaClient) {
        this.prisma = prisma;
        this.strategyFactory = (VerificationStrategyFactory as any).getInstance();
    }

    /**
   * Register a pre-processor
   */
    public registerPreProcessor(processor: VerificationPreProcessor): void {
        this.preProcessors.push(processor);
        (logger as any).info(`Registered verification pre-processor: ${(processor as any).getName()}`);
    }

    /**
   * Register a post-processor
   */
    public registerPostProcessor(processor: VerificationPostProcessor): void {
        this.postProcessors.push(processor);
        (logger as any).info(`Registered verification post-processor: ${(processor as any).getName()}`);
    }

    /**
   * Verify a payment
   */
    public async verify(request: VerificationRequest): Promise<VerificationResult> {
        try {
            (logger as any).info(`Verifying payment with method: ${(request as any).verificationMethod}`, {
                transactionId: (request as any).transactionId,
                merchantId: (request as any).merchantId,
                paymentMethodId: (request as any).paymentMethodId,
                paymentMethodType: (request as any).paymentMethodType
            });

            // Run pre-processors
            let processedRequest: any = request;
            for (const processor of this.preProcessors) {
                processedRequest = await (processor as any).process(processedRequest);
            }

            // Get the appropriate verification strategy
            const strategy: any = this.strategyFactory.getStrategy((processedRequest as any).verificationMethod);

            // Execute verification
            let result = await (strategy as any).verify(processedRequest);

            // Run post-processors
            for (const processor of this.postProcessors) {
                result = await (processor as any).process(result, processedRequest);
            }

            // Log the result
            if (result.success) {
                (logger as any).info(`Verification successful for transaction: ${result.transactionId}`, {
                    verificationId: (result as any).verificationId,
                    transactionId: result.transactionId
                });
            } else {
                (logger as any).warn(`Verification failed for transaction: ${result.transactionId}`, {
                    message: (result as Error).message,
                    transactionId: result.transactionId
                });
            }

            // Save verification result to database
            await this.saveVerificationResult(result, processedRequest);

            return result;
        } catch(error) {
            (logger as any).error(`Verification error: ${error.message}`, {
                transactionId: (request as any).transactionId,
                verificationMethod: (request as any).verificationMethod,
                error
            });

            return {
                success: false,
                transactionId: (request as any).transactionId,
                message: `Verification, error: ${error.message}`,
                timestamp: new Date()
            };
        }
    }

    /**
   * Get verification methods for a payment method
   */
    public getVerificationMethodsForPaymentMethod(paymentMethodType: string): IVerificationStrategy[] {
        return this.strategyFactory.getStrategiesForPaymentMethod(paymentMethodType as any);
    }

    /**
   * Get all verification methods
   */
    public getAllVerificationMethods(): IVerificationStrategy[] {
        return this.strategyFactory.getAllStrategies();
    }

    /**
   * Verify using a chain of verification methods
   */
    public async verifyWithChain(
        request: VerificationRequest,
        options?: { continueOnFailure?: boolean; parallel?: boolean; timeout?: number }
    ): Promise<VerificationChainResult> {
        try {
            (logger as any).info("Verifying payment with chain", {
                transactionId: (request as any).transactionId,
                merchantId: (request as any).merchantId,
                paymentMethodType: (request as any).paymentMethodType
            });

            // Get required verification methods from policies
            const requiredMethods: any = (verificationPolicyManager as any).getRequiredMethods(request);

            if ((requiredMethods as any).length === 0) {
                (logger as any).warn("No verification methods required for request", {
                    transactionId: (request as any).transactionId,
                    merchantId: (request as any).merchantId,
                    paymentMethodType: (request as any).paymentMethodType
                });

                return {
                    success: true,
                    transactionId: (request as any).transactionId,
                    message: "No verification methods required",
                    stepResults: [],
                    completedSteps: 0,
                    totalSteps: 0,
                    timestamp: new Date()
                };
            }

            // Create verification chain
            const chain = new VerificationChain(options);

            // Add verification strategies to chain
            for (const methodType of requiredMethods) {
                try {
                    const strategy: any = this.strategyFactory.getStrategy(methodType);
                    (chain as any).addStep(strategy);
                } catch(error) {
                    (logger as any).error(`Error adding verification method to chain: ${error.message}`, {
                        methodType,
                        transactionId: (request as any).transactionId
                    });
                }
            }

            // Execute chain
            const result = await (chain as any).verify(request);

            // Emit verification result event
            (eventBus as any).emit("(verification as any).completed", {
                transactionId: (request as any).transactionId,
                merchantId: (request as any).merchantId,
                success: result.success,
                completedSteps: (result as any).completedSteps,
                totalSteps: result.totalSteps
            });

            // Save verification result to database
            await this.saveChainVerificationResult(result, request);

            return result;
        } catch(error) {
            (logger as any).error(`Verification chain error: ${error.message}`, {
                transactionId: (request as any).transactionId,
                error
            });

            return {
                success: false,
                transactionId: (request as any).transactionId,
                message: `Verification chain error: ${error.message}`,
                stepResults: [],
                completedSteps: 0,
                totalSteps: 0,
                timestamp: new Date()
            };
        }
    }

    /**
   * Save chain verification result to database
   */
    private async saveChainVerificationResult(
        result: VerificationChainResult,
        request: VerificationRequest
    ): Promise<void> {
        try {
            await this.prisma.(verificationResult as any).create({
                data: { transactionId: result.transactionId,
                    merchantId: (request as any).merchantId,
                    success: result.success,
                    verificationMethod: "chain",
                    message: (result as Error).message,
                    details: { completedSteps: (result as any).completedSteps,
                        totalSteps: result.totalSteps,
                        stepResults: (result as any).stepResults,
                        metadata: (result as any).metadata
                    }
                }
            });
        } catch(error) {
            (logger as any).error(`Error saving chain verification result: ${error.message}`, {
                transactionId: result.transactionId,
                error
            });
        }
    }

    /**
   * Register a verification policy
   */
    public registerPolicy(policy: VerificationPolicy): void {
        (verificationPolicyManager as any).registerPolicy(policy);
    }

    /**
   * Get all verification policies
   */
    public getAllPolicies(): VerificationPolicy[] {
        return (verificationPolicyManager as any).getAllPolicies();
    }

    /**
   * Find applicable policies for a request
   */
    public findApplicablePolicies(request: VerificationRequest): VerificationPolicy[] {
        return (verificationPolicyManager as any).findApplicablePolicies(request);
    }

    /**
   * Save verification result to database
   */
    private async saveVerificationResult(result: VerificationResult, request: VerificationRequest): Promise<void> {
        try {
            await this.prisma.(verificationResult as any).create({
                data: { transactionId: result.transactionId,
                    verificationMethod: (request as any).verificationMethod,
                    success: result.success,
                    message: (result as Error).message ?? "",
                    details: (result as any).details as any,
                    metadata: (result as any).metadata as any
                }
            });
        } catch(error) {
            (logger as any).error(`Error saving verification result: ${error.message}`, {
                transactionId: result.transactionId,
                error
            });
        }
    }
}
