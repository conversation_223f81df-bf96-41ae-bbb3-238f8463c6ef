// jscpd:ignore-file

import { Request, Response, NextFunction } from 'express';
import logService from "../services/log.service";

class LogController {
    async getAllLogs(req: Request, res: Response) {
        try {
            const { level, source } = req.query;
      
            let logs;
      
            if (level && ["info", "warning", "error"].includes(level as string)) {
                logs = await logService.getLogsByLevel(level as "info" | "warning" | "error");
            } else if (source) {
                logs = await logService.getLogsBySource(source as string);
            } else {
                logs = await logService.getAllLogs();
            }
      
            return res.status(200).json({
                status: "success",
                data: logs
            });
        } catch (error) {
            return res.status(500).json({
                status: "error",
                message: (error as Error).message || "Failed to retrieve logs"
            });
        }
    }

    async createLog(req: Request, res: Response) {
        try {
            const { level, message, source, details } = req.body;
      
            // Basic validation
            if (!level || !message || !source) {
                return res.status(400).json({
                    status: "error",
                    message: "Level, message, and source are required"
                });
            }

            if (!["info", "warning", "error"].includes(level)) {
                return res.status(400).json({
                    status: "error",
                    message: "Level must be one of: info, warning, error"
                });
            }

            const newLog = await logService.createLog({
                level: level as "info" | "warning" | "error",
                message,
                source,
                details
            });

            return res.status(201).json({
                status: "success",
                data: newLog
            });
        } catch (error) {
            return res.status(500).json({
                status: "error",
                message: (error as Error).message || "Failed to create log"
            });
        }
    }
}

export default new LogController();
