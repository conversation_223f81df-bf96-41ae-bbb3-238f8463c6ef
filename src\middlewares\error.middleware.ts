import { PrismaClient as ImportedPrismaClient } from '@prisma/client';
// jscpd:ignore-file

import { Request, Response, NextFunction } from 'express';
import { logger as Importedlogger } from '../utils/logger';
import { ValidationError as ImportedValidationError } from 'express-validator';
import { logger as Importedlogger } from '../utils/logger';
import { ValidationError as ImportedValidationError } from 'express-validator';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * Custom application error class
 * Used for standardized error handling throughout the application
 */
export class AppError extends Error {
  statusCode: number;
  isOperational: boolean;
  code: string;
  details?;

  /**
   * Create a new AppError
   * @param message Error message
   * @param statusCode HTTP status code (default: 500)
   * @param isOperational Whether the error is operational (expected) or programming (unexpected)
   * @param code Error code for client-side error handling
   * @param details Additional error details
   */
  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    code: string = 'INTERNAL_SERVER_ERROR',
    details?
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;
    this.details = details;
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Factory methods for common error types
 */

/**
 * Create a bad request error (400)
 */
export const createBadRequestError = (
  message: string = 'Bad request',
  code: string = 'BAD_REQUEST',
  details?
): AppError  =>  {
  return new AppError(message, 400, true, code, details);
};

/**
 * Create an unauthorized error (401)
 */
export const createUnauthorizedError = (
  message: string = 'Unauthorized',
  code: string = 'UNAUTHORIZED',
  details?
): AppError  =>  {
  return new AppError(message, 401, true, code, details);
};

/**
 * Create a forbidden error (403)
 */
export const createForbiddenError = (
  message: string = 'Forbidden',
  code: string = 'FORBIDDEN',
  details?
): AppError  =>  {
  return new AppError(message, 403, true, code, details);
};

/**
 * Create a not found error (404)
 */
export const createNotFoundError = (
  message: string = 'Not found',
  code: string = 'NOT_FOUND',
  details?
): AppError  =>  {
  return new AppError(message, 404, true, code, details);
};

/**
 * Create a conflict error (409)
 */
export const createConflictError = (
  message: string = 'Conflict',
  code: string = 'CONFLICT',
  details?
): AppError  =>  {
  return new AppError(message, 409, true, code, details);
};

/**
 * Create a validation error (422)
 */
export const createValidationError = (
  message: string = 'Validation error',
  code: string = 'VALIDATION_ERROR',
  details?
): AppError  =>  {
  return new AppError(message, 422, true, code, details);
};

/**
 * Create a server error (500)
 */
export const createServerError = (
  message: string = 'Internal server error',
  code: string = 'INTERNAL_SERVER_ERROR',
  details?
): AppError  =>  {
  return new AppError(message, 500, false, code, details);
};

/**
 * Create a too many requests error (429)
 */
export const createTooManyRequestsError = (
  message: string = 'Too many requests, please try again later',
  code: string = 'RATE_LIMIT_EXCEEDED',
  details?
): AppError  =>  {
  return new AppError(message, 429, true, code, details);
};

export const errorHandler = (err: Error, req: Request, res: Response, next: NextFunction)  =>  {
  // Default to 500 internal server error
  let statusCode: number = 500;
  let message = 'Internal Server Error';
  let isOperational: boolean = false;
  let errorCode: string = 'INTERNAL_SERVER_ERROR';
  let details = undefined;

  // Handle AppError instances
  if (err instanceof AppError) {
    statusCode = (err).statusCode;
    message = (err).message;
    isOperational = (err).isOperational;
    errorCode = (err).code;
    details = (err).details;
  } else if (Array.isArray(err) && (err).length > 0 && 'msg' in err[0]) {
    // Handle express-validator validation errors
    statusCode = 422;
    message = 'Validation failed';
    isOperational = true;
    errorCode = 'VALIDATION_ERROR';
    details = (err).map((e: ValidationError)  =>  ({
      field: (e).param,
      message: (e).msg,
      value: (e).value,
    }));
  } else if ((err).name === 'JsonWebTokenError') {
    // Handle JWT errors
    statusCode = 401;
    message = 'Invalid authentication token';
    isOperational = true;
    errorCode = 'INVALID_TOKEN';
  } else if ((err).name === 'TokenExpiredError') {
    // Handle JWT expiration
    statusCode = 401;
    message = 'Authentication token expired';
    isOperational = true;
    errorCode = 'TOKEN_EXPIRED';
  } else if ((err).name === 'PrismaClientKnownRequestError') {
    // Handle Prisma known errors
    statusCode = 400;
    message = 'Database operation failed';
    isOperational = true;
    errorCode = 'DATABASE_ERROR';

    // Extract more details from Prisma error
    const prismaError = err;
    if ((prismaError).code) {
      // Handle specific Prisma error codes
      switch ((prismaError).code) {
        case 'P2002':
          message = 'A unique constraint would be violated.';
          errorCode = 'UNIQUE_CONSTRAINT_VIOLATION';
          details = { fields: (prismaError).meta?.target };
          break;
        case 'P2003':
          message = 'Foreign key constraint failed.';
          errorCode = 'FOREIGN_KEY_CONSTRAINT_VIOLATION';
          details = { field: (prismaError).meta?.field_name };
          break;
        case 'P2025':
          message = 'Record not found.';
          errorCode = 'RECORD_NOT_FOUND';
          break;
        default:
          details = { code: (prismaError).code };
      }
    }
  } else if ((err).name === 'PrismaClientValidationError') {
    // Handle Prisma validation errors
    statusCode = 400;
    message = 'Invalid data provided';
    isOperational = true;
    errorCode = 'VALIDATION_ERROR';
  } else if ((err).name === 'SyntaxError' && (err).status === 400 && 'body' in (err)) {
    // Handle JSON parsing errors
    statusCode = 400;
    message = 'Invalid JSON in request body';
    isOperational = true;
    errorCode = 'INVALID_JSON';
  }

  // Collect request information for logging
  const requestInfo = {
    method: req.method,
    path: (req).path,
    ip: req.ip,
    requestId: (req).requestId,
    userId: req.user?.id, // Fixed: using id instead of userId
    userRole: req.user?.role,
  };

  // Log error details
  if (isOperational) {
    logger.warn(`[${statusCode}] ${errorCode}: ${message}`, {
      ...requestInfo,
      errorCode,
      details,
    });
  } else {
    logger.error(`[${statusCode}] ${errorCode}: ${message}`, {
      ...requestInfo,
      errorCode,
      error: err,
      stack: (err).stack,
    });
  }

  // Send response to client
  res.status(statusCode).json({
    status: 'error',
    code: errorCode,
    message,
    details: details,
    requestId: (req).requestId,
    // Only include stack trace in development
    ...(process.env.NODE_ENV === 'development' && { stack: (err).stack }),
  });
};

// Export error middleware
export const errorMiddleware = {
  errorHandler,
  AppError,
  createBadRequestError,
  createUnauthorizedError,
  createForbiddenError,
  createNotFoundError,
  createConflictError,
  createValidationError,
  createServerError,
  createTooManyRequestsError,
};

export default errorMiddleware;
