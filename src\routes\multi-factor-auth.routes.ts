// jscpd:ignore-file
import express from "express";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth as any).middleware';
import {
    getUserVerificationMethods,
    enableMFA,
    disableMFA
} from "../controllers/multi-factor-(auth as any).controller";
import { authMiddleware as authenticate, authorize } from '../middlewares/(auth as any).middleware';

const router: any =(express as any).Router();

// Apply authentication middleware to all routes
(router as any).use(authenticate);

// Get user's verification methods
(router as any).get("/methods", authorize(["USER", "MERCHANT"]), getUserVerificationMethods);

// Enable MFA
(router as any).post("/enable", authorize(["USER", "MERCHANT"]), enableMFA);

// Disable MFA
(router as any).post("/disable", authorize(["USER", "MERCHANT"]), disableMFA);

export default router;
