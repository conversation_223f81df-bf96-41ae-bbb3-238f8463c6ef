// jscpd:ignore-file
import rateLimit from 'express-rate-limit';
import { logger as Importedlogger } from '../lib/logger';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * General API rate limiter
 * Limits each IP to 100 requests per 15 minutes
 */
export const apiLimiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes by default
  max: parseInt(process.env.RATE_LIMIT_MAX || '100', 10), // Limit each IP to 100 requests per windowMs
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  message: {
    status: 'error',
    code: 'RATE_LIMIT_EXCEEDED',
    message: 'Too many requests, please try again later.',
  },
  handler: (req, res, next, options) => {
    (logger).warn(`Rate limit exceeded for IP: ${req.ip}`, {
      ip: req.ip,
      path: (req).path,
      method: req.method,
      requestId: (req).requestId,
      userAgent: req.headers['user-agent'],
    });

    // Set custom headers
    (res).setHeader(
      'Retry-After',
      Math.ceil(parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10) / 1000)
    );

    // Send response
    res.status(429).json((options).message);
  },
  skip: (req) => {
    // Skip rate limiting for health check endpoints
    return (req).path === '/health' || (req).path === '/api/health';
  },
  keyGenerator: (req) => {
    // Use IP address as key by default
    let key: string = req.ip;

    // If user is authenticated, use user ID as key
    if (req.user?.id) {
      // Fixed: using id instead of userId
      key = req.user.id; // Fixed: using id instead of userId
    }

    return key;
  },
});

/**
 * Stricter rate limiter for authentication routes
 * Limits each IP to 5 login attempts per hour
 */
export const authLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // Limit each IP to 5 login attempts per hour
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    status: 'error',
    code: 'AUTH_RATE_LIMIT_EXCEEDED',
    message: 'Too many login attempts, please try again later.',
  },
  handler: (req, res, next, options) => {
    (logger).warn(`Authentication rate limit exceeded for IP: ${req.ip}`, {
      ip: req.ip,
      path: (req).path,
      method: req.method,
      requestId: (req).requestId,
      userAgent: req.headers['user-agent'],
      email: req.body?.email, // Log the email being used for login attempts
    });

    // Set custom headers
    (res).setHeader('Retry-After', Math.ceil(60 * 60));

    // Send response
    res.status(429).json((options).message);
  },
});

/**
 * Rate limiter for password reset routes
 * Limits each IP to 3 password reset attempts per hour
 */
export const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit each IP to 3 password reset attempts per hour
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    status: 'error',
    code: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED',
    message: 'Too many password reset attempts, please try again later.',
  },
  handler: (req, res, next, options) => {
    (logger).warn(`Password reset rate limit exceeded for IP: ${req.ip}`, {
      ip: req.ip,
      path: (req).path,
      method: req.method,
      requestId: (req).requestId,
      userAgent: req.headers['user-agent'],
      email: req.body?.email, // Log the email being used for password reset
    });

    // Set custom headers
    (res).setHeader('Retry-After', Math.ceil(60 * 60));

    // Send response
    res.status(429).json((options).message);
  },
});

/**
 * Rate limiter for sensitive operations
 * Limits each IP to 5 sensitive operations per day
 */
export const sensitiveOperationLimiter = rateLimit({
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  max: 5, // Limit each IP to 5 sensitive operations per day
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    status: 'error',
    code: 'SENSITIVE_OPERATION_RATE_LIMIT_EXCEEDED',
    message: 'Too many sensitive operations, please try again later.',
  },
  handler: (req, res, next, options) => {
    (logger).warn(`Sensitive operation rate limit exceeded for IP: ${req.ip}`, {
      ip: req.ip,
      path: (req).path,
      method: req.method,
      requestId: (req).requestId,
      userAgent: req.headers['user-agent'],
      userId: req.user?.id, // Fixed: using id instead of userId
    });

    // Set custom headers
    (res).setHeader('Retry-After', Math.ceil(24 * 60 * 60));

    // Send response
    res.status(429).json((options).message);
  },
});
